from typing import List, Dict, Optional
import logging
import json
import time
import re
from datetime import datetime
import google.generativeai as genai
from langchain.prompts import ChatPromptTemplate

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configure Gemini API
# This will use the key from ingestion.py if already configured
# If you need to set a different key, you can set the GEMINI_API_KEY environment variable
import os
api_key = os.environ.get('GEMINI_API_KEY')
if api_key:
    genai.configure(api_key=api_key)
    logger.info("Configured Gemini API with environment variable")

class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

PROMPT_MAP_COLUMNS = """
You are an expert in data mapping and column standardization. Your task is to map standardized template headers to input column headers.

Given the following:
1. Original column headers: {original_headers}
2. Sample data (first 5 rows): {sample_data}
3. Template headers to map to: {template_headers}
4. Previous mapping issues (if any): {issues}

Please provide a mapping in JSON format where:
- Keys are the template headers
- Values are the corresponding original column headers
- Only include mappings you are confident about
- Ensure one-to-one mapping (no duplicate original headers as values)
- If no good match exists for a template header, omit it from the mapping

The following columns are REQUIRED and must always be included in the mapping:
- Origin Zip
- Destination Zip
- Origin City
- Destination City
- Origin State
- Destination State
- Shipment Mode
- Equipment Type
- Customer Name
- Broker Name
- Broker Primary Reference
- COGS Total
- Revenue Total
- Origin Close

For these required columns, if no good match exists, set the value to null, but still include the key in the mapping.

Consider the following when mapping:
1. Column names might have different formats (camelCase, snake_case, etc.)
2. Some columns might be abbreviated or use synonyms
3. Pay attention to the sample data to understand the column's content
4. Ensure the mapping makes logical sense based on the data

Return only the JSON mapping, no additional text.
"""

def validate_mapping(mapping: Dict[str, str], template_headers: List[str]) -> None:
    """
    Validate the mapping dictionary to ensure it meets our requirements.

    Args:
        mapping: The mapping dictionary to validate (keys are template headers, values are original headers or null)
        template_headers: List of valid template headers

    Raises:
        ValueError: If the mapping is invalid
    """
    if not isinstance(mapping, dict):
        raise ValueError("Mapping must be a dictionary")

    # Check for one-to-one mapping (no duplicate original headers as values)
    # Exclude null values from this check
    non_null_values = [v for v in mapping.values() if v is not None]
    if len(non_null_values) != len(set(non_null_values)):
        raise ValueError("Multiple template headers map to single original header")

    # Check that all keys are valid template headers
    invalid_headers = set(mapping.keys()) - set(template_headers)
    if invalid_headers:
        raise ValueError(f"Invalid template headers in mapping: {invalid_headers}")

    # Check that required columns are included in the mapping
    required_cols = [
        "origin_zip", "destination_zip", "origin_city", "destination_city",
        "origin_state", "destination_state", "shipment_mode", "equipment_type",
        "customer_name", "broker_name", "broker_primary_reference",
        "cogs_total", "revenue_total", "origin_close"
    ]

    # Create a mapping of normalized column names to actual column names
    # Normalize by converting to lowercase and removing spaces/underscores
    normalized_mapping = {}
    for key in mapping.keys():
        normalized_key = key.lower().replace(" ", "").replace("_", "")
        normalized_mapping[normalized_key] = key

    # Check for missing required columns
    missing_required = []
    for col in required_cols:
        normalized_col = col.lower().replace("_", "")
        if normalized_col not in normalized_mapping:
            missing_required.append(col)

    # Add missing required columns with null values
    if missing_required:
        for col in missing_required:
            mapping[col] = None
        logger.info(f"Added missing required columns with null values: {missing_required}")

def ai_column_mapping(
    original_headers: List[str],
    template_headers: List[str],
    sample_data: Optional[List[Dict[str, str]]] = None,
    issues: Optional[Dict[str, str]] = None,
    max_retries: int = 3
) -> Dict[str, str]:
    """
    Generates a mapping dictionary based on provided column headers using AI.

    Args:
        original_headers: List of original column headers to be mapped.
        template_headers: List of template column headers to map the original headers to.
        sample_data: Optional sample data to help with mapping.
        issues: Optional issues from previous mapping attempts.
        max_retries: Maximum number of retries if mapping fails.

    Returns:
        mapping: A dictionary where keys are original column headers and values are their corresponding template headers.

    Raises:
        ValueError: If mapping fails after max_retries attempts
    """
    for attempt in range(max_retries):
        try:
            logger.info(f"Attempting AI mapping (attempt {attempt + 1}/{max_retries})")

            # Prepare the input data with proper datetime serialization
            input_data = {
                "original_headers": json.dumps(original_headers, indent=2),
                "template_headers": json.dumps(template_headers, indent=2),
                "sample_data": json.dumps(sample_data, indent=2, cls=DateTimeEncoder) if sample_data else "No sample data provided",
                "issues": json.dumps(issues, indent=2) if issues else "No issues provided"
            }

            # Initialize the LLM
            prompt_template = ChatPromptTemplate.from_template(PROMPT_MAP_COLUMNS)
            model = genai.GenerativeModel('gemini-2.0-flash')
            chat = model.start_chat()

            # Get the mapping
            formatted_prompt = prompt_template.format(**input_data)
            logger.info(f"Sending prompt to Gemini: {formatted_prompt[:200]}...")

            response = chat.send_message(formatted_prompt)
            logger.info(f"Received response from Gemini: {response.text[:200]}...")

            # Extract JSON from the response
            # Sometimes the model might wrap the JSON in markdown code blocks or add explanatory text
            response_text = response.text.strip()

            # Remove markdown code blocks if present
            response_text = re.sub(r'```json\s*|```\s*', '', response_text)

            # Try to find JSON in the response using regex
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(0)
                logger.info(f"Extracted JSON from response: {json_str[:100]}...")
                try:
                    mapping = json.loads(json_str)
                except json.JSONDecodeError as e:
                    logger.error(f"JSON parsing error: {e}")
                    logger.error(f"JSON string: {json_str}")
                    raise
            else:
                # If no JSON pattern found, try the whole response
                logger.info(f"No JSON pattern found, trying to parse entire response")
                try:
                    mapping = json.loads(response_text)
                except json.JSONDecodeError as e:
                    logger.error(f"JSON parsing error: {e}")
                    logger.error(f"Response text: {response_text}")
                    raise

            # Validate the mapping
            validate_mapping(mapping, template_headers)

            # Log each mapping individually, similar to traditional mapping
            mapped_count = sum(1 for v in mapping.values() if v is not None)
            null_count = sum(1 for v in mapping.values() if v is None)
            logger.info(f"AI mapping successful: {mapped_count} columns mapped, {null_count} required columns with null values")
            for template_col, orig_col in mapping.items():
                if orig_col is not None:
                    logger.info(f"AI mapping: '{template_col}' <- '{orig_col}'")
                else:
                    logger.info(f"AI mapping: '{template_col}' <- null (required column with no match)")

            return mapping

        except Exception as e:
            logger.warning(f"Mapping attempt {attempt + 1} failed: {str(e)}")
            if attempt == max_retries - 1:
                logger.error("All mapping attempts failed")
                raise ValueError(f"Failed to generate valid mapping after {max_retries} attempts: {str(e)}")

            # Wait before retrying
            time.sleep(1)  # Simple backoff strategy