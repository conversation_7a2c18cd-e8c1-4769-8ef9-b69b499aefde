{"version": "2.0", "metadata": {"apiVersion": "2019-11-01", "endpointPrefix": "compute-optimizer", "jsonVersion": "1.0", "protocol": "json", "serviceFullName": "AWS Compute Optimizer", "serviceId": "Compute Optimizer", "signatureVersion": "v4", "signingName": "compute-optimizer", "targetPrefix": "ComputeOptimizerService", "uid": "compute-optimizer-2019-11-01"}, "operations": {"DeleteRecommendationPreferences": {"name": "DeleteRecommendationPreferences", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteRecommendationPreferencesRequest"}, "output": {"shape": "DeleteRecommendationPreferencesResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes a recommendation preference, such as enhanced infrastructure metrics.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/enhanced-infrastructure-metrics.html\">Activating enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "DescribeRecommendationExportJobs": {"name": "DescribeRecommendationExportJobs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeRecommendationExportJobsRequest"}, "output": {"shape": "DescribeRecommendationExportJobsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Describes recommendation export jobs created in the last seven days.</p> <p>Use the <a>ExportAutoScalingGroupRecommendations</a> or <a>ExportEC2InstanceRecommendations</a> actions to request an export of your recommendations. Then use the <a>DescribeRecommendationExportJobs</a> action to view your export jobs.</p>"}, "ExportAutoScalingGroupRecommendations": {"name": "ExportAutoScalingGroupRecommendations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ExportAutoScalingGroupRecommendationsRequest"}, "output": {"shape": "ExportAutoScalingGroupRecommendationsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Exports optimization recommendations for Auto Scaling groups.</p> <p>Recommendations are exported in a comma-separated values (.csv) file, and its metadata in a JavaScript Object Notation (JSON) (.json) file, to an existing Amazon Simple Storage Service (Amazon S3) bucket that you specify. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/exporting-recommendations.html\">Exporting Recommendations</a> in the <i>Compute Optimizer User Guide</i>.</p> <p>You can have only one Auto Scaling group export job in progress per Amazon Web Services Region.</p>"}, "ExportEBSVolumeRecommendations": {"name": "ExportEBSVolumeRecommendations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ExportEBSVolumeRecommendationsRequest"}, "output": {"shape": "ExportEBSVolumeRecommendationsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Exports optimization recommendations for Amazon EBS volumes.</p> <p>Recommendations are exported in a comma-separated values (.csv) file, and its metadata in a JavaScript Object Notation (JSON) (.json) file, to an existing Amazon Simple Storage Service (Amazon S3) bucket that you specify. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/exporting-recommendations.html\">Exporting Recommendations</a> in the <i>Compute Optimizer User Guide</i>.</p> <p>You can have only one Amazon EBS volume export job in progress per Amazon Web Services Region.</p>"}, "ExportEC2InstanceRecommendations": {"name": "ExportEC2InstanceRecommendations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ExportEC2InstanceRecommendationsRequest"}, "output": {"shape": "ExportEC2InstanceRecommendationsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Exports optimization recommendations for Amazon EC2 instances.</p> <p>Recommendations are exported in a comma-separated values (.csv) file, and its metadata in a JavaScript Object Notation (JSON) (.json) file, to an existing Amazon Simple Storage Service (Amazon S3) bucket that you specify. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/exporting-recommendations.html\">Exporting Recommendations</a> in the <i>Compute Optimizer User Guide</i>.</p> <p>You can have only one Amazon EC2 instance export job in progress per Amazon Web Services Region.</p>"}, "ExportECSServiceRecommendations": {"name": "ExportECSServiceRecommendations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ExportECSServiceRecommendationsRequest"}, "output": {"shape": "ExportECSServiceRecommendationsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p> Exports optimization recommendations for Amazon ECS services on Fargate. </p> <p>Recommendations are exported in a CSV file, and its metadata in a JSON file, to an existing Amazon Simple Storage Service (Amazon S3) bucket that you specify. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/exporting-recommendations.html\">Exporting Recommendations</a> in the <i>Compute Optimizer User Guide</i>.</p> <p>You can only have one Amazon ECS service export job in progress per Amazon Web Services Region.</p>"}, "ExportLambdaFunctionRecommendations": {"name": "ExportLambdaFunctionRecommendations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ExportLambdaFunctionRecommendationsRequest"}, "output": {"shape": "ExportLambdaFunctionRecommendationsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Exports optimization recommendations for Lambda functions.</p> <p>Recommendations are exported in a comma-separated values (.csv) file, and its metadata in a JavaScript Object Notation (JSON) (.json) file, to an existing Amazon Simple Storage Service (Amazon S3) bucket that you specify. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/exporting-recommendations.html\">Exporting Recommendations</a> in the <i>Compute Optimizer User Guide</i>.</p> <p>You can have only one Lambda function export job in progress per Amazon Web Services Region.</p>"}, "GetAutoScalingGroupRecommendations": {"name": "GetAutoScalingGroupRecommendations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetAutoScalingGroupRecommendationsRequest"}, "output": {"shape": "GetAutoScalingGroupRecommendationsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns Auto Scaling group recommendations.</p> <p>Compute Optimizer generates recommendations for Amazon EC2 Auto Scaling groups that meet a specific set of requirements. For more information, see the <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/requirements.html\">Supported resources and requirements</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "GetEBSVolumeRecommendations": {"name": "GetEBSVolumeRecommendations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEBSVolumeRecommendationsRequest"}, "output": {"shape": "GetEBSVolumeRecommendationsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns Amazon Elastic Block Store (Amazon EBS) volume recommendations.</p> <p>Compute Optimizer generates recommendations for Amazon EBS volumes that meet a specific set of requirements. For more information, see the <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/requirements.html\">Supported resources and requirements</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "GetEC2InstanceRecommendations": {"name": "GetEC2InstanceRecommendations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEC2InstanceRecommendationsRequest"}, "output": {"shape": "GetEC2InstanceRecommendationsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns Amazon EC2 instance recommendations.</p> <p>Compute Optimizer generates recommendations for Amazon Elastic Compute Cloud (Amazon EC2) instances that meet a specific set of requirements. For more information, see the <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/requirements.html\">Supported resources and requirements</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "GetEC2RecommendationProjectedMetrics": {"name": "GetEC2RecommendationProjectedMetrics", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEC2RecommendationProjectedMetricsRequest"}, "output": {"shape": "GetEC2RecommendationProjectedMetricsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the projected utilization metrics of Amazon EC2 instance recommendations.</p> <note> <p>The <code>Cpu</code> and <code>Memory</code> metrics are the only projected utilization metrics returned when you run this action. Additionally, the <code>Memory</code> metric is returned only for resources that have the unified CloudWatch agent installed on them. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/metrics.html#cw-agent\">Enabling Memory Utilization with the CloudWatch Agent</a>.</p> </note>"}, "GetECSServiceRecommendationProjectedMetrics": {"name": "GetECSServiceRecommendationProjectedMetrics", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetECSServiceRecommendationProjectedMetricsRequest"}, "output": {"shape": "GetECSServiceRecommendationProjectedMetricsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p> Returns the projected metrics of Amazon ECS service recommendations. </p>"}, "GetECSServiceRecommendations": {"name": "GetECSServiceRecommendations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetECSServiceRecommendationsRequest"}, "output": {"shape": "GetECSServiceRecommendationsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p> Returns Amazon ECS service recommendations. </p> <p> Compute Optimizer generates recommendations for Amazon ECS services on Fargate that meet a specific set of requirements. For more information, see the <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/requirements.html\">Supported resources and requirements</a> in the <i>Compute Optimizer User Guide</i>. </p>"}, "GetEffectiveRecommendationPreferences": {"name": "GetEffectiveRecommendationPreferences", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEffectiveRecommendationPreferencesRequest"}, "output": {"shape": "GetEffectiveRecommendationPreferencesResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the recommendation preferences that are in effect for a given resource, such as enhanced infrastructure metrics. Considers all applicable preferences that you might have set at the resource, account, and organization level.</p> <p>When you create a recommendation preference, you can set its status to <code>Active</code> or <code>Inactive</code>. Use this action to view the recommendation preferences that are in effect, or <code>Active</code>.</p>"}, "GetEnrollmentStatus": {"name": "GetEnrollmentStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEnrollmentStatusRequest"}, "output": {"shape": "GetEnrollmentStatusResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the enrollment (opt in) status of an account to the Compute Optimizer service.</p> <p>If the account is the management account of an organization, this action also confirms the enrollment status of member accounts of the organization. Use the <a>GetEnrollmentStatusesForOrganization</a> action to get detailed information about the enrollment status of member accounts of an organization.</p>"}, "GetEnrollmentStatusesForOrganization": {"name": "GetEnrollmentStatusesForOrganization", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetEnrollmentStatusesForOrganizationRequest"}, "output": {"shape": "GetEnrollmentStatusesForOrganizationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the Compute Optimizer enrollment (opt-in) status of organization member accounts, if your account is an organization management account.</p> <p>To get the enrollment status of standalone accounts, use the <a>GetEnrollmentStatus</a> action.</p>"}, "GetLambdaFunctionRecommendations": {"name": "GetLambdaFunctionRecommendations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetLambdaFunctionRecommendationsRequest"}, "output": {"shape": "GetLambdaFunctionRecommendationsResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Returns Lambda function recommendations.</p> <p>Compute Optimizer generates recommendations for functions that meet a specific set of requirements. For more information, see the <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/requirements.html\">Supported resources and requirements</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "GetRecommendationPreferences": {"name": "GetRecommendationPreferences", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetRecommendationPreferencesRequest"}, "output": {"shape": "GetRecommendationPreferencesResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns existing recommendation preferences, such as enhanced infrastructure metrics.</p> <p>Use the <code>scope</code> parameter to specify which preferences to return. You can specify to return preferences for an organization, a specific account ID, or a specific EC2 instance or Auto Scaling group Amazon Resource Name (ARN).</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/enhanced-infrastructure-metrics.html\">Activating enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "GetRecommendationSummaries": {"name": "GetRecommendationSummaries", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetRecommendationSummariesRequest"}, "output": {"shape": "GetRecommendationSummariesResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Returns the optimization findings for an account.</p> <p>It returns the number of:</p> <ul> <li> <p>Amazon EC2 instances in an account that are <code>Underprovisioned</code>, <code>Overprovisioned</code>, or <code>Optimized</code>.</p> </li> <li> <p>Auto Scaling groups in an account that are <code>NotOptimized</code>, or <code>Optimized</code>.</p> </li> <li> <p>Amazon EBS volumes in an account that are <code>NotOptimized</code>, or <code>Optimized</code>.</p> </li> <li> <p>Lambda functions in an account that are <code>NotOptimized</code>, or <code>Optimized</code>.</p> </li> <li> <p>Amazon ECS services in an account that are <code>Underprovisioned</code>, <code>Overprovisioned</code>, or <code>Optimized</code>.</p> </li> </ul>"}, "PutRecommendationPreferences": {"name": "PutRecommendationPreferences", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutRecommendationPreferencesRequest"}, "output": {"shape": "PutRecommendationPreferencesResponse"}, "errors": [{"shape": "OptInRequiredException"}, {"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "ResourceNotFoundException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates a new recommendation preference or updates an existing recommendation preference, such as enhanced infrastructure metrics.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/enhanced-infrastructure-metrics.html\">Activating enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "UpdateEnrollmentStatus": {"name": "UpdateEnrollmentStatus", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateEnrollmentStatusRequest"}, "output": {"shape": "UpdateEnrollmentStatusResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ServiceUnavailableException"}, {"shape": "AccessDeniedException"}, {"shape": "InvalidParameterValueException"}, {"shape": "MissingAuthenticationToken"}, {"shape": "ThrottlingException"}], "documentation": "<p>Updates the enrollment (opt in and opt out) status of an account to the Compute Optimizer service.</p> <p>If the account is a management account of an organization, this action can also be used to enroll member accounts of the organization.</p> <p>You must have the appropriate permissions to opt in to Compute Optimizer, to view its recommendations, and to opt out. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/security-iam.html\">Controlling access with Amazon Web Services Identity and Access Management</a> in the <i>Compute Optimizer User Guide</i>.</p> <p>When you opt in, Compute Optimizer automatically creates a service-linked role in your account to access its data. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/using-service-linked-roles.html\">Using Service-Linked Roles for Compute Optimizer</a> in the <i>Compute Optimizer User Guide</i>.</p>"}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "exception": true, "synthetic": true}, "AccountEnrollmentStatus": {"type": "structure", "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID.</p>"}, "status": {"shape": "Status", "documentation": "<p>The account enrollment status.</p>"}, "statusReason": {"shape": "StatusReason", "documentation": "<p>The reason for the account enrollment status.</p> <p>For example, an account might show a status of <code>Pending</code> because member accounts of an organization require more time to be enrolled in the service.</p>"}, "lastUpdatedTimestamp": {"shape": "LastUpdatedTimestamp", "documentation": "<p>The Unix epoch timestamp, in seconds, of when the account enrollment status was last updated.</p>"}}, "documentation": "<p>Describes the enrollment status of an organization's member accounts in Compute Optimizer.</p>"}, "AccountEnrollmentStatuses": {"type": "list", "member": {"shape": "AccountEnrollmentStatus"}}, "AccountId": {"type": "string"}, "AccountIds": {"type": "list", "member": {"shape": "AccountId"}}, "AutoScalingConfiguration": {"type": "string", "enum": ["TargetTrackingScalingCpu", "TargetTrackingScalingMemory"]}, "AutoScalingGroupArn": {"type": "string"}, "AutoScalingGroupArns": {"type": "list", "member": {"shape": "AutoScalingGroupArn"}}, "AutoScalingGroupConfiguration": {"type": "structure", "members": {"desiredCapacity": {"shape": "DesiredCapacity", "documentation": "<p>The desired capacity, or number of instances, for the Auto Scaling group.</p>"}, "minSize": {"shape": "MinSize", "documentation": "<p>The minimum size, or minimum number of instances, for the Auto Scaling group.</p>"}, "maxSize": {"shape": "MaxSize", "documentation": "<p>The maximum size, or maximum number of instances, for the Auto Scaling group.</p>"}, "instanceType": {"shape": "InstanceType", "documentation": "<p>The instance type for the Auto Scaling group.</p>"}}, "documentation": "<p>Describes the configuration of an Auto Scaling group.</p>"}, "AutoScalingGroupName": {"type": "string"}, "AutoScalingGroupRecommendation": {"type": "structure", "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the Auto Scaling group.</p>"}, "autoScalingGroupArn": {"shape": "AutoScalingGroupArn", "documentation": "<p>The Amazon Resource Name (ARN) of the Auto Scaling group.</p>"}, "autoScalingGroupName": {"shape": "AutoScalingGroupName", "documentation": "<p>The name of the Auto Scaling group.</p>"}, "finding": {"shape": "Finding", "documentation": "<p>The finding classification of the Auto Scaling group.</p> <p>Findings for Auto Scaling groups include:</p> <ul> <li> <p> <b> <code>NotOptimized</code> </b>—An Auto Scaling group is considered not optimized when Compute Optimizer identifies a recommendation that can provide better performance for your workload.</p> </li> <li> <p> <b> <code>Optimized</code> </b>—An Auto Scaling group is considered optimized when Compute Optimizer determines that the group is correctly provisioned to run your workload based on the chosen instance type. For optimized resources, Compute Optimizer might recommend a new generation instance type.</p> </li> </ul>"}, "utilizationMetrics": {"shape": "UtilizationMetrics", "documentation": "<p>An array of objects that describe the utilization metrics of the Auto Scaling group.</p>"}, "lookBackPeriodInDays": {"shape": "LookBackPeriodInDays", "documentation": "<p>The number of days for which utilization metrics were analyzed for the Auto Scaling group.</p>"}, "currentConfiguration": {"shape": "AutoScalingGroupConfiguration", "documentation": "<p>An array of objects that describe the current configuration of the Auto Scaling group.</p>"}, "recommendationOptions": {"shape": "AutoScalingGroupRecommendationOptions", "documentation": "<p>An array of objects that describe the recommendation options for the Auto Scaling group.</p>"}, "lastRefreshTimestamp": {"shape": "LastRefreshTimestamp", "documentation": "<p>The timestamp of when the Auto Scaling group recommendation was last generated.</p>"}, "currentPerformanceRisk": {"shape": "CurrentPerformanceRisk", "documentation": "<p>The risk of the current Auto Scaling group not meeting the performance needs of its workloads. The higher the risk, the more likely the current Auto Scaling group configuration has insufficient capacity and cannot meet workload requirements.</p>"}, "effectiveRecommendationPreferences": {"shape": "EffectiveRecommendationPreferences", "documentation": "<p>An object that describes the effective recommendation preferences for the Auto Scaling group.</p>"}, "inferredWorkloadTypes": {"shape": "InferredWorkloadTypes", "documentation": "<p>The applications that might be running on the instances in the Auto Scaling group as inferred by Compute Optimizer.</p> <p>Compute Optimizer can infer if one of the following applications might be running on the instances:</p> <ul> <li> <p> <code>AmazonEmr</code> - Infers that Amazon EMR might be running on the instances.</p> </li> <li> <p> <code>ApacheCassandra</code> - Infers that Apache Cassandra might be running on the instances.</p> </li> <li> <p> <code>ApacheHadoop</code> - Infers that Apache Hadoop might be running on the instances.</p> </li> <li> <p> <code>Memcached</code> - Infers that Memcached might be running on the instances.</p> </li> <li> <p> <code>NGINX</code> - Infers that NGINX might be running on the instances.</p> </li> <li> <p> <code>PostgreSql</code> - Infers that PostgreSQL might be running on the instances.</p> </li> <li> <p> <code>Redis</code> - Infers that <PERSON><PERSON> might be running on the instances.</p> </li> </ul>"}}, "documentation": "<p>Describes an Auto Scaling group recommendation.</p>"}, "AutoScalingGroupRecommendationOption": {"type": "structure", "members": {"configuration": {"shape": "AutoScalingGroupConfiguration", "documentation": "<p>An array of objects that describe an Auto Scaling group configuration.</p>"}, "projectedUtilizationMetrics": {"shape": "ProjectedUtilizationMetrics", "documentation": "<p>An array of objects that describe the projected utilization metrics of the Auto Scaling group recommendation option.</p> <note> <p>The <code>Cpu</code> and <code>Memory</code> metrics are the only projected utilization metrics returned. Additionally, the <code>Memory</code> metric is returned only for resources that have the unified CloudWatch agent installed on them. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/metrics.html#cw-agent\">Enabling Memory Utilization with the CloudWatch Agent</a>.</p> </note>"}, "performanceRisk": {"shape": "PerformanceRisk", "documentation": "<p>The performance risk of the Auto Scaling group configuration recommendation.</p> <p>Performance risk indicates the likelihood of the recommended instance type not meeting the resource needs of your workload. Compute Optimizer calculates an individual performance risk score for each specification of the recommended instance, including CPU, memory, EBS throughput, EBS IOPS, disk throughput, disk IOPS, network throughput, and network PPS. The performance risk of the recommended instance is calculated as the maximum performance risk score across the analyzed resource specifications.</p> <p>The value ranges from <code>0</code> - <code>4</code>, with <code>0</code> meaning that the recommended resource is predicted to always provide enough hardware capability. The higher the performance risk is, the more likely you should validate whether the recommendation will meet the performance requirements of your workload before migrating your resource.</p>"}, "rank": {"shape": "Rank", "documentation": "<p>The rank of the Auto Scaling group recommendation option.</p> <p>The top recommendation option is ranked as <code>1</code>.</p>"}, "savingsOpportunity": {"shape": "SavingsOpportunity", "documentation": "<p>An object that describes the savings opportunity for the Auto Scaling group recommendation option. Savings opportunity includes the estimated monthly savings amount and percentage.</p>"}, "migrationEffort": {"shape": "MigrationEffort", "documentation": "<p>The level of effort required to migrate from the current instance type to the recommended instance type.</p> <p>For example, the migration effort is <code>Low</code> if Amazon EMR is the inferred workload type and an Amazon Web Services Graviton instance type is recommended. The migration effort is <code>Medium</code> if a workload type couldn't be inferred but an Amazon Web Services Graviton instance type is recommended. The migration effort is <code>VeryLow</code> if both the current and recommended instance types are of the same CPU architecture.</p>"}}, "documentation": "<p>Describes a recommendation option for an Auto Scaling group.</p>"}, "AutoScalingGroupRecommendationOptions": {"type": "list", "member": {"shape": "AutoScalingGroupRecommendationOption"}}, "AutoScalingGroupRecommendations": {"type": "list", "member": {"shape": "AutoScalingGroupRecommendation"}}, "Code": {"type": "string"}, "ContainerConfiguration": {"type": "structure", "members": {"containerName": {"shape": "ContainerName", "documentation": "<p> The name of the container. </p>"}, "memorySizeConfiguration": {"shape": "MemorySizeConfiguration", "documentation": "<p> The memory size configurations for the container. </p>"}, "cpu": {"shape": "NullableCpu", "documentation": "<p> The number of CPU units reserved for the container. </p>"}}, "documentation": "<p> Describes the container configurations within the tasks of your Amazon ECS service. </p>"}, "ContainerConfigurations": {"type": "list", "member": {"shape": "ContainerConfiguration"}}, "ContainerName": {"type": "string"}, "ContainerRecommendation": {"type": "structure", "members": {"containerName": {"shape": "ContainerName", "documentation": "<p> The name of the container. </p>"}, "memorySizeConfiguration": {"shape": "MemorySizeConfiguration", "documentation": "<p> The recommended memory size configurations for the container. </p>"}, "cpu": {"shape": "NullableCpu", "documentation": "<p> The recommended number of CPU units reserved for the container. </p>"}}, "documentation": "<p> The CPU and memory recommendations for a container within the tasks of your Amazon ECS service. </p>"}, "ContainerRecommendations": {"type": "list", "member": {"shape": "ContainerRecommendation"}}, "CpuSize": {"type": "integer"}, "CpuVendorArchitecture": {"type": "string", "enum": ["AWS_ARM64", "CURRENT"]}, "CpuVendorArchitectures": {"type": "list", "member": {"shape": "CpuVendorArchitecture"}}, "CreationTimestamp": {"type": "timestamp"}, "Currency": {"type": "string", "enum": ["USD", "CNY"]}, "CurrentInstanceType": {"type": "string"}, "CurrentPerformanceRisk": {"type": "string", "enum": ["VeryLow", "Low", "Medium", "High"]}, "CurrentPerformanceRiskRatings": {"type": "structure", "members": {"high": {"shape": "High", "documentation": "<p>A count of the applicable resource types with a high performance risk rating.</p>"}, "medium": {"shape": "Medium", "documentation": "<p>A count of the applicable resource types with a medium performance risk rating.</p>"}, "low": {"shape": "Low", "documentation": "<p>A count of the applicable resource types with a low performance risk rating.</p>"}, "veryLow": {"shape": "VeryLow", "documentation": "<p>A count of the applicable resource types with a very low performance risk rating.</p>"}}, "documentation": "<p>Describes the performance risk ratings for a given resource type.</p> <p>Resources with a <code>high</code> or <code>medium</code> rating are at risk of not meeting the performance needs of their workloads, while resources with a <code>low</code> rating are performing well in their workloads.</p>"}, "DeleteRecommendationPreferencesRequest": {"type": "structure", "required": ["resourceType", "recommendationPreferenceNames"], "members": {"resourceType": {"shape": "ResourceType", "documentation": "<p>The target resource type of the recommendation preference to delete.</p> <p>The <code>Ec2Instance</code> option encompasses standalone instances and instances that are part of Auto Scaling groups. The <code>AutoScalingGroup</code> option encompasses only instances that are part of an Auto Scaling group.</p> <note> <p>The valid values for this parameter are <code>Ec2Instance</code> and <code>AutoScalingGroup</code>.</p> </note>"}, "scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>An object that describes the scope of the recommendation preference to delete.</p> <p>You can delete recommendation preferences that are created at the organization level (for management accounts of an organization only), account level, and resource level. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/enhanced-infrastructure-metrics.html\">Activating enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "recommendationPreferenceNames": {"shape": "RecommendationPreferenceNames", "documentation": "<p>The name of the recommendation preference to delete.</p>"}}}, "DeleteRecommendationPreferencesResponse": {"type": "structure", "members": {}}, "DescribeRecommendationExportJobsRequest": {"type": "structure", "members": {"jobIds": {"shape": "JobIds", "documentation": "<p>The identification numbers of the export jobs to return.</p> <p>An export job ID is returned when you create an export using the <a>ExportAutoScalingGroupRecommendations</a> or <a>ExportEC2InstanceRecommendations</a> actions.</p> <p>All export jobs created in the last seven days are returned if this parameter is omitted.</p>"}, "filters": {"shape": "JobFilters", "documentation": "<p>An array of objects to specify a filter that returns a more specific list of export jobs.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to advance to the next page of export jobs.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of export jobs to return with a single request.</p> <p>To retrieve the remaining results, make another request with the returned <code>nextToken</code> value.</p>"}}}, "DescribeRecommendationExportJobsResponse": {"type": "structure", "members": {"recommendationExportJobs": {"shape": "RecommendationExportJobs", "documentation": "<p>An array of objects that describe recommendation export jobs.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to advance to the next page of export jobs.</p> <p>This value is null when there are no more pages of export jobs to return.</p>"}}}, "DesiredCapacity": {"type": "integer"}, "DestinationBucket": {"type": "string"}, "DestinationKey": {"type": "string"}, "DestinationKeyPrefix": {"type": "string"}, "EBSFilter": {"type": "structure", "members": {"name": {"shape": "EBSFilterName", "documentation": "<p>The name of the filter.</p> <p>Specify <code>Finding</code> to return recommendations with a specific finding classification (for example, <code>NotOptimized</code>).</p>"}, "values": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The value of the filter.</p> <p>The valid values are <code>Optimized</code>, or <code>NotOptimized</code>.</p>"}}, "documentation": "<p>Describes a filter that returns a more specific list of Amazon Elastic Block Store (Amazon EBS) volume recommendations. Use this filter with the <a>GetEBSVolumeRecommendations</a> action.</p> <p>You can use <code>LambdaFunctionRecommendationFilter</code> with the <a>GetLambdaFunctionRecommendations</a> action, <code>JobFilter</code> with the <a>DescribeRecommendationExportJobs</a> action, and <code>Filter</code> with the <a>GetAutoScalingGroupRecommendations</a> and <a>GetEC2InstanceRecommendations</a> actions.</p>"}, "EBSFilterName": {"type": "string", "enum": ["Finding"]}, "EBSFilters": {"type": "list", "member": {"shape": "EBSFilter"}}, "EBSFinding": {"type": "string", "enum": ["Optimized", "NotOptimized"]}, "EBSMetricName": {"type": "string", "enum": ["VolumeReadOpsPerSecond", "VolumeWriteOpsPerSecond", "VolumeReadBytesPerSecond", "VolumeWriteBytesPerSecond"]}, "EBSUtilizationMetric": {"type": "structure", "members": {"name": {"shape": "EBSMetricName", "documentation": "<p>The name of the utilization metric.</p> <p>The following utilization metrics are available:</p> <ul> <li> <p> <code>VolumeReadOpsPerSecond</code> - The completed read operations per second from the volume in a specified period of time.</p> <p>Unit: Count</p> </li> <li> <p> <code>VolumeWriteOpsPerSecond</code> - The completed write operations per second to the volume in a specified period of time.</p> <p>Unit: Count</p> </li> <li> <p> <code>VolumeReadBytesPerSecond</code> - The bytes read per second from the volume in a specified period of time.</p> <p>Unit: Bytes</p> </li> <li> <p> <code>VolumeWriteBytesPerSecond</code> - The bytes written to the volume in a specified period of time.</p> <p>Unit: Bytes</p> </li> </ul>"}, "statistic": {"shape": "MetricStatistic", "documentation": "<p>The statistic of the utilization metric.</p> <p>The Compute Optimizer API, Command Line Interface (CLI), and SDKs return utilization metrics using only the <code>Maximum</code> statistic, which is the highest value observed during the specified period.</p> <p>The Compute Optimizer console displays graphs for some utilization metrics using the <code>Average</code> statistic, which is the value of <code>Sum</code> / <code>SampleCount</code> during the specified period. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/viewing-recommendations.html\">Viewing resource recommendations</a> in the <i>Compute Optimizer User Guide</i>. You can also get averaged utilization metric data for your resources using Amazon CloudWatch. For more information, see the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/WhatIsCloudWatch.html\">Amazon CloudWatch User Guide</a>.</p>"}, "value": {"shape": "MetricValue", "documentation": "<p>The value of the utilization metric.</p>"}}, "documentation": "<p>Describes a utilization metric of an Amazon Elastic Block Store (Amazon EBS) volume.</p> <p>Compare the utilization metric data of your resource against its projected utilization metric data to determine the performance difference between your current resource and the recommended option.</p>"}, "EBSUtilizationMetrics": {"type": "list", "member": {"shape": "EBSUtilizationMetric"}}, "ECSServiceLaunchType": {"type": "string", "enum": ["EC2", "Fargate"]}, "ECSServiceMetricName": {"type": "string", "enum": ["Cpu", "Memory"]}, "ECSServiceMetricStatistic": {"type": "string", "enum": ["Maximum", "Average"]}, "ECSServiceProjectedMetric": {"type": "structure", "members": {"name": {"shape": "ECSServiceMetricName", "documentation": "<p> The name of the projected metric. </p> <p>The following metrics are available:</p> <ul> <li> <p> <code>Cpu</code> — The percentage of allocated compute units that are currently in use on the service tasks.</p> </li> <li> <p> <code>Memory</code> — The percentage of memory that's currently in use on the service tasks.</p> </li> </ul>"}, "timestamps": {"shape": "Timestamps", "documentation": "<p> The timestamps of the projected metric. </p>"}, "upperBoundValues": {"shape": "<PERSON>ric<PERSON><PERSON><PERSON>", "documentation": "<p> The upper bound values for the projected metric. </p>"}, "lowerBoundValues": {"shape": "<PERSON>ric<PERSON><PERSON><PERSON>", "documentation": "<p> The lower bound values for the projected metric. </p>"}}, "documentation": "<p> Describes the projected metrics of an Amazon ECS service recommendation option. </p> <p>To determine the performance difference between your current Amazon ECS service and the recommended option, compare the metric data of your service against its projected metric data.</p>"}, "ECSServiceProjectedMetrics": {"type": "list", "member": {"shape": "ECSServiceProjectedMetric"}}, "ECSServiceProjectedUtilizationMetric": {"type": "structure", "members": {"name": {"shape": "ECSServiceMetricName", "documentation": "<p> The name of the projected utilization metric. </p> <p>The following utilization metrics are available:</p> <ul> <li> <p> <code>Cpu</code> — The percentage of allocated compute units that are currently in use on the service tasks.</p> </li> <li> <p> <code>Memory</code> — The percentage of memory that's currently in use on the service tasks.</p> </li> </ul>"}, "statistic": {"shape": "ECSServiceMetricStatistic", "documentation": "<p>The statistic of the projected utilization metric.</p> <p>The Compute Optimizer API, Command Line Interface (CLI), and SDKs return utilization metrics using only the <code>Maximum</code> statistic, which is the highest value observed during the specified period.</p> <p>The Compute Optimizer console displays graphs for some utilization metrics using the <code>Average</code> statistic, which is the value of <code>Sum</code> / <code>SampleCount</code> during the specified period. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/viewing-recommendations.html\">Viewing resource recommendations</a> in the <i>Compute Optimizer User Guide</i>. You can also get averaged utilization metric data for your resources using Amazon CloudWatch. For more information, see the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/WhatIsCloudWatch.html\">Amazon CloudWatch User Guide</a>.</p>"}, "lowerBoundValue": {"shape": "LowerBoundValue", "documentation": "<p> The lower bound values for the projected utilization metrics. </p>"}, "upperBoundValue": {"shape": "UpperBoundValue", "documentation": "<p> The upper bound values for the projected utilization metrics. </p>"}}, "documentation": "<p> Describes the projected utilization metrics of an Amazon ECS service recommendation option. </p> <p>To determine the performance difference between your current Amazon ECS service and the recommended option, compare the utilization metric data of your service against its projected utilization metric data.</p>"}, "ECSServiceProjectedUtilizationMetrics": {"type": "list", "member": {"shape": "ECSServiceProjectedUtilizationMetric"}}, "ECSServiceRecommendation": {"type": "structure", "members": {"serviceArn": {"shape": "ServiceArn", "documentation": "<p> The Amazon Resource Name (ARN) of the current Amazon ECS service. </p> <p> The following is the format of the ARN: </p> <p> <code>arn:aws:ecs:region:aws_account_id:service/cluster-name/service-name</code> </p>"}, "accountId": {"shape": "AccountId", "documentation": "<p> The Amazon Web Services account ID of the Amazon ECS service. </p>"}, "currentServiceConfiguration": {"shape": "ServiceConfiguration", "documentation": "<p> The configuration of the current Amazon ECS service. </p>"}, "utilizationMetrics": {"shape": "ECSServiceUtilizationMetrics", "documentation": "<p> An array of objects that describe the utilization metrics of the Amazon ECS service. </p>"}, "lookbackPeriodInDays": {"shape": "LookBackPeriodInDays", "documentation": "<p> The number of days the Amazon ECS service utilization metrics were analyzed. </p>"}, "launchType": {"shape": "ECSServiceLaunchType", "documentation": "<p> The launch type the Amazon ECS service is using. </p> <note> <p>Compute Optimizer only supports the Fargate launch type.</p> </note>"}, "lastRefreshTimestamp": {"shape": "LastRefreshTimestamp", "documentation": "<p> The timestamp of when the Amazon ECS service recommendation was last generated. </p>"}, "finding": {"shape": "ECSServiceRecommendationFinding", "documentation": "<p> The finding classification of an Amazon ECS service. </p> <p>Findings for Amazon ECS services include:</p> <ul> <li> <p> <b> <code>Underprovisioned</code> </b> — When Compute Optimizer detects that there’s not enough memory or CPU, an Amazon ECS service is considered under-provisioned. An under-provisioned service might result in poor application performance.</p> </li> <li> <p> <b> <code>Overprovisioned</code> </b> — When Compute Optimizer detects that there’s excessive memory or CPU, an Amazon ECS service is considered over-provisioned. An over-provisioned service might result in additional infrastructure costs. </p> </li> <li> <p> <b> <code>Optimized</code> </b> — When both the CPU and memory of your Amazon ECS service meet the performance requirements of your workload, the service is considered optimized.</p> </li> </ul>"}, "findingReasonCodes": {"shape": "ECSServiceRecommendationFindingReasonCodes", "documentation": "<p> The reason for the finding classification of an Amazon ECS service. </p> <p>Finding reason codes for Amazon ECS services include:</p> <ul> <li> <p> <b> <code>CPUUnderprovisioned</code> </b> — The service CPU configuration can be sized up to enhance the performance of your workload. This is identified by analyzing the <code>CPUUtilization</code> metric of the current service during the look-back period.</p> </li> <li> <p> <b> <code>CPUOverprovisioned</code> </b> — The service CPU configuration can be sized down while still meeting the performance requirements of your workload. This is identified by analyzing the <code>CPUUtilization</code> metric of the current service during the look-back period. </p> </li> <li> <p> <b> <code>MemoryUnderprovisioned</code> </b> — The service memory configuration can be sized up to enhance the performance of your workload. This is identified by analyzing the <code>MemoryUtilization</code> metric of the current service during the look-back period.</p> </li> <li> <p> <b> <code>MemoryOverprovisioned</code> </b> — The service memory configuration can be sized down while still meeting the performance requirements of your workload. This is identified by analyzing the <code>MemoryUtilization</code> metric of the current service during the look-back period.</p> </li> </ul>"}, "serviceRecommendationOptions": {"shape": "ECSServiceRecommendationOptions", "documentation": "<p> An array of objects that describe the recommendation options for the Amazon ECS service. </p>"}, "currentPerformanceRisk": {"shape": "CurrentPerformanceRisk", "documentation": "<p> The risk of the current Amazon ECS service not meeting the performance needs of its workloads. The higher the risk, the more likely the current service can't meet the performance requirements of its workload. </p>"}}, "documentation": "<p> Describes an Amazon ECS service recommendation. </p>"}, "ECSServiceRecommendationFilter": {"type": "structure", "members": {"name": {"shape": "ECSServiceRecommendationFilterName", "documentation": "<p> The name of the filter. </p> <p> Specify <code>Finding</code> to return recommendations with a specific finding classification. </p> <p> Specify <code>FindingReasonCode</code> to return recommendations with a specific finding reason code. </p>"}, "values": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p> The value of the filter. </p> <p>The valid values for this parameter are as follows:</p> <ul> <li> <p>If you specify the <code>name</code> parameter as <code>Finding</code>, specify <code>Optimized</code>, <code>NotOptimized</code>, or <code>Unavailable</code>.</p> </li> <li> <p>If you specify the <code>name</code> parameter as <code>FindingReasonCode</code>, specify <code>CPUUnderprovisioned</code>, <code>CPUOverprovisioned</code>, <code>MemoryUnderprovisioned</code>, or <code>MemoryOverprovisioned</code>.</p> </li> </ul>"}}, "documentation": "<p> Describes a filter that returns a more specific list of Amazon ECS service recommendations. Use this filter with the <a>GetECSServiceRecommendations</a> action. </p>"}, "ECSServiceRecommendationFilterName": {"type": "string", "enum": ["Finding", "FindingReasonCode"]}, "ECSServiceRecommendationFilters": {"type": "list", "member": {"shape": "ECSServiceRecommendationFilter"}}, "ECSServiceRecommendationFinding": {"type": "string", "enum": ["Optimized", "Underprovisioned", "Overprovisioned"]}, "ECSServiceRecommendationFindingReasonCode": {"type": "string", "enum": ["MemoryOverprovisioned", "MemoryUnderprovisioned", "CPUOverprovisioned", "CPUUnderprovisioned"]}, "ECSServiceRecommendationFindingReasonCodes": {"type": "list", "member": {"shape": "ECSServiceRecommendationFindingReasonCode"}}, "ECSServiceRecommendationOption": {"type": "structure", "members": {"memory": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p> The memory size of the Amazon ECS service recommendation option. </p>"}, "cpu": {"shape": "NullableCpu", "documentation": "<p> The CPU size of the Amazon ECS service recommendation option. </p>"}, "savingsOpportunity": {"shape": "SavingsOpportunity"}, "projectedUtilizationMetrics": {"shape": "ECSServiceProjectedUtilizationMetrics", "documentation": "<p> An array of objects that describe the projected utilization metrics of the Amazon ECS service recommendation option. </p>"}, "containerRecommendations": {"shape": "ContainerRecommendations", "documentation": "<p> The CPU and memory size recommendations for the containers within the task of your Amazon ECS service. </p>"}}, "documentation": "<p> Describes the recommendation options for an Amazon ECS service. </p>"}, "ECSServiceRecommendationOptions": {"type": "list", "member": {"shape": "ECSServiceRecommendationOption"}}, "ECSServiceRecommendations": {"type": "list", "member": {"shape": "ECSServiceRecommendation"}}, "ECSServiceRecommendedOptionProjectedMetric": {"type": "structure", "members": {"recommendedCpuUnits": {"shape": "CpuSize", "documentation": "<p> The recommended CPU size for the Amazon ECS service. </p>"}, "recommendedMemorySize": {"shape": "MemorySize", "documentation": "<p> The recommended memory size for the Amazon ECS service. </p>"}, "projectedMetrics": {"shape": "ECSServiceProjectedMetrics", "documentation": "<p> An array of objects that describe the projected metric. </p>"}}, "documentation": "<p> Describes the projected metrics of an Amazon ECS service recommendation option. </p> <p>To determine the performance difference between your current Amazon ECS service and the recommended option, compare the metric data of your service against its projected metric data.</p>"}, "ECSServiceRecommendedOptionProjectedMetrics": {"type": "list", "member": {"shape": "ECSServiceRecommendedOptionProjectedMetric"}}, "ECSServiceUtilizationMetric": {"type": "structure", "members": {"name": {"shape": "ECSServiceMetricName", "documentation": "<p> The name of the utilization metric. </p> <p>The following utilization metrics are available:</p> <ul> <li> <p> <code>Cpu</code> — The amount of CPU capacity that's used in the service.</p> </li> <li> <p> <code>Memory</code> — The amount of memory that's used in the service.</p> </li> </ul>"}, "statistic": {"shape": "ECSServiceMetricStatistic", "documentation": "<p>The statistic of the utilization metric.</p> <p>The Compute Optimizer API, Command Line Interface (CLI), and SDKs return utilization metrics using only the <code>Maximum</code> statistic, which is the highest value observed during the specified period.</p> <p>The Compute Optimizer console displays graphs for some utilization metrics using the <code>Average</code> statistic, which is the value of <code>Sum</code> / <code>SampleCount</code> during the specified period. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/viewing-recommendations.html\">Viewing resource recommendations</a> in the <i>Compute Optimizer User Guide</i>. You can also get averaged utilization metric data for your resources using Amazon CloudWatch. For more information, see the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/WhatIsCloudWatch.html\">Amazon CloudWatch User Guide</a>.</p>"}, "value": {"shape": "MetricValue", "documentation": "<p> The value of the utilization metric. </p>"}}, "documentation": "<p> Describes the utilization metric of an Amazon ECS service. </p> <p>To determine the performance difference between your current Amazon ECS service and the recommended option, compare the utilization metric data of your service against its projected utilization metric data.</p>"}, "ECSServiceUtilizationMetrics": {"type": "list", "member": {"shape": "ECSServiceUtilizationMetric"}}, "EffectiveRecommendationPreferences": {"type": "structure", "members": {"cpuVendorArchitectures": {"shape": "CpuVendorArchitectures", "documentation": "<p>Describes the CPU vendor and architecture for an instance or Auto Scaling group recommendations.</p> <p>For example, when you specify <code>AWS_ARM64</code> with:</p> <ul> <li> <p>A <a>GetEC2InstanceRecommendations</a> or <a>GetAutoScalingGroupRecommendations</a> request, Compute Optimizer returns recommendations that consist of Graviton2 instance types only.</p> </li> <li> <p>A <a>GetEC2RecommendationProjectedMetrics</a> request, Compute Optimizer returns projected utilization metrics for Graviton2 instance type recommendations only.</p> </li> <li> <p>A <a>ExportEC2InstanceRecommendations</a> or <a>ExportAutoScalingGroupRecommendations</a> request, Compute Optimizer exports recommendations that consist of Graviton2 instance types only.</p> </li> </ul>"}, "enhancedInfrastructureMetrics": {"shape": "EnhancedInfrastructureMetrics", "documentation": "<p>Describes the activation status of the enhanced infrastructure metrics preference.</p> <p>A status of <code>Active</code> confirms that the preference is applied in the latest recommendation refresh, and a status of <code>Inactive</code> confirms that it's not yet applied to recommendations.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/enhanced-infrastructure-metrics.html\">Enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "inferredWorkloadTypes": {"shape": "InferredWorkloadTypesPreference", "documentation": "<p>Describes the activation status of the inferred workload types preference.</p> <p>A status of <code>Active</code> confirms that the preference is applied in the latest recommendation refresh. A status of <code>Inactive</code> confirms that it's not yet applied to recommendations.</p>"}, "externalMetricsPreference": {"shape": "ExternalMetricsPreference", "documentation": "<p> An object that describes the external metrics recommendation preference. </p> <p> If the preference is applied in the latest recommendation refresh, an object with a valid <code>source</code> value appears in the response. If the preference isn't applied to the recommendations already, then this object doesn't appear in the response. </p>"}}, "documentation": "<p>Describes the effective recommendation preferences for a resource.</p>"}, "EnhancedInfrastructureMetrics": {"type": "string", "enum": ["Active", "Inactive"]}, "EnrollmentFilter": {"type": "structure", "members": {"name": {"shape": "EnrollmentFilterName", "documentation": "<p>The name of the filter.</p> <p>Specify <code>Status</code> to return accounts with a specific enrollment status (for example, <code>Active</code>).</p>"}, "values": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The value of the filter.</p> <p>The valid values are <code>Active</code>, <code>Inactive</code>, <code>Pending</code>, and <code>Failed</code>.</p>"}}, "documentation": "<p>Describes a filter that returns a more specific list of account enrollment statuses. Use this filter with the <a>GetEnrollmentStatusesForOrganization</a> action.</p>"}, "EnrollmentFilterName": {"type": "string", "enum": ["Status"]}, "EnrollmentFilters": {"type": "list", "member": {"shape": "EnrollmentFilter"}}, "ErrorMessage": {"type": "string"}, "EstimatedMonthlySavings": {"type": "structure", "members": {"currency": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The currency of the estimated monthly savings.</p>"}, "value": {"shape": "Value", "documentation": "<p>The value of the estimated monthly savings.</p>"}}, "documentation": "<p>Describes the estimated monthly savings amount possible, based on On-Demand instance pricing, by adopting Compute Optimizer recommendations for a given resource.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/view-ec2-recommendations.html#ec2-savings-calculation\">Estimated monthly savings and savings opportunities</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "ExportAutoScalingGroupRecommendationsRequest": {"type": "structure", "required": ["s3DestinationConfig"], "members": {"accountIds": {"shape": "AccountIds", "documentation": "<p>The IDs of the Amazon Web Services accounts for which to export Auto Scaling group recommendations.</p> <p>If your account is the management account of an organization, use this parameter to specify the member account for which you want to export recommendations.</p> <p>This parameter cannot be specified together with the include member accounts parameter. The parameters are mutually exclusive.</p> <p>Recommendations for member accounts are not included in the export if this parameter, or the include member accounts parameter, is omitted.</p> <p>You can specify multiple account IDs per request.</p>"}, "filters": {"shape": "Filters", "documentation": "<p>An array of objects to specify a filter that exports a more specific set of Auto Scaling group recommendations.</p>"}, "fieldsToExport": {"shape": "ExportableAutoScalingGroupFields", "documentation": "<p>The recommendations data to include in the export file. For more information about the fields that can be exported, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/exporting-recommendations.html#exported-files\">Exported files</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "s3DestinationConfig": {"shape": "S3DestinationConfig", "documentation": "<p>An object to specify the destination Amazon Simple Storage Service (Amazon S3) bucket name and key prefix for the export job.</p> <p>You must create the destination Amazon S3 bucket for your recommendations export before you create the export job. Compute Optimizer does not create the S3 bucket for you. After you create the S3 bucket, ensure that it has the required permissions policy to allow Compute Optimizer to write the export file to it. If you plan to specify an object prefix when you create the export job, you must include the object prefix in the policy that you add to the S3 bucket. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/create-s3-bucket-policy-for-compute-optimizer.html\">Amazon S3 Bucket Policy for Compute Optimizer</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "fileFormat": {"shape": "FileFormat", "documentation": "<p>The format of the export file.</p> <p>The only export file format currently supported is <code>Csv</code>.</p>"}, "includeMemberAccounts": {"shape": "IncludeMemberAccounts", "documentation": "<p>Indicates whether to include recommendations for resources in all member accounts of the organization if your account is the management account of an organization.</p> <p>The member accounts must also be opted in to Compute Optimizer, and trusted access for Compute Optimizer must be enabled in the organization account. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/security-iam.html#trusted-service-access\">Compute Optimizer and Amazon Web Services Organizations trusted access</a> in the <i>Compute Optimizer User Guide</i>.</p> <p>Recommendations for member accounts of the organization are not included in the export file if this parameter is omitted.</p> <p>This parameter cannot be specified together with the account IDs parameter. The parameters are mutually exclusive.</p> <p>Recommendations for member accounts are not included in the export if this parameter, or the account IDs parameter, is omitted.</p>"}, "recommendationPreferences": {"shape": "RecommendationPreferences", "documentation": "<p>An object to specify the preferences for the Auto Scaling group recommendations to export.</p>"}}}, "ExportAutoScalingGroupRecommendationsResponse": {"type": "structure", "members": {"jobId": {"shape": "JobId", "documentation": "<p>The identification number of the export job.</p> <p>Use the <a>DescribeRecommendationExportJobs</a> action, and specify the job ID to view the status of an export job.</p>"}, "s3Destination": {"shape": "S3Destination", "documentation": "<p>An object that describes the destination Amazon S3 bucket of a recommendations export file.</p>"}}}, "ExportDestination": {"type": "structure", "members": {"s3": {"shape": "S3Destination", "documentation": "<p>An object that describes the destination Amazon Simple Storage Service (Amazon S3) bucket name and object keys of a recommendations export file, and its associated metadata file.</p>"}}, "documentation": "<p>Describes the destination of the recommendations export and metadata files.</p>"}, "ExportEBSVolumeRecommendationsRequest": {"type": "structure", "required": ["s3DestinationConfig"], "members": {"accountIds": {"shape": "AccountIds", "documentation": "<p>The IDs of the Amazon Web Services accounts for which to export Amazon EBS volume recommendations.</p> <p>If your account is the management account of an organization, use this parameter to specify the member account for which you want to export recommendations.</p> <p>This parameter cannot be specified together with the include member accounts parameter. The parameters are mutually exclusive.</p> <p>Recommendations for member accounts are not included in the export if this parameter, or the include member accounts parameter, is omitted.</p> <p>You can specify multiple account IDs per request.</p>"}, "filters": {"shape": "EBSFilters", "documentation": "<p>An array of objects to specify a filter that exports a more specific set of Amazon EBS volume recommendations.</p>"}, "fieldsToExport": {"shape": "ExportableVolumeFields", "documentation": "<p>The recommendations data to include in the export file. For more information about the fields that can be exported, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/exporting-recommendations.html#exported-files\">Exported files</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "s3DestinationConfig": {"shape": "S3DestinationConfig"}, "fileFormat": {"shape": "FileFormat", "documentation": "<p>The format of the export file.</p> <p>The only export file format currently supported is <code>Csv</code>.</p>"}, "includeMemberAccounts": {"shape": "IncludeMemberAccounts", "documentation": "<p>Indicates whether to include recommendations for resources in all member accounts of the organization if your account is the management account of an organization.</p> <p>The member accounts must also be opted in to Compute Optimizer, and trusted access for Compute Optimizer must be enabled in the organization account. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/security-iam.html#trusted-service-access\">Compute Optimizer and Amazon Web Services Organizations trusted access</a> in the <i>Compute Optimizer User Guide</i>.</p> <p>Recommendations for member accounts of the organization are not included in the export file if this parameter is omitted.</p> <p>This parameter cannot be specified together with the account IDs parameter. The parameters are mutually exclusive.</p> <p>Recommendations for member accounts are not included in the export if this parameter, or the account IDs parameter, is omitted.</p>"}}}, "ExportEBSVolumeRecommendationsResponse": {"type": "structure", "members": {"jobId": {"shape": "JobId", "documentation": "<p>The identification number of the export job.</p> <p>Use the <a>DescribeRecommendationExportJobs</a> action, and specify the job ID to view the status of an export job.</p>"}, "s3Destination": {"shape": "S3Destination"}}}, "ExportEC2InstanceRecommendationsRequest": {"type": "structure", "required": ["s3DestinationConfig"], "members": {"accountIds": {"shape": "AccountIds", "documentation": "<p>The IDs of the Amazon Web Services accounts for which to export instance recommendations.</p> <p>If your account is the management account of an organization, use this parameter to specify the member account for which you want to export recommendations.</p> <p>This parameter cannot be specified together with the include member accounts parameter. The parameters are mutually exclusive.</p> <p>Recommendations for member accounts are not included in the export if this parameter, or the include member accounts parameter, is omitted.</p> <p>You can specify multiple account IDs per request.</p>"}, "filters": {"shape": "Filters", "documentation": "<p>An array of objects to specify a filter that exports a more specific set of instance recommendations.</p>"}, "fieldsToExport": {"shape": "ExportableInstanceFields", "documentation": "<p>The recommendations data to include in the export file. For more information about the fields that can be exported, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/exporting-recommendations.html#exported-files\">Exported files</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "s3DestinationConfig": {"shape": "S3DestinationConfig", "documentation": "<p>An object to specify the destination Amazon Simple Storage Service (Amazon S3) bucket name and key prefix for the export job.</p> <p>You must create the destination Amazon S3 bucket for your recommendations export before you create the export job. Compute Optimizer does not create the S3 bucket for you. After you create the S3 bucket, ensure that it has the required permissions policy to allow Compute Optimizer to write the export file to it. If you plan to specify an object prefix when you create the export job, you must include the object prefix in the policy that you add to the S3 bucket. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/create-s3-bucket-policy-for-compute-optimizer.html\">Amazon S3 Bucket Policy for Compute Optimizer</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "fileFormat": {"shape": "FileFormat", "documentation": "<p>The format of the export file.</p> <p>The only export file format currently supported is <code>Csv</code>.</p>"}, "includeMemberAccounts": {"shape": "IncludeMemberAccounts", "documentation": "<p>Indicates whether to include recommendations for resources in all member accounts of the organization if your account is the management account of an organization.</p> <p>The member accounts must also be opted in to Compute Optimizer, and trusted access for Compute Optimizer must be enabled in the organization account. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/security-iam.html#trusted-service-access\">Compute Optimizer and Amazon Web Services Organizations trusted access</a> in the <i>Compute Optimizer User Guide</i>.</p> <p>Recommendations for member accounts of the organization are not included in the export file if this parameter is omitted.</p> <p>Recommendations for member accounts are not included in the export if this parameter, or the account IDs parameter, is omitted.</p>"}, "recommendationPreferences": {"shape": "RecommendationPreferences", "documentation": "<p>An object to specify the preferences for the Amazon EC2 instance recommendations to export.</p>"}}}, "ExportEC2InstanceRecommendationsResponse": {"type": "structure", "members": {"jobId": {"shape": "JobId", "documentation": "<p>The identification number of the export job.</p> <p>Use the <a>DescribeRecommendationExportJobs</a> action, and specify the job ID to view the status of an export job.</p>"}, "s3Destination": {"shape": "S3Destination", "documentation": "<p>An object that describes the destination Amazon S3 bucket of a recommendations export file.</p>"}}}, "ExportECSServiceRecommendationsRequest": {"type": "structure", "required": ["s3DestinationConfig"], "members": {"accountIds": {"shape": "AccountIds", "documentation": "<p> The Amazon Web Services account IDs for the export Amazon ECS service recommendations. </p> <p>If your account is the management account or the delegated administrator of an organization, use this parameter to specify the member account you want to export recommendations to.</p> <p>This parameter can't be specified together with the include member accounts parameter. The parameters are mutually exclusive.</p> <p>If this parameter or the include member accounts parameter is omitted, the recommendations for member accounts aren't included in the export.</p> <p>You can specify multiple account IDs per request.</p>"}, "filters": {"shape": "ECSServiceRecommendationFilters", "documentation": "<p> An array of objects to specify a filter that exports a more specific set of Amazon ECS service recommendations. </p>"}, "fieldsToExport": {"shape": "ExportableECSServiceFields", "documentation": "<p>The recommendations data to include in the export file. For more information about the fields that can be exported, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/exporting-recommendations.html#exported-files\">Exported files</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "s3DestinationConfig": {"shape": "S3DestinationConfig"}, "fileFormat": {"shape": "FileFormat", "documentation": "<p> The format of the export file. </p> <p>The CSV file is the only export file format currently supported.</p>"}, "includeMemberAccounts": {"shape": "IncludeMemberAccounts", "documentation": "<p>If your account is the management account or the delegated administrator of an organization, this parameter indicates whether to include recommendations for resources in all member accounts of the organization.</p> <p>The member accounts must also be opted in to Compute Optimizer, and trusted access for Compute Optimizer must be enabled in the organization account. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/security-iam.html#trusted-service-access\">Compute Optimizer and Amazon Web Services Organizations trusted access</a> in the <i>Compute Optimizer User Guide</i>.</p> <p>If this parameter is omitted, recommendations for member accounts of the organization aren't included in the export file.</p> <p>If this parameter or the account ID parameter is omitted, recommendations for member accounts aren't included in the export.</p>"}}}, "ExportECSServiceRecommendationsResponse": {"type": "structure", "members": {"jobId": {"shape": "JobId", "documentation": "<p> The identification number of the export job. </p> <p>To view the status of an export job, use the <a>DescribeRecommendationExportJobs</a> action and specify the job ID. </p>"}, "s3Destination": {"shape": "S3Destination"}}}, "ExportLambdaFunctionRecommendationsRequest": {"type": "structure", "required": ["s3DestinationConfig"], "members": {"accountIds": {"shape": "AccountIds", "documentation": "<p>The IDs of the Amazon Web Services accounts for which to export Lambda function recommendations.</p> <p>If your account is the management account of an organization, use this parameter to specify the member account for which you want to export recommendations.</p> <p>This parameter cannot be specified together with the include member accounts parameter. The parameters are mutually exclusive.</p> <p>Recommendations for member accounts are not included in the export if this parameter, or the include member accounts parameter, is omitted.</p> <p>You can specify multiple account IDs per request.</p>"}, "filters": {"shape": "LambdaFunctionRecommendationFilters", "documentation": "<p>An array of objects to specify a filter that exports a more specific set of Lambda function recommendations.</p>"}, "fieldsToExport": {"shape": "ExportableLambdaFunctionFields", "documentation": "<p>The recommendations data to include in the export file. For more information about the fields that can be exported, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/exporting-recommendations.html#exported-files\">Exported files</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "s3DestinationConfig": {"shape": "S3DestinationConfig"}, "fileFormat": {"shape": "FileFormat", "documentation": "<p>The format of the export file.</p> <p>The only export file format currently supported is <code>Csv</code>.</p>"}, "includeMemberAccounts": {"shape": "IncludeMemberAccounts", "documentation": "<p>Indicates whether to include recommendations for resources in all member accounts of the organization if your account is the management account of an organization.</p> <p>The member accounts must also be opted in to Compute Optimizer, and trusted access for Compute Optimizer must be enabled in the organization account. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/security-iam.html#trusted-service-access\">Compute Optimizer and Amazon Web Services Organizations trusted access</a> in the <i>Compute Optimizer User Guide</i>.</p> <p>Recommendations for member accounts of the organization are not included in the export file if this parameter is omitted.</p> <p>This parameter cannot be specified together with the account IDs parameter. The parameters are mutually exclusive.</p> <p>Recommendations for member accounts are not included in the export if this parameter, or the account IDs parameter, is omitted.</p>"}}}, "ExportLambdaFunctionRecommendationsResponse": {"type": "structure", "members": {"jobId": {"shape": "JobId", "documentation": "<p>The identification number of the export job.</p> <p>Use the <a>DescribeRecommendationExportJobs</a> action, and specify the job ID to view the status of an export job.</p>"}, "s3Destination": {"shape": "S3Destination"}}}, "ExportableAutoScalingGroupField": {"type": "string", "enum": ["AccountId", "AutoScalingGroupArn", "AutoScalingGroupName", "Finding", "UtilizationMetricsCpuMaximum", "UtilizationMetricsMemoryMaximum", "UtilizationMetricsEbsReadOpsPerSecondMaximum", "UtilizationMetricsEbsWriteOpsPerSecondMaximum", "UtilizationMetricsEbsReadBytesPerSecondMaximum", "UtilizationMetricsEbsWriteBytesPerSecondMaximum", "UtilizationMetricsDiskReadOpsPerSecondMaximum", "UtilizationMetricsDiskWriteOpsPerSecondMaximum", "UtilizationMetricsDiskReadBytesPerSecondMaximum", "UtilizationMetricsDiskWriteBytesPerSecondMaximum", "UtilizationMetricsNetworkInBytesPerSecondMaximum", "UtilizationMetricsNetworkOutBytesPerSecondMaximum", "UtilizationMetricsNetworkPacketsInPerSecondMaximum", "UtilizationMetricsNetworkPacketsOutPerSecondMaximum", "LookbackPeriodInDays", "CurrentConfigurationInstanceType", "CurrentConfigurationDesiredCapacity", "CurrentConfigurationMinSize", "CurrentConfigurationMaxSize", "CurrentOnDemandPrice", "CurrentStandardOneYearNoUpfrontReservedPrice", "CurrentStandardThreeYearNoUpfrontReservedPrice", "CurrentVCpus", "Current<PERSON><PERSON>ory", "CurrentStorage", "CurrentNetwork", "RecommendationOptionsConfigurationInstanceType", "RecommendationOptionsConfigurationDesiredCapacity", "RecommendationOptionsConfigurationMinSize", "RecommendationOptionsConfigurationMaxSize", "RecommendationOptionsProjectedUtilizationMetricsCpuMaximum", "RecommendationOptionsProjectedUtilizationMetricsMemoryMaximum", "RecommendationOptionsPerformanceRisk", "RecommendationOptionsOnDemandPrice", "RecommendationOptionsStandardOneYearNoUpfrontReservedPrice", "RecommendationOptionsStandardThreeYearNoUpfrontReservedPrice", "RecommendationOptionsVcpus", "RecommendationOptionsMemory", "RecommendationOptionsStorage", "RecommendationOptionsNetwork", "LastRefreshTimestamp", "CurrentPerformanceRisk", "RecommendationOptionsSavingsOpportunityPercentage", "RecommendationOptionsEstimatedMonthlySavingsCurrency", "RecommendationOptionsEstimatedMonthlySavingsValue", "EffectiveRecommendationPreferencesCpuVendorArchitectures", "EffectiveRecommendationPreferencesEnhancedInfrastructureMetrics", "EffectiveRecommendationPreferencesInferredWorkloadTypes", "InferredWorkloadTypes", "RecommendationOptionsMigrationEffort"]}, "ExportableAutoScalingGroupFields": {"type": "list", "member": {"shape": "ExportableAutoScalingGroupField"}}, "ExportableECSServiceField": {"type": "string", "enum": ["AccountId", "ServiceArn", "LookbackPeriodInDays", "LastRefreshTimestamp", "LaunchType", "CurrentPerformanceRisk", "CurrentServiceConfigurationMemory", "CurrentServiceConfigurationCpu", "CurrentServiceConfigurationTaskDefinitionArn", "CurrentServiceConfigurationAutoScalingConfiguration", "CurrentServiceContainerConfigurations", "UtilizationMetricsCpuMaximum", "UtilizationMetricsMemoryMaximum", "Finding", "FindingReasonCodes", "RecommendationOptionsMemory", "RecommendationOptionsCpu", "RecommendationOptionsSavingsOpportunityPercentage", "RecommendationOptionsEstimatedMonthlySavingsCurrency", "RecommendationOptionsEstimatedMonthlySavingsValue", "RecommendationOptionsContainerRecommendations", "RecommendationOptionsProjectedUtilizationMetricsCpuMaximum", "RecommendationOptionsProjectedUtilizationMetricsMemoryMaximum"]}, "ExportableECSServiceFields": {"type": "list", "member": {"shape": "ExportableECSServiceField"}}, "ExportableInstanceField": {"type": "string", "enum": ["AccountId", "InstanceArn", "InstanceName", "Finding", "FindingReasonCodes", "LookbackPeriodInDays", "CurrentInstanceType", "UtilizationMetricsCpuMaximum", "UtilizationMetricsMemoryMaximum", "UtilizationMetricsEbsReadOpsPerSecondMaximum", "UtilizationMetricsEbsWriteOpsPerSecondMaximum", "UtilizationMetricsEbsReadBytesPerSecondMaximum", "UtilizationMetricsEbsWriteBytesPerSecondMaximum", "UtilizationMetricsDiskReadOpsPerSecondMaximum", "UtilizationMetricsDiskWriteOpsPerSecondMaximum", "UtilizationMetricsDiskReadBytesPerSecondMaximum", "UtilizationMetricsDiskWriteBytesPerSecondMaximum", "UtilizationMetricsNetworkInBytesPerSecondMaximum", "UtilizationMetricsNetworkOutBytesPerSecondMaximum", "UtilizationMetricsNetworkPacketsInPerSecondMaximum", "UtilizationMetricsNetworkPacketsOutPerSecondMaximum", "CurrentOnDemandPrice", "CurrentStandardOneYearNoUpfrontReservedPrice", "CurrentStandardThreeYearNoUpfrontReservedPrice", "CurrentVCpus", "Current<PERSON><PERSON>ory", "CurrentStorage", "CurrentNetwork", "RecommendationOptionsInstanceType", "RecommendationOptionsProjectedUtilizationMetricsCpuMaximum", "RecommendationOptionsProjectedUtilizationMetricsMemoryMaximum", "RecommendationOptionsPlatformDifferences", "RecommendationOptionsPerformanceRisk", "RecommendationOptionsVcpus", "RecommendationOptionsMemory", "RecommendationOptionsStorage", "RecommendationOptionsNetwork", "RecommendationOptionsOnDemandPrice", "RecommendationOptionsStandardOneYearNoUpfrontReservedPrice", "RecommendationOptionsStandardThreeYearNoUpfrontReservedPrice", "RecommendationsSourcesRecommendationSourceArn", "RecommendationsSourcesRecommendationSourceType", "LastRefreshTimestamp", "CurrentPerformanceRisk", "RecommendationOptionsSavingsOpportunityPercentage", "RecommendationOptionsEstimatedMonthlySavingsCurrency", "RecommendationOptionsEstimatedMonthlySavingsValue", "EffectiveRecommendationPreferencesCpuVendorArchitectures", "EffectiveRecommendationPreferencesEnhancedInfrastructureMetrics", "EffectiveRecommendationPreferencesInferredWorkloadTypes", "InferredWorkloadTypes", "RecommendationOptionsMigrationEffort", "EffectiveRecommendationPreferencesExternalMetricsSource"]}, "ExportableInstanceFields": {"type": "list", "member": {"shape": "ExportableInstanceField"}}, "ExportableLambdaFunctionField": {"type": "string", "enum": ["AccountId", "FunctionArn", "FunctionVersion", "Finding", "FindingReasonCodes", "NumberOfInvocations", "UtilizationMetricsDurationMaximum", "UtilizationMetricsDurationAverage", "UtilizationMetricsMemoryMaximum", "UtilizationMetricsMemoryAverage", "LookbackPeriodInDays", "CurrentConfigurationMemorySize", "CurrentConfigurationTimeout", "CurrentCostTotal", "CurrentCostAverage", "RecommendationOptionsConfigurationMemorySize", "RecommendationOptionsCostLow", "RecommendationOptionsCostHigh", "RecommendationOptionsProjectedUtilizationMetricsDurationLowerBound", "RecommendationOptionsProjectedUtilizationMetricsDurationUpperBound", "RecommendationOptionsProjectedUtilizationMetricsDurationExpected", "LastRefreshTimestamp", "CurrentPerformanceRisk", "RecommendationOptionsSavingsOpportunityPercentage", "RecommendationOptionsEstimatedMonthlySavingsCurrency", "RecommendationOptionsEstimatedMonthlySavingsValue"]}, "ExportableLambdaFunctionFields": {"type": "list", "member": {"shape": "ExportableLambdaFunctionField"}}, "ExportableVolumeField": {"type": "string", "enum": ["AccountId", "VolumeArn", "Finding", "UtilizationMetricsVolumeReadOpsPerSecondMaximum", "UtilizationMetricsVolumeWriteOpsPerSecondMaximum", "UtilizationMetricsVolumeReadBytesPerSecondMaximum", "UtilizationMetricsVolumeWriteBytesPerSecondMaximum", "LookbackPeriodInDays", "CurrentConfigurationVolumeType", "CurrentConfigurationVolumeBaselineIOPS", "CurrentConfigurationVolumeBaselineThroughput", "CurrentConfigurationVolumeBurstIOPS", "CurrentConfigurationVolumeBurstThroughput", "CurrentConfigurationVolumeSize", "CurrentMonthlyPrice", "RecommendationOptionsConfigurationVolumeType", "RecommendationOptionsConfigurationVolumeBaselineIOPS", "RecommendationOptionsConfigurationVolumeBaselineThroughput", "RecommendationOptionsConfigurationVolumeBurstIOPS", "RecommendationOptionsConfigurationVolumeBurstThroughput", "RecommendationOptionsConfigurationVolumeSize", "RecommendationOptionsMonthlyPrice", "RecommendationOptionsPerformanceRisk", "LastRefreshTimestamp", "CurrentPerformanceRisk", "RecommendationOptionsSavingsOpportunityPercentage", "RecommendationOptionsEstimatedMonthlySavingsCurrency", "RecommendationOptionsEstimatedMonthlySavingsValue"]}, "ExportableVolumeFields": {"type": "list", "member": {"shape": "ExportableVolumeField"}}, "ExternalMetricsPreference": {"type": "structure", "members": {"source": {"shape": "ExternalMetricsSource", "documentation": "<p> Contains the source options for external metrics preferences. </p>"}}, "documentation": "<p> Describes the external metrics preferences for EC2 rightsizing recommendations. </p>"}, "ExternalMetricsSource": {"type": "string", "enum": ["Datadog", "Dynatrace", "NewRelic", "Instana"]}, "FailureReason": {"type": "string"}, "FileFormat": {"type": "string", "enum": ["Csv"]}, "Filter": {"type": "structure", "members": {"name": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The name of the filter.</p> <p>Specify <code>Finding</code> to return recommendations with a specific finding classification (for example, <code>Underprovisioned</code>).</p> <p>Specify <code>RecommendationSourceType</code> to return recommendations of a specific resource type (for example, <code>Ec2Instance</code>).</p> <p>Specify <code>FindingReasonCodes</code> to return recommendations with a specific finding reason code (for example, <code>CPUUnderprovisioned</code>).</p>"}, "values": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The value of the filter.</p> <p>The valid values for this parameter are as follows, depending on what you specify for the <code>name</code> parameter and the resource type that you wish to filter results for:</p> <ul> <li> <p>Specify <code>Optimized</code> or <code>NotOptimized</code> if you specify the <code>name</code> parameter as <code>Finding</code> and you want to filter results for Auto Scaling groups.</p> </li> <li> <p>Specify <code>Underprovisioned</code>, <code>Overprovisioned</code>, or <code>Optimized</code> if you specify the <code>name</code> parameter as <code>Finding</code> and you want to filter results for EC2 instances.</p> </li> <li> <p>Specify <code>Ec2Instance</code> or <code>AutoScalingGroup</code> if you specify the <code>name</code> parameter as <code>RecommendationSourceType</code>.</p> </li> <li> <p>Specify one of the following options if you specify the <code>name</code> parameter as <code>FindingReasonCodes</code>:</p> <ul> <li> <p> <b> <code>CPUOverprovisioned</code> </b> — The instance’s CPU configuration can be sized down while still meeting the performance requirements of your workload.</p> </li> <li> <p> <b> <code>CPUUnderprovisioned</code> </b> — The instance’s CPU configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better CPU performance.</p> </li> <li> <p> <b> <code>MemoryOverprovisioned</code> </b> — The instance’s memory configuration can be sized down while still meeting the performance requirements of your workload.</p> </li> <li> <p> <b> <code>MemoryUnderprovisioned</code> </b> — The instance’s memory configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better memory performance.</p> </li> <li> <p> <b> <code>EBSThroughputOverprovisioned</code> </b> — The instance’s EBS throughput configuration can be sized down while still meeting the performance requirements of your workload.</p> </li> <li> <p> <b> <code>EBSThroughputUnderprovisioned</code> </b> — The instance’s EBS throughput configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better EBS throughput performance.</p> </li> <li> <p> <b> <code>EBSIOPSOverprovisioned</code> </b> — The instance’s EBS IOPS configuration can be sized down while still meeting the performance requirements of your workload.</p> </li> <li> <p> <b> <code>EBSIOPSUnderprovisioned</code> </b> — The instance’s EBS IOPS configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better EBS IOPS performance.</p> </li> <li> <p> <b> <code>NetworkBandwidthOverprovisioned</code> </b> — The instance’s network bandwidth configuration can be sized down while still meeting the performance requirements of your workload.</p> </li> <li> <p> <b> <code>NetworkBandwidthUnderprovisioned</code> </b> — The instance’s network bandwidth configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better network bandwidth performance. This finding reason happens when the <code>NetworkIn</code> or <code>NetworkOut</code> performance of an instance is impacted.</p> </li> <li> <p> <b> <code>NetworkPPSOverprovisioned</code> </b> — The instance’s network PPS (packets per second) configuration can be sized down while still meeting the performance requirements of your workload.</p> </li> <li> <p> <b> <code>NetworkPPSUnderprovisioned</code> </b> — The instance’s network PPS (packets per second) configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better network PPS performance.</p> </li> <li> <p> <b> <code>DiskIOPSOverprovisioned</code> </b> — The instance’s disk IOPS configuration can be sized down while still meeting the performance requirements of your workload.</p> </li> <li> <p> <b> <code>DiskIOPSUnderprovisioned</code> </b> — The instance’s disk IOPS configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better disk IOPS performance.</p> </li> <li> <p> <b> <code>DiskThroughputOverprovisioned</code> </b> — The instance’s disk throughput configuration can be sized down while still meeting the performance requirements of your workload.</p> </li> <li> <p> <b> <code>DiskThroughputUnderprovisioned</code> </b> — The instance’s disk throughput configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better disk throughput performance.</p> </li> </ul> </li> </ul>"}}, "documentation": "<p>Describes a filter that returns a more specific list of recommendations. Use this filter with the <a>GetAutoScalingGroupRecommendations</a> and <a>GetEC2InstanceRecommendations</a> actions.</p> <p>You can use <code>EBSFilter</code> with the <a>GetEBSVolumeRecommendations</a> action, <code>LambdaFunctionRecommendationFilter</code> with the <a>GetLambdaFunctionRecommendations</a> action, and <code>JobFilter</code> with the <a>DescribeRecommendationExportJobs</a> action.</p>"}, "FilterName": {"type": "string", "enum": ["Finding", "FindingReasonCodes", "RecommendationSourceType"]}, "FilterValue": {"type": "string"}, "FilterValues": {"type": "list", "member": {"shape": "FilterValue"}}, "Filters": {"type": "list", "member": {"shape": "Filter"}}, "Finding": {"type": "string", "enum": ["Underprovisioned", "Overprovisioned", "Optimized", "NotOptimized"]}, "FindingReasonCode": {"type": "string", "enum": ["MemoryOverprovisioned", "MemoryUnderprovisioned"]}, "FunctionArn": {"type": "string"}, "FunctionArns": {"type": "list", "member": {"shape": "FunctionArn"}}, "FunctionVersion": {"type": "string"}, "GetAutoScalingGroupRecommendationsRequest": {"type": "structure", "members": {"accountIds": {"shape": "AccountIds", "documentation": "<p>The ID of the Amazon Web Services account for which to return Auto Scaling group recommendations.</p> <p>If your account is the management account of an organization, use this parameter to specify the member account for which you want to return Auto Scaling group recommendations.</p> <p>Only one account ID can be specified per request.</p>"}, "autoScalingGroupArns": {"shape": "AutoScalingGroupArns", "documentation": "<p>The Amazon Resource Name (ARN) of the Auto Scaling groups for which to return recommendations.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to advance to the next page of Auto Scaling group recommendations.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of Auto Scaling group recommendations to return with a single request.</p> <p>To retrieve the remaining results, make another request with the returned <code>nextToken</code> value.</p>"}, "filters": {"shape": "Filters", "documentation": "<p>An array of objects to specify a filter that returns a more specific list of Auto Scaling group recommendations.</p>"}, "recommendationPreferences": {"shape": "RecommendationPreferences", "documentation": "<p>An object to specify the preferences for the Auto Scaling group recommendations to return in the response.</p>"}}}, "GetAutoScalingGroupRecommendationsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to advance to the next page of Auto Scaling group recommendations.</p> <p>This value is null when there are no more pages of Auto Scaling group recommendations to return.</p>"}, "autoScalingGroupRecommendations": {"shape": "AutoScalingGroupRecommendations", "documentation": "<p>An array of objects that describe Auto Scaling group recommendations.</p>"}, "errors": {"shape": "GetRecommendationErrors", "documentation": "<p>An array of objects that describe errors of the request.</p> <p>For example, an error is returned if you request recommendations for an unsupported Auto Scaling group.</p>"}}}, "GetEBSVolumeRecommendationsRequest": {"type": "structure", "members": {"volumeArns": {"shape": "VolumeArns", "documentation": "<p>The Amazon Resource Name (ARN) of the volumes for which to return recommendations.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to advance to the next page of volume recommendations.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of volume recommendations to return with a single request.</p> <p>To retrieve the remaining results, make another request with the returned <code>nextToken</code> value.</p>"}, "filters": {"shape": "EBSFilters", "documentation": "<p>An array of objects to specify a filter that returns a more specific list of volume recommendations.</p>"}, "accountIds": {"shape": "AccountIds", "documentation": "<p>The ID of the Amazon Web Services account for which to return volume recommendations.</p> <p>If your account is the management account of an organization, use this parameter to specify the member account for which you want to return volume recommendations.</p> <p>Only one account ID can be specified per request.</p>"}}}, "GetEBSVolumeRecommendationsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to advance to the next page of volume recommendations.</p> <p>This value is null when there are no more pages of volume recommendations to return.</p>"}, "volumeRecommendations": {"shape": "VolumeRecommendations", "documentation": "<p>An array of objects that describe volume recommendations.</p>"}, "errors": {"shape": "GetRecommendationErrors", "documentation": "<p>An array of objects that describe errors of the request.</p> <p>For example, an error is returned if you request recommendations for an unsupported volume.</p>"}}}, "GetEC2InstanceRecommendationsRequest": {"type": "structure", "members": {"instanceArns": {"shape": "InstanceArns", "documentation": "<p>The Amazon Resource Name (ARN) of the instances for which to return recommendations.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to advance to the next page of instance recommendations.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of instance recommendations to return with a single request.</p> <p>To retrieve the remaining results, make another request with the returned <code>nextToken</code> value.</p>"}, "filters": {"shape": "Filters", "documentation": "<p>An array of objects to specify a filter that returns a more specific list of instance recommendations.</p>"}, "accountIds": {"shape": "AccountIds", "documentation": "<p>The ID of the Amazon Web Services account for which to return instance recommendations.</p> <p>If your account is the management account of an organization, use this parameter to specify the member account for which you want to return instance recommendations.</p> <p>Only one account ID can be specified per request.</p>"}, "recommendationPreferences": {"shape": "RecommendationPreferences", "documentation": "<p>An object to specify the preferences for the Amazon EC2 instance recommendations to return in the response.</p>"}}}, "GetEC2InstanceRecommendationsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to advance to the next page of instance recommendations.</p> <p>This value is null when there are no more pages of instance recommendations to return.</p>"}, "instanceRecommendations": {"shape": "InstanceRecommendations", "documentation": "<p>An array of objects that describe instance recommendations.</p>"}, "errors": {"shape": "GetRecommendationErrors", "documentation": "<p>An array of objects that describe errors of the request.</p> <p>For example, an error is returned if you request recommendations for an instance of an unsupported instance family.</p>"}}}, "GetEC2RecommendationProjectedMetricsRequest": {"type": "structure", "required": ["instanceArn", "stat", "period", "startTime", "endTime"], "members": {"instanceArn": {"shape": "InstanceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the instances for which to return recommendation projected metrics.</p>"}, "stat": {"shape": "MetricStatistic", "documentation": "<p>The statistic of the projected metrics.</p>"}, "period": {"shape": "Period", "documentation": "<p>The granularity, in seconds, of the projected metrics data points.</p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p>The timestamp of the first projected metrics data point to return.</p>"}, "endTime": {"shape": "Timestamp", "documentation": "<p>The timestamp of the last projected metrics data point to return.</p>"}, "recommendationPreferences": {"shape": "RecommendationPreferences", "documentation": "<p>An object to specify the preferences for the Amazon EC2 recommendation projected metrics to return in the response.</p>"}}}, "GetEC2RecommendationProjectedMetricsResponse": {"type": "structure", "members": {"recommendedOptionProjectedMetrics": {"shape": "RecommendedOptionProjectedMetrics", "documentation": "<p>An array of objects that describes projected metrics.</p>"}}}, "GetECSServiceRecommendationProjectedMetricsRequest": {"type": "structure", "required": ["serviceArn", "stat", "period", "startTime", "endTime"], "members": {"serviceArn": {"shape": "ServiceArn", "documentation": "<p> The ARN that identifies the Amazon ECS service. </p> <p> The following is the format of the ARN: </p> <p> <code>arn:aws:ecs:region:aws_account_id:service/cluster-name/service-name</code> </p>"}, "stat": {"shape": "MetricStatistic", "documentation": "<p> The statistic of the projected metrics. </p>"}, "period": {"shape": "Period", "documentation": "<p> The granularity, in seconds, of the projected metrics data points. </p>"}, "startTime": {"shape": "Timestamp", "documentation": "<p> The timestamp of the first projected metrics data point to return. </p>"}, "endTime": {"shape": "Timestamp", "documentation": "<p> The timestamp of the last projected metrics data point to return. </p>"}}}, "GetECSServiceRecommendationProjectedMetricsResponse": {"type": "structure", "members": {"recommendedOptionProjectedMetrics": {"shape": "ECSServiceRecommendedOptionProjectedMetrics", "documentation": "<p> An array of objects that describes the projected metrics. </p>"}}}, "GetECSServiceRecommendationsRequest": {"type": "structure", "members": {"serviceArns": {"shape": "ServiceArns", "documentation": "<p> The ARN that identifies the Amazon ECS service. </p> <p> The following is the format of the ARN: </p> <p> <code>arn:aws:ecs:region:aws_account_id:service/cluster-name/service-name</code> </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The token to advance to the next page of Amazon ECS service recommendations. </p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> The maximum number of Amazon ECS service recommendations to return with a single request. </p> <p>To retrieve the remaining results, make another request with the returned <code>nextToken</code> value.</p>"}, "filters": {"shape": "ECSServiceRecommendationFilters", "documentation": "<p> An array of objects to specify a filter that returns a more specific list of Amazon ECS service recommendations. </p>"}, "accountIds": {"shape": "AccountIds", "documentation": "<p> Return the Amazon ECS service recommendations to the specified Amazon Web Services account IDs. </p> <p>If your account is the management account or the delegated administrator of an organization, use this parameter to return the Amazon ECS service recommendations to specific member accounts.</p> <p>You can only specify one account ID per request.</p>"}}}, "GetECSServiceRecommendationsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p> The token to advance to the next page of Amazon ECS service recommendations. </p>"}, "ecsServiceRecommendations": {"shape": "ECSServiceRecommendations", "documentation": "<p> An array of objects that describe the Amazon ECS service recommendations. </p>"}, "errors": {"shape": "GetRecommendationErrors", "documentation": "<p> An array of objects that describe errors of the request. </p>"}}}, "GetEffectiveRecommendationPreferencesRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource for which to confirm effective recommendation preferences. Only EC2 instance and Auto Scaling group ARNs are currently supported.</p>"}}}, "GetEffectiveRecommendationPreferencesResponse": {"type": "structure", "members": {"enhancedInfrastructureMetrics": {"shape": "EnhancedInfrastructureMetrics", "documentation": "<p>The status of the enhanced infrastructure metrics recommendation preference. Considers all applicable preferences that you might have set at the resource, account, and organization level.</p> <p>A status of <code>Active</code> confirms that the preference is applied in the latest recommendation refresh, and a status of <code>Inactive</code> confirms that it's not yet applied to recommendations.</p> <p>To validate whether the preference is applied to your last generated set of recommendations, review the <code>effectiveRecommendationPreferences</code> value in the response of the <a>GetAutoScalingGroupRecommendations</a> and <a>GetEC2InstanceRecommendations</a> actions.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/enhanced-infrastructure-metrics.html\">Enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "externalMetricsPreference": {"shape": "ExternalMetricsPreference", "documentation": "<p>The provider of the external metrics recommendation preference. Considers all applicable preferences that you might have set at the account and organization level.</p> <p>If the preference is applied in the latest recommendation refresh, an object with a valid <code>source</code> value appears in the response. If the preference isn't applied to the recommendations already, then this object doesn't appear in the response.</p> <p>To validate whether the preference is applied to your last generated set of recommendations, review the <code>effectiveRecommendationPreferences</code> value in the response of the <a>GetEC2InstanceRecommendations</a> actions.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/external-metrics-ingestion.html\">Enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p>"}}}, "GetEnrollmentStatusRequest": {"type": "structure", "members": {}}, "GetEnrollmentStatusResponse": {"type": "structure", "members": {"status": {"shape": "Status", "documentation": "<p>The enrollment status of the account.</p>"}, "statusReason": {"shape": "StatusReason", "documentation": "<p>The reason for the enrollment status of the account.</p> <p>For example, an account might show a status of <code>Pending</code> because member accounts of an organization require more time to be enrolled in the service.</p>"}, "memberAccountsEnrolled": {"shape": "MemberAccountsEnrolled", "documentation": "<p>Confirms the enrollment status of member accounts of the organization, if the account is a management account of an organization.</p>"}, "lastUpdatedTimestamp": {"shape": "LastUpdatedTimestamp", "documentation": "<p>The Unix epoch timestamp, in seconds, of when the account enrollment status was last updated.</p>"}, "numberOfMemberAccountsOptedIn": {"shape": "NumberOfMemberAccountsOptedIn", "documentation": "<p>The count of organization member accounts that are opted in to the service, if your account is an organization management account.</p>"}}}, "GetEnrollmentStatusesForOrganizationRequest": {"type": "structure", "members": {"filters": {"shape": "EnrollmentFilters", "documentation": "<p>An array of objects to specify a filter that returns a more specific list of account enrollment statuses.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to advance to the next page of account enrollment statuses.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of account enrollment statuses to return with a single request. You can specify up to 100 statuses to return with each request.</p> <p>To retrieve the remaining results, make another request with the returned <code>nextToken</code> value.</p>"}}}, "GetEnrollmentStatusesForOrganizationResponse": {"type": "structure", "members": {"accountEnrollmentStatuses": {"shape": "AccountEnrollmentStatuses", "documentation": "<p>An array of objects that describe the enrollment statuses of organization member accounts.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to advance to the next page of account enrollment statuses.</p> <p>This value is null when there are no more pages of account enrollment statuses to return.</p>"}}}, "GetLambdaFunctionRecommendationsRequest": {"type": "structure", "members": {"functionArns": {"shape": "FunctionArns", "documentation": "<p>The Amazon Resource Name (ARN) of the functions for which to return recommendations.</p> <p>You can specify a qualified or unqualified ARN. If you specify an unqualified ARN without a function version suffix, Compute Optimizer will return recommendations for the latest (<code>$LATEST</code>) version of the function. If you specify a qualified ARN with a version suffix, Compute Optimizer will return recommendations for the specified function version. For more information about using function versions, see <a href=\"https://docs.aws.amazon.com/lambda/latest/dg/configuration-versions.html#versioning-versions-using\">Using versions</a> in the <i>Lambda Developer Guide</i>.</p>"}, "accountIds": {"shape": "AccountIds", "documentation": "<p>The ID of the Amazon Web Services account for which to return function recommendations.</p> <p>If your account is the management account of an organization, use this parameter to specify the member account for which you want to return function recommendations.</p> <p>Only one account ID can be specified per request.</p>"}, "filters": {"shape": "LambdaFunctionRecommendationFilters", "documentation": "<p>An array of objects to specify a filter that returns a more specific list of function recommendations.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to advance to the next page of function recommendations.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of function recommendations to return with a single request.</p> <p>To retrieve the remaining results, make another request with the returned <code>nextToken</code> value.</p>"}}}, "GetLambdaFunctionRecommendationsResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to advance to the next page of function recommendations.</p> <p>This value is null when there are no more pages of function recommendations to return.</p>"}, "lambdaFunctionRecommendations": {"shape": "LambdaFunctionRecommendations", "documentation": "<p>An array of objects that describe function recommendations.</p>"}}}, "GetRecommendationError": {"type": "structure", "members": {"identifier": {"shape": "Identifier", "documentation": "<p>The ID of the error.</p>"}, "code": {"shape": "Code", "documentation": "<p>The error code.</p>"}, "message": {"shape": "Message", "documentation": "<p>The message, or reason, for the error.</p>"}}, "documentation": "<p>Describes an error experienced when getting recommendations.</p> <p>For example, an error is returned if you request recommendations for an unsupported Auto Scaling group, or if you request recommendations for an instance of an unsupported instance family.</p>"}, "GetRecommendationErrors": {"type": "list", "member": {"shape": "GetRecommendationError"}}, "GetRecommendationPreferencesRequest": {"type": "structure", "required": ["resourceType"], "members": {"resourceType": {"shape": "ResourceType", "documentation": "<p>The target resource type of the recommendation preference for which to return preferences.</p> <p>The <code>Ec2Instance</code> option encompasses standalone instances and instances that are part of Auto Scaling groups. The <code>AutoScalingGroup</code> option encompasses only instances that are part of an Auto Scaling group.</p> <note> <p>The valid values for this parameter are <code>Ec2Instance</code> and <code>AutoScalingGroup</code>.</p> </note>"}, "scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>An object that describes the scope of the recommendation preference to return.</p> <p>You can return recommendation preferences that are created at the organization level (for management accounts of an organization only), account level, and resource level. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/enhanced-infrastructure-metrics.html\">Activating enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to advance to the next page of recommendation preferences.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of recommendation preferences to return with a single request.</p> <p>To retrieve the remaining results, make another request with the returned <code>nextToken</code> value.</p>"}}}, "GetRecommendationPreferencesResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to advance to the next page of recommendation preferences.</p> <p>This value is null when there are no more pages of recommendation preferences to return.</p>"}, "recommendationPreferencesDetails": {"shape": "RecommendationPreferencesDetails", "documentation": "<p>An array of objects that describe recommendation preferences.</p>"}}}, "GetRecommendationSummariesRequest": {"type": "structure", "members": {"accountIds": {"shape": "AccountIds", "documentation": "<p>The ID of the Amazon Web Services account for which to return recommendation summaries.</p> <p>If your account is the management account of an organization, use this parameter to specify the member account for which you want to return recommendation summaries.</p> <p>Only one account ID can be specified per request.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token to advance to the next page of recommendation summaries.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of recommendation summaries to return with a single request.</p> <p>To retrieve the remaining results, make another request with the returned <code>nextToken</code> value.</p>"}}}, "GetRecommendationSummariesResponse": {"type": "structure", "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token to use to advance to the next page of recommendation summaries.</p> <p>This value is null when there are no more pages of recommendation summaries to return.</p>"}, "recommendationSummaries": {"shape": "RecommendationSummaries", "documentation": "<p>An array of objects that summarize a recommendation.</p>"}}}, "High": {"type": "long"}, "Identifier": {"type": "string"}, "IncludeMemberAccounts": {"type": "boolean"}, "InferredWorkloadType": {"type": "string", "enum": ["AmazonEmr", "ApacheCassandra", "ApacheHadoop", "Memca<PERSON>", "<PERSON><PERSON><PERSON>", "PostgreSql", "Redis", "Kafka"]}, "InferredWorkloadTypes": {"type": "list", "member": {"shape": "InferredWorkloadType"}}, "InferredWorkloadTypesPreference": {"type": "string", "enum": ["Active", "Inactive"]}, "InstanceArn": {"type": "string"}, "InstanceArns": {"type": "list", "member": {"shape": "InstanceArn"}}, "InstanceName": {"type": "string"}, "InstanceRecommendation": {"type": "structure", "members": {"instanceArn": {"shape": "InstanceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the current instance.</p>"}, "accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the instance.</p>"}, "instanceName": {"shape": "InstanceName", "documentation": "<p>The name of the current instance.</p>"}, "currentInstanceType": {"shape": "CurrentInstanceType", "documentation": "<p>The instance type of the current instance.</p>"}, "finding": {"shape": "Finding", "documentation": "<p>The finding classification of the instance.</p> <p>Findings for instances include:</p> <ul> <li> <p> <b> <code>Underprovisioned</code> </b>—An instance is considered under-provisioned when at least one specification of your instance, such as CPU, memory, or network, does not meet the performance requirements of your workload. Under-provisioned instances may lead to poor application performance.</p> </li> <li> <p> <b> <code>Overprovisioned</code> </b>—An instance is considered over-provisioned when at least one specification of your instance, such as CPU, memory, or network, can be sized down while still meeting the performance requirements of your workload, and no specification is under-provisioned. Over-provisioned instances may lead to unnecessary infrastructure cost.</p> </li> <li> <p> <b> <code>Optimized</code> </b>—An instance is considered optimized when all specifications of your instance, such as CPU, memory, and network, meet the performance requirements of your workload and is not over provisioned. For optimized resources, Compute Optimizer might recommend a new generation instance type.</p> </li> </ul>"}, "findingReasonCodes": {"shape": "InstanceRecommendationFindingReasonCodes", "documentation": "<p>The reason for the finding classification of the instance.</p> <p>Finding reason codes for instances include:</p> <ul> <li> <p> <b> <code>CPUOverprovisioned</code> </b> — The instance’s CPU configuration can be sized down while still meeting the performance requirements of your workload. This is identified by analyzing the <code>CPUUtilization</code> metric of the current instance during the look-back period.</p> </li> <li> <p> <b> <code>CPUUnderprovisioned</code> </b> — The instance’s CPU configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better CPU performance. This is identified by analyzing the <code>CPUUtilization</code> metric of the current instance during the look-back period.</p> </li> <li> <p> <b> <code>MemoryOverprovisioned</code> </b> — The instance’s memory configuration can be sized down while still meeting the performance requirements of your workload. This is identified by analyzing the memory utilization metric of the current instance during the look-back period.</p> </li> <li> <p> <b> <code>MemoryUnderprovisioned</code> </b> — The instance’s memory configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better memory performance. This is identified by analyzing the memory utilization metric of the current instance during the look-back period.</p> <note> <p>Memory utilization is analyzed only for resources that have the unified CloudWatch agent installed on them. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/metrics.html#cw-agent\">Enabling memory utilization with the Amazon CloudWatch Agent</a> in the <i>Compute Optimizer User Guide</i>. On Linux instances, Compute Optimizer analyses the <code>mem_used_percent</code> metric in the <code>CWAgent</code> namespace, or the legacy <code>MemoryUtilization</code> metric in the <code>System/Linux</code> namespace. On Windows instances, Compute Optimizer analyses the <code>Memory % Committed Bytes In Use</code> metric in the <code>CWAgent</code> namespace.</p> </note> </li> <li> <p> <b> <code>EBSThroughputOverprovisioned</code> </b> — The instance’s EBS throughput configuration can be sized down while still meeting the performance requirements of your workload. This is identified by analyzing the <code>VolumeReadOps</code> and <code>VolumeWriteOps</code> metrics of EBS volumes attached to the current instance during the look-back period.</p> </li> <li> <p> <b> <code>EBSThroughputUnderprovisioned</code> </b> — The instance’s EBS throughput configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better EBS throughput performance. This is identified by analyzing the <code>VolumeReadOps</code> and <code>VolumeWriteOps</code> metrics of EBS volumes attached to the current instance during the look-back period.</p> </li> <li> <p> <b> <code>EBSIOPSOverprovisioned</code> </b> — The instance’s EBS IOPS configuration can be sized down while still meeting the performance requirements of your workload. This is identified by analyzing the <code>VolumeReadBytes</code> and <code>VolumeWriteBytes</code> metric of EBS volumes attached to the current instance during the look-back period.</p> </li> <li> <p> <b> <code>EBSIOPSUnderprovisioned</code> </b> — The instance’s EBS IOPS configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better EBS IOPS performance. This is identified by analyzing the <code>VolumeReadBytes</code> and <code>VolumeWriteBytes</code> metric of EBS volumes attached to the current instance during the look-back period.</p> </li> <li> <p> <b> <code>NetworkBandwidthOverprovisioned</code> </b> — The instance’s network bandwidth configuration can be sized down while still meeting the performance requirements of your workload. This is identified by analyzing the <code>NetworkIn</code> and <code>NetworkOut</code> metrics of the current instance during the look-back period.</p> </li> <li> <p> <b> <code>NetworkBandwidthUnderprovisioned</code> </b> — The instance’s network bandwidth configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better network bandwidth performance. This is identified by analyzing the <code>NetworkIn</code> and <code>NetworkOut</code> metrics of the current instance during the look-back period. This finding reason happens when the <code>NetworkIn</code> or <code>NetworkOut</code> performance of an instance is impacted.</p> </li> <li> <p> <b> <code>NetworkPPSOverprovisioned</code> </b> — The instance’s network PPS (packets per second) configuration can be sized down while still meeting the performance requirements of your workload. This is identified by analyzing the <code>NetworkPacketsIn</code> and <code>NetworkPacketsIn</code> metrics of the current instance during the look-back period.</p> </li> <li> <p> <b> <code>NetworkPPSUnderprovisioned</code> </b> — The instance’s network PPS (packets per second) configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better network PPS performance. This is identified by analyzing the <code>NetworkPacketsIn</code> and <code>NetworkPacketsIn</code> metrics of the current instance during the look-back period.</p> </li> <li> <p> <b> <code>DiskIOPSOverprovisioned</code> </b> — The instance’s disk IOPS configuration can be sized down while still meeting the performance requirements of your workload. This is identified by analyzing the <code>DiskReadOps</code> and <code>DiskWriteOps</code> metrics of the current instance during the look-back period.</p> </li> <li> <p> <b> <code>DiskIOPSUnderprovisioned</code> </b> — The instance’s disk IOPS configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better disk IOPS performance. This is identified by analyzing the <code>DiskReadOps</code> and <code>DiskWriteOps</code> metrics of the current instance during the look-back period.</p> </li> <li> <p> <b> <code>DiskThroughputOverprovisioned</code> </b> — The instance’s disk throughput configuration can be sized down while still meeting the performance requirements of your workload. This is identified by analyzing the <code>DiskReadBytes</code> and <code>DiskWriteBytes</code> metrics of the current instance during the look-back period.</p> </li> <li> <p> <b> <code>DiskThroughputUnderprovisioned</code> </b> — The instance’s disk throughput configuration doesn't meet the performance requirements of your workload and there is an alternative instance type that provides better disk throughput performance. This is identified by analyzing the <code>DiskReadBytes</code> and <code>DiskWriteBytes</code> metrics of the current instance during the look-back period.</p> </li> </ul> <note> <p>For more information about instance metrics, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/viewing_metrics_with_cloudwatch.html\">List the available CloudWatch metrics for your instances</a> in the <i>Amazon Elastic Compute Cloud User Guide</i>. For more information about EBS volume metrics, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/using_cloudwatch_ebs.html\">Amazon CloudWatch metrics for Amazon EBS</a> in the <i>Amazon Elastic Compute Cloud User Guide</i>.</p> </note>"}, "utilizationMetrics": {"shape": "UtilizationMetrics", "documentation": "<p>An array of objects that describe the utilization metrics of the instance.</p>"}, "lookBackPeriodInDays": {"shape": "LookBackPeriodInDays", "documentation": "<p>The number of days for which utilization metrics were analyzed for the instance.</p>"}, "recommendationOptions": {"shape": "RecommendationOptions", "documentation": "<p>An array of objects that describe the recommendation options for the instance.</p>"}, "recommendationSources": {"shape": "RecommendationSources", "documentation": "<p>An array of objects that describe the source resource of the recommendation.</p>"}, "lastRefreshTimestamp": {"shape": "LastRefreshTimestamp", "documentation": "<p>The timestamp of when the instance recommendation was last generated.</p>"}, "currentPerformanceRisk": {"shape": "CurrentPerformanceRisk", "documentation": "<p>The risk of the current instance not meeting the performance needs of its workloads. The higher the risk, the more likely the current instance cannot meet the performance requirements of its workload.</p>"}, "effectiveRecommendationPreferences": {"shape": "EffectiveRecommendationPreferences", "documentation": "<p>An object that describes the effective recommendation preferences for the instance.</p>"}, "inferredWorkloadTypes": {"shape": "InferredWorkloadTypes", "documentation": "<p>The applications that might be running on the instance as inferred by Compute Optimizer.</p> <p>Compute Optimizer can infer if one of the following applications might be running on the instance:</p> <ul> <li> <p> <code>AmazonEmr</code> - Infers that Amazon EMR might be running on the instance.</p> </li> <li> <p> <code>ApacheCassandra</code> - Infers that Apache Cassandra might be running on the instance.</p> </li> <li> <p> <code>ApacheHadoop</code> - Infers that Apache Hadoop might be running on the instance.</p> </li> <li> <p> <code>Memcached</code> - Infers that Memca<PERSON> might be running on the instance.</p> </li> <li> <p> <code>NGINX</code> - Infers that NGINX might be running on the instance.</p> </li> <li> <p> <code>PostgreSql</code> - Infers that PostgreSQL might be running on the instance.</p> </li> <li> <p> <code>Redis</code> - Infers that <PERSON><PERSON> might be running on the instance.</p> </li> <li> <p> <code>Kafka</code> - Infers that <PERSON><PERSON><PERSON> might be running on the instance.</p> </li> </ul>"}}, "documentation": "<p>Describes an Amazon EC2 instance recommendation.</p>"}, "InstanceRecommendationFindingReasonCode": {"type": "string", "enum": ["CPUOverprovisioned", "CPUUnderprovisioned", "MemoryOverprovisioned", "MemoryUnderprovisioned", "EBSThroughputOverprovisioned", "EBSThroughputUnderprovisioned", "EBSIOPSOverprovisioned", "EBSIOPSUnderprovisioned", "NetworkBandwidthOverprovisioned", "NetworkBandwidthUnderprovisioned", "NetworkPPSOverprovisioned", "NetworkPPSUnderprovisioned", "DiskIOPSOverprovisioned", "DiskIOPSUnderprovisioned", "DiskThroughputOverprovisioned", "DiskThroughputUnderprovisioned"]}, "InstanceRecommendationFindingReasonCodes": {"type": "list", "member": {"shape": "InstanceRecommendationFindingReasonCode"}}, "InstanceRecommendationOption": {"type": "structure", "members": {"instanceType": {"shape": "InstanceType", "documentation": "<p>The instance type of the instance recommendation.</p>"}, "projectedUtilizationMetrics": {"shape": "ProjectedUtilizationMetrics", "documentation": "<p>An array of objects that describe the projected utilization metrics of the instance recommendation option.</p> <note> <p>The <code>Cpu</code> and <code>Memory</code> metrics are the only projected utilization metrics returned. Additionally, the <code>Memory</code> metric is returned only for resources that have the unified CloudWatch agent installed on them. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/metrics.html#cw-agent\">Enabling Memory Utilization with the CloudWatch Agent</a>.</p> </note>"}, "platformDifferences": {"shape": "PlatformDifferences", "documentation": "<p>Describes the configuration differences between the current instance and the recommended instance type. You should consider the configuration differences before migrating your workloads from the current instance to the recommended instance type. The <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ec2-instance-resize.html\">Change the instance type guide for Linux</a> and <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/ec2-instance-resize.html\">Change the instance type guide for Windows</a> provide general guidance for getting started with an instance migration.</p> <p>Platform differences include:</p> <ul> <li> <p> <b> <code>Hypervisor</code> </b> — The hypervisor of the recommended instance type is different than that of the current instance. For example, the recommended instance type uses a Nitro hypervisor and the current instance uses a Xen hypervisor. The differences that you should consider between these hypervisors are covered in the <a href=\"http://aws.amazon.com/ec2/faqs/#Nitro_Hypervisor\">Nitro Hypervisor</a> section of the Amazon EC2 frequently asked questions. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/instance-types.html#ec2-nitro-instances\">Instances built on the Nitro System</a> in the <i>Amazon EC2 User Guide for Linux</i>, or <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/instance-types.html#ec2-nitro-instances\">Instances built on the Nitro System</a> in the <i>Amazon EC2 User Guide for Windows</i>.</p> </li> <li> <p> <b> <code>NetworkInterface</code> </b> — The network interface of the recommended instance type is different than that of the current instance. For example, the recommended instance type supports enhanced networking and the current instance might not. To enable enhanced networking for the recommended instance type, you must install the Elastic Network Adapter (ENA) driver or the Intel 82599 Virtual Function driver. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/instance-types.html#instance-networking-storage\">Networking and storage features</a> and <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/enhanced-networking.html\">Enhanced networking on Linux</a> in the <i>Amazon EC2 User Guide for Linux</i>, or <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/instance-types.html#instance-networking-storage\">Networking and storage features</a> and <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/enhanced-networking.html\">Enhanced networking on Windows</a> in the <i>Amazon EC2 User Guide for Windows</i>.</p> </li> <li> <p> <b> <code>StorageInterface</code> </b> — The storage interface of the recommended instance type is different than that of the current instance. For example, the recommended instance type uses an NVMe storage interface and the current instance does not. To access NVMe volumes for the recommended instance type, you will need to install or upgrade the NVMe driver. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/instance-types.html#instance-networking-storage\">Networking and storage features</a> and <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/nvme-ebs-volumes.html\">Amazon EBS and NVMe on Linux instances</a> in the <i>Amazon EC2 User Guide for Linux</i>, or <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/instance-types.html#instance-networking-storage\">Networking and storage features</a> and <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/nvme-ebs-volumes.html\">Amazon EBS and NVMe on Windows instances</a> in the <i>Amazon EC2 User Guide for Windows</i>.</p> </li> <li> <p> <b> <code>InstanceStoreAvailability</code> </b> — The recommended instance type does not support instance store volumes and the current instance does. Before migrating, you might need to back up the data on your instance store volumes if you want to preserve them. For more information, see <a href=\"https://aws.amazon.com/premiumsupport/knowledge-center/back-up-instance-store-ebs/\">How do I back up an instance store volume on my Amazon EC2 instance to Amazon EBS?</a> in the <i>Amazon Web Services Premium Support Knowledge Base</i>. For more information, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/instance-types.html#instance-networking-storage\">Networking and storage features</a> and <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/InstanceStorage.html\">Amazon EC2 instance store</a> in the <i>Amazon EC2 User Guide for Linux</i>, or see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/instance-types.html#instance-networking-storage\">Networking and storage features</a> and <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/InstanceStorage.html\">Amazon EC2 instance store</a> in the <i>Amazon EC2 User Guide for Windows</i>.</p> </li> <li> <p> <b> <code>VirtualizationType</code> </b> — The recommended instance type uses the hardware virtual machine (HVM) virtualization type and the current instance uses the paravirtual (PV) virtualization type. For more information about the differences between these virtualization types, see <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/virtualization_types.html\">Linux AMI virtualization types</a> in the <i>Amazon EC2 User Guide for Linux</i>, or <a href=\"https://docs.aws.amazon.com/AWSEC2/latest/WindowsGuide/windows-ami-version-history.html#virtualization-types\">Windows AMI virtualization types</a> in the <i>Amazon EC2 User Guide for Windows</i>.</p> </li> <li> <p> <b> <code>Architecture</code> </b> — The CPU architecture between the recommended instance type and the current instance is different. For example, the recommended instance type might use an Arm CPU architecture and the current instance type might use a different one, such as x86. Before migrating, you should consider recompiling the software on your instance for the new architecture. Alternatively, you might switch to an Amazon Machine Image (AMI) that supports the new architecture. For more information about the CPU architecture for each instance type, see <a href=\"http://aws.amazon.com/ec2/instance-types/\">Amazon EC2 Instance Types</a>.</p> </li> </ul>"}, "performanceRisk": {"shape": "PerformanceRisk", "documentation": "<p>The performance risk of the instance recommendation option.</p> <p>Performance risk indicates the likelihood of the recommended instance type not meeting the resource needs of your workload. Compute Optimizer calculates an individual performance risk score for each specification of the recommended instance, including CPU, memory, EBS throughput, EBS IOPS, disk throughput, disk IOPS, network throughput, and network PPS. The performance risk of the recommended instance is calculated as the maximum performance risk score across the analyzed resource specifications.</p> <p>The value ranges from <code>0</code> - <code>4</code>, with <code>0</code> meaning that the recommended resource is predicted to always provide enough hardware capability. The higher the performance risk is, the more likely you should validate whether the recommendation will meet the performance requirements of your workload before migrating your resource.</p>"}, "rank": {"shape": "Rank", "documentation": "<p>The rank of the instance recommendation option.</p> <p>The top recommendation option is ranked as <code>1</code>.</p>"}, "savingsOpportunity": {"shape": "SavingsOpportunity", "documentation": "<p>An object that describes the savings opportunity for the instance recommendation option. Savings opportunity includes the estimated monthly savings amount and percentage.</p>"}, "migrationEffort": {"shape": "MigrationEffort", "documentation": "<p>The level of effort required to migrate from the current instance type to the recommended instance type.</p> <p>For example, the migration effort is <code>Low</code> if Amazon EMR is the inferred workload type and an Amazon Web Services Graviton instance type is recommended. The migration effort is <code>Medium</code> if a workload type couldn't be inferred but an Amazon Web Services Graviton instance type is recommended. The migration effort is <code>VeryLow</code> if both the current and recommended instance types are of the same CPU architecture.</p>"}}, "documentation": "<p>Describes a recommendation option for an Amazon EC2 instance.</p>"}, "InstanceRecommendations": {"type": "list", "member": {"shape": "InstanceRecommendation"}}, "InstanceType": {"type": "string"}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>An internal error has occurred. Try your call again.</p>", "exception": true, "fault": true}, "InvalidParameterValueException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The value supplied for the input parameter is out of range or not valid.</p>", "exception": true, "synthetic": true}, "JobFilter": {"type": "structure", "members": {"name": {"shape": "JobFilterName", "documentation": "<p>The name of the filter.</p> <p>Specify <code>ResourceType</code> to return export jobs of a specific resource type (for example, <code>Ec2Instance</code>).</p> <p>Specify <code>JobStatus</code> to return export jobs with a specific status (e.g, <code>Complete</code>).</p>"}, "values": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The value of the filter.</p> <p>The valid values for this parameter are as follows, depending on what you specify for the <code>name</code> parameter:</p> <ul> <li> <p>Specify <code>Ec2Instance</code> or <code>AutoScalingGroup</code> if you specify the <code>name</code> parameter as <code>ResourceType</code>. There is no filter for EBS volumes because volume recommendations cannot be exported at this time.</p> </li> <li> <p>Specify <code>Queued</code>, <code>InProgress</code>, <code>Complete</code>, or <code>Failed</code> if you specify the <code>name</code> parameter as <code>JobStatus</code>.</p> </li> </ul>"}}, "documentation": "<p>Describes a filter that returns a more specific list of recommendation export jobs. Use this filter with the <a>DescribeRecommendationExportJobs</a> action.</p> <p>You can use <code>EBSFilter</code> with the <a>GetEBSVolumeRecommendations</a> action, <code>LambdaFunctionRecommendationFilter</code> with the <a>GetLambdaFunctionRecommendations</a> action, and <code>Filter</code> with the <a>GetAutoScalingGroupRecommendations</a> and <a>GetEC2InstanceRecommendations</a> actions.</p>"}, "JobFilterName": {"type": "string", "enum": ["ResourceType", "JobStatus"]}, "JobFilters": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}}, "JobId": {"type": "string"}, "JobIds": {"type": "list", "member": {"shape": "JobId"}}, "JobStatus": {"type": "string", "enum": ["Queued", "InProgress", "Complete", "Failed"]}, "LambdaFunctionMemoryMetricName": {"type": "string", "enum": ["Duration"]}, "LambdaFunctionMemoryMetricStatistic": {"type": "string", "enum": ["LowerBound", "UpperBound", "Expected"]}, "LambdaFunctionMemoryProjectedMetric": {"type": "structure", "members": {"name": {"shape": "LambdaFunctionMemoryMetricName", "documentation": "<p>The name of the projected utilization metric.</p>"}, "statistic": {"shape": "LambdaFunctionMemoryMetricStatistic", "documentation": "<p>The statistic of the projected utilization metric.</p>"}, "value": {"shape": "MetricValue", "documentation": "<p>The values of the projected utilization metrics.</p>"}}, "documentation": "<p>Describes a projected utilization metric of an Lambda function recommendation option.</p>"}, "LambdaFunctionMemoryProjectedMetrics": {"type": "list", "member": {"shape": "LambdaFunctionMemoryProjectedMetric"}}, "LambdaFunctionMemoryRecommendationOption": {"type": "structure", "members": {"rank": {"shape": "Rank", "documentation": "<p>The rank of the function recommendation option.</p> <p>The top recommendation option is ranked as <code>1</code>.</p>"}, "memorySize": {"shape": "MemorySize", "documentation": "<p>The memory size, in MB, of the function recommendation option.</p>"}, "projectedUtilizationMetrics": {"shape": "LambdaFunctionMemoryProjectedMetrics", "documentation": "<p>An array of objects that describe the projected utilization metrics of the function recommendation option.</p>"}, "savingsOpportunity": {"shape": "SavingsOpportunity", "documentation": "<p>An object that describes the savings opportunity for the Lambda function recommendation option. Savings opportunity includes the estimated monthly savings amount and percentage.</p>"}}, "documentation": "<p>Describes a recommendation option for an Lambda function.</p>"}, "LambdaFunctionMemoryRecommendationOptions": {"type": "list", "member": {"shape": "LambdaFunctionMemoryRecommendationOption"}}, "LambdaFunctionMetricName": {"type": "string", "enum": ["Duration", "Memory"]}, "LambdaFunctionMetricStatistic": {"type": "string", "enum": ["Maximum", "Average"]}, "LambdaFunctionRecommendation": {"type": "structure", "members": {"functionArn": {"shape": "FunctionArn", "documentation": "<p>The Amazon Resource Name (ARN) of the current function.</p>"}, "functionVersion": {"shape": "FunctionVersion", "documentation": "<p>The version number of the current function.</p>"}, "accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the function.</p>"}, "currentMemorySize": {"shape": "MemorySize", "documentation": "<p>The amount of memory, in MB, that's allocated to the current function.</p>"}, "numberOfInvocations": {"shape": "NumberOfInvocations", "documentation": "<p>The number of times your function code was applied during the look-back period.</p>"}, "utilizationMetrics": {"shape": "LambdaFunctionUtilizationMetrics", "documentation": "<p>An array of objects that describe the utilization metrics of the function.</p>"}, "lookbackPeriodInDays": {"shape": "LookBackPeriodInDays", "documentation": "<p>The number of days for which utilization metrics were analyzed for the function.</p>"}, "lastRefreshTimestamp": {"shape": "LastRefreshTimestamp", "documentation": "<p>The timestamp of when the function recommendation was last generated.</p>"}, "finding": {"shape": "LambdaFunctionRecommendationFinding", "documentation": "<p>The finding classification of the function.</p> <p>Findings for functions include:</p> <ul> <li> <p> <b> <code>Optimized</code> </b> — The function is correctly provisioned to run your workload based on its current configuration and its utilization history. This finding classification does not include finding reason codes.</p> </li> <li> <p> <b> <code>NotOptimized</code> </b> — The function is performing at a higher level (over-provisioned) or at a lower level (under-provisioned) than required for your workload because its current configuration is not optimal. Over-provisioned resources might lead to unnecessary infrastructure cost, and under-provisioned resources might lead to poor application performance. This finding classification can include the <code>MemoryUnderprovisioned</code> and <code>MemoryUnderprovisioned</code> finding reason codes.</p> </li> <li> <p> <b> <code>Unavailable</code> </b> — Compute Optimizer was unable to generate a recommendation for the function. This could be because the function has not accumulated sufficient metric data, or the function does not qualify for a recommendation. This finding classification can include the <code>InsufficientData</code> and <code>Inconclusive</code> finding reason codes.</p> <note> <p>Functions with a finding of unavailable are not returned unless you specify the <code>filter</code> parameter with a value of <code>Unavailable</code> in your <code>GetLambdaFunctionRecommendations</code> request.</p> </note> </li> </ul>"}, "findingReasonCodes": {"shape": "LambdaFunctionRecommendationFindingReasonCodes", "documentation": "<p>The reason for the finding classification of the function.</p> <note> <p>Functions that have a finding classification of <code>Optimized</code> don't have a finding reason code.</p> </note> <p>Finding reason codes for functions include:</p> <ul> <li> <p> <b> <code>MemoryOverprovisioned</code> </b> — The function is over-provisioned when its memory configuration can be sized down while still meeting the performance requirements of your workload. An over-provisioned function might lead to unnecessary infrastructure cost. This finding reason code is part of the <code>NotOptimized</code> finding classification.</p> </li> <li> <p> <b> <code>MemoryUnderprovisioned</code> </b> — The function is under-provisioned when its memory configuration doesn't meet the performance requirements of the workload. An under-provisioned function might lead to poor application performance. This finding reason code is part of the <code>NotOptimized</code> finding classification.</p> </li> <li> <p> <b> <code>InsufficientData</code> </b> — The function does not have sufficient metric data for Compute Optimizer to generate a recommendation. For more information, see the <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/requirements.html\">Supported resources and requirements</a> in the <i>Compute Optimizer User Guide</i>. This finding reason code is part of the <code>Unavailable</code> finding classification.</p> </li> <li> <p> <b> <code>Inconclusive</code> </b> — The function does not qualify for a recommendation because Compute Optimizer cannot generate a recommendation with a high degree of confidence. This finding reason code is part of the <code>Unavailable</code> finding classification.</p> </li> </ul>"}, "memorySizeRecommendationOptions": {"shape": "LambdaFunctionMemoryRecommendationOptions", "documentation": "<p>An array of objects that describe the memory configuration recommendation options for the function.</p>"}, "currentPerformanceRisk": {"shape": "CurrentPerformanceRisk", "documentation": "<p>The risk of the current Lambda function not meeting the performance needs of its workloads. The higher the risk, the more likely the current Lambda function requires more memory.</p>"}}, "documentation": "<p>Describes an Lambda function recommendation.</p>"}, "LambdaFunctionRecommendationFilter": {"type": "structure", "members": {"name": {"shape": "LambdaFunctionRecommendationFilterName", "documentation": "<p>The name of the filter.</p> <p>Specify <code>Finding</code> to return recommendations with a specific finding classification (for example, <code>NotOptimized</code>).</p> <p>Specify <code>FindingReasonCode</code> to return recommendations with a specific finding reason code (for example, <code>MemoryUnderprovisioned</code>).</p>"}, "values": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The value of the filter.</p> <p>The valid values for this parameter are as follows, depending on what you specify for the <code>name</code> parameter:</p> <ul> <li> <p>Specify <code>Optimized</code>, <code>NotOptimized</code>, or <code>Unavailable</code> if you specify the <code>name</code> parameter as <code>Finding</code>.</p> </li> <li> <p>Specify <code>MemoryOverprovisioned</code>, <code>MemoryUnderprovisioned</code>, <code>InsufficientData</code>, or <code>Inconclusive</code> if you specify the <code>name</code> parameter as <code>FindingReasonCode</code>.</p> </li> </ul>"}}, "documentation": "<p>Describes a filter that returns a more specific list of Lambda function recommendations. Use this filter with the <a>GetLambdaFunctionRecommendations</a> action.</p> <p>You can use <code>EBSFilter</code> with the <a>GetEBSVolumeRecommendations</a> action, <code>JobFilter</code> with the <a>DescribeRecommendationExportJobs</a> action, and <code>Filter</code> with the <a>GetAutoScalingGroupRecommendations</a> and <a>GetEC2InstanceRecommendations</a> actions.</p>"}, "LambdaFunctionRecommendationFilterName": {"type": "string", "enum": ["Finding", "FindingReasonCode"]}, "LambdaFunctionRecommendationFilters": {"type": "list", "member": {"shape": "LambdaFunctionRecommendationFilter"}}, "LambdaFunctionRecommendationFinding": {"type": "string", "enum": ["Optimized", "NotOptimized", "Unavailable"]}, "LambdaFunctionRecommendationFindingReasonCode": {"type": "string", "enum": ["MemoryOverprovisioned", "MemoryUnderprovisioned", "InsufficientData", "Inconclusive"]}, "LambdaFunctionRecommendationFindingReasonCodes": {"type": "list", "member": {"shape": "LambdaFunctionRecommendationFindingReasonCode"}}, "LambdaFunctionRecommendations": {"type": "list", "member": {"shape": "LambdaFunctionRecommendation"}}, "LambdaFunctionUtilizationMetric": {"type": "structure", "members": {"name": {"shape": "LambdaFunctionMetricName", "documentation": "<p>The name of the utilization metric.</p> <p>The following utilization metrics are available:</p> <ul> <li> <p> <code>Duration</code> - The amount of time that your function code spends processing an event.</p> </li> <li> <p> <code>Memory</code> - The amount of memory used per invocation.</p> </li> </ul>"}, "statistic": {"shape": "LambdaFunctionMetricStatistic", "documentation": "<p>The statistic of the utilization metric.</p> <p>The Compute Optimizer API, Command Line Interface (CLI), and SDKs return utilization metrics using only the <code>Maximum</code> statistic, which is the highest value observed during the specified period.</p> <p>The Compute Optimizer console displays graphs for some utilization metrics using the <code>Average</code> statistic, which is the value of <code>Sum</code> / <code>SampleCount</code> during the specified period. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/viewing-recommendations.html\">Viewing resource recommendations</a> in the <i>Compute Optimizer User Guide</i>. You can also get averaged utilization metric data for your resources using Amazon CloudWatch. For more information, see the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/WhatIsCloudWatch.html\">Amazon CloudWatch User Guide</a>.</p>"}, "value": {"shape": "MetricValue", "documentation": "<p>The value of the utilization metric.</p>"}}, "documentation": "<p>Describes a utilization metric of an Lambda function.</p>"}, "LambdaFunctionUtilizationMetrics": {"type": "list", "member": {"shape": "LambdaFunctionUtilizationMetric"}}, "LastRefreshTimestamp": {"type": "timestamp"}, "LastUpdatedTimestamp": {"type": "timestamp"}, "LimitExceededException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request exceeds a limit of the service.</p>", "exception": true, "synthetic": true}, "LookBackPeriodInDays": {"type": "double"}, "Low": {"type": "long"}, "LowerBoundValue": {"type": "double"}, "MaxResults": {"type": "integer", "box": true, "max": 1000, "min": 0}, "MaxSize": {"type": "integer"}, "Medium": {"type": "long"}, "MemberAccountsEnrolled": {"type": "boolean"}, "MemorySize": {"type": "integer"}, "MemorySizeConfiguration": {"type": "structure", "members": {"memory": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p> The amount of memory in the container. </p>"}, "memoryReservation": {"shape": "NullableMemoryReservation", "documentation": "<p> The limit of memory reserve for the container. </p>"}}, "documentation": "<p> The memory size configurations of a container. </p>"}, "Message": {"type": "string"}, "MetadataKey": {"type": "string"}, "MetricName": {"type": "string", "enum": ["Cpu", "Memory", "EBS_READ_OPS_PER_SECOND", "EBS_WRITE_OPS_PER_SECOND", "EBS_READ_BYTES_PER_SECOND", "EBS_WRITE_BYTES_PER_SECOND", "DISK_READ_OPS_PER_SECOND", "DISK_WRITE_OPS_PER_SECOND", "DISK_READ_BYTES_PER_SECOND", "DISK_WRITE_BYTES_PER_SECOND", "NETWORK_IN_BYTES_PER_SECOND", "NETWORK_OUT_BYTES_PER_SECOND", "NETWORK_PACKETS_IN_PER_SECOND", "NETWORK_PACKETS_OUT_PER_SECOND"]}, "MetricStatistic": {"type": "string", "enum": ["Maximum", "Average"]}, "MetricValue": {"type": "double"}, "MetricValues": {"type": "list", "member": {"shape": "MetricValue"}}, "MigrationEffort": {"type": "string", "enum": ["VeryLow", "Low", "Medium", "High"]}, "MinSize": {"type": "integer"}, "MissingAuthenticationToken": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request must contain either a valid (registered) Amazon Web Services access key ID or X.509 certificate.</p>", "exception": true, "synthetic": true}, "NextToken": {"type": "string"}, "NullableCpu": {"type": "integer"}, "NullableMemory": {"type": "integer"}, "NullableMemoryReservation": {"type": "integer"}, "NumberOfInvocations": {"type": "long"}, "NumberOfMemberAccountsOptedIn": {"type": "integer"}, "OptInRequiredException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The account is not opted in to Compute Optimizer.</p>", "exception": true, "synthetic": true}, "PerformanceRisk": {"type": "double", "max": 4, "min": 0}, "Period": {"type": "integer"}, "PlatformDifference": {"type": "string", "enum": ["Hypervisor", "NetworkInterface", "StorageInterface", "InstanceStoreAvailability", "VirtualizationType", "Architecture"]}, "PlatformDifferences": {"type": "list", "member": {"shape": "PlatformDifference"}}, "ProjectedMetric": {"type": "structure", "members": {"name": {"shape": "MetricName", "documentation": "<p>The name of the projected utilization metric.</p> <p>The following projected utilization metrics are returned:</p> <ul> <li> <p> <code>Cpu</code> - The projected percentage of allocated EC2 compute units that would be in use on the recommendation option had you used that resource during the analyzed period. This metric identifies the processing power required to run an application on the recommendation option.</p> <p>Depending on the instance type, tools in your operating system can show a lower percentage than CloudWatch when the instance is not allocated a full processor core.</p> <p>Units: Percent</p> </li> <li> <p> <code>Memory</code> - The percentage of memory that would be in use on the recommendation option had you used that resource during the analyzed period. This metric identifies the amount of memory required to run an application on the recommendation option.</p> <p>Units: Percent</p> <note> <p>The <code>Memory</code> metric is returned only for resources that have the unified CloudWatch agent installed on them. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/metrics.html#cw-agent\">Enabling Memory Utilization with the CloudWatch Agent</a>.</p> </note> </li> </ul>"}, "timestamps": {"shape": "Timestamps", "documentation": "<p>The timestamps of the projected utilization metric.</p>"}, "values": {"shape": "<PERSON>ric<PERSON><PERSON><PERSON>", "documentation": "<p>The values of the projected utilization metrics.</p>"}}, "documentation": "<p>Describes a projected utilization metric of a recommendation option, such as an Amazon EC2 instance. This represents the projected utilization of a recommendation option had you used that resource during the analyzed period.</p> <p>Compare the utilization metric data of your resource against its projected utilization metric data to determine the performance difference between your current resource and the recommended option.</p> <note> <p>The <code>Cpu</code> and <code>Memory</code> metrics are the only projected utilization metrics returned when you run the <a>GetEC2RecommendationProjectedMetrics</a> action. Additionally, the <code>Memory</code> metric is returned only for resources that have the unified CloudWatch agent installed on them. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/metrics.html#cw-agent\">Enabling Memory Utilization with the CloudWatch Agent</a>.</p> </note>"}, "ProjectedMetrics": {"type": "list", "member": {"shape": "ProjectedMetric"}}, "ProjectedUtilizationMetrics": {"type": "list", "member": {"shape": "UtilizationMetric"}}, "PutRecommendationPreferencesRequest": {"type": "structure", "required": ["resourceType"], "members": {"resourceType": {"shape": "ResourceType", "documentation": "<p>The target resource type of the recommendation preference to create.</p> <p>The <code>Ec2Instance</code> option encompasses standalone instances and instances that are part of Auto Scaling groups. The <code>AutoScalingGroup</code> option encompasses only instances that are part of an Auto Scaling group.</p> <note> <p>The valid values for this parameter are <code>Ec2Instance</code> and <code>AutoScalingGroup</code>.</p> </note>"}, "scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>An object that describes the scope of the recommendation preference to create.</p> <p>You can create recommendation preferences at the organization level (for management accounts of an organization only), account level, and resource level. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/enhanced-infrastructure-metrics.html\">Activating enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p> <note> <p>You cannot create recommendation preferences for Auto Scaling groups at the organization and account levels. You can create recommendation preferences for Auto Scaling groups only at the resource level by specifying a scope name of <code>ResourceArn</code> and a scope value of the Auto Scaling group Amazon Resource Name (ARN). This will configure the preference for all instances that are part of the specified Auto Scaling group. You also cannot create recommendation preferences at the resource level for instances that are part of an Auto Scaling group. You can create recommendation preferences at the resource level only for standalone instances.</p> </note>"}, "enhancedInfrastructureMetrics": {"shape": "EnhancedInfrastructureMetrics", "documentation": "<p>The status of the enhanced infrastructure metrics recommendation preference to create or update.</p> <p>Specify the <code>Active</code> status to activate the preference, or specify <code>Inactive</code> to deactivate the preference.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/enhanced-infrastructure-metrics.html\">Enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "inferredWorkloadTypes": {"shape": "InferredWorkloadTypesPreference", "documentation": "<p>The status of the inferred workload types recommendation preference to create or update.</p> <note> <p>The inferred workload type feature is active by default. To deactivate it, create a recommendation preference.</p> </note> <p>Specify the <code>Inactive</code> status to deactivate the feature, or specify <code>Active</code> to activate it.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/inferred-workload-types.html\">Inferred workload types</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "externalMetricsPreference": {"shape": "ExternalMetricsPreference", "documentation": "<p>The provider of the external metrics recommendation preference to create or update.</p> <p>Specify a valid provider in the <code>source</code> field to activate the preference. To delete this preference, see the <a>DeleteRecommendationPreferences</a> action.</p> <p>This preference can only be set for the <code>Ec2Instance</code> resource type.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/external-metrics-ingestion.html\">External metrics ingestion</a> in the <i>Compute Optimizer User Guide</i>.</p>"}}}, "PutRecommendationPreferencesResponse": {"type": "structure", "members": {}}, "Rank": {"type": "integer"}, "ReasonCodeSummaries": {"type": "list", "member": {"shape": "ReasonCodeSummary"}}, "ReasonCodeSummary": {"type": "structure", "members": {"name": {"shape": "FindingReasonCode", "documentation": "<p>The name of the finding reason code.</p>"}, "value": {"shape": "SummaryValue", "documentation": "<p>The value of the finding reason code summary.</p>"}}, "documentation": "<p>A summary of a finding reason code.</p>"}, "RecommendationExportJob": {"type": "structure", "members": {"jobId": {"shape": "JobId", "documentation": "<p>The identification number of the export job.</p>"}, "destination": {"shape": "ExportDestination", "documentation": "<p>An object that describes the destination of the export file.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The resource type of the exported recommendations.</p>"}, "status": {"shape": "JobStatus", "documentation": "<p>The status of the export job.</p>"}, "creationTimestamp": {"shape": "CreationTimestamp", "documentation": "<p>The timestamp of when the export job was created.</p>"}, "lastUpdatedTimestamp": {"shape": "LastUpdatedTimestamp", "documentation": "<p>The timestamp of when the export job was last updated.</p>"}, "failureReason": {"shape": "FailureReason", "documentation": "<p>The reason for an export job failure.</p>"}}, "documentation": "<p>Describes a recommendation export job.</p> <p>Use the <a>DescribeRecommendationExportJobs</a> action to view your recommendation export jobs.</p> <p>Use the <a>ExportAutoScalingGroupRecommendations</a> or <a>ExportEC2InstanceRecommendations</a> actions to request an export of your recommendations.</p>"}, "RecommendationExportJobs": {"type": "list", "member": {"shape": "RecommendationExportJob"}}, "RecommendationOptions": {"type": "list", "member": {"shape": "InstanceRecommendationOption"}}, "RecommendationPreferenceName": {"type": "string", "enum": ["EnhancedInfrastructureMetrics", "InferredWorkloadTypes", "ExternalMetricsPreference"]}, "RecommendationPreferenceNames": {"type": "list", "member": {"shape": "RecommendationPreferenceName"}}, "RecommendationPreferences": {"type": "structure", "members": {"cpuVendorArchitectures": {"shape": "CpuVendorArchitectures", "documentation": "<p>Specifies the CPU vendor and architecture for Amazon EC2 instance and Auto Scaling group recommendations.</p> <p>For example, when you specify <code>AWS_ARM64</code> with:</p> <ul> <li> <p>A <a>GetEC2InstanceRecommendations</a> or <a>GetAutoScalingGroupRecommendations</a> request, Compute Optimizer returns recommendations that consist of Graviton2 instance types only.</p> </li> <li> <p>A <a>GetEC2RecommendationProjectedMetrics</a> request, Compute Optimizer returns projected utilization metrics for Graviton2 instance type recommendations only.</p> </li> <li> <p>A <a>ExportEC2InstanceRecommendations</a> or <a>ExportAutoScalingGroupRecommendations</a> request, Compute Optimizer exports recommendations that consist of Graviton2 instance types only.</p> </li> </ul>"}}, "documentation": "<p>Describes the recommendation preferences to return in the response of a <a>GetAutoScalingGroupRecommendations</a>, <a>GetEC2InstanceRecommendations</a>, and <a>GetEC2RecommendationProjectedMetrics</a> request.</p>"}, "RecommendationPreferencesDetail": {"type": "structure", "members": {"scope": {"shape": "<PERSON><PERSON>", "documentation": "<p>An object that describes the scope of the recommendation preference.</p> <p>Recommendation preferences can be created at the organization level (for management accounts of an organization only), account level, and resource level. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/enhanced-infrastructure-metrics.html\">Activating enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The target resource type of the recommendation preference to create.</p> <p>The <code>Ec2Instance</code> option encompasses standalone instances and instances that are part of Auto Scaling groups. The <code>AutoScalingGroup</code> option encompasses only instances that are part of an Auto Scaling group.</p>"}, "enhancedInfrastructureMetrics": {"shape": "EnhancedInfrastructureMetrics", "documentation": "<p>The status of the enhanced infrastructure metrics recommendation preference.</p> <p>When the recommendations page is refreshed, a status of <code>Active</code> confirms that the preference is applied to the recommendations, and a status of <code>Inactive</code> confirms that the preference isn't yet applied to recommendations.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/enhanced-infrastructure-metrics.html\">Enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "inferredWorkloadTypes": {"shape": "InferredWorkloadTypesPreference", "documentation": "<p>The status of the inferred workload types recommendation preference.</p> <p>When the recommendations page is refreshed, a status of <code>Active</code> confirms that the preference is applied to the recommendations, and a status of <code>Inactive</code> confirms that the preference isn't yet applied to recommendations.</p>"}, "externalMetricsPreference": {"shape": "ExternalMetricsPreference", "documentation": "<p> An object that describes the external metrics recommendation preference. </p> <p> If the preference is applied in the latest recommendation refresh, an object with a valid <code>source</code> value appears in the response. If the preference isn't applied to the recommendations already, then this object doesn't appear in the response. </p>"}}, "documentation": "<p>Describes a recommendation preference.</p>"}, "RecommendationPreferencesDetails": {"type": "list", "member": {"shape": "RecommendationPreferencesDetail"}}, "RecommendationSource": {"type": "structure", "members": {"recommendationSourceArn": {"shape": "RecommendationSourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the recommendation source.</p>"}, "recommendationSourceType": {"shape": "RecommendationSourceType", "documentation": "<p>The resource type of the recommendation source.</p>"}}, "documentation": "<p>Describes the source of a recommendation, such as an Amazon EC2 instance or Auto Scaling group.</p>"}, "RecommendationSourceArn": {"type": "string"}, "RecommendationSourceType": {"type": "string", "enum": ["Ec2Instance", "AutoScalingGroup", "EbsVolume", "LambdaFunction", "EcsService"]}, "RecommendationSources": {"type": "list", "member": {"shape": "RecommendationSource"}}, "RecommendationSummaries": {"type": "list", "member": {"shape": "RecommendationSummary"}}, "RecommendationSummary": {"type": "structure", "members": {"summaries": {"shape": "Summaries", "documentation": "<p>An array of objects that describe a recommendation summary.</p>"}, "recommendationResourceType": {"shape": "RecommendationSourceType", "documentation": "<p>The resource type that the recommendation summary applies to.</p>"}, "accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the recommendation summary.</p>"}, "savingsOpportunity": {"shape": "SavingsOpportunity", "documentation": "<p>An object that describes the savings opportunity for a given resource type. Savings opportunity includes the estimated monthly savings amount and percentage.</p>"}, "currentPerformanceRiskRatings": {"shape": "CurrentPerformanceRiskRatings", "documentation": "<p>An object that describes the performance risk ratings for a given resource type.</p>"}}, "documentation": "<p>A summary of a recommendation.</p>"}, "RecommendedInstanceType": {"type": "string"}, "RecommendedOptionProjectedMetric": {"type": "structure", "members": {"recommendedInstanceType": {"shape": "RecommendedInstanceType", "documentation": "<p>The recommended instance type.</p>"}, "rank": {"shape": "Rank", "documentation": "<p>The rank of the recommendation option projected metric.</p> <p>The top recommendation option is ranked as <code>1</code>.</p> <p>The projected metric rank correlates to the recommendation option rank. For example, the projected metric ranked as <code>1</code> is related to the recommendation option that is also ranked as <code>1</code> in the same response.</p>"}, "projectedMetrics": {"shape": "ProjectedMetrics", "documentation": "<p>An array of objects that describe a projected utilization metric.</p>"}}, "documentation": "<p>Describes a projected utilization metric of a recommendation option.</p> <note> <p>The <code>Cpu</code> and <code>Memory</code> metrics are the only projected utilization metrics returned when you run the <a>GetEC2RecommendationProjectedMetrics</a> action. Additionally, the <code>Memory</code> metric is returned only for resources that have the unified CloudWatch agent installed on them. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/metrics.html#cw-agent\">Enabling Memory Utilization with the CloudWatch Agent</a>.</p> </note>"}, "RecommendedOptionProjectedMetrics": {"type": "list", "member": {"shape": "RecommendedOptionProjectedMetric"}}, "ResourceArn": {"type": "string"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>A resource that is required for the action doesn't exist.</p>", "exception": true, "synthetic": true}, "ResourceType": {"type": "string", "enum": ["Ec2Instance", "AutoScalingGroup", "EbsVolume", "LambdaFunction", "NotApplicable", "EcsService"]}, "S3Destination": {"type": "structure", "members": {"bucket": {"shape": "DestinationBucket", "documentation": "<p>The name of the Amazon S3 bucket used as the destination of an export file.</p>"}, "key": {"shape": "DestinationKey", "documentation": "<p>The Amazon S3 bucket key of an export file.</p> <p>The key uniquely identifies the object, or export file, in the S3 bucket.</p>"}, "metadataKey": {"shape": "Metada<PERSON><PERSON><PERSON>", "documentation": "<p>The Amazon S3 bucket key of a metadata file.</p> <p>The key uniquely identifies the object, or metadata file, in the S3 bucket.</p>"}}, "documentation": "<p>Describes the destination Amazon Simple Storage Service (Amazon S3) bucket name and object keys of a recommendations export file, and its associated metadata file.</p>"}, "S3DestinationConfig": {"type": "structure", "members": {"bucket": {"shape": "DestinationBucket", "documentation": "<p>The name of the Amazon S3 bucket to use as the destination for an export job.</p>"}, "keyPrefix": {"shape": "DestinationKeyPrefix", "documentation": "<p>The Amazon S3 bucket prefix for an export job.</p>"}}, "documentation": "<p>Describes the destination Amazon Simple Storage Service (Amazon S3) bucket name and key prefix for a recommendations export job.</p> <p>You must create the destination Amazon S3 bucket for your recommendations export before you create the export job. Compute Optimizer does not create the S3 bucket for you. After you create the S3 bucket, ensure that it has the required permission policy to allow Compute Optimizer to write the export file to it. If you plan to specify an object prefix when you create the export job, you must include the object prefix in the policy that you add to the S3 bucket. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/create-s3-bucket-policy-for-compute-optimizer.html\">Amazon S3 Bucket Policy for Compute Optimizer</a> in the <i>Compute Optimizer User Guide</i>.</p>"}, "SavingsOpportunity": {"type": "structure", "members": {"savingsOpportunityPercentage": {"shape": "SavingsOpportunityPercentage", "documentation": "<p>The estimated monthly savings possible as a percentage of monthly cost by adopting Compute Optimizer recommendations for a given resource.</p>"}, "estimatedMonthlySavings": {"shape": "EstimatedMonthlySavings", "documentation": "<p>An object that describes the estimated monthly savings amount possible, based on On-Demand instance pricing, by adopting Compute Optimizer recommendations for a given resource.</p>"}}, "documentation": "<p>Describes the savings opportunity for recommendations of a given resource type or for the recommendation option of an individual resource.</p> <p>Savings opportunity represents the estimated monthly savings you can achieve by implementing a given Compute Optimizer recommendation.</p> <important> <p>Savings opportunity data requires that you opt in to Cost Explorer, as well as activate <b>Receive Amazon EC2 resource recommendations</b> in the Cost Explorer preferences page. That creates a connection between Cost Explorer and Compute Optimizer. With this connection, Cost Explorer generates savings estimates considering the price of existing resources, the price of recommended resources, and historical usage data. Estimated monthly savings reflects the projected dollar savings associated with each of the recommendations generated. For more information, see <a href=\"https://docs.aws.amazon.com/cost-management/latest/userguide/ce-enable.html\">Enabling Cost Explorer</a> and <a href=\"https://docs.aws.amazon.com/cost-management/latest/userguide/ce-rightsizing.html\">Optimizing your cost with Rightsizing Recommendations</a> in the <i>Cost Management User Guide</i>.</p> </important>"}, "SavingsOpportunityPercentage": {"type": "double"}, "Scope": {"type": "structure", "members": {"name": {"shape": "ScopeName", "documentation": "<p>The name of the scope.</p> <p>The following scopes are possible:</p> <ul> <li> <p> <code>Organization</code> - Specifies that the recommendation preference applies at the organization level, for all member accounts of an organization.</p> </li> <li> <p> <code>AccountId</code> - Specifies that the recommendation preference applies at the account level, for all resources of a given resource type in an account.</p> </li> <li> <p> <code>ResourceArn</code> - Specifies that the recommendation preference applies at the individual resource level.</p> </li> </ul>"}, "value": {"shape": "ScopeValue", "documentation": "<p>The value of the scope.</p> <p>If you specified the <code>name</code> of the scope as:</p> <ul> <li> <p> <code>Organization</code> - The <code>value</code> must be <code>ALL_ACCOUNTS</code>.</p> </li> <li> <p> <code>AccountId</code> - The <code>value</code> must be a 12-digit Amazon Web Services account ID.</p> </li> <li> <p> <code>ResourceArn</code> - The <code>value</code> must be the Amazon Resource Name (ARN) of an EC2 instance or an Auto Scaling group.</p> </li> </ul> <p>Only EC2 instance and Auto Scaling group ARNs are currently supported.</p>"}}, "documentation": "<p>Describes the scope of a recommendation preference.</p> <p>Recommendation preferences can be created at the organization level (for management accounts of an organization only), account level, and resource level. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/enhanced-infrastructure-metrics.html\">Activating enhanced infrastructure metrics</a> in the <i>Compute Optimizer User Guide</i>.</p> <note> <p>You cannot create recommendation preferences for Auto Scaling groups at the organization and account levels. You can create recommendation preferences for Auto Scaling groups only at the resource level by specifying a scope name of <code>ResourceArn</code> and a scope value of the Auto Scaling group Amazon Resource Name (ARN). This will configure the preference for all instances that are part of the specified Auto Scaling group. You also cannot create recommendation preferences at the resource level for instances that are part of an Auto Scaling group. You can create recommendation preferences at the resource level only for standalone instances.</p> </note>"}, "ScopeName": {"type": "string", "enum": ["Organization", "AccountId", "ResourceArn"]}, "ScopeValue": {"type": "string"}, "ServiceArn": {"type": "string"}, "ServiceArns": {"type": "list", "member": {"shape": "ServiceArn"}}, "ServiceConfiguration": {"type": "structure", "members": {"memory": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "documentation": "<p> The amount of memory used by the tasks in the Amazon ECS service. </p>"}, "cpu": {"shape": "NullableCpu", "documentation": "<p> The number of CPU units used by the tasks in the Amazon ECS service. </p>"}, "containerConfigurations": {"shape": "ContainerConfigurations", "documentation": "<p> The container configurations within a task of an Amazon ECS service. </p>"}, "autoScalingConfiguration": {"shape": "AutoScalingConfiguration", "documentation": "<p> Describes the Auto Scaling configuration methods for an Amazon ECS service. This affects the generated recommendations. For example, if Auto Scaling is configured on a service’s CPU, then Compute Optimizer doesn’t generate CPU size recommendations. </p> <p>The Auto Scaling configuration methods include:</p> <ul> <li> <p> <code>TARGET_TRACKING_SCALING_CPU</code> — If the Amazon ECS service is configured to use target scaling on CPU, Compute Optimizer doesn't generate CPU recommendations.</p> </li> <li> <p> <code>TARGET_TRACKING_SCALING_MEMORY</code> — If the Amazon ECS service is configured to use target scaling on memory, Compute Optimizer doesn't generate memory recommendations.</p> </li> </ul> <p>For more information about step scaling and target scaling, see <a href=\"https://docs.aws.amazon.com/autoscaling/application/userguide/application-auto-scaling-step-scaling-policies.html\"> Step scaling policies for Application Auto Scaling</a> and <a href=\"https://docs.aws.amazon.com/autoscaling/application/userguide/application-auto-scaling-target-tracking.html\"> Target tracking scaling policies for Application Auto Scaling</a> in the <i>Application Auto Scaling User Guide</i>.</p>"}, "taskDefinitionArn": {"shape": "TaskDefinitionArn", "documentation": "<p> The task definition ARN used by the tasks in the Amazon ECS service. </p>"}}, "documentation": "<p> The Amazon ECS service configurations used for recommendations. </p>"}, "ServiceUnavailableException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request has failed due to a temporary failure of the server.</p>", "exception": true, "fault": true}, "Status": {"type": "string", "enum": ["Active", "Inactive", "Pending", "Failed"]}, "StatusReason": {"type": "string"}, "Summaries": {"type": "list", "member": {"shape": "Summary"}}, "Summary": {"type": "structure", "members": {"name": {"shape": "Finding", "documentation": "<p>The finding classification of the recommendation.</p>"}, "value": {"shape": "SummaryValue", "documentation": "<p>The value of the recommendation summary.</p>"}, "reasonCodeSummaries": {"shape": "ReasonCodeSummaries", "documentation": "<p>An array of objects that summarize a finding reason code.</p>"}}, "documentation": "<p>The summary of a recommendation.</p>"}, "SummaryValue": {"type": "double"}, "TaskDefinitionArn": {"type": "string"}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>The request was denied due to request throttling.</p>", "exception": true, "synthetic": true}, "Timestamp": {"type": "timestamp"}, "Timestamps": {"type": "list", "member": {"shape": "Timestamp"}}, "UpdateEnrollmentStatusRequest": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "Status", "documentation": "<p>The new enrollment status of the account.</p> <p>The following status options are available:</p> <ul> <li> <p> <code>Active</code> - Opts in your account to the Compute Optimizer service. Compute Optimizer begins analyzing the configuration and utilization metrics of your Amazon Web Services resources after you opt in. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/metrics.html\">Metrics analyzed by Compute Optimizer</a> in the <i>Compute Optimizer User Guide</i>.</p> </li> <li> <p> <code>Inactive</code> - Opts out your account from the Compute Optimizer service. Your account's recommendations and related metrics data will be deleted from Compute Optimizer after you opt out.</p> </li> </ul> <note> <p>The <code>Pending</code> and <code>Failed</code> options cannot be used to update the enrollment status of an account. They are returned in the response of a request to update the enrollment status of an account.</p> </note>"}, "includeMemberAccounts": {"shape": "IncludeMemberAccounts", "documentation": "<p>Indicates whether to enroll member accounts of the organization if the account is the management account of an organization.</p>"}}}, "UpdateEnrollmentStatusResponse": {"type": "structure", "members": {"status": {"shape": "Status", "documentation": "<p>The enrollment status of the account.</p>"}, "statusReason": {"shape": "StatusReason", "documentation": "<p>The reason for the enrollment status of the account. For example, an account might show a status of <code>Pending</code> because member accounts of an organization require more time to be enrolled in the service.</p>"}}}, "UpperBoundValue": {"type": "double"}, "UtilizationMetric": {"type": "structure", "members": {"name": {"shape": "MetricName", "documentation": "<p>The name of the utilization metric.</p> <p>The following utilization metrics are available:</p> <ul> <li> <p> <code>Cpu</code> - The percentage of allocated EC2 compute units that are currently in use on the instance. This metric identifies the processing power required to run an application on the instance.</p> <p>Depending on the instance type, tools in your operating system can show a lower percentage than CloudWatch when the instance is not allocated a full processor core.</p> <p>Units: Percent</p> </li> <li> <p> <code>Memory</code> - The percentage of memory that is currently in use on the instance. This metric identifies the amount of memory required to run an application on the instance.</p> <p>Units: Percent</p> <note> <p>The <code>Memory</code> metric is returned only for resources that have the unified CloudWatch agent installed on them. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/metrics.html#cw-agent\">Enabling Memory Utilization with the CloudWatch Agent</a>.</p> </note> </li> <li> <p> <code>EBS_READ_OPS_PER_SECOND</code> - The completed read operations from all EBS volumes attached to the instance in a specified period of time.</p> <p>Unit: Count</p> </li> <li> <p> <code>EBS_WRITE_OPS_PER_SECOND</code> - The completed write operations to all EBS volumes attached to the instance in a specified period of time.</p> <p>Unit: Count</p> </li> <li> <p> <code>EBS_READ_BYTES_PER_SECOND</code> - The bytes read from all EBS volumes attached to the instance in a specified period of time.</p> <p>Unit: Bytes</p> </li> <li> <p> <code>EBS_WRITE_BYTES_PER_SECOND</code> - The bytes written to all EBS volumes attached to the instance in a specified period of time.</p> <p>Unit: Bytes</p> </li> <li> <p> <code>DISK_READ_OPS_PER_SECOND</code> - The completed read operations from all instance store volumes available to the instance in a specified period of time.</p> <p>If there are no instance store volumes, either the value is <code>0</code> or the metric is not reported.</p> </li> <li> <p> <code>DISK_WRITE_OPS_PER_SECOND</code> - The completed write operations from all instance store volumes available to the instance in a specified period of time.</p> <p>If there are no instance store volumes, either the value is <code>0</code> or the metric is not reported.</p> </li> <li> <p> <code>DISK_READ_BYTES_PER_SECOND</code> - The bytes read from all instance store volumes available to the instance. This metric is used to determine the volume of the data the application reads from the disk of the instance. This can be used to determine the speed of the application.</p> <p>If there are no instance store volumes, either the value is <code>0</code> or the metric is not reported.</p> </li> <li> <p> <code>DISK_WRITE_BYTES_PER_SECOND</code> - The bytes written to all instance store volumes available to the instance. This metric is used to determine the volume of the data the application writes onto the disk of the instance. This can be used to determine the speed of the application.</p> <p>If there are no instance store volumes, either the value is <code>0</code> or the metric is not reported.</p> </li> <li> <p> <code>NETWORK_IN_BYTES_PER_SECOND</code> - The number of bytes received by the instance on all network interfaces. This metric identifies the volume of incoming network traffic to a single instance.</p> </li> <li> <p> <code>NETWORK_OUT_BYTES_PER_SECOND</code> - The number of bytes sent out by the instance on all network interfaces. This metric identifies the volume of outgoing network traffic from a single instance.</p> </li> <li> <p> <code>NETWORK_PACKETS_IN_PER_SECOND</code> - The number of packets received by the instance on all network interfaces. This metric identifies the volume of incoming traffic in terms of the number of packets on a single instance.</p> </li> <li> <p> <code>NETWORK_PACKETS_OUT_PER_SECOND</code> - The number of packets sent out by the instance on all network interfaces. This metric identifies the volume of outgoing traffic in terms of the number of packets on a single instance.</p> </li> </ul>"}, "statistic": {"shape": "MetricStatistic", "documentation": "<p>The statistic of the utilization metric.</p> <p>The Compute Optimizer API, Command Line Interface (CLI), and SDKs return utilization metrics using only the <code>Maximum</code> statistic, which is the highest value observed during the specified period.</p> <p>The Compute Optimizer console displays graphs for some utilization metrics using the <code>Average</code> statistic, which is the value of <code>Sum</code> / <code>SampleCount</code> during the specified period. For more information, see <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/viewing-recommendations.html\">Viewing resource recommendations</a> in the <i>Compute Optimizer User Guide</i>. You can also get averaged utilization metric data for your resources using Amazon CloudWatch. For more information, see the <a href=\"https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/WhatIsCloudWatch.html\">Amazon CloudWatch User Guide</a>.</p>"}, "value": {"shape": "MetricValue", "documentation": "<p>The value of the utilization metric.</p>"}}, "documentation": "<p>Describes a utilization metric of a resource, such as an Amazon EC2 instance.</p> <p>Compare the utilization metric data of your resource against its projected utilization metric data to determine the performance difference between your current resource and the recommended option.</p>"}, "UtilizationMetrics": {"type": "list", "member": {"shape": "UtilizationMetric"}}, "Value": {"type": "double"}, "VeryLow": {"type": "long"}, "VolumeArn": {"type": "string"}, "VolumeArns": {"type": "list", "member": {"shape": "VolumeArn"}}, "VolumeBaselineIOPS": {"type": "integer"}, "VolumeBaselineThroughput": {"type": "integer"}, "VolumeBurstIOPS": {"type": "integer"}, "VolumeBurstThroughput": {"type": "integer"}, "VolumeConfiguration": {"type": "structure", "members": {"volumeType": {"shape": "VolumeType", "documentation": "<p>The volume type.</p> <p>This can be <code>gp2</code> for General Purpose SSD, <code>io1</code> or <code>io2</code> for Provisioned IOPS SSD, <code>st1</code> for Throughput Optimized HDD, <code>sc1</code> for Cold HDD, or <code>standard</code> for Magnetic volumes.</p>"}, "volumeSize": {"shape": "VolumeSize", "documentation": "<p>The size of the volume, in GiB.</p>"}, "volumeBaselineIOPS": {"shape": "VolumeBaselineIOPS", "documentation": "<p>The baseline IOPS of the volume.</p>"}, "volumeBurstIOPS": {"shape": "VolumeBurstIOPS", "documentation": "<p>The burst IOPS of the volume.</p>"}, "volumeBaselineThroughput": {"shape": "VolumeBaselineThroughput", "documentation": "<p>The baseline throughput of the volume.</p>"}, "volumeBurstThroughput": {"shape": "VolumeBurstThroughput", "documentation": "<p>The burst throughput of the volume.</p>"}}, "documentation": "<p>Describes the configuration of an Amazon Elastic Block Store (Amazon EBS) volume.</p>"}, "VolumeRecommendation": {"type": "structure", "members": {"volumeArn": {"shape": "VolumeArn", "documentation": "<p>The Amazon Resource Name (ARN) of the current volume.</p>"}, "accountId": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID of the volume.</p>"}, "currentConfiguration": {"shape": "VolumeConfiguration", "documentation": "<p>An array of objects that describe the current configuration of the volume.</p>"}, "finding": {"shape": "EBSFinding", "documentation": "<p>The finding classification of the volume.</p> <p>Findings for volumes include:</p> <ul> <li> <p> <b> <code>NotOptimized</code> </b>—A volume is considered not optimized when Compute Optimizer identifies a recommendation that can provide better performance for your workload.</p> </li> <li> <p> <b> <code>Optimized</code> </b>—An volume is considered optimized when Compute Optimizer determines that the volume is correctly provisioned to run your workload based on the chosen volume type. For optimized resources, Compute Optimizer might recommend a new generation volume type.</p> </li> </ul>"}, "utilizationMetrics": {"shape": "EBSUtilizationMetrics", "documentation": "<p>An array of objects that describe the utilization metrics of the volume.</p>"}, "lookBackPeriodInDays": {"shape": "LookBackPeriodInDays", "documentation": "<p>The number of days for which utilization metrics were analyzed for the volume.</p>"}, "volumeRecommendationOptions": {"shape": "VolumeRecommendationOptions", "documentation": "<p>An array of objects that describe the recommendation options for the volume.</p>"}, "lastRefreshTimestamp": {"shape": "LastRefreshTimestamp", "documentation": "<p>The timestamp of when the volume recommendation was last generated.</p>"}, "currentPerformanceRisk": {"shape": "CurrentPerformanceRisk", "documentation": "<p>The risk of the current EBS volume not meeting the performance needs of its workloads. The higher the risk, the more likely the current EBS volume doesn't have sufficient capacity.</p>"}}, "documentation": "<p>Describes an Amazon Elastic Block Store (Amazon EBS) volume recommendation.</p>"}, "VolumeRecommendationOption": {"type": "structure", "members": {"configuration": {"shape": "VolumeConfiguration", "documentation": "<p>An array of objects that describe a volume configuration.</p>"}, "performanceRisk": {"shape": "PerformanceRisk", "documentation": "<p>The performance risk of the volume recommendation option.</p> <p>Performance risk is the likelihood of the recommended volume type meeting the performance requirement of your workload.</p> <p>The value ranges from <code>0</code> - <code>4</code>, with <code>0</code> meaning that the recommended resource is predicted to always provide enough hardware capability. The higher the performance risk is, the more likely you should validate whether the recommendation will meet the performance requirements of your workload before migrating your resource.</p>"}, "rank": {"shape": "Rank", "documentation": "<p>The rank of the volume recommendation option.</p> <p>The top recommendation option is ranked as <code>1</code>.</p>"}, "savingsOpportunity": {"shape": "SavingsOpportunity", "documentation": "<p>An object that describes the savings opportunity for the EBS volume recommendation option. Savings opportunity includes the estimated monthly savings amount and percentage.</p>"}}, "documentation": "<p>Describes a recommendation option for an Amazon Elastic Block Store (Amazon EBS) instance.</p>"}, "VolumeRecommendationOptions": {"type": "list", "member": {"shape": "VolumeRecommendationOption"}}, "VolumeRecommendations": {"type": "list", "member": {"shape": "VolumeRecommendation"}}, "VolumeSize": {"type": "integer"}, "VolumeType": {"type": "string"}}, "documentation": "<p>Compute Optimizer is a service that analyzes the configuration and utilization metrics of your Amazon Web Services compute resources, such as Amazon EC2 instances, Amazon EC2 Auto Scaling groups, Lambda functions, Amazon EBS volumes, and Amazon ECS services on Fargate. It reports whether your resources are optimal, and generates optimization recommendations to reduce the cost and improve the performance of your workloads. Compute Optimizer also provides recent utilization metric data, in addition to projected utilization metric data for the recommendations, which you can use to evaluate which recommendation provides the best price-performance trade-off. The analysis of your usage patterns can help you decide when to move or resize your running resources, and still meet your performance and capacity requirements. For more information about Compute Optimizer, including the required permissions to use the service, see the <a href=\"https://docs.aws.amazon.com/compute-optimizer/latest/ug/\">Compute Optimizer User Guide</a>.</p>"}