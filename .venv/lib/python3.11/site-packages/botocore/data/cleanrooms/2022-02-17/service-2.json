{"version": "2.0", "metadata": {"apiVersion": "2022-02-17", "endpointPrefix": "cleanrooms", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "AWS Clean Rooms Service", "serviceId": "CleanRooms", "signatureVersion": "v4", "signingName": "cleanrooms", "uid": "cleanrooms-2022-02-17"}, "operations": {"BatchGetSchema": {"name": "BatchGetSchema", "http": {"method": "POST", "requestUri": "/collaborations/{collaborationIdentifier}/batch-schema", "responseCode": 200}, "input": {"shape": "BatchGetSchemaInput"}, "output": {"shape": "BatchGetSchemaOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves multiple schemas by their identifiers.</p>"}, "CreateCollaboration": {"name": "CreateCollaboration", "http": {"method": "POST", "requestUri": "/collaborations", "responseCode": 200}, "input": {"shape": "CreateCollaborationInput"}, "output": {"shape": "CreateCollaborationOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a new collaboration.</p>"}, "CreateConfiguredTable": {"name": "CreateConfiguredTable", "http": {"method": "POST", "requestUri": "/configuredTables", "responseCode": 200}, "input": {"shape": "CreateConfiguredTableInput"}, "output": {"shape": "CreateConfiguredTableOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a new configured table resource.</p>", "idempotent": true}, "CreateConfiguredTableAnalysisRule": {"name": "CreateConfiguredTableAnalysisRule", "http": {"method": "POST", "requestUri": "/configuredTables/{configuredTableIdentifier}/analysisRule", "responseCode": 200}, "input": {"shape": "CreateConfiguredTableAnalysisRuleInput"}, "output": {"shape": "CreateConfiguredTableAnalysisRuleOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a new analysis rule for a configured table. Currently, only one analysis rule can be created for a given configured table.</p>", "idempotent": true}, "CreateConfiguredTableAssociation": {"name": "CreateConfiguredTableAssociation", "http": {"method": "POST", "requestUri": "/memberships/{membershipIdentifier}/configuredTableAssociations", "responseCode": 200}, "input": {"shape": "CreateConfiguredTableAssociationInput"}, "output": {"shape": "CreateConfiguredTableAssociationOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a configured table association. A configured table association links a configured table with a collaboration.</p>"}, "CreateMembership": {"name": "CreateMembership", "http": {"method": "POST", "requestUri": "/memberships", "responseCode": 200}, "input": {"shape": "CreateMembershipInput"}, "output": {"shape": "CreateMembershipOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a membership for a specific collaboration identifier and joins the collaboration.</p>"}, "DeleteCollaboration": {"name": "DeleteCollaboration", "http": {"method": "DELETE", "requestUri": "/collaborations/{collaborationIdentifier}", "responseCode": 204}, "input": {"shape": "DeleteCollaborationInput"}, "output": {"shape": "DeleteCollaborationOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a collaboration. It can only be called by the collaboration owner.</p>", "idempotent": true}, "DeleteConfiguredTable": {"name": "DeleteConfiguredTable", "http": {"method": "DELETE", "requestUri": "/configuredTables/{configuredTableIdentifier}", "responseCode": 204}, "input": {"shape": "DeleteConfiguredTableInput"}, "output": {"shape": "DeleteConfiguredTableOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a configured table.</p>", "idempotent": true}, "DeleteConfiguredTableAnalysisRule": {"name": "DeleteConfiguredTableAnalysisRule", "http": {"method": "DELETE", "requestUri": "/configuredTables/{configuredTableIdentifier}/analysisRule/{analysisRuleType}", "responseCode": 204}, "input": {"shape": "DeleteConfiguredTableAnalysisRuleInput"}, "output": {"shape": "DeleteConfiguredTableAnalysisRuleOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a configured table analysis rule.</p>", "idempotent": true}, "DeleteConfiguredTableAssociation": {"name": "DeleteConfiguredTableAssociation", "http": {"method": "DELETE", "requestUri": "/memberships/{membershipIdentifier}/configuredTableAssociations/{configuredTableAssociationIdentifier}", "responseCode": 204}, "input": {"shape": "DeleteConfiguredTableAssociationInput"}, "output": {"shape": "DeleteConfiguredTableAssociationOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a configured table association.</p>", "idempotent": true}, "DeleteMember": {"name": "DeleteMember", "http": {"method": "DELETE", "requestUri": "/collaborations/{collaborationIdentifier}/member/{accountId}", "responseCode": 204}, "input": {"shape": "DeleteMemberInput"}, "output": {"shape": "DeleteMemberOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Removes the specified member from a collaboration. The removed member is placed in the Removed status and can't interact with the collaboration. The removed member's data is inaccessible to active members of the collaboration.</p>", "idempotent": true}, "DeleteMembership": {"name": "DeleteMembership", "http": {"method": "DELETE", "requestUri": "/memberships/{membershipIdentifier}", "responseCode": 204}, "input": {"shape": "DeleteMembershipInput"}, "output": {"shape": "DeleteMembershipOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Deletes a specified membership. All resources under a membership must be deleted.</p>", "idempotent": true}, "GetCollaboration": {"name": "GetCollaboration", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}", "responseCode": 200}, "input": {"shape": "GetCollaborationInput"}, "output": {"shape": "GetCollaborationOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns metadata about a collaboration.</p>"}, "GetConfiguredTable": {"name": "GetConfiguredTable", "http": {"method": "GET", "requestUri": "/configuredTables/{configuredTableIdentifier}", "responseCode": 200}, "input": {"shape": "GetConfiguredTableInput"}, "output": {"shape": "GetConfiguredTableOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a configured table.</p>"}, "GetConfiguredTableAnalysisRule": {"name": "GetConfiguredTableAnalysisRule", "http": {"method": "GET", "requestUri": "/configuredTables/{configuredTableIdentifier}/analysisRule/{analysisRuleType}", "responseCode": 200}, "input": {"shape": "GetConfiguredTableAnalysisRuleInput"}, "output": {"shape": "GetConfiguredTableAnalysisRuleOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a configured table analysis rule.</p>"}, "GetConfiguredTableAssociation": {"name": "GetConfiguredTableAssociation", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/configuredTableAssociations/{configuredTableAssociationIdentifier}", "responseCode": 200}, "input": {"shape": "GetConfiguredTableAssociationInput"}, "output": {"shape": "GetConfiguredTableAssociationOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a configured table association.</p>"}, "GetMembership": {"name": "GetMembership", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}", "responseCode": 200}, "input": {"shape": "GetMembershipInput"}, "output": {"shape": "GetMembershipOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a specified membership for an identifier.</p>"}, "GetProtectedQuery": {"name": "GetProtectedQuery", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/protectedQueries/{protectedQueryIdentifier}", "responseCode": 200}, "input": {"shape": "GetProtectedQueryInput"}, "output": {"shape": "GetProtectedQueryOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns query processing metadata.</p>"}, "GetSchema": {"name": "GetSchema", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}/schemas/{name}", "responseCode": 200}, "input": {"shape": "GetSchemaInput"}, "output": {"shape": "GetSchemaOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves the schema for a relation within a collaboration.</p>"}, "GetSchemaAnalysisRule": {"name": "GetSchemaAnalysisRule", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}/schemas/{name}/analysisRule/{type}", "responseCode": 200}, "input": {"shape": "GetSchemaAnalysisRuleInput"}, "output": {"shape": "GetSchemaAnalysisRuleOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Retrieves a schema analysis rule.</p>"}, "ListCollaborations": {"name": "ListCollaborations", "http": {"method": "GET", "requestUri": "/collaborations", "responseCode": 200}, "input": {"shape": "ListCollaborationsInput"}, "output": {"shape": "ListCollaborationsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists collaborations the caller owns, is active in, or has been invited to.</p>"}, "ListConfiguredTableAssociations": {"name": "ListConfiguredTableAssociations", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/configuredTableAssociations", "responseCode": 200}, "input": {"shape": "ListConfiguredTableAssociationsInput"}, "output": {"shape": "ListConfiguredTableAssociationsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists configured table associations for a membership.</p>"}, "ListConfiguredTables": {"name": "ListConfiguredTables", "http": {"method": "GET", "requestUri": "/configuredTables", "responseCode": 200}, "input": {"shape": "ListConfiguredTablesInput"}, "output": {"shape": "ListConfiguredTablesOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists configured tables.</p>"}, "ListMembers": {"name": "ListMembers", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}/members", "responseCode": 200}, "input": {"shape": "ListMembersInput"}, "output": {"shape": "ListMembersOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all members within a collaboration.</p>"}, "ListMemberships": {"name": "ListMemberships", "http": {"method": "GET", "requestUri": "/memberships", "responseCode": 200}, "input": {"shape": "ListMembershipsInput"}, "output": {"shape": "ListMembershipsOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all memberships resources within the caller's account.</p>"}, "ListProtectedQueries": {"name": "ListProtectedQueries", "http": {"method": "GET", "requestUri": "/memberships/{membershipIdentifier}/protectedQueries", "responseCode": 200}, "input": {"shape": "ListProtectedQueriesInput"}, "output": {"shape": "ListProtectedQueriesOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists protected queries, sorted by the most recent query.</p>"}, "ListSchemas": {"name": "ListSchemas", "http": {"method": "GET", "requestUri": "/collaborations/{collaborationIdentifier}/schemas", "responseCode": 200}, "input": {"shape": "ListSchemasInput"}, "output": {"shape": "ListSchemasOutput"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists the schemas for relations within a collaboration.</p>"}, "StartProtectedQuery": {"name": "StartProtectedQuery", "http": {"method": "POST", "requestUri": "/memberships/{membershipIdentifier}/protectedQueries", "responseCode": 200}, "input": {"shape": "StartProtectedQueryInput"}, "output": {"shape": "StartProtectedQueryOutput"}, "errors": [{"shape": "ServiceQuotaExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Creates a protected query that is started by AWS Clean Rooms.</p>"}, "UpdateCollaboration": {"name": "UpdateCollaboration", "http": {"method": "PATCH", "requestUri": "/collaborations/{collaborationIdentifier}", "responseCode": 200}, "input": {"shape": "UpdateCollaborationInput"}, "output": {"shape": "UpdateCollaborationOutput"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates collaboration metadata and can only be called by the collaboration owner.</p>"}, "UpdateConfiguredTable": {"name": "UpdateConfiguredTable", "http": {"method": "PATCH", "requestUri": "/configuredTables/{configuredTableIdentifier}", "responseCode": 200}, "input": {"shape": "UpdateConfiguredTableInput"}, "output": {"shape": "UpdateConfiguredTableOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates a configured table.</p>"}, "UpdateConfiguredTableAnalysisRule": {"name": "UpdateConfiguredTableAnalysisRule", "http": {"method": "PATCH", "requestUri": "/configuredTables/{configuredTableIdentifier}/analysisRule/{analysisRuleType}", "responseCode": 200}, "input": {"shape": "UpdateConfiguredTableAnalysisRuleInput"}, "output": {"shape": "UpdateConfiguredTableAnalysisRuleOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates a configured table analysis rule.</p>"}, "UpdateConfiguredTableAssociation": {"name": "UpdateConfiguredTableAssociation", "http": {"method": "PATCH", "requestUri": "/memberships/{membershipIdentifier}/configuredTableAssociations/{configuredTableAssociationIdentifier}", "responseCode": 200}, "input": {"shape": "UpdateConfiguredTableAssociationInput"}, "output": {"shape": "UpdateConfiguredTableAssociationOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates a configured table association.</p>"}, "UpdateMembership": {"name": "UpdateMembership", "http": {"method": "PATCH", "requestUri": "/memberships/{membershipIdentifier}", "responseCode": 200}, "input": {"shape": "UpdateMembershipInput"}, "output": {"shape": "UpdateMembershipOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates a membership.</p>"}, "UpdateProtectedQuery": {"name": "UpdateProtectedQuery", "http": {"method": "PATCH", "requestUri": "/memberships/{membershipIdentifier}/protectedQueries/{protectedQueryIdentifier}", "responseCode": 200}, "input": {"shape": "UpdateProtectedQueryInput"}, "output": {"shape": "UpdateProtectedQueryOutput"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the processing of a currently running query.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "String"}, "reason": {"shape": "AccessDeniedExceptionReason", "documentation": "<p>A reason code for the exception.</p>"}}, "documentation": "<p>Caller does not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AccessDeniedExceptionReason": {"type": "string", "enum": ["INSUFFICIENT_PERMISSIONS"]}, "AccountId": {"type": "string", "max": 12, "min": 12, "pattern": "\\d+"}, "AggregateColumn": {"type": "structure", "required": ["columnNames", "function"], "members": {"columnNames": {"shape": "AggregateColumnColumnNamesList", "documentation": "<p>Column names in configured table of aggregate columns.</p>"}, "function": {"shape": "AggregateFunctionName", "documentation": "<p>Aggregation function that can be applied to aggregate column in query.</p>"}}, "documentation": "<p>Column in configured table that can be used in aggregate function in query.</p>"}, "AggregateColumnColumnNamesList": {"type": "list", "member": {"shape": "AnalysisRuleColumnName"}, "min": 1}, "AggregateFunctionName": {"type": "string", "enum": ["SUM", "SUM_DISTINCT", "COUNT", "COUNT_DISTINCT", "AVG"]}, "AggregationConstraint": {"type": "structure", "required": ["columnName", "minimum", "type"], "members": {"columnName": {"shape": "AnalysisRuleColumnName", "documentation": "<p>Column in aggregation constraint for which there must be a minimum number of distinct values in an output row for it to be in the query output.</p>"}, "minimum": {"shape": "AggregationConstraintMinimumInteger", "documentation": "<p>The minimum number of distinct values that an output row must be an aggregation of. Minimum threshold of distinct values for a specified column that must exist in an output row for it to be in the query output.</p>"}, "type": {"shape": "AggregationType", "documentation": "<p>The type of aggregation the constraint allows. The only valid value is currently `COUNT_DISTINCT`.</p>"}}, "documentation": "<p>Constraint on query output removing output rows that do not meet a minimum number of distinct values of a specified column.</p>"}, "AggregationConstraintMinimumInteger": {"type": "integer", "box": true, "min": 2}, "AggregationConstraints": {"type": "list", "member": {"shape": "AggregationConstraint"}, "min": 1}, "AggregationType": {"type": "string", "enum": ["COUNT_DISTINCT"]}, "AllowedColumnList": {"type": "list", "member": {"shape": "ColumnName"}, "max": 250, "min": 1}, "AnalysisMethod": {"type": "string", "enum": ["DIRECT_QUERY"]}, "AnalysisRule": {"type": "structure", "required": ["collaborationId", "type", "name", "createTime", "updateTime", "policy"], "members": {"collaborationId": {"shape": "CollaborationIdentifier", "documentation": "<p>The unique ID for the associated collaboration.</p>"}, "type": {"shape": "AnalysisRuleType", "documentation": "<p>The type of analysis rule. Valid values are `AGGREGATION` and `LIST`.</p>"}, "name": {"shape": "TableAlias", "documentation": "<p>The name for the analysis rule.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time the analysis rule was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the analysis rule was last updated.</p>"}, "policy": {"shape": "AnalysisRulePolicy", "documentation": "<p>A policy that describes the associated data usage limitations.</p>"}}, "documentation": "<p>A specification about how data from the configured table can be used in a query.</p>"}, "AnalysisRuleAggregation": {"type": "structure", "required": ["aggregateColumns", "joinColumns", "dimensionColumns", "scalarFunctions", "outputConstraints"], "members": {"aggregateColumns": {"shape": "AnalysisRuleAggregationAggregateColumnsList", "documentation": "<p>The columns that query runners are allowed to use in aggregation queries.</p>"}, "joinColumns": {"shape": "AnalysisRuleColumnList", "documentation": "<p>Columns in configured table that can be used in join statements and/or as aggregate columns. They can never be outputted directly.</p>"}, "joinRequired": {"shape": "JoinRequiredOption", "documentation": "<p>Control that requires member who runs query to do a join with their configured table and/or other configured table in query</p>"}, "dimensionColumns": {"shape": "AnalysisRuleColumnList", "documentation": "<p>The columns that query runners are allowed to select, group by, or filter by.</p>"}, "scalarFunctions": {"shape": "ScalarFunctionsList", "documentation": "<p>Set of scalar functions that are allowed to be used on dimension columns and the output of aggregation of metrics.</p>"}, "outputConstraints": {"shape": "AggregationConstraints", "documentation": "<p>Columns that must meet a specific threshold value (after an aggregation function is applied to it) for each output row to be returned.</p>"}}, "documentation": "<p>Enables query structure and specified queries that product aggregate statistics.</p>"}, "AnalysisRuleAggregationAggregateColumnsList": {"type": "list", "member": {"shape": "AggregateColumn"}, "min": 1}, "AnalysisRuleColumnList": {"type": "list", "member": {"shape": "AnalysisRuleColumnName"}}, "AnalysisRuleColumnName": {"type": "string", "max": 127, "min": 1, "pattern": "[a-z0-9_](([a-z0-9_ ]+-)*([a-z0-9_ ]+))?"}, "AnalysisRuleList": {"type": "structure", "required": ["joinColumns", "listColumns"], "members": {"joinColumns": {"shape": "AnalysisRuleListJoinColumnsList", "documentation": "<p>Columns that can be used to join a configured table with the table of the member who can query and another members' configured tables.</p>"}, "listColumns": {"shape": "AnalysisRuleColumnList", "documentation": "<p>Columns that can be listed in the output.</p>"}}, "documentation": "<p>A type of analysis rule that enables row-level analysis.</p>"}, "AnalysisRuleListJoinColumnsList": {"type": "list", "member": {"shape": "AnalysisRuleColumnName"}, "min": 1}, "AnalysisRulePolicy": {"type": "structure", "members": {"v1": {"shape": "AnalysisRulePolicyV1", "documentation": "<p>Controls on the query specifications that can be run on configured table..</p>"}}, "documentation": "<p>Controls on the query specifications that can be run on configured table..</p>", "union": true}, "AnalysisRulePolicyV1": {"type": "structure", "members": {"list": {"shape": "AnalysisRuleList", "documentation": "<p>Analysis rule type that enables only list queries on a configured table.</p>"}, "aggregation": {"shape": "AnalysisRuleAggregation", "documentation": "<p>Analysis rule type that enables only aggregation queries on a configured table.</p>"}}, "documentation": "<p>Controls on the query specifications that can be run on configured table..</p>", "union": true}, "AnalysisRuleType": {"type": "string", "enum": ["AGGREGATION", "LIST"]}, "AnalysisRuleTypeList": {"type": "list", "member": {"shape": "AnalysisRuleType"}}, "BatchGetSchemaError": {"type": "structure", "required": ["name", "code", "message"], "members": {"name": {"shape": "TableAlias", "documentation": "<p>An error name for the error.</p>"}, "code": {"shape": "String", "documentation": "<p>An error code for the error. </p>"}, "message": {"shape": "String", "documentation": "<p>An error message for the error.</p>"}}, "documentation": "<p>An error describing why a schema could not be fetched.</p>"}, "BatchGetSchemaErrorList": {"type": "list", "member": {"shape": "BatchGetSchemaError"}, "max": 25, "min": 0}, "BatchGetSchemaInput": {"type": "structure", "required": ["collaborationIdentifier", "names"], "members": {"collaborationIdentifier": {"shape": "CollaborationIdentifier", "documentation": "<p>A unique identifier for the collaboration that the schemas belong to. Currently accepts collaboration ID.</p>", "location": "uri", "locationName": "collaborationIdentifier"}, "names": {"shape": "TableAliasList", "documentation": "<p>The names for the schema objects to retrieve.&gt;</p>"}}}, "BatchGetSchemaOutput": {"type": "structure", "required": ["schemas", "errors"], "members": {"schemas": {"shape": "SchemaList", "documentation": "<p>The retrieved list of schemas.</p>"}, "errors": {"shape": "BatchGetSchemaErrorList", "documentation": "<p>Error reasons for schemas that could not be retrieved. One error is returned for every schema that could not be retrieved.</p>"}}}, "Boolean": {"type": "boolean", "box": true}, "Collaboration": {"type": "structure", "required": ["id", "arn", "name", "creatorAccountId", "creatorDisplayName", "createTime", "updateTime", "memberStatus", "queryLogStatus"], "members": {"id": {"shape": "UUID", "documentation": "<p>The unique ID for the collaboration.</p>"}, "arn": {"shape": "CollaborationArn", "documentation": "<p>The unique ARN for the collaboration.</p>"}, "name": {"shape": "CollaborationName", "documentation": "<p>A human-readable identifier provided by the collaboration owner. Display names are not unique.</p>"}, "description": {"shape": "CollaborationDescription", "documentation": "<p>A description of the collaboration provided by the collaboration owner.</p>"}, "creatorAccountId": {"shape": "AccountId", "documentation": "<p>The identifier used to reference members of the collaboration. Currently only supports AWS account ID.</p>"}, "creatorDisplayName": {"shape": "DisplayName", "documentation": "<p>A display name of the collaboration creator.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time when the collaboration was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the collaboration metadata was last updated.</p>"}, "memberStatus": {"shape": "MemberStatus", "documentation": "<p>The status of a member in a collaboration.</p>"}, "membershipId": {"shape": "UUID", "documentation": "<p>The unique ID for your membership within the collaboration.</p>"}, "membershipArn": {"shape": "MembershipArn", "documentation": "<p>The unique ARN for your membership within the collaboration.</p>"}, "dataEncryptionMetadata": {"shape": "DataEncryptionMetadata", "documentation": "<p>The settings for client-side encryption for cryptographic computing.</p>"}, "queryLogStatus": {"shape": "CollaborationQueryLogStatus", "documentation": "<p>An indicator as to whether query logging has been enabled or disabled for the collaboration.</p>"}}, "documentation": "<p>The multi-party data share environment. The collaboration contains metadata about its purpose and participants.</p>"}, "CollaborationArn": {"type": "string", "max": 100, "min": 0, "pattern": "arn:aws:[\\w]+:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:collaboration/[\\d\\w-]+"}, "CollaborationDescription": {"type": "string", "max": 255, "min": 1, "pattern": "(?!\\s*$)[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t\\r\\n]*"}, "CollaborationIdentifier": {"type": "string", "max": 36, "min": 36, "pattern": ".*[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}.*"}, "CollaborationName": {"type": "string", "max": 100, "min": 1, "pattern": "(?!\\s*$)[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t]*"}, "CollaborationQueryLogStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "CollaborationSummary": {"type": "structure", "required": ["id", "arn", "name", "creatorAccountId", "creatorDisplayName", "createTime", "updateTime", "memberStatus"], "members": {"id": {"shape": "UUID", "documentation": "<p>The identifier for the collaboration.</p>"}, "arn": {"shape": "CollaborationArn", "documentation": "<p>The ARN of the collaboration.</p>"}, "name": {"shape": "CollaborationName", "documentation": "<p>A human-readable identifier provided by the collaboration owner. Display names are not unique.</p>"}, "creatorAccountId": {"shape": "AccountId", "documentation": "<p>The identifier used to reference members of the collaboration. Currently only supports AWS Account ID.</p>"}, "creatorDisplayName": {"shape": "DisplayName", "documentation": "<p>The display name of the collaboration creator.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time when the collaboration was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the collaboration metadata was last updated.</p>"}, "memberStatus": {"shape": "MemberStatus", "documentation": "<p>The status of a member in a collaboration.</p>"}, "membershipId": {"shape": "UUID", "documentation": "<p>The identifier of a member in a collaboration.</p>"}, "membershipArn": {"shape": "MembershipArn", "documentation": "<p>The ARN of a member in a collaboration.</p>"}}, "documentation": "<p>The metadata of the collaboration.</p>"}, "CollaborationSummaryList": {"type": "list", "member": {"shape": "CollaborationSummary"}}, "Column": {"type": "structure", "required": ["name", "type"], "members": {"name": {"shape": "ColumnName", "documentation": "<p>The name of the column.</p>"}, "type": {"shape": "ColumnTypeString", "documentation": "<p>The type of the column.</p>"}}, "documentation": "<p>A column within a schema relation, derived from the underlying AWS Glue table.</p>"}, "ColumnList": {"type": "list", "member": {"shape": "Column"}}, "ColumnName": {"type": "string", "max": 128, "min": 0, "pattern": "[a-z0-9_](([a-z0-9_ ]+-)*([a-z0-9_ ]+))?"}, "ColumnTypeString": {"type": "string", "max": 255, "min": 0, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t]*"}, "ConfiguredTable": {"type": "structure", "required": ["id", "arn", "name", "tableReference", "createTime", "updateTime", "analysisRuleTypes", "analysisMethod", "allowedColumns"], "members": {"id": {"shape": "UUID", "documentation": "<p>The unique ID for the configured table.</p>"}, "arn": {"shape": "ConfiguredTableArn", "documentation": "<p>The unique ARN for the configured table.</p>"}, "name": {"shape": "DisplayName", "documentation": "<p>A name for the configured table.</p>"}, "description": {"shape": "TableDescription", "documentation": "<p>A description for the configured table.</p>"}, "tableReference": {"shape": "TableReference", "documentation": "<p>The AWS Glue table that this configured table represents.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time the configured table was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the configured table was last updated</p>"}, "analysisRuleTypes": {"shape": "ConfiguredTableAnalysisRuleTypeList", "documentation": "<p>The types of analysis rules associated with this configured table. Valid values are `AGGREGATION` and `LIST`. Currently, only one analysis rule may be associated with a configured table.</p>"}, "analysisMethod": {"shape": "AnalysisMethod", "documentation": "<p>The analysis method for the configured table. The only valid value is currently `DIRECT_QUERY`.</p>"}, "allowedColumns": {"shape": "AllowedColumnList", "documentation": "<p>The columns within the underlying AWS Glue table that can be utilized within collaborations.</p>"}}, "documentation": "<p>A table that has been configured for use in a collaboration.</p>"}, "ConfiguredTableAnalysisRule": {"type": "structure", "required": ["configuredTableId", "configuredTableArn", "policy", "type", "createTime", "updateTime"], "members": {"configuredTableId": {"shape": "UUID", "documentation": "<p>The unique ID for the configured table.</p>"}, "configuredTableArn": {"shape": "ConfiguredTableArn", "documentation": "<p>The unique ARN for the configured table.</p>"}, "policy": {"shape": "ConfiguredTableAnalysisRulePolicy", "documentation": "<p>The policy that controls SQL query rules.</p>"}, "type": {"shape": "ConfiguredTableAnalysisRuleType", "documentation": "<p>The type of configured table analysis rule. Valid values are `AGGREGATION` and `LIST`.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time the configured table analysis rule was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the configured table analysis rule was last updated.</p>"}}, "documentation": "<p>A configured table analysis rule, which limits how data for this table can be used.</p>"}, "ConfiguredTableAnalysisRulePolicy": {"type": "structure", "members": {"v1": {"shape": "ConfiguredTableAnalysisRulePolicyV1", "documentation": "<p>Controls on the query specifications that can be run on a configured table.</p>"}}, "documentation": "<p>Controls on the query specifications that can be run on a configured table.</p>", "union": true}, "ConfiguredTableAnalysisRulePolicyV1": {"type": "structure", "members": {"list": {"shape": "AnalysisRuleList", "documentation": "<p>Analysis rule type that enables only list queries on a configured table.</p>"}, "aggregation": {"shape": "AnalysisRuleAggregation", "documentation": "<p>Analysis rule type that enables only aggregation queries on a configured table.</p>"}}, "documentation": "<p>Controls on the query specifications that can be run on a configured table.</p>", "union": true}, "ConfiguredTableAnalysisRuleType": {"type": "string", "enum": ["AGGREGATION", "LIST"]}, "ConfiguredTableAnalysisRuleTypeList": {"type": "list", "member": {"shape": "ConfiguredTableAnalysisRuleType"}}, "ConfiguredTableArn": {"type": "string", "max": 100, "min": 0, "pattern": "arn:aws:[\\w]+:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:configuredTable/[\\d\\w-]+"}, "ConfiguredTableAssociation": {"type": "structure", "required": ["arn", "id", "configuredTableId", "configuredTableArn", "membershipId", "membershipArn", "roleArn", "name", "createTime", "updateTime"], "members": {"arn": {"shape": "ConfiguredTableAssociationArn", "documentation": "<p>The unique ARN for the configured table association.</p>"}, "id": {"shape": "UUID", "documentation": "<p>The unique ID for the configured table association.</p>"}, "configuredTableId": {"shape": "UUID", "documentation": "<p>The unique ID for the configured table that the association refers to.</p>"}, "configuredTableArn": {"shape": "ConfiguredTableArn", "documentation": "<p>The unique ARN for the configured table that the association refers to.</p>"}, "membershipId": {"shape": "UUID", "documentation": "<p>The unique ID for the membership this configured table association belongs to.</p>"}, "membershipArn": {"shape": "MembershipArn", "documentation": "<p>The unique ARN for the membership this configured table association belongs to.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The service will assume this role to access catalog metadata and query the table.</p>"}, "name": {"shape": "TableAlias", "documentation": "<p>The name of the configured table association, in lowercase. The table is identified by this name when running protected queries against the underlying data.</p>"}, "description": {"shape": "TableDescription", "documentation": "<p>A description of the configured table association.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time the configured table association was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the configured table association was last updated.</p>"}}, "documentation": "<p>A configured table association links a configured table to a collaboration.</p>"}, "ConfiguredTableAssociationArn": {"type": "string", "max": 100, "min": 0, "pattern": "arn:aws:[\\w]+:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:configuredTableAssociation/[\\d\\w-]+/[\\d\\w-]+"}, "ConfiguredTableAssociationIdentifier": {"type": "string", "max": 36, "min": 36, "pattern": ".*[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}.*"}, "ConfiguredTableAssociationSummary": {"type": "structure", "required": ["configuredTableId", "membershipId", "membershipArn", "name", "createTime", "updateTime", "id", "arn"], "members": {"configuredTableId": {"shape": "UUID", "documentation": "<p>The unique configured table ID that this configured table association refers to.</p>"}, "membershipId": {"shape": "MembershipIdentifier", "documentation": "<p>The unique ID for the membership that the configured table association belongs to.</p>"}, "membershipArn": {"shape": "MembershipArn", "documentation": "<p>The unique ARN for the membership that the configured table association belongs to.</p>"}, "name": {"shape": "TableAlias", "documentation": "<p>The name of the configured table association. The table is identified by this name when running Protected Queries against the underlying data.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time the configured table association was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the configured table association was last updated.</p>"}, "id": {"shape": "UUID", "documentation": "<p>The unique ID for the configured table association.</p>"}, "arn": {"shape": "ConfiguredTableAssociationArn", "documentation": "<p>The unique ARN for the configured table association.</p>"}}, "documentation": "<p>The configured table association summary for the objects listed by the request.</p>"}, "ConfiguredTableAssociationSummaryList": {"type": "list", "member": {"shape": "ConfiguredTableAssociationSummary"}}, "ConfiguredTableIdentifier": {"type": "string", "max": 36, "min": 36, "pattern": ".*[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}.*"}, "ConfiguredTableSummary": {"type": "structure", "required": ["id", "arn", "name", "createTime", "updateTime", "analysisRuleTypes", "analysisMethod"], "members": {"id": {"shape": "ConfiguredTableIdentifier", "documentation": "<p>The unique ID of the configured table.</p>"}, "arn": {"shape": "ConfiguredTableArn", "documentation": "<p>The unique ARN of the configured table.</p>"}, "name": {"shape": "DisplayName", "documentation": "<p>The name of the configured table.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time the configured table was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the configured table was last updated.</p>"}, "analysisRuleTypes": {"shape": "ConfiguredTableAnalysisRuleTypeList", "documentation": "<p>The types of analysis rules associated with this configured table.</p>"}, "analysisMethod": {"shape": "AnalysisMethod", "documentation": "<p>The analysis method for the configured tables. The only valid value is currently `DIRECT_QUERY`.</p>"}}, "documentation": "<p>The configured table summary for the objects listed by the request.</p>"}, "ConfiguredTableSummaryList": {"type": "list", "member": {"shape": "ConfiguredTableSummary", "documentation": "<p>The member of the configured table summary list.</p>"}}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The ID of the conflicting resource.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the conflicting resource.</p>"}, "reason": {"shape": "ConflictExceptionReason", "documentation": "<p>A reason code for the exception.</p>"}}, "documentation": "<p>Updating or deleting a resource can cause an inconsistent state.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "ConflictExceptionReason": {"type": "string", "enum": ["ALREADY_EXISTS", "SUBRESOURCES_EXIST", "INVALID_STATE"]}, "CreateCollaborationInput": {"type": "structure", "required": ["members", "name", "description", "creatorMemberAbilities", "creatorDisplayName", "queryLogStatus"], "members": {"members": {"shape": "MemberList", "documentation": "<p>A list of initial members, not including the creator. This list is immutable.</p>"}, "name": {"shape": "CollaborationName", "documentation": "<p>The display name for a collaboration.</p>"}, "description": {"shape": "CollaborationDescription", "documentation": "<p>A description of the collaboration provided by the collaboration owner.</p>"}, "creatorMemberAbilities": {"shape": "MemberAbilities", "documentation": "<p>The abilities granted to the collaboration creator.</p>"}, "creatorDisplayName": {"shape": "DisplayName", "documentation": "<p>The display name of the collaboration creator.</p>"}, "dataEncryptionMetadata": {"shape": "DataEncryptionMetadata", "documentation": "<p>The settings for client-side encryption with Cryptographic Computing for Clean Rooms.</p>"}, "queryLogStatus": {"shape": "CollaborationQueryLogStatus", "documentation": "<p>An indicator as to whether query logging has been enabled or disabled for the collaboration.</p>"}}}, "CreateCollaborationOutput": {"type": "structure", "required": ["collaboration"], "members": {"collaboration": {"shape": "Collaboration", "documentation": "<p>The entire created collaboration object.</p>"}}}, "CreateConfiguredTableAnalysisRuleInput": {"type": "structure", "required": ["configuredTableIdentifier", "analysisRuleType", "analysisRulePolicy"], "members": {"configuredTableIdentifier": {"shape": "ConfiguredTableIdentifier", "documentation": "<p>The identifier for the configured table to create the analysis rule for. Currently accepts the configured table ID. </p>", "location": "uri", "locationName": "configuredTableIdentifier"}, "analysisRuleType": {"shape": "ConfiguredTableAnalysisRuleType", "documentation": "<p>The type of analysis rule. Valid values are AGGREGATION and LIST.</p>"}, "analysisRulePolicy": {"shape": "ConfiguredTableAnalysisRulePolicy", "documentation": "<p>The entire created configured table analysis rule object.</p>"}}}, "CreateConfiguredTableAnalysisRuleOutput": {"type": "structure", "required": ["analysisRule"], "members": {"analysisRule": {"shape": "ConfiguredTableAnalysisRule", "documentation": "<p>The entire created analysis rule.</p>"}}}, "CreateConfiguredTableAssociationInput": {"type": "structure", "required": ["name", "membershipIdentifier", "configuredTableIdentifier", "roleArn"], "members": {"name": {"shape": "TableAlias", "documentation": "<p>The name of the configured table association. This name is used to query the underlying configured table.</p>"}, "description": {"shape": "TableDescription", "documentation": "<p>A description for the configured table association.</p>"}, "membershipIdentifier": {"shape": "MembershipIdentifier", "documentation": "<p>A unique identifier for one of your memberships for a collaboration. The configured table is associated to the collaboration that this membership belongs to. Currently accepts a membership ID.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "configuredTableIdentifier": {"shape": "ConfiguredTableIdentifier", "documentation": "<p>A unique identifier for the configured table to be associated to. Currently accepts a configured table ID.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The service will assume this role to access catalog metadata and query the table.</p>"}}}, "CreateConfiguredTableAssociationOutput": {"type": "structure", "required": ["configuredTableAssociation"], "members": {"configuredTableAssociation": {"shape": "ConfiguredTableAssociation", "documentation": "<p>The entire configured table association object.</p>"}}}, "CreateConfiguredTableInput": {"type": "structure", "required": ["name", "tableReference", "allowedColumns", "analysisMethod"], "members": {"name": {"shape": "DisplayName", "documentation": "<p>The name of the configured table.</p>"}, "description": {"shape": "TableDescription", "documentation": "<p>A description for the configured table.</p>"}, "tableReference": {"shape": "TableReference", "documentation": "<p>A reference to the AWS Glue table being configured.</p>"}, "allowedColumns": {"shape": "AllowedColumnList", "documentation": "<p>The columns of the underlying table that can be used by collaborations or analysis rules.</p>"}, "analysisMethod": {"shape": "AnalysisMethod", "documentation": "<p>The analysis method for the configured tables. The only valid value is currently `DIRECT_QUERY`.</p>"}}}, "CreateConfiguredTableOutput": {"type": "structure", "required": ["configuredTable"], "members": {"configuredTable": {"shape": "ConfiguredTable", "documentation": "<p>The created configured table.</p>"}}}, "CreateMembershipInput": {"type": "structure", "required": ["collaborationIdentifier", "queryLogStatus"], "members": {"collaborationIdentifier": {"shape": "CollaborationIdentifier", "documentation": "<p>The unique ID for the associated collaboration.</p>"}, "queryLogStatus": {"shape": "MembershipQueryLogStatus", "documentation": "<p>An indicator as to whether query logging has been enabled or disabled for the collaboration.</p>"}}}, "CreateMembershipOutput": {"type": "structure", "required": ["membership"], "members": {"membership": {"shape": "Membership", "documentation": "<p>The membership that was created.</p>"}}}, "DataEncryptionMetadata": {"type": "structure", "required": ["allowCleartext", "allowDuplicates", "allowJoinsOnColumnsWithDifferentNames", "preserveNulls"], "members": {"allowCleartext": {"shape": "Boolean", "documentation": "<p>Indicates whether encrypted tables can contain cleartext data (true) or are to cryptographically process every column (false).</p>"}, "allowDuplicates": {"shape": "Boolean", "documentation": "<p>Indicates whether Fingerprint columns can contain duplicate entries (true) or are to contain only non-repeated values (false).</p>"}, "allowJoinsOnColumnsWithDifferentNames": {"shape": "Boolean", "documentation": "<p>Indicates whether Fingerprint columns can be joined on any other Fingerprint column with a different name (true) or can only be joined on Fingerprint columns of the same name (false).</p>"}, "preserveNulls": {"shape": "Boolean", "documentation": "<p>Indicates whether NULL values are to be copied as NULL to encrypted tables (true) or cryptographically processed (false).</p>"}}, "documentation": "<p>The settings for client-side encryption for cryptographic computing.</p>"}, "DeleteCollaborationInput": {"type": "structure", "required": ["collaborationIdentifier"], "members": {"collaborationIdentifier": {"shape": "CollaborationIdentifier", "documentation": "<p>The identifier for the collaboration.</p>", "location": "uri", "locationName": "collaborationIdentifier"}}}, "DeleteCollaborationOutput": {"type": "structure", "members": {}}, "DeleteConfiguredTableAnalysisRuleInput": {"type": "structure", "required": ["configuredTableIdentifier", "analysisRuleType"], "members": {"configuredTableIdentifier": {"shape": "ConfiguredTableIdentifier", "documentation": "<p>The unique identifier for the configured table that the analysis rule applies to. Currently accepts the configured table ID.</p>", "location": "uri", "locationName": "configuredTableIdentifier"}, "analysisRuleType": {"shape": "ConfiguredTableAnalysisRuleType", "documentation": "<p>The analysis rule type to be deleted. Configured table analysis rules are uniquely identified by their configured table identifier and analysis rule type.</p>", "location": "uri", "locationName": "analysisRuleType"}}}, "DeleteConfiguredTableAnalysisRuleOutput": {"type": "structure", "members": {}, "documentation": "<p>An empty response that indicates a successful delete.</p>"}, "DeleteConfiguredTableAssociationInput": {"type": "structure", "required": ["configuredTableAssociationIdentifier", "membershipIdentifier"], "members": {"configuredTableAssociationIdentifier": {"shape": "ConfiguredTableAssociationIdentifier", "documentation": "<p>The unique ID for the configured table association to be deleted. Currently accepts the configured table ID.</p>", "location": "uri", "locationName": "configuredTableAssociationIdentifier"}, "membershipIdentifier": {"shape": "MembershipIdentifier", "documentation": "<p>A unique identifier for the membership that the configured table association belongs to. Currently accepts the membership ID.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "DeleteConfiguredTableAssociationOutput": {"type": "structure", "members": {}}, "DeleteConfiguredTableInput": {"type": "structure", "required": ["configuredTableIdentifier"], "members": {"configuredTableIdentifier": {"shape": "ConfiguredTableIdentifier", "documentation": "<p>The unique ID for the configured table to delete.</p>", "location": "uri", "locationName": "configuredTableIdentifier"}}}, "DeleteConfiguredTableOutput": {"type": "structure", "members": {}, "documentation": "<p>The empty output for a successful deletion.</p>"}, "DeleteMemberInput": {"type": "structure", "required": ["collaborationIdentifier", "accountId"], "members": {"collaborationIdentifier": {"shape": "CollaborationIdentifier", "documentation": "<p>The unique identifier for the associated collaboration.</p>", "location": "uri", "locationName": "collaborationIdentifier"}, "accountId": {"shape": "AccountId", "documentation": "<p>The account ID of the member to remove.</p>", "location": "uri", "locationName": "accountId"}}}, "DeleteMemberOutput": {"type": "structure", "members": {}}, "DeleteMembershipInput": {"type": "structure", "required": ["membershipIdentifier"], "members": {"membershipIdentifier": {"shape": "MembershipIdentifier", "documentation": "<p>The identifier for a membership resource.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "DeleteMembershipOutput": {"type": "structure", "members": {}}, "DisplayName": {"type": "string", "max": 100, "min": 1, "pattern": "(?!\\s*$)[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t]*"}, "Double": {"type": "double", "box": true}, "FilterableMemberStatus": {"type": "string", "enum": ["INVITED", "ACTIVE"]}, "GetCollaborationInput": {"type": "structure", "required": ["collaborationIdentifier"], "members": {"collaborationIdentifier": {"shape": "CollaborationIdentifier", "documentation": "<p>The identifier for the collaboration.</p>", "location": "uri", "locationName": "collaborationIdentifier"}}}, "GetCollaborationOutput": {"type": "structure", "required": ["collaboration"], "members": {"collaboration": {"shape": "Collaboration", "documentation": "<p>The entire collaboration for this identifier.</p>"}}}, "GetConfiguredTableAnalysisRuleInput": {"type": "structure", "required": ["configuredTableIdentifier", "analysisRuleType"], "members": {"configuredTableIdentifier": {"shape": "ConfiguredTableIdentifier", "documentation": "<p>The unique identifier for the configured table to retrieve. Currently accepts the configured table ID.</p>", "location": "uri", "locationName": "configuredTableIdentifier"}, "analysisRuleType": {"shape": "ConfiguredTableAnalysisRuleType", "documentation": "<p>The analysis rule to be retrieved. Configured table analysis rules are uniquely identified by their configured table identifier and analysis rule type.</p>", "location": "uri", "locationName": "analysisRuleType"}}}, "GetConfiguredTableAnalysisRuleOutput": {"type": "structure", "required": ["analysisRule"], "members": {"analysisRule": {"shape": "ConfiguredTableAnalysisRule", "documentation": "<p>The entire analysis rule output.</p>"}}}, "GetConfiguredTableAssociationInput": {"type": "structure", "required": ["configuredTableAssociationIdentifier", "membershipIdentifier"], "members": {"configuredTableAssociationIdentifier": {"shape": "ConfiguredTableAssociationIdentifier", "documentation": "<p>The unique ID for the configured table association to retrieve. Currently accepts the configured table ID.</p>", "location": "uri", "locationName": "configuredTableAssociationIdentifier"}, "membershipIdentifier": {"shape": "MembershipIdentifier", "documentation": "<p>A unique identifier for the membership that the configured table association belongs to. Currently accepts the membership ID.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "GetConfiguredTableAssociationOutput": {"type": "structure", "required": ["configuredTableAssociation"], "members": {"configuredTableAssociation": {"shape": "ConfiguredTableAssociation", "documentation": "<p>The entire configured table association object.</p>"}}}, "GetConfiguredTableInput": {"type": "structure", "required": ["configuredTableIdentifier"], "members": {"configuredTableIdentifier": {"shape": "ConfiguredTableIdentifier", "documentation": "<p>The unique ID for the configured table to retrieve.</p>", "location": "uri", "locationName": "configuredTableIdentifier"}}}, "GetConfiguredTableOutput": {"type": "structure", "required": ["configuredTable"], "members": {"configuredTable": {"shape": "ConfiguredTable", "documentation": "<p>The retrieved configured table.</p>"}}}, "GetMembershipInput": {"type": "structure", "required": ["membershipIdentifier"], "members": {"membershipIdentifier": {"shape": "MembershipIdentifier", "documentation": "<p>The identifier for a membership resource.</p>", "location": "uri", "locationName": "membershipIdentifier"}}}, "GetMembershipOutput": {"type": "structure", "required": ["membership"], "members": {"membership": {"shape": "Membership", "documentation": "<p>The membership retrieved for the provided identifier.</p>"}}}, "GetProtectedQueryInput": {"type": "structure", "required": ["membershipIdentifier", "protectedQueryIdentifier"], "members": {"membershipIdentifier": {"shape": "MembershipIdentifier", "documentation": "<p>The identifier for a membership in a protected query instance.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "protectedQueryIdentifier": {"shape": "ProtectedQueryIdentifier", "documentation": "<p>The identifier for a protected query instance.</p>", "location": "uri", "locationName": "protectedQueryIdentifier"}}}, "GetProtectedQueryOutput": {"type": "structure", "required": ["protected<PERSON>uery"], "members": {"protectedQuery": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The query processing metadata.</p>"}}}, "GetSchemaAnalysisRuleInput": {"type": "structure", "required": ["collaborationIdentifier", "name", "type"], "members": {"collaborationIdentifier": {"shape": "CollaborationIdentifier", "documentation": "<p>A unique identifier for the collaboration that the schema belongs to. Currently accepts a collaboration ID.</p>", "location": "uri", "locationName": "collaborationIdentifier"}, "name": {"shape": "TableAlias", "documentation": "<p>The name of the schema to retrieve the analysis rule for.</p>", "location": "uri", "locationName": "name"}, "type": {"shape": "AnalysisRuleType", "documentation": "<p>The type of the schema analysis rule to retrieve. Schema analysis rules are uniquely identified by a combination of the collaboration, the schema name, and their type.</p>", "location": "uri", "locationName": "type"}}}, "GetSchemaAnalysisRuleOutput": {"type": "structure", "required": ["analysisRule"], "members": {"analysisRule": {"shape": "AnalysisRule", "documentation": "<p>A specification about how data from the configured table can be used.</p>"}}}, "GetSchemaInput": {"type": "structure", "required": ["collaborationIdentifier", "name"], "members": {"collaborationIdentifier": {"shape": "CollaborationIdentifier", "documentation": "<p>A unique identifier for the collaboration that the schema belongs to. Currently accepts a collaboration ID.</p>", "location": "uri", "locationName": "collaborationIdentifier"}, "name": {"shape": "TableAlias", "documentation": "<p>The name of the relation to retrieve the schema for.</p>", "location": "uri", "locationName": "name"}}}, "GetSchemaOutput": {"type": "structure", "required": ["schema"], "members": {"schema": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The entire schema object.</p>"}}}, "GlueResourceName": {"type": "string", "max": 128, "min": 0, "pattern": "[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?"}, "GlueTableReference": {"type": "structure", "required": ["tableName", "databaseName"], "members": {"tableName": {"shape": "GlueResourceName", "documentation": "<p>The name of the AWS Glue table.</p>"}, "databaseName": {"shape": "GlueResourceName", "documentation": "<p>The name of the database the AWS Glue table belongs to.</p>"}}, "documentation": "<p>A reference to a table within an AWS Glue data catalog.</p>"}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Unexpected error during processing of request.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "JoinRequiredOption": {"type": "string", "enum": ["QUERY_RUNNER"]}, "KeyPrefix": {"type": "string", "pattern": "[\\w!.*/-]*"}, "ListCollaborationsInput": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call. Service chooses a default if it has not been set. Service may return a nextToken even if the maximum results has not been met.</p>", "location": "querystring", "locationName": "maxResults"}, "memberStatus": {"shape": "FilterableMemberStatus", "documentation": "<p>The caller's status in a collaboration.</p>", "location": "querystring", "locationName": "memberStatus"}}}, "ListCollaborationsOutput": {"type": "structure", "required": ["collaborationList"], "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>"}, "collaborationList": {"shape": "CollaborationSummaryList", "documentation": "<p>The list of collaborations.</p>"}}}, "ListConfiguredTableAssociationsInput": {"type": "structure", "required": ["membershipIdentifier"], "members": {"membershipIdentifier": {"shape": "MembershipIdentifier", "documentation": "<p>A unique identifier for the membership to list configured table associations for. Currently accepts the membership ID.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListConfiguredTableAssociationsOutput": {"type": "structure", "required": ["configuredTableAssociationSummaries"], "members": {"configuredTableAssociationSummaries": {"shape": "ConfiguredTableAssociationSummaryList", "documentation": "<p>The retrieved list of configured table associations.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>"}}}, "ListConfiguredTablesInput": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListConfiguredTablesOutput": {"type": "structure", "required": ["configuredTableSummaries"], "members": {"configuredTableSummaries": {"shape": "ConfiguredTableSummaryList", "documentation": "<p>The configured tables listed by the request.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>"}}}, "ListMembersInput": {"type": "structure", "required": ["collaborationIdentifier"], "members": {"collaborationIdentifier": {"shape": "CollaborationIdentifier", "documentation": "<p>The identifier of the collaboration in which the members are listed.</p>", "location": "uri", "locationName": "collaborationIdentifier"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListMembersOutput": {"type": "structure", "required": ["memberSummaries"], "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>"}, "memberSummaries": {"shape": "MemberSummaryList", "documentation": "<p>The list of members returned by the ListMembers operation.</p>"}}}, "ListMembershipsInput": {"type": "structure", "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}, "status": {"shape": "MembershipStatus", "documentation": "<p>A filter which will return only memberships in the specified status.</p>", "location": "querystring", "locationName": "status"}}}, "ListMembershipsOutput": {"type": "structure", "required": ["membershipSummaries"], "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>"}, "membershipSummaries": {"shape": "MembershipSummaryList", "documentation": "<p>The list of memberships returned from the ListMemberships operation.</p>"}}}, "ListProtectedQueriesInput": {"type": "structure", "required": ["membershipIdentifier"], "members": {"membershipIdentifier": {"shape": "MembershipIdentifier", "documentation": "<p>The identifier for the membership in the collaboration.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "status": {"shape": "ProtectedQueryStatus", "documentation": "<p>A filter on the status of the protected query.</p>", "location": "querystring", "locationName": "status"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call. Service chooses a default if it has not been set. Service can return a nextToken even if the maximum results has not been met. </p>", "location": "querystring", "locationName": "maxResults"}}}, "ListProtectedQueriesOutput": {"type": "structure", "required": ["protectedQueries"], "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>"}, "protectedQueries": {"shape": "ProtectedQuerySummaryList", "documentation": "<p>A list of protected queries.</p>"}}}, "ListSchemasInput": {"type": "structure", "required": ["collaborationIdentifier"], "members": {"collaborationIdentifier": {"shape": "CollaborationIdentifier", "documentation": "<p>A unique identifier for the collaboration that the schema belongs to. Currently accepts a collaboration ID.</p>", "location": "uri", "locationName": "collaborationIdentifier"}, "schemaType": {"shape": "SchemaType", "documentation": "<p>If present, filter schemas by schema type. The only valid schema type is currently `TABLE`.</p>", "location": "querystring", "locationName": "schemaType"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>", "location": "querystring", "locationName": "nextToken"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of the results that is returned per call.</p>", "location": "querystring", "locationName": "maxResults"}}}, "ListSchemasOutput": {"type": "structure", "required": ["schemaSummaries"], "members": {"schemaSummaries": {"shape": "SchemaSummaryList", "documentation": "<p>The retrieved list of schemas.</p>"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>The token value retrieved from a previous call to access the next page of results.</p>"}}}, "Long": {"type": "long", "box": true}, "MaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "MemberAbilities": {"type": "list", "member": {"shape": "MemberAbility"}}, "MemberAbility": {"type": "string", "enum": ["CAN_QUERY", "CAN_RECEIVE_RESULTS"]}, "MemberList": {"type": "list", "member": {"shape": "MemberSpecification"}, "max": 9, "min": 0}, "MemberSpecification": {"type": "structure", "required": ["accountId", "memberAbilities", "displayName"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The identifier used to reference members of the collaboration. Currently only supports AWS Account ID.</p>"}, "memberAbilities": {"shape": "MemberAbilities", "documentation": "<p>The abilities granted to the collaboration member.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The member's display name.</p>"}}, "documentation": "<p>Basic metadata used to construct a new member.</p>"}, "MemberStatus": {"type": "string", "enum": ["INVITED", "ACTIVE", "LEFT", "REMOVED"]}, "MemberSummary": {"type": "structure", "required": ["accountId", "status", "displayName", "abilities", "createTime", "updateTime"], "members": {"accountId": {"shape": "AccountId", "documentation": "<p>The identifier used to reference members of the collaboration. Currently only supports AWS Account ID.</p>"}, "status": {"shape": "MemberStatus", "documentation": "<p>The status of the member. Valid values are `INVITED`, `ACTIVE`, `LEFT`, and `REMOVED`.</p>"}, "displayName": {"shape": "DisplayName", "documentation": "<p>The member's display name.</p>"}, "abilities": {"shape": "MemberAbilities", "documentation": "<p>The abilities granted to the collaboration member.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time when the member was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the member metadata was last updated.</p>"}, "membershipId": {"shape": "UUID", "documentation": "<p>The unique ID for the member's associated membership, if present.</p>"}, "membershipArn": {"shape": "MembershipArn", "documentation": "<p>The unique ARN for the member's associated membership, if present.</p>"}}, "documentation": "<p>The member object listed by the request.</p>"}, "MemberSummaryList": {"type": "list", "member": {"shape": "MemberSummary"}}, "Membership": {"type": "structure", "required": ["id", "arn", "collaborationArn", "collaborationId", "collaborationCreatorAccountId", "collaborationCreatorDisplayName", "collaborationName", "createTime", "updateTime", "status", "memberAbilities", "queryLogStatus"], "members": {"id": {"shape": "UUID", "documentation": "<p>The unique ID of the membership.</p>"}, "arn": {"shape": "MembershipArn", "documentation": "<p>The unique ARN for the membership.</p>"}, "collaborationArn": {"shape": "CollaborationArn", "documentation": "<p>The unique ARN for the membership's associated collaboration.</p>"}, "collaborationId": {"shape": "UUID", "documentation": "<p>The unique ID for the membership's collaboration.</p>"}, "collaborationCreatorAccountId": {"shape": "AccountId", "documentation": "<p>The identifier used to reference members of the collaboration. Currently only supports AWS account ID.</p>"}, "collaborationCreatorDisplayName": {"shape": "DisplayName", "documentation": "<p>The display name of the collaboration creator.</p>"}, "collaborationName": {"shape": "CollaborationName", "documentation": "<p>The name of the membership's collaboration.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time when the membership was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the membership metadata was last updated.</p>"}, "status": {"shape": "MembershipStatus", "documentation": "<p>The status of the membership. Valid values are `ACTIVE`, `REMOVED`, and `COLLABORATION_DELETED`.</p>"}, "memberAbilities": {"shape": "MemberAbilities", "documentation": "<p>The abilities granted to the collaboration member.</p>"}, "queryLogStatus": {"shape": "MembershipQueryLogStatus", "documentation": "<p>An indicator as to whether query logging has been enabled or disabled for the collaboration.</p>"}}, "documentation": "<p>The membership object.</p>"}, "MembershipArn": {"type": "string", "max": 100, "min": 0, "pattern": "arn:aws:[\\w]+:[\\w]{2}-[\\w]{4,9}-[\\d]:[\\d]{12}:membership/[\\d\\w-]+"}, "MembershipIdentifier": {"type": "string", "max": 36, "min": 36, "pattern": ".*[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}.*"}, "MembershipQueryLogStatus": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "MembershipStatus": {"type": "string", "enum": ["ACTIVE", "REMOVED", "COLLABORATION_DELETED"]}, "MembershipSummary": {"type": "structure", "required": ["id", "arn", "collaborationArn", "collaborationId", "collaborationCreatorAccountId", "collaborationCreatorDisplayName", "collaborationName", "createTime", "updateTime", "status", "memberAbilities"], "members": {"id": {"shape": "UUID", "documentation": "<p>The unique ID for the membership's collaboration.</p>"}, "arn": {"shape": "MembershipArn", "documentation": "<p>The unique ARN for the membership.</p>"}, "collaborationArn": {"shape": "CollaborationArn", "documentation": "<p>The unique ARN for the membership's associated collaboration.</p>"}, "collaborationId": {"shape": "CollaborationIdentifier", "documentation": "<p>The unique ID for the membership's collaboration.</p>"}, "collaborationCreatorAccountId": {"shape": "AccountId", "documentation": "<p>The identifier of the AWS principal that created the collaboration. Currently only supports AWS account ID.</p>"}, "collaborationCreatorDisplayName": {"shape": "DisplayName", "documentation": "<p>The display name of the collaboration creator.</p>"}, "collaborationName": {"shape": "CollaborationName", "documentation": "<p>The name for the membership's collaboration.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time when the membership was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the membership metadata was last updated.</p>"}, "status": {"shape": "MembershipStatus", "documentation": "<p>The status of the membership. Valid values are `ACTIVE`, `REMOVED`, and `COLLABORATION_DELETED`.</p>"}, "memberAbilities": {"shape": "MemberAbilities", "documentation": "<p>The abilities granted to the collaboration member.</p>"}}, "documentation": "<p>The membership object listed by the request.</p>"}, "MembershipSummaryList": {"type": "list", "member": {"shape": "MembershipSummary"}}, "PaginationToken": {"type": "string", "max": 10240, "min": 0}, "ProtectedQuery": {"type": "structure", "required": ["id", "membershipId", "membershipArn", "createTime", "sqlParameters", "status", "resultConfiguration"], "members": {"id": {"shape": "UUID", "documentation": "<p>The identifier for a protected query instance.</p>"}, "membershipId": {"shape": "UUID", "documentation": "<p>The identifier for the membership.</p>"}, "membershipArn": {"shape": "MembershipArn", "documentation": "<p>The ARN of the membership.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time at which the protected query was created.</p>"}, "sqlParameters": {"shape": "ProtectedQuerySQLParameters", "documentation": "<p>The protected query SQL parameters.</p>"}, "status": {"shape": "ProtectedQueryStatus", "documentation": "<p>The status of the query.</p>"}, "resultConfiguration": {"shape": "ProtectedQueryResultConfiguration", "documentation": "<p>Contains any details needed to write the query results.</p>"}, "statistics": {"shape": "ProtectedQueryStatistics", "documentation": "<p>Statistics about protected query execution.</p>"}, "result": {"shape": "Protected<PERSON>ueryResult", "documentation": "<p>The result of the protected query.</p>"}, "error": {"shape": "Protected<PERSON><PERSON>y<PERSON><PERSON>r", "documentation": "<p>An error thrown by the protected query.</p>"}}, "documentation": "<p>The parameters for an AWS Clean Rooms protected query.</p>"}, "ProtectedQueryError": {"type": "structure", "required": ["message", "code"], "members": {"message": {"shape": "String", "documentation": "<p>A description of why the query failed.</p>"}, "code": {"shape": "String", "documentation": "<p>An error code for the error.</p>"}}, "documentation": "<p>Details of errors thrown by the protected query.</p>"}, "ProtectedQueryIdentifier": {"type": "string", "max": 36, "min": 1, "pattern": ".*[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}.*"}, "ProtectedQueryOutput": {"type": "structure", "members": {"s3": {"shape": "ProtectedQueryS3Output", "documentation": "<p>If present, the output for a protected query with an `S3` output type.</p>"}}, "documentation": "<p>Contains details about the protected query output.</p>", "union": true}, "ProtectedQueryOutputConfiguration": {"type": "structure", "members": {"s3": {"shape": "ProtectedQueryS3OutputConfiguration", "documentation": "<p>Required configuration for a protected query with an `S3` output type.</p>"}}, "documentation": "<p>Contains configuration details for protected query output.</p>", "union": true}, "ProtectedQueryResult": {"type": "structure", "required": ["output"], "members": {"output": {"shape": "ProtectedQueryOutput", "documentation": "<p>The output of the protected query.</p>"}}, "documentation": "<p>Details about the query results.</p>"}, "ProtectedQueryResultConfiguration": {"type": "structure", "required": ["outputConfiguration"], "members": {"outputConfiguration": {"shape": "ProtectedQueryOutputConfiguration", "documentation": "<p>Configuration for protected query results.</p>"}}, "documentation": "<p>Contains configurations for protected query results.</p>"}, "ProtectedQueryS3Output": {"type": "structure", "required": ["location"], "members": {"location": {"shape": "String", "documentation": "<p>The S3 location of the result.</p>"}}, "documentation": "<p>Contains output information for protected queries with an S3 output type.</p>"}, "ProtectedQueryS3OutputConfiguration": {"type": "structure", "required": ["resultFormat", "bucket"], "members": {"resultFormat": {"shape": "ResultFormat", "documentation": "<p>Intended file format of the result.</p>"}, "bucket": {"shape": "ProtectedQueryS3OutputConfigurationBucketString", "documentation": "<p>The S3 bucket to unload the protected query results.</p>"}, "keyPrefix": {"shape": "KeyPrefix", "documentation": "<p>The S3 prefix to unload the protected query results.</p>"}}, "documentation": "<p>Contains the configuration to write the query results to S3.</p>"}, "ProtectedQueryS3OutputConfigurationBucketString": {"type": "string", "max": 63, "min": 3, "pattern": ".*(?!^(\\d+\\.)+\\d+$)(^(([a-z0-9]|[a-z0-9][a-z0-9\\-]*[a-z0-9])\\.)*([a-z0-9]|[a-z0-9][a-z0-9\\-]*[a-z0-9])$).*"}, "ProtectedQuerySQLParameters": {"type": "structure", "required": ["queryString"], "members": {"queryString": {"shape": "ProtectedQuerySQLParametersQueryStringString", "documentation": "<p>The query string to be submitted.</p>"}}, "documentation": "<p>The parameters for the SQL type Protected Query.</p>", "sensitive": true}, "ProtectedQuerySQLParametersQueryStringString": {"type": "string", "max": 15000, "min": 0}, "ProtectedQueryStatistics": {"type": "structure", "members": {"totalDurationInMillis": {"shape": "<PERSON>", "documentation": "<p>The duration of the Protected Query, from creation until query completion.</p>"}}, "documentation": "<p>Contains statistics about the execution of the protected query.</p>"}, "ProtectedQueryStatus": {"type": "string", "enum": ["SUBMITTED", "STARTED", "CANCELLED", "CANCELLING", "FAILED", "SUCCESS", "TIMED_OUT"]}, "ProtectedQuerySummary": {"type": "structure", "required": ["id", "membershipId", "membershipArn", "createTime", "status"], "members": {"id": {"shape": "UUID", "documentation": "<p>The unique ID of the protected query.</p>"}, "membershipId": {"shape": "UUID", "documentation": "<p>The unique ID for the membership that initiated the protected query.</p>"}, "membershipArn": {"shape": "MembershipArn", "documentation": "<p>The unique ARN for the membership that initiated the protected query.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time the protected query was created.</p>"}, "status": {"shape": "ProtectedQueryStatus", "documentation": "<p>The status of the protected query. Value values are `SUBMITTED`, `STARTED`, `CANCELLED`, `CANCELLING`, `FAILED`, `SUCCESS`, `TIMED_OUT`.</p>"}}, "documentation": "<p>The protected query summary for the objects listed by the request.</p>"}, "ProtectedQuerySummaryList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "ProtectedQueryType": {"type": "string", "enum": ["SQL"]}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>The Id of the missing resource.</p>"}, "resourceType": {"shape": "ResourceType", "documentation": "<p>The type of the missing resource.</p>"}}, "documentation": "<p>Request references a resource which does not exist.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "ResourceType": {"type": "string", "enum": ["CONFIGURED_TABLE", "COLLABORATION", "MEMBERSHIP", "CONFIGURED_TABLE_ASSOCIATION"]}, "ResultFormat": {"type": "string", "enum": ["CSV", "PARQUET"]}, "RoleArn": {"type": "string", "max": 512, "min": 32, "pattern": "arn:aws:iam::[\\w]+:role/[\\w+=,./@-]+"}, "ScalarFunctions": {"type": "string", "enum": ["TRUNC", "ABS", "CEILING", "FLOOR", "LN", "LOG", "ROUND", "SQRT", "CAST", "LOWER", "RTRIM", "UPPER", "COALESCE"]}, "ScalarFunctionsList": {"type": "list", "member": {"shape": "ScalarFunctions"}}, "Schema": {"type": "structure", "required": ["columns", "partitionKeys", "analysisRuleTypes", "creatorAccountId", "name", "collaborationId", "collaborationArn", "description", "createTime", "updateTime", "type"], "members": {"columns": {"shape": "ColumnList", "documentation": "<p>The columns for the relation this schema represents.</p>"}, "partitionKeys": {"shape": "ColumnList", "documentation": "<p>The partition keys for the data set underlying this schema.</p>"}, "analysisRuleTypes": {"shape": "AnalysisRuleTypeList", "documentation": "<p>The analysis rule types associated with the schema. Valued values are LIST and AGGREGATION. Currently, only one entry is present.</p>"}, "analysisMethod": {"shape": "AnalysisMethod", "documentation": "<p>The analysis method for the schema. The only valid value is currently DIRECT_QUERY.</p>"}, "creatorAccountId": {"shape": "AccountId", "documentation": "<p>The unique account ID for the AWS account that owns the schema.</p>"}, "name": {"shape": "TableAlias", "documentation": "<p>A name for the schema. The schema relation is referred to by this name when queried by a protected query.</p>"}, "collaborationId": {"shape": "UUID", "documentation": "<p>The unique ID for the collaboration that the schema belongs to.</p>"}, "collaborationArn": {"shape": "CollaborationArn", "documentation": "<p>The unique ARN for the collaboration that the schema belongs to.</p>"}, "description": {"shape": "TableDescription", "documentation": "<p>A description for the schema.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time the schema was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the schema was last updated.</p>"}, "type": {"shape": "SchemaType", "documentation": "<p>The type of schema. The only valid value is currently `TABLE`.</p>"}}, "documentation": "<p>A schema is a relation within a collaboration.</p>"}, "SchemaList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON>"}, "max": 25, "min": 0}, "SchemaSummary": {"type": "structure", "required": ["name", "type", "creatorAccountId", "createTime", "updateTime", "collaborationId", "collaborationArn", "analysisRuleTypes"], "members": {"name": {"shape": "TableAlias", "documentation": "<p>The name for the schema object.</p>"}, "type": {"shape": "SchemaType", "documentation": "<p>The type of schema object. The only valid schema type is currently `TABLE`.</p>"}, "creatorAccountId": {"shape": "AccountId", "documentation": "<p>The unique account ID for the AWS account that owns the schema.</p>"}, "createTime": {"shape": "Timestamp", "documentation": "<p>The time the schema object was created.</p>"}, "updateTime": {"shape": "Timestamp", "documentation": "<p>The time the schema object was last updated.</p>"}, "collaborationId": {"shape": "UUID", "documentation": "<p>The unique ID for the collaboration that the schema belongs to.</p>"}, "collaborationArn": {"shape": "CollaborationArn", "documentation": "<p>The unique ARN for the collaboration that the schema belongs to.</p>"}, "analysisRuleTypes": {"shape": "AnalysisRuleTypeList", "documentation": "<p>The types of analysis rules that are associated with this schema object.</p>"}, "analysisMethod": {"shape": "AnalysisMethod", "documentation": "<p>The analysis method for the associated schema. The only valid value is currently `DIRECT_QUERY`.</p>"}}, "documentation": "<p>The schema summary for the objects listed by the request.</p>"}, "SchemaSummaryList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "SchemaType": {"type": "string", "enum": ["TABLE"]}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message", "quotaName", "quotaValue"], "members": {"message": {"shape": "String"}, "quotaName": {"shape": "String", "documentation": "<p>The name of the quota.</p>"}, "quotaValue": {"shape": "Double", "documentation": "<p>The value of the quota.</p>"}}, "documentation": "<p>Request denied because service quota has been exceeded.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "StartProtectedQueryInput": {"type": "structure", "required": ["type", "membershipIdentifier", "sqlParameters", "resultConfiguration"], "members": {"type": {"shape": "ProtectedQueryType", "documentation": "<p>The type of the protected query to be started.</p>"}, "membershipIdentifier": {"shape": "MembershipIdentifier", "documentation": "<p>A unique identifier for the membership to run this query against. Currently accepts a membership ID.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "sqlParameters": {"shape": "ProtectedQuerySQLParameters", "documentation": "<p>The protected SQL query parameters.</p>"}, "resultConfiguration": {"shape": "ProtectedQueryResultConfiguration", "documentation": "<p>The details needed to write the query results.</p>"}}}, "StartProtectedQueryOutput": {"type": "structure", "required": ["protected<PERSON>uery"], "members": {"protectedQuery": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The protected query.</p>"}}}, "String": {"type": "string"}, "TableAlias": {"type": "string", "max": 128, "min": 0, "pattern": "[a-zA-Z0-9_](([a-zA-Z0-9_ ]+-)*([a-zA-Z0-9_ ]+))?"}, "TableAliasList": {"type": "list", "member": {"shape": "TableAlias"}, "max": 25, "min": 1}, "TableDescription": {"type": "string", "max": 255, "min": 0, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDBFF-\\uDC00\\uDFFF\\t\\r\\n]*"}, "TableReference": {"type": "structure", "members": {"glue": {"shape": "GlueTableReference", "documentation": "<p>If present, a reference to the AWS Glue table referred to by this table reference.</p>"}}, "documentation": "<p>A pointer to the data set that underlies this table. Currently, this can only be an AWS Glue table.</p>", "union": true}, "TargetProtectedQueryStatus": {"type": "string", "enum": ["CANCELLED"]}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "Timestamp": {"type": "timestamp"}, "UUID": {"type": "string", "max": 36, "min": 36, "pattern": ".*[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}.*"}, "UpdateCollaborationInput": {"type": "structure", "required": ["collaborationIdentifier"], "members": {"collaborationIdentifier": {"shape": "CollaborationIdentifier", "documentation": "<p>The identifier for the collaboration.</p>", "location": "uri", "locationName": "collaborationIdentifier"}, "name": {"shape": "CollaborationName", "documentation": "<p>A human-readable identifier provided by the collaboration owner. Display names are not unique.</p>"}, "description": {"shape": "CollaborationDescription", "documentation": "<p>A description of the collaboration.</p>"}}}, "UpdateCollaborationOutput": {"type": "structure", "required": ["collaboration"], "members": {"collaboration": {"shape": "Collaboration", "documentation": "<p>The entire collaboration that has been updated.</p>"}}}, "UpdateConfiguredTableAnalysisRuleInput": {"type": "structure", "required": ["configuredTableIdentifier", "analysisRuleType", "analysisRulePolicy"], "members": {"configuredTableIdentifier": {"shape": "ConfiguredTableIdentifier", "documentation": "<p>The unique identifier for the configured table that the analysis rule applies to. Currently accepts the configured table ID.</p>", "location": "uri", "locationName": "configuredTableIdentifier"}, "analysisRuleType": {"shape": "ConfiguredTableAnalysisRuleType", "documentation": "<p>The analysis rule type to be updated. Configured table analysis rules are uniquely identified by their configured table identifier and analysis rule type.</p>", "location": "uri", "locationName": "analysisRuleType"}, "analysisRulePolicy": {"shape": "ConfiguredTableAnalysisRulePolicy", "documentation": "<p>The new analysis rule policy for the configured table analysis rule.</p>"}}}, "UpdateConfiguredTableAnalysisRuleOutput": {"type": "structure", "required": ["analysisRule"], "members": {"analysisRule": {"shape": "ConfiguredTableAnalysisRule", "documentation": "<p>The entire updated analysis rule.</p>"}}}, "UpdateConfiguredTableAssociationInput": {"type": "structure", "required": ["configuredTableAssociationIdentifier", "membershipIdentifier"], "members": {"configuredTableAssociationIdentifier": {"shape": "ConfiguredTableAssociationIdentifier", "documentation": "<p>The unique identifier for the configured table association to update. Currently accepts the configured table association ID.</p>", "location": "uri", "locationName": "configuredTableAssociationIdentifier"}, "membershipIdentifier": {"shape": "MembershipIdentifier", "documentation": "<p>The unique ID for the membership that the configured table association belongs to.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "description": {"shape": "TableDescription", "documentation": "<p>A new description for the configured table association.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The service will assume this role to access catalog metadata and query the table.</p>"}}}, "UpdateConfiguredTableAssociationOutput": {"type": "structure", "required": ["configuredTableAssociation"], "members": {"configuredTableAssociation": {"shape": "ConfiguredTableAssociation", "documentation": "<p>The entire updated configured table association.</p>"}}}, "UpdateConfiguredTableInput": {"type": "structure", "required": ["configuredTableIdentifier"], "members": {"configuredTableIdentifier": {"shape": "ConfiguredTableIdentifier", "documentation": "<p>The identifier for the configured table to update. Currently accepts the configured table ID.</p>", "location": "uri", "locationName": "configuredTableIdentifier"}, "name": {"shape": "DisplayName", "documentation": "<p>A new name for the configured table.</p>"}, "description": {"shape": "TableDescription", "documentation": "<p>A new description for the configured table.</p>"}}}, "UpdateConfiguredTableOutput": {"type": "structure", "required": ["configuredTable"], "members": {"configuredTable": {"shape": "ConfiguredTable", "documentation": "<p>The updated configured table.</p>"}}}, "UpdateMembershipInput": {"type": "structure", "required": ["membershipIdentifier"], "members": {"membershipIdentifier": {"shape": "MembershipIdentifier", "documentation": "<p>The unique identifier of the membership.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "queryLogStatus": {"shape": "MembershipQueryLogStatus", "documentation": "<p>An indicator as to whether query logging has been enabled or disabled for the collaboration.</p>"}}}, "UpdateMembershipOutput": {"type": "structure", "required": ["membership"], "members": {"membership": {"shape": "Membership"}}}, "UpdateProtectedQueryInput": {"type": "structure", "required": ["membershipIdentifier", "protectedQueryIdentifier", "targetStatus"], "members": {"membershipIdentifier": {"shape": "MembershipIdentifier", "documentation": "<p>The identifier for a member of a protected query instance.</p>", "location": "uri", "locationName": "membershipIdentifier"}, "protectedQueryIdentifier": {"shape": "ProtectedQueryIdentifier", "documentation": "<p>The identifier for a protected query instance.</p>", "location": "uri", "locationName": "protectedQueryIdentifier"}, "targetStatus": {"shape": "TargetProtectedQueryStatus", "documentation": "<p>The target status of a query. Used to update the execution status of a currently running query.</p>"}}}, "UpdateProtectedQueryOutput": {"type": "structure", "required": ["protected<PERSON>uery"], "members": {"protectedQuery": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The protected query output.</p>"}}}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "String"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>A reason code for the exception.</p>"}, "fieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>Validation errors for specific input parameters.</p>"}}, "documentation": "<p>The input fails to satisfy the specified constraints.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["name", "message"], "members": {"name": {"shape": "String", "documentation": "<p>The name of the input parameter.</p>"}, "message": {"shape": "String", "documentation": "<p>A message for the input validation error.</p>"}}, "documentation": "<p>Describes validation errors for specific input parameters.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}}, "ValidationExceptionReason": {"type": "string", "enum": ["FIELD_VALIDATION_FAILED", "INVALID_CONFIGURATION"]}}, "documentation": "<note> <p>AWS Clean Rooms is in preview release and is subject to change.</p> </note> <p>Welcome to the <i>AWS Clean Rooms API Reference</i>.</p> <p>AWS Clean Rooms is an AWS service that helps multiple parties to join their data together in a secure collaboration workspace. In the collaboration, members who can query and receive results can get insights into the combined data without either party getting access to the other party's raw data.</p> <p>To learn more about AWS Clean Rooms concepts, procedures, and best practices, see the <a href=\"https://docs.aws.amazon.com/clean-rooms/latest/userguide/what-is.html\">AWS Clean Rooms User Guide</a>.</p>"}