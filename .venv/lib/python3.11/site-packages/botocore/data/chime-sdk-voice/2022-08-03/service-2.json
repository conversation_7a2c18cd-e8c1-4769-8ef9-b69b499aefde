{"version": "2.0", "metadata": {"apiVersion": "2022-08-03", "endpointPrefix": "voice-chime", "protocol": "rest-json", "serviceFullName": "Amazon Chime SDK Voice", "serviceId": "Chime SDK Voice", "signatureVersion": "v4", "signingName": "chime", "uid": "chime-sdk-voice-2022-08-03"}, "operations": {"AssociatePhoneNumbersWithVoiceConnector": {"name": "AssociatePhoneNumbersWithVoiceConnector", "http": {"method": "POST", "requestUri": "/voice-connectors/{voiceConnectorId}?operation=associate-phone-numbers", "responseCode": 200}, "input": {"shape": "AssociatePhoneNumbersWithVoiceConnectorRequest"}, "output": {"shape": "AssociatePhoneNumbersWithVoiceConnectorResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "AssociatePhoneNumbersWithVoiceConnectorGroup": {"name": "AssociatePhoneNumbersWithVoiceConnectorGroup", "http": {"method": "POST", "requestUri": "/voice-connector-groups/{voiceConnectorGroupId}?operation=associate-phone-numbers", "responseCode": 200}, "input": {"shape": "AssociatePhoneNumbersWithVoiceConnectorGroupRequest"}, "output": {"shape": "AssociatePhoneNumbersWithVoiceConnectorGroupResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "BatchDeletePhoneNumber": {"name": "BatchDeletePhoneNumber", "http": {"method": "POST", "requestUri": "/phone-numbers?operation=batch-delete", "responseCode": 200}, "input": {"shape": "BatchDeletePhoneNumberRequest"}, "output": {"shape": "BatchDeletePhoneNumberResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "BatchUpdatePhoneNumber": {"name": "BatchUpdatePhoneNumber", "http": {"method": "POST", "requestUri": "/phone-numbers?operation=batch-update", "responseCode": 200}, "input": {"shape": "BatchUpdatePhoneNumberRequest"}, "output": {"shape": "BatchUpdatePhoneNumberResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "CreatePhoneNumberOrder": {"name": "CreatePhoneNumberOrder", "http": {"method": "POST", "requestUri": "/phone-number-orders", "responseCode": 201}, "input": {"shape": "CreatePhoneNumberOrderRequest"}, "output": {"shape": "CreatePhoneNumberOrderResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "CreateProxySession": {"name": "CreateProxySession", "http": {"method": "POST", "requestUri": "/voice-connectors/{voiceConnectorId}/proxy-sessions", "responseCode": 201}, "input": {"shape": "CreateProxySessionRequest"}, "output": {"shape": "CreateProxySessionResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "CreateSipMediaApplication": {"name": "CreateSipMediaApplication", "http": {"method": "POST", "requestUri": "/sip-media-applications", "responseCode": 201}, "input": {"shape": "CreateSipMediaApplicationRequest"}, "output": {"shape": "CreateSipMediaApplicationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "CreateSipMediaApplicationCall": {"name": "CreateSipMediaApplicationCall", "http": {"method": "POST", "requestUri": "/sip-media-applications/{sipMediaApplicationId}/calls", "responseCode": 201}, "input": {"shape": "CreateSipMediaApplicationCallRequest"}, "output": {"shape": "CreateSipMediaApplicationCallResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ThrottledClientException"}, {"shape": "UnauthorizedClientException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "CreateSipRule": {"name": "CreateSipRule", "http": {"method": "POST", "requestUri": "/sip-rules", "responseCode": 201}, "input": {"shape": "CreateSipRuleRequest"}, "output": {"shape": "CreateSipRuleResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ConflictException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "CreateVoiceConnector": {"name": "CreateVoiceConnector", "http": {"method": "POST", "requestUri": "/voice-connectors", "responseCode": 201}, "input": {"shape": "CreateVoiceConnectorRequest"}, "output": {"shape": "CreateVoiceConnectorResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "CreateVoiceConnectorGroup": {"name": "CreateVoiceConnectorGroup", "http": {"method": "POST", "requestUri": "/voice-connector-groups", "responseCode": 201}, "input": {"shape": "CreateVoiceConnectorGroupRequest"}, "output": {"shape": "CreateVoiceConnectorGroupResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DeletePhoneNumber": {"name": "DeletePhoneNumber", "http": {"method": "DELETE", "requestUri": "/phone-numbers/{phoneNumberId}", "responseCode": 204}, "input": {"shape": "DeletePhoneNumberRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DeleteProxySession": {"name": "DeleteProxySession", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/proxy-sessions/{proxySessionId}", "responseCode": 204}, "input": {"shape": "DeleteProxySessionRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DeleteSipMediaApplication": {"name": "DeleteSipMediaApplication", "http": {"method": "DELETE", "requestUri": "/sip-media-applications/{sipMediaApplicationId}", "responseCode": 204}, "input": {"shape": "DeleteSipMediaApplicationRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DeleteSipRule": {"name": "DeleteSipRule", "http": {"method": "DELETE", "requestUri": "/sip-rules/{sipRuleId}", "responseCode": 204}, "input": {"shape": "DeleteSipRuleRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DeleteVoiceConnector": {"name": "DeleteVoiceConnector", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DeleteVoiceConnectorEmergencyCallingConfiguration": {"name": "DeleteVoiceConnectorEmergencyCallingConfiguration", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/emergency-calling-configuration", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorEmergencyCallingConfigurationRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DeleteVoiceConnectorGroup": {"name": "DeleteVoiceConnectorGroup", "http": {"method": "DELETE", "requestUri": "/voice-connector-groups/{voiceConnectorGroupId}", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorGroupRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DeleteVoiceConnectorOrigination": {"name": "DeleteVoiceConnectorOrigination", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/origination", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorOriginationRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DeleteVoiceConnectorProxy": {"name": "DeleteVoiceConnectorProxy", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/programmable-numbers/proxy", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorProxyRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DeleteVoiceConnectorStreamingConfiguration": {"name": "DeleteVoiceConnectorStreamingConfiguration", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/streaming-configuration", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorStreamingConfigurationRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DeleteVoiceConnectorTermination": {"name": "DeleteVoiceConnectorTermination", "http": {"method": "DELETE", "requestUri": "/voice-connectors/{voiceConnectorId}/termination", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorTerminationRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DeleteVoiceConnectorTerminationCredentials": {"name": "DeleteVoiceConnectorTerminationCredentials", "http": {"method": "POST", "requestUri": "/voice-connectors/{voiceConnectorId}/termination/credentials?operation=delete", "responseCode": 204}, "input": {"shape": "DeleteVoiceConnectorTerminationCredentialsRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DisassociatePhoneNumbersFromVoiceConnector": {"name": "DisassociatePhoneNumbersFromVoiceConnector", "http": {"method": "POST", "requestUri": "/voice-connectors/{voiceConnectorId}?operation=disassociate-phone-numbers", "responseCode": 200}, "input": {"shape": "DisassociatePhoneNumbersFromVoiceConnectorRequest"}, "output": {"shape": "DisassociatePhoneNumbersFromVoiceConnectorResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "DisassociatePhoneNumbersFromVoiceConnectorGroup": {"name": "DisassociatePhoneNumbersFromVoiceConnectorGroup", "http": {"method": "POST", "requestUri": "/voice-connector-groups/{voiceConnectorGroupId}?operation=disassociate-phone-numbers", "responseCode": 200}, "input": {"shape": "DisassociatePhoneNumbersFromVoiceConnectorGroupRequest"}, "output": {"shape": "DisassociatePhoneNumbersFromVoiceConnectorGroupResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetGlobalSettings": {"name": "GetGlobalSettings", "http": {"method": "GET", "requestUri": "/settings", "responseCode": 200}, "output": {"shape": "GetGlobalSettingsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetPhoneNumber": {"name": "GetPhoneNumber", "http": {"method": "GET", "requestUri": "/phone-numbers/{phoneNumberId}"}, "input": {"shape": "GetPhoneNumberRequest"}, "output": {"shape": "GetPhoneNumberResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetPhoneNumberOrder": {"name": "GetPhoneNumberOrder", "http": {"method": "GET", "requestUri": "/phone-number-orders/{phoneNumberOrderId}", "responseCode": 200}, "input": {"shape": "GetPhoneNumberOrderRequest"}, "output": {"shape": "GetPhoneNumberOrderResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetPhoneNumberSettings": {"name": "GetPhoneNumberSettings", "http": {"method": "GET", "requestUri": "/settings/phone-number", "responseCode": 200}, "output": {"shape": "GetPhoneNumberSettingsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetProxySession": {"name": "GetProxySession", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/proxy-sessions/{proxySessionId}", "responseCode": 200}, "input": {"shape": "GetProxySessionRequest"}, "output": {"shape": "GetProxySessionResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetSipMediaApplication": {"name": "GetSipMediaApplication", "http": {"method": "GET", "requestUri": "/sip-media-applications/{sipMediaApplicationId}", "responseCode": 200}, "input": {"shape": "GetSipMediaApplicationRequest"}, "output": {"shape": "GetSipMediaApplicationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetSipMediaApplicationAlexaSkillConfiguration": {"name": "GetSipMediaApplicationAlexaSkillConfiguration", "http": {"method": "GET", "requestUri": "/sip-media-applications/{sipMediaApplicationId}/alexa-skill-configuration", "responseCode": 200}, "input": {"shape": "GetSipMediaApplicationAlexaSkillConfigurationRequest"}, "output": {"shape": "GetSipMediaApplicationAlexaSkillConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetSipMediaApplicationLoggingConfiguration": {"name": "GetSipMediaApplicationLoggingConfiguration", "http": {"method": "GET", "requestUri": "/sip-media-applications/{sipMediaApplicationId}/logging-configuration", "responseCode": 200}, "input": {"shape": "GetSipMediaApplicationLoggingConfigurationRequest"}, "output": {"shape": "GetSipMediaApplicationLoggingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetSipRule": {"name": "GetSipRule", "http": {"method": "GET", "requestUri": "/sip-rules/{sipRuleId}", "responseCode": 200}, "input": {"shape": "GetSipRuleRequest"}, "output": {"shape": "GetSipRuleResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetVoiceConnector": {"name": "GetVoiceConnector", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorRequest"}, "output": {"shape": "GetVoiceConnectorResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetVoiceConnectorEmergencyCallingConfiguration": {"name": "GetVoiceConnectorEmergencyCallingConfiguration", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/emergency-calling-configuration", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorEmergencyCallingConfigurationRequest"}, "output": {"shape": "GetVoiceConnectorEmergencyCallingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetVoiceConnectorGroup": {"name": "GetVoiceConnectorGroup", "http": {"method": "GET", "requestUri": "/voice-connector-groups/{voiceConnectorGroupId}", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorGroupRequest"}, "output": {"shape": "GetVoiceConnectorGroupResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetVoiceConnectorLoggingConfiguration": {"name": "GetVoiceConnectorLoggingConfiguration", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/logging-configuration", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorLoggingConfigurationRequest"}, "output": {"shape": "GetVoiceConnectorLoggingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetVoiceConnectorOrigination": {"name": "GetVoiceConnectorOrigination", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/origination", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorOriginationRequest"}, "output": {"shape": "GetVoiceConnectorOriginationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetVoiceConnectorProxy": {"name": "GetVoiceConnectorProxy", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/programmable-numbers/proxy", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorProxyRequest"}, "output": {"shape": "GetVoiceConnectorProxyResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetVoiceConnectorStreamingConfiguration": {"name": "GetVoiceConnectorStreamingConfiguration", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/streaming-configuration", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorStreamingConfigurationRequest"}, "output": {"shape": "GetVoiceConnectorStreamingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetVoiceConnectorTermination": {"name": "GetVoiceConnectorTermination", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/termination", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorTerminationRequest"}, "output": {"shape": "GetVoiceConnectorTerminationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "GetVoiceConnectorTerminationHealth": {"name": "GetVoiceConnectorTerminationHealth", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/termination/health", "responseCode": 200}, "input": {"shape": "GetVoiceConnectorTerminationHealthRequest"}, "output": {"shape": "GetVoiceConnectorTerminationHealthResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "ListAvailableVoiceConnectorRegions": {"name": "ListAvailableVoiceConnectorRegions", "http": {"method": "GET", "requestUri": "/voice-connector-regions", "responseCode": 200}, "output": {"shape": "ListAvailableVoiceConnectorRegionsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "ListPhoneNumberOrders": {"name": "ListPhoneNumberOrders", "http": {"method": "GET", "requestUri": "/phone-number-orders", "responseCode": 200}, "input": {"shape": "ListPhoneNumberOrdersRequest"}, "output": {"shape": "ListPhoneNumberOrdersResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "ListPhoneNumbers": {"name": "ListPhoneNumbers", "http": {"method": "GET", "requestUri": "/phone-numbers"}, "input": {"shape": "ListPhoneNumbersRequest"}, "output": {"shape": "ListPhoneNumbersResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "ListProxySessions": {"name": "ListProxySessions", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/proxy-sessions", "responseCode": 200}, "input": {"shape": "ListProxySessionsRequest"}, "output": {"shape": "ListProxySessionsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "ListSipMediaApplications": {"name": "ListSipMediaApplications", "http": {"method": "GET", "requestUri": "/sip-media-applications", "responseCode": 200}, "input": {"shape": "ListSipMediaApplicationsRequest"}, "output": {"shape": "ListSipMediaApplicationsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "ListSipRules": {"name": "ListSipRules", "http": {"method": "GET", "requestUri": "/sip-rules", "responseCode": 200}, "input": {"shape": "ListSipRulesRequest"}, "output": {"shape": "ListSipRulesResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "ListSupportedPhoneNumberCountries": {"name": "ListSupportedPhoneNumberCountries", "http": {"method": "GET", "requestUri": "/phone-number-countries", "responseCode": 200}, "input": {"shape": "ListSupportedPhoneNumberCountriesRequest"}, "output": {"shape": "ListSupportedPhoneNumberCountriesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "ListVoiceConnectorGroups": {"name": "ListVoiceConnectorGroups", "http": {"method": "GET", "requestUri": "/voice-connector-groups", "responseCode": 200}, "input": {"shape": "ListVoiceConnectorGroupsRequest"}, "output": {"shape": "ListVoiceConnectorGroupsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "ListVoiceConnectorTerminationCredentials": {"name": "ListVoiceConnectorTerminationCredentials", "http": {"method": "GET", "requestUri": "/voice-connectors/{voiceConnectorId}/termination/credentials", "responseCode": 200}, "input": {"shape": "ListVoiceConnectorTerminationCredentialsRequest"}, "output": {"shape": "ListVoiceConnectorTerminationCredentialsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "ListVoiceConnectors": {"name": "ListVoiceConnectors", "http": {"method": "GET", "requestUri": "/voice-connectors", "responseCode": 200}, "input": {"shape": "ListVoiceConnectorsRequest"}, "output": {"shape": "ListVoiceConnectorsResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "PutSipMediaApplicationAlexaSkillConfiguration": {"name": "PutSipMediaApplicationAlexaSkillConfiguration", "http": {"method": "PUT", "requestUri": "/sip-media-applications/{sipMediaApplicationId}/alexa-skill-configuration", "responseCode": 200}, "input": {"shape": "PutSipMediaApplicationAlexaSkillConfigurationRequest"}, "output": {"shape": "PutSipMediaApplicationAlexaSkillConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "PutSipMediaApplicationLoggingConfiguration": {"name": "PutSipMediaApplicationLoggingConfiguration", "http": {"method": "PUT", "requestUri": "/sip-media-applications/{sipMediaApplicationId}/logging-configuration", "responseCode": 200}, "input": {"shape": "PutSipMediaApplicationLoggingConfigurationRequest"}, "output": {"shape": "PutSipMediaApplicationLoggingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "PutVoiceConnectorEmergencyCallingConfiguration": {"name": "PutVoiceConnectorEmergencyCallingConfiguration", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/emergency-calling-configuration", "responseCode": 200}, "input": {"shape": "PutVoiceConnectorEmergencyCallingConfigurationRequest"}, "output": {"shape": "PutVoiceConnectorEmergencyCallingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "PutVoiceConnectorLoggingConfiguration": {"name": "PutVoiceConnectorLoggingConfiguration", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/logging-configuration", "responseCode": 200}, "input": {"shape": "PutVoiceConnectorLoggingConfigurationRequest"}, "output": {"shape": "PutVoiceConnectorLoggingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "PutVoiceConnectorOrigination": {"name": "PutVoiceConnectorOrigination", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/origination", "responseCode": 200}, "input": {"shape": "PutVoiceConnectorOriginationRequest"}, "output": {"shape": "PutVoiceConnectorOriginationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "PutVoiceConnectorProxy": {"name": "PutVoiceConnectorProxy", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/programmable-numbers/proxy"}, "input": {"shape": "PutVoiceConnectorProxyRequest"}, "output": {"shape": "PutVoiceConnectorProxyResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "AccessDeniedException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "PutVoiceConnectorStreamingConfiguration": {"name": "PutVoiceConnectorStreamingConfiguration", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/streaming-configuration", "responseCode": 200}, "input": {"shape": "PutVoiceConnectorStreamingConfigurationRequest"}, "output": {"shape": "PutVoiceConnectorStreamingConfigurationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "PutVoiceConnectorTermination": {"name": "PutVoiceConnectorTermination", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}/termination", "responseCode": 200}, "input": {"shape": "PutVoiceConnectorTerminationRequest"}, "output": {"shape": "PutVoiceConnectorTerminationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "PutVoiceConnectorTerminationCredentials": {"name": "PutVoiceConnectorTerminationCredentials", "http": {"method": "POST", "requestUri": "/voice-connectors/{voiceConnectorId}/termination/credentials?operation=put", "responseCode": 204}, "input": {"shape": "PutVoiceConnectorTerminationCredentialsRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "RestorePhoneNumber": {"name": "RestorePhoneNumber", "http": {"method": "POST", "requestUri": "/phone-numbers/{phoneNumberId}?operation=restore", "responseCode": 200}, "input": {"shape": "RestorePhoneNumberRequest"}, "output": {"shape": "RestorePhoneNumberResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "SearchAvailablePhoneNumbers": {"name": "SearchAvailablePhoneNumbers", "http": {"method": "GET", "requestUri": "/search?type=phone-numbers"}, "input": {"shape": "SearchAvailablePhoneNumbersRequest"}, "output": {"shape": "SearchAvailablePhoneNumbersResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "AccessDeniedException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "UpdateGlobalSettings": {"name": "UpdateGlobalSettings", "http": {"method": "PUT", "requestUri": "/settings", "responseCode": 204}, "input": {"shape": "UpdateGlobalSettingsRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "UpdatePhoneNumber": {"name": "UpdatePhoneNumber", "http": {"method": "POST", "requestUri": "/phone-numbers/{phoneNumberId}", "responseCode": 200}, "input": {"shape": "UpdatePhoneNumberRequest"}, "output": {"shape": "UpdatePhoneNumberResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ConflictException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "UpdatePhoneNumberSettings": {"name": "UpdatePhoneNumberSettings", "http": {"method": "PUT", "requestUri": "/settings/phone-number", "responseCode": 204}, "input": {"shape": "UpdatePhoneNumberSettingsRequest"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "UpdateProxySession": {"name": "UpdateProxySession", "http": {"method": "POST", "requestUri": "/voice-connectors/{voiceConnectorId}/proxy-sessions/{proxySessionId}", "responseCode": 201}, "input": {"shape": "UpdateProxySessionRequest"}, "output": {"shape": "UpdateProxySessionResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "UpdateSipMediaApplication": {"name": "UpdateSipMediaApplication", "http": {"method": "PUT", "requestUri": "/sip-media-applications/{sipMediaApplicationId}", "responseCode": 200}, "input": {"shape": "UpdateSipMediaApplicationRequest"}, "output": {"shape": "UpdateSipMediaApplicationResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "UpdateSipMediaApplicationCall": {"name": "UpdateSipMediaApplicationCall", "http": {"method": "POST", "requestUri": "/sip-media-applications/{sipMediaApplicationId}/calls/{transactionId}", "responseCode": 202}, "input": {"shape": "UpdateSipMediaApplicationCallRequest"}, "output": {"shape": "UpdateSipMediaApplicationCallResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ThrottledClientException"}, {"shape": "UnauthorizedClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "UpdateSipRule": {"name": "UpdateSipRule", "http": {"method": "PUT", "requestUri": "/sip-rules/{sipRuleId}", "responseCode": 202}, "input": {"shape": "UpdateSipRuleRequest"}, "output": {"shape": "UpdateSipRuleResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ResourceLimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "UpdateVoiceConnector": {"name": "UpdateVoiceConnector", "http": {"method": "PUT", "requestUri": "/voice-connectors/{voiceConnectorId}", "responseCode": 200}, "input": {"shape": "UpdateVoiceConnectorRequest"}, "output": {"shape": "UpdateVoiceConnectorResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "UpdateVoiceConnectorGroup": {"name": "UpdateVoiceConnectorGroup", "http": {"method": "PUT", "requestUri": "/voice-connector-groups/{voiceConnectorGroupId}", "responseCode": 202}, "input": {"shape": "UpdateVoiceConnectorGroupRequest"}, "output": {"shape": "UpdateVoiceConnectorGroupResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}, "ValidateE911Address": {"name": "ValidateE911Address", "http": {"method": "POST", "requestUri": "/emergency-calling/address", "responseCode": 202}, "input": {"shape": "ValidateE911AddressRequest"}, "output": {"shape": "ValidateE911AddressResponse"}, "errors": [{"shape": "UnauthorizedClientException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "ThrottledClientException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}]}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 403}, "exception": true}, "Address": {"type": "structure", "members": {"streetName": {"shape": "SensitiveNonEmptyString"}, "streetSuffix": {"shape": "SensitiveNonEmptyString"}, "postDirectional": {"shape": "SensitiveNonEmptyString"}, "preDirectional": {"shape": "SensitiveNonEmptyString"}, "streetNumber": {"shape": "SensitiveNonEmptyString"}, "city": {"shape": "SensitiveNonEmptyString"}, "state": {"shape": "SensitiveNonEmptyString"}, "postalCode": {"shape": "SensitiveNonEmptyString"}, "postalCodePlus4": {"shape": "SensitiveNonEmptyString"}, "country": {"shape": "SensitiveNonEmptyString"}}}, "AlexaSkillId": {"type": "string", "max": 64, "pattern": "amzn1\\.application-oa2-client\\.[0-9a-fA-F]{32}", "sensitive": true}, "AlexaSkillIdList": {"type": "list", "member": {"shape": "AlexaSkillId"}, "max": 1, "min": 1}, "AlexaSkillStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "Alpha2CountryCode": {"type": "string", "pattern": "[A-Z]{2}"}, "AreaCode": {"type": "string", "pattern": "^$|^[0-9]{3,3}$"}, "AssociatePhoneNumbersWithVoiceConnectorGroupRequest": {"type": "structure", "required": ["VoiceConnectorGroupId", "E164PhoneNumbers"], "members": {"VoiceConnectorGroupId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorGroupId"}, "E164PhoneNumbers": {"shape": "E164PhoneNumberList"}, "ForceAssociate": {"shape": "NullableBoolean"}}}, "AssociatePhoneNumbersWithVoiceConnectorGroupResponse": {"type": "structure", "members": {"PhoneNumberErrors": {"shape": "PhoneNumberErrorList"}}}, "AssociatePhoneNumbersWithVoiceConnectorRequest": {"type": "structure", "required": ["VoiceConnectorId", "E164PhoneNumbers"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}, "E164PhoneNumbers": {"shape": "E164PhoneNumberList"}, "ForceAssociate": {"shape": "NullableBoolean"}}}, "AssociatePhoneNumbersWithVoiceConnectorResponse": {"type": "structure", "members": {"PhoneNumberErrors": {"shape": "PhoneNumberErrorList"}}}, "BadRequestException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 400}, "exception": true}, "BatchDeletePhoneNumberRequest": {"type": "structure", "required": ["PhoneNumberIds"], "members": {"PhoneNumberIds": {"shape": "NonEmptyStringList"}}}, "BatchDeletePhoneNumberResponse": {"type": "structure", "members": {"PhoneNumberErrors": {"shape": "PhoneNumberErrorList"}}}, "BatchUpdatePhoneNumberRequest": {"type": "structure", "required": ["UpdatePhoneNumberRequestItems"], "members": {"UpdatePhoneNumberRequestItems": {"shape": "UpdatePhoneNumberRequestItemList"}}}, "BatchUpdatePhoneNumberResponse": {"type": "structure", "members": {"PhoneNumberErrors": {"shape": "PhoneNumberErrorList"}}}, "Boolean": {"type": "boolean"}, "CallingName": {"type": "string", "pattern": "^$|^[a-zA-Z0-9 ]{2,15}$", "sensitive": true}, "CallingNameStatus": {"type": "string", "enum": ["Unassigned", "UpdateInProgress", "UpdateSucceeded", "UpdateFailed"]}, "CallingRegion": {"type": "string"}, "CallingRegionList": {"type": "list", "member": {"shape": "CallingRegion"}}, "CandidateAddress": {"type": "structure", "members": {"streetInfo": {"shape": "SensitiveNonEmptyString"}, "streetNumber": {"shape": "SensitiveNonEmptyString"}, "city": {"shape": "SensitiveNonEmptyString"}, "state": {"shape": "SensitiveNonEmptyString"}, "postalCode": {"shape": "SensitiveNonEmptyString"}, "postalCodePlus4": {"shape": "SensitiveNonEmptyString"}, "country": {"shape": "SensitiveNonEmptyString"}}}, "CandidateAddressList": {"type": "list", "member": {"shape": "Candidate<PERSON><PERSON><PERSON>"}}, "Capability": {"type": "string", "enum": ["Voice", "SMS"]}, "CapabilityList": {"type": "list", "member": {"shape": "Capability"}}, "ConflictException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 409}, "exception": true}, "Country": {"type": "string", "pattern": "^$|^[A-Z]{2,2}$"}, "CountryList": {"type": "list", "member": {"shape": "Country"}, "max": 100, "min": 1}, "CpsLimit": {"type": "integer", "min": 1}, "CreatePhoneNumberOrderRequest": {"type": "structure", "required": ["ProductType", "E164PhoneNumbers"], "members": {"ProductType": {"shape": "PhoneNumberProductType"}, "E164PhoneNumbers": {"shape": "E164PhoneNumberList"}}}, "CreatePhoneNumberOrderResponse": {"type": "structure", "members": {"PhoneNumberOrder": {"shape": "PhoneNumberOrder"}}}, "CreateProxySessionRequest": {"type": "structure", "required": ["ParticipantPhoneNumbers", "Capabilities", "VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "location": "uri", "locationName": "voiceConnectorId"}, "ParticipantPhoneNumbers": {"shape": "ParticipantPhoneNumberList"}, "Name": {"shape": "ProxySessionNameString"}, "ExpiryMinutes": {"shape": "PositiveInteger"}, "Capabilities": {"shape": "CapabilityList"}, "NumberSelectionBehavior": {"shape": "NumberSelectionBehavior"}, "GeoMatchLevel": {"shape": "GeoMatchLevel"}, "GeoMatchParams": {"shape": "GeoMatchParams"}}}, "CreateProxySessionResponse": {"type": "structure", "members": {"ProxySession": {"shape": "ProxySession"}}}, "CreateSipMediaApplicationCallRequest": {"type": "structure", "required": ["FromPhoneNumber", "ToPhoneNumber", "SipMediaApplicationId"], "members": {"FromPhoneNumber": {"shape": "E164PhoneNumber"}, "ToPhoneNumber": {"shape": "E164PhoneNumber"}, "SipMediaApplicationId": {"shape": "NonEmptyString", "location": "uri", "locationName": "sipMediaApplicationId"}, "SipHeaders": {"shape": "SipHeadersMap"}, "ArgumentsMap": {"shape": "SMACreateCallArgumentsMap"}}}, "CreateSipMediaApplicationCallResponse": {"type": "structure", "members": {"SipMediaApplicationCall": {"shape": "SipMediaApplicationCall"}}}, "CreateSipMediaApplicationRequest": {"type": "structure", "required": ["AwsRegion", "Name", "Endpoints"], "members": {"AwsRegion": {"shape": "String"}, "Name": {"shape": "SipMediaApplicationName"}, "Endpoints": {"shape": "SipMediaApplicationEndpointList"}}}, "CreateSipMediaApplicationResponse": {"type": "structure", "members": {"SipMediaApplication": {"shape": "SipMediaApplication"}}}, "CreateSipRuleRequest": {"type": "structure", "required": ["Name", "TriggerType", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "members": {"Name": {"shape": "SipRuleName"}, "TriggerType": {"shape": "SipRuleTriggerType"}, "TriggerValue": {"shape": "NonEmptyString"}, "Disabled": {"shape": "NullableBoolean"}, "TargetApplications": {"shape": "SipRuleTargetApplicationList"}}}, "CreateSipRuleResponse": {"type": "structure", "members": {"SipRule": {"shape": "SipRule"}}}, "CreateVoiceConnectorGroupRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "VoiceConnectorGroupName"}, "VoiceConnectorItems": {"shape": "VoiceConnectorItemList"}}}, "CreateVoiceConnectorGroupResponse": {"type": "structure", "members": {"VoiceConnectorGroup": {"shape": "VoiceConnectorGroup"}}}, "CreateVoiceConnectorRequest": {"type": "structure", "required": ["Name", "RequireEncryption"], "members": {"Name": {"shape": "VoiceConnectorName"}, "AwsRegion": {"shape": "VoiceConnectorAwsRegion"}, "RequireEncryption": {"shape": "Boolean"}}}, "CreateVoiceConnectorResponse": {"type": "structure", "members": {"VoiceConnector": {"shape": "VoiceConnector"}}}, "Credential": {"type": "structure", "members": {"Username": {"shape": "SensitiveString"}, "Password": {"shape": "SensitiveString"}}}, "CredentialList": {"type": "list", "member": {"shape": "Credential"}}, "DNISEmergencyCallingConfiguration": {"type": "structure", "required": ["EmergencyPhoneNumber", "CallingCountry"], "members": {"EmergencyPhoneNumber": {"shape": "E164PhoneNumber"}, "TestPhoneNumber": {"shape": "E164PhoneNumber"}, "CallingCountry": {"shape": "Alpha2CountryCode"}}}, "DNISEmergencyCallingConfigurationList": {"type": "list", "member": {"shape": "DNISEmergencyCallingConfiguration"}}, "DataRetentionInHours": {"type": "integer", "min": 0}, "DeletePhoneNumberRequest": {"type": "structure", "required": ["PhoneNumberId"], "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString", "location": "uri", "locationName": "phoneNumberId"}}}, "DeleteProxySessionRequest": {"type": "structure", "required": ["VoiceConnectorId", "ProxySessionId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "location": "uri", "locationName": "voiceConnectorId"}, "ProxySessionId": {"shape": "NonEmptyString128", "location": "uri", "locationName": "proxySessionId"}}}, "DeleteSipMediaApplicationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "location": "uri", "locationName": "sipMediaApplicationId"}}}, "DeleteSipRuleRequest": {"type": "structure", "required": ["SipRuleId"], "members": {"SipRuleId": {"shape": "NonEmptyString", "location": "uri", "locationName": "sipRuleId"}}}, "DeleteVoiceConnectorEmergencyCallingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "DeleteVoiceConnectorGroupRequest": {"type": "structure", "required": ["VoiceConnectorGroupId"], "members": {"VoiceConnectorGroupId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorGroupId"}}}, "DeleteVoiceConnectorOriginationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "DeleteVoiceConnectorProxyRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "location": "uri", "locationName": "voiceConnectorId"}}}, "DeleteVoiceConnectorRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "DeleteVoiceConnectorStreamingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "DeleteVoiceConnectorTerminationCredentialsRequest": {"type": "structure", "required": ["Usernames", "VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}, "Usernames": {"shape": "SensitiveStringList"}}}, "DeleteVoiceConnectorTerminationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "DisassociatePhoneNumbersFromVoiceConnectorGroupRequest": {"type": "structure", "required": ["VoiceConnectorGroupId", "E164PhoneNumbers"], "members": {"VoiceConnectorGroupId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorGroupId"}, "E164PhoneNumbers": {"shape": "E164PhoneNumberList"}}}, "DisassociatePhoneNumbersFromVoiceConnectorGroupResponse": {"type": "structure", "members": {"PhoneNumberErrors": {"shape": "PhoneNumberErrorList"}}}, "DisassociatePhoneNumbersFromVoiceConnectorRequest": {"type": "structure", "required": ["VoiceConnectorId", "E164PhoneNumbers"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}, "E164PhoneNumbers": {"shape": "E164PhoneNumberList"}}}, "DisassociatePhoneNumbersFromVoiceConnectorResponse": {"type": "structure", "members": {"PhoneNumberErrors": {"shape": "PhoneNumberErrorList"}}}, "E164PhoneNumber": {"type": "string", "pattern": "^\\+?[1-9]\\d{1,14}$", "sensitive": true}, "E164PhoneNumberList": {"type": "list", "member": {"shape": "E164PhoneNumber"}}, "EmergencyCallingConfiguration": {"type": "structure", "members": {"DNIS": {"shape": "DNISEmergencyCallingConfigurationList"}}}, "ErrorCode": {"type": "string", "enum": ["BadRequest", "Conflict", "Forbidden", "NotFound", "PreconditionFailed", "ResourceLimitExceeded", "ServiceFailure", "AccessDenied", "ServiceUnavailable", "Throttled", "Throttling", "Unauthorized", "Unprocessable", "VoiceConnectorGroupAssociationsExist", "PhoneNumberAssociationsExist", "Gone"]}, "ForbiddenException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 403}, "exception": true}, "FunctionArn": {"type": "string", "max": 10000, "pattern": "arn:(aws[a-zA-Z-]*)?:lambda:[a-z]{2}((-gov)|(-iso(b?)))?-[a-z]+-\\d{1}:\\d{12}:function:[a-zA-Z0-9-_]+(:(\\$LATEST|[a-zA-Z0-9-_]+))?", "sensitive": true}, "GeoMatchLevel": {"type": "string", "enum": ["Country", "AreaCode"]}, "GeoMatchParams": {"type": "structure", "required": ["Country", "AreaCode"], "members": {"Country": {"shape": "Country"}, "AreaCode": {"shape": "AreaCode"}}}, "GetGlobalSettingsResponse": {"type": "structure", "members": {"VoiceConnector": {"shape": "VoiceConnectorSettings"}}}, "GetPhoneNumberOrderRequest": {"type": "structure", "required": ["PhoneNumberOrderId"], "members": {"PhoneNumberOrderId": {"shape": "GuidString", "location": "uri", "locationName": "phoneNumberOrderId"}}}, "GetPhoneNumberOrderResponse": {"type": "structure", "members": {"PhoneNumberOrder": {"shape": "PhoneNumberOrder"}}}, "GetPhoneNumberRequest": {"type": "structure", "required": ["PhoneNumberId"], "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString", "location": "uri", "locationName": "phoneNumberId"}}}, "GetPhoneNumberResponse": {"type": "structure", "members": {"PhoneNumber": {"shape": "PhoneNumber"}}}, "GetPhoneNumberSettingsResponse": {"type": "structure", "members": {"CallingName": {"shape": "CallingName"}, "CallingNameUpdatedTimestamp": {"shape": "Iso8601Timestamp"}}}, "GetProxySessionRequest": {"type": "structure", "required": ["VoiceConnectorId", "ProxySessionId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "location": "uri", "locationName": "voiceConnectorId"}, "ProxySessionId": {"shape": "NonEmptyString128", "location": "uri", "locationName": "proxySessionId"}}}, "GetProxySessionResponse": {"type": "structure", "members": {"ProxySession": {"shape": "ProxySession"}}}, "GetSipMediaApplicationAlexaSkillConfigurationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "location": "uri", "locationName": "sipMediaApplicationId"}}}, "GetSipMediaApplicationAlexaSkillConfigurationResponse": {"type": "structure", "members": {"SipMediaApplicationAlexaSkillConfiguration": {"shape": "SipMediaApplicationAlexaSkillConfiguration"}}}, "GetSipMediaApplicationLoggingConfigurationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "location": "uri", "locationName": "sipMediaApplicationId"}}}, "GetSipMediaApplicationLoggingConfigurationResponse": {"type": "structure", "members": {"SipMediaApplicationLoggingConfiguration": {"shape": "SipMediaApplicationLoggingConfiguration"}}}, "GetSipMediaApplicationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "location": "uri", "locationName": "sipMediaApplicationId"}}}, "GetSipMediaApplicationResponse": {"type": "structure", "members": {"SipMediaApplication": {"shape": "SipMediaApplication"}}}, "GetSipRuleRequest": {"type": "structure", "required": ["SipRuleId"], "members": {"SipRuleId": {"shape": "NonEmptyString", "location": "uri", "locationName": "sipRuleId"}}}, "GetSipRuleResponse": {"type": "structure", "members": {"SipRule": {"shape": "SipRule"}}}, "GetVoiceConnectorEmergencyCallingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorEmergencyCallingConfigurationResponse": {"type": "structure", "members": {"EmergencyCallingConfiguration": {"shape": "EmergencyCallingConfiguration"}}}, "GetVoiceConnectorGroupRequest": {"type": "structure", "required": ["VoiceConnectorGroupId"], "members": {"VoiceConnectorGroupId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorGroupId"}}}, "GetVoiceConnectorGroupResponse": {"type": "structure", "members": {"VoiceConnectorGroup": {"shape": "VoiceConnectorGroup"}}}, "GetVoiceConnectorLoggingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorLoggingConfigurationResponse": {"type": "structure", "members": {"LoggingConfiguration": {"shape": "LoggingConfiguration"}}}, "GetVoiceConnectorOriginationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorOriginationResponse": {"type": "structure", "members": {"Origination": {"shape": "Origination"}}}, "GetVoiceConnectorProxyRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorProxyResponse": {"type": "structure", "members": {"Proxy": {"shape": "Proxy"}}}, "GetVoiceConnectorRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorResponse": {"type": "structure", "members": {"VoiceConnector": {"shape": "VoiceConnector"}}}, "GetVoiceConnectorStreamingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorStreamingConfigurationResponse": {"type": "structure", "members": {"StreamingConfiguration": {"shape": "StreamingConfiguration"}}}, "GetVoiceConnectorTerminationHealthRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorTerminationHealthResponse": {"type": "structure", "members": {"TerminationHealth": {"shape": "TerminationHealth"}}}, "GetVoiceConnectorTerminationRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "GetVoiceConnectorTerminationResponse": {"type": "structure", "members": {"Termination": {"shape": "Termination"}}}, "GuidString": {"type": "string", "pattern": "[a-fA-F0-9]{8}(?:-[a-fA-F0-9]{4}){3}-[a-fA-F0-9]{12}"}, "Integer": {"type": "integer"}, "Iso8601Timestamp": {"type": "timestamp", "timestampFormat": "iso8601"}, "ListAvailableVoiceConnectorRegionsResponse": {"type": "structure", "members": {"VoiceConnectorRegions": {"shape": "VoiceConnectorAwsRegionList"}}}, "ListPhoneNumberOrdersRequest": {"type": "structure", "members": {"NextToken": {"shape": "String", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "ResultMax", "location": "querystring", "locationName": "max-results"}}}, "ListPhoneNumberOrdersResponse": {"type": "structure", "members": {"PhoneNumberOrders": {"shape": "PhoneNumberOrderList"}, "NextToken": {"shape": "String"}}}, "ListPhoneNumbersRequest": {"type": "structure", "members": {"Status": {"shape": "String", "location": "querystring", "locationName": "status"}, "ProductType": {"shape": "PhoneNumberProductType", "location": "querystring", "locationName": "product-type"}, "FilterName": {"shape": "PhoneNumberAssociationName", "location": "querystring", "locationName": "filter-name"}, "FilterValue": {"shape": "String", "location": "querystring", "locationName": "filter-value"}, "MaxResults": {"shape": "ResultMax", "location": "querystring", "locationName": "max-results"}, "NextToken": {"shape": "String", "location": "querystring", "locationName": "next-token"}}}, "ListPhoneNumbersResponse": {"type": "structure", "members": {"PhoneNumbers": {"shape": "PhoneNumberList"}, "NextToken": {"shape": "String"}}}, "ListProxySessionsRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "location": "uri", "locationName": "voiceConnectorId"}, "Status": {"shape": "ProxySessionStatus", "location": "querystring", "locationName": "status"}, "NextToken": {"shape": "NextTokenString", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "ResultMax", "location": "querystring", "locationName": "max-results"}}}, "ListProxySessionsResponse": {"type": "structure", "members": {"ProxySessions": {"shape": "ProxySessions"}, "NextToken": {"shape": "NextTokenString"}}}, "ListSipMediaApplicationsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "ResultMax", "location": "querystring", "locationName": "max-results"}, "NextToken": {"shape": "NextTokenString", "location": "querystring", "locationName": "next-token"}}}, "ListSipMediaApplicationsResponse": {"type": "structure", "members": {"SipMediaApplications": {"shape": "SipMediaApplicationList"}, "NextToken": {"shape": "NextTokenString"}}}, "ListSipRulesRequest": {"type": "structure", "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "location": "querystring", "locationName": "sip-media-application"}, "MaxResults": {"shape": "ResultMax", "location": "querystring", "locationName": "max-results"}, "NextToken": {"shape": "NextTokenString", "location": "querystring", "locationName": "next-token"}}}, "ListSipRulesResponse": {"type": "structure", "members": {"SipRules": {"shape": "SipRuleList"}, "NextToken": {"shape": "NextTokenString"}}}, "ListSupportedPhoneNumberCountriesRequest": {"type": "structure", "required": ["ProductType"], "members": {"ProductType": {"shape": "PhoneNumberProductType", "location": "querystring", "locationName": "product-type"}}}, "ListSupportedPhoneNumberCountriesResponse": {"type": "structure", "members": {"PhoneNumberCountries": {"shape": "PhoneNumberCountriesList"}}}, "ListVoiceConnectorGroupsRequest": {"type": "structure", "members": {"NextToken": {"shape": "String", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "ResultMax", "location": "querystring", "locationName": "max-results"}}}, "ListVoiceConnectorGroupsResponse": {"type": "structure", "members": {"VoiceConnectorGroups": {"shape": "VoiceConnectorGroupList"}, "NextToken": {"shape": "String"}}}, "ListVoiceConnectorTerminationCredentialsRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}}}, "ListVoiceConnectorTerminationCredentialsResponse": {"type": "structure", "members": {"Usernames": {"shape": "SensitiveStringList"}}}, "ListVoiceConnectorsRequest": {"type": "structure", "members": {"NextToken": {"shape": "String", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "ResultMax", "location": "querystring", "locationName": "max-results"}}}, "ListVoiceConnectorsResponse": {"type": "structure", "members": {"VoiceConnectors": {"shape": "VoiceConnectorList"}, "NextToken": {"shape": "String"}}}, "LoggingConfiguration": {"type": "structure", "members": {"EnableSIPLogs": {"shape": "Boolean"}}}, "NextTokenString": {"type": "string", "max": 65535}, "NonEmptyString": {"type": "string", "pattern": ".*\\S.*"}, "NonEmptyString128": {"type": "string", "max": 128, "min": 1, "pattern": ".*\\S.*"}, "NonEmptyStringList": {"type": "list", "member": {"shape": "String"}, "min": 1}, "NotFoundException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 404}, "exception": true}, "NotificationTarget": {"type": "string", "enum": ["EventBridge", "SNS", "SQS"]}, "NullableBoolean": {"type": "boolean"}, "NumberSelectionBehavior": {"type": "string", "enum": ["PreferSticky", "AvoidSticky"]}, "OrderedPhoneNumber": {"type": "structure", "members": {"E164PhoneNumber": {"shape": "E164PhoneNumber"}, "Status": {"shape": "OrderedPhoneNumberStatus"}}}, "OrderedPhoneNumberList": {"type": "list", "member": {"shape": "OrderedPhoneNumber"}}, "OrderedPhoneNumberStatus": {"type": "string", "enum": ["Processing", "Acquired", "Failed"]}, "Origination": {"type": "structure", "members": {"Routes": {"shape": "OriginationRouteList"}, "Disabled": {"shape": "Boolean"}}}, "OriginationRoute": {"type": "structure", "members": {"Host": {"shape": "String"}, "Port": {"shape": "Port"}, "Protocol": {"shape": "OriginationRouteProtocol"}, "Priority": {"shape": "OriginationRoutePriority"}, "Weight": {"shape": "OriginationRouteWeight"}}}, "OriginationRouteList": {"type": "list", "member": {"shape": "OriginationRoute"}}, "OriginationRoutePriority": {"type": "integer", "max": 100, "min": 1}, "OriginationRouteProtocol": {"type": "string", "enum": ["TCP", "UDP"]}, "OriginationRouteWeight": {"type": "integer", "max": 100, "min": 1}, "Participant": {"type": "structure", "members": {"PhoneNumber": {"shape": "E164PhoneNumber"}, "ProxyPhoneNumber": {"shape": "E164PhoneNumber"}}}, "ParticipantPhoneNumberList": {"type": "list", "member": {"shape": "E164PhoneNumber"}, "max": 2, "min": 2}, "Participants": {"type": "list", "member": {"shape": "Participant"}}, "PhoneNumber": {"type": "structure", "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString"}, "E164PhoneNumber": {"shape": "E164PhoneNumber"}, "Country": {"shape": "Alpha2CountryCode"}, "Type": {"shape": "PhoneNumberType"}, "ProductType": {"shape": "PhoneNumberProductType"}, "Status": {"shape": "PhoneNumberStatus"}, "Capabilities": {"shape": "PhoneNumberCapabilities"}, "Associations": {"shape": "PhoneNumberAssociationList"}, "CallingName": {"shape": "CallingName"}, "CallingNameStatus": {"shape": "CallingNameStatus"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp"}, "DeletionTimestamp": {"shape": "Iso8601Timestamp"}, "OrderId": {"shape": "GuidString"}}}, "PhoneNumberAssociation": {"type": "structure", "members": {"Value": {"shape": "String"}, "Name": {"shape": "PhoneNumberAssociationName"}, "AssociatedTimestamp": {"shape": "Iso8601Timestamp"}}}, "PhoneNumberAssociationList": {"type": "list", "member": {"shape": "PhoneNumberAssociation"}}, "PhoneNumberAssociationName": {"type": "string", "enum": ["VoiceConnectorId", "VoiceConnectorGroupId", "SipRuleId"]}, "PhoneNumberCapabilities": {"type": "structure", "members": {"InboundCall": {"shape": "NullableBoolean"}, "OutboundCall": {"shape": "NullableBoolean"}, "InboundSMS": {"shape": "NullableBoolean"}, "OutboundSMS": {"shape": "NullableBoolean"}, "InboundMMS": {"shape": "NullableBoolean"}, "OutboundMMS": {"shape": "NullableBoolean"}}}, "PhoneNumberCountriesList": {"type": "list", "member": {"shape": "PhoneNumberCountry"}}, "PhoneNumberCountry": {"type": "structure", "members": {"CountryCode": {"shape": "Alpha2CountryCode"}, "SupportedPhoneNumberTypes": {"shape": "PhoneNumberTypeList"}}}, "PhoneNumberError": {"type": "structure", "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString"}, "ErrorCode": {"shape": "ErrorCode"}, "ErrorMessage": {"shape": "String"}}}, "PhoneNumberErrorList": {"type": "list", "member": {"shape": "PhoneNumberError"}}, "PhoneNumberList": {"type": "list", "member": {"shape": "PhoneNumber"}}, "PhoneNumberMaxResults": {"type": "integer", "max": 500, "min": 1}, "PhoneNumberOrder": {"type": "structure", "members": {"PhoneNumberOrderId": {"shape": "GuidString"}, "ProductType": {"shape": "PhoneNumberProductType"}, "Status": {"shape": "PhoneNumberOrderStatus"}, "OrderType": {"shape": "PhoneNumberOrderType"}, "OrderedPhoneNumbers": {"shape": "OrderedPhoneNumberList"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp"}}}, "PhoneNumberOrderList": {"type": "list", "member": {"shape": "PhoneNumberOrder"}}, "PhoneNumberOrderStatus": {"type": "string", "enum": ["Processing", "Successful", "Failed", "Partial", "PendingDocuments", "Submitted", "FOC", "ChangeRequested", "Exception", "CancelRequested", "Cancelled"]}, "PhoneNumberOrderType": {"type": "string", "enum": ["New", "Porting"]}, "PhoneNumberProductType": {"type": "string", "enum": ["VoiceConnector", "SipMediaApplicationDialIn"]}, "PhoneNumberStatus": {"type": "string", "enum": ["Cancelled", "PortinCancelRequested", "PortinInProgress", "AcquireInProgress", "AcquireFailed", "Unassigned", "Assigned", "ReleaseInProgress", "DeleteInProgress", "ReleaseFailed", "DeleteFailed"]}, "PhoneNumberType": {"type": "string", "enum": ["Local", "<PERSON>ll<PERSON><PERSON>"]}, "PhoneNumberTypeList": {"type": "list", "member": {"shape": "PhoneNumberType"}}, "Port": {"type": "integer", "max": 65535, "min": 0}, "PositiveInteger": {"type": "integer", "min": 1}, "Proxy": {"type": "structure", "members": {"DefaultSessionExpiryMinutes": {"shape": "Integer"}, "Disabled": {"shape": "Boolean"}, "FallBackPhoneNumber": {"shape": "E164PhoneNumber"}, "PhoneNumberCountries": {"shape": "StringList"}}}, "ProxySession": {"type": "structure", "members": {"VoiceConnectorId": {"shape": "NonEmptyString128"}, "ProxySessionId": {"shape": "NonEmptyString128"}, "Name": {"shape": "String128"}, "Status": {"shape": "ProxySessionStatus"}, "ExpiryMinutes": {"shape": "PositiveInteger"}, "Capabilities": {"shape": "CapabilityList"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp"}, "EndedTimestamp": {"shape": "Iso8601Timestamp"}, "Participants": {"shape": "Participants"}, "NumberSelectionBehavior": {"shape": "NumberSelectionBehavior"}, "GeoMatchLevel": {"shape": "GeoMatchLevel"}, "GeoMatchParams": {"shape": "GeoMatchParams"}}}, "ProxySessionNameString": {"type": "string", "pattern": "^$|^[a-zA-Z0-9 ]{0,30}$", "sensitive": true}, "ProxySessionStatus": {"type": "string", "enum": ["Open", "InProgress", "Closed"]}, "ProxySessions": {"type": "list", "member": {"shape": "ProxySession"}}, "PutSipMediaApplicationAlexaSkillConfigurationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "location": "uri", "locationName": "sipMediaApplicationId"}, "SipMediaApplicationAlexaSkillConfiguration": {"shape": "SipMediaApplicationAlexaSkillConfiguration"}}}, "PutSipMediaApplicationAlexaSkillConfigurationResponse": {"type": "structure", "members": {"SipMediaApplicationAlexaSkillConfiguration": {"shape": "SipMediaApplicationAlexaSkillConfiguration"}}}, "PutSipMediaApplicationLoggingConfigurationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "location": "uri", "locationName": "sipMediaApplicationId"}, "SipMediaApplicationLoggingConfiguration": {"shape": "SipMediaApplicationLoggingConfiguration"}}}, "PutSipMediaApplicationLoggingConfigurationResponse": {"type": "structure", "members": {"SipMediaApplicationLoggingConfiguration": {"shape": "SipMediaApplicationLoggingConfiguration"}}}, "PutVoiceConnectorEmergencyCallingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId", "EmergencyCallingConfiguration"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}, "EmergencyCallingConfiguration": {"shape": "EmergencyCallingConfiguration"}}}, "PutVoiceConnectorEmergencyCallingConfigurationResponse": {"type": "structure", "members": {"EmergencyCallingConfiguration": {"shape": "EmergencyCallingConfiguration"}}}, "PutVoiceConnectorLoggingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId", "LoggingConfiguration"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}, "LoggingConfiguration": {"shape": "LoggingConfiguration"}}}, "PutVoiceConnectorLoggingConfigurationResponse": {"type": "structure", "members": {"LoggingConfiguration": {"shape": "LoggingConfiguration"}}}, "PutVoiceConnectorOriginationRequest": {"type": "structure", "required": ["VoiceConnectorId", "Origination"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}, "Origination": {"shape": "Origination"}}}, "PutVoiceConnectorOriginationResponse": {"type": "structure", "members": {"Origination": {"shape": "Origination"}}}, "PutVoiceConnectorProxyRequest": {"type": "structure", "required": ["DefaultSessionExpiryMinutes", "PhoneNumberPoolCountries", "VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "location": "uri", "locationName": "voiceConnectorId"}, "DefaultSessionExpiryMinutes": {"shape": "Integer"}, "PhoneNumberPoolCountries": {"shape": "CountryList"}, "FallBackPhoneNumber": {"shape": "E164PhoneNumber"}, "Disabled": {"shape": "Boolean"}}}, "PutVoiceConnectorProxyResponse": {"type": "structure", "members": {"Proxy": {"shape": "Proxy"}}}, "PutVoiceConnectorStreamingConfigurationRequest": {"type": "structure", "required": ["VoiceConnectorId", "StreamingConfiguration"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}, "StreamingConfiguration": {"shape": "StreamingConfiguration"}}}, "PutVoiceConnectorStreamingConfigurationResponse": {"type": "structure", "members": {"StreamingConfiguration": {"shape": "StreamingConfiguration"}}}, "PutVoiceConnectorTerminationCredentialsRequest": {"type": "structure", "required": ["VoiceConnectorId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}, "Credentials": {"shape": "CredentialList"}}}, "PutVoiceConnectorTerminationRequest": {"type": "structure", "required": ["VoiceConnectorId", "Termination"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}, "Termination": {"shape": "Termination"}}}, "PutVoiceConnectorTerminationResponse": {"type": "structure", "members": {"Termination": {"shape": "Termination"}}}, "ResourceLimitExceededException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 400}, "exception": true}, "RestorePhoneNumberRequest": {"type": "structure", "required": ["PhoneNumberId"], "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString", "location": "uri", "locationName": "phoneNumberId"}}}, "RestorePhoneNumberResponse": {"type": "structure", "members": {"PhoneNumber": {"shape": "PhoneNumber"}}}, "ResultMax": {"type": "integer", "max": 100, "min": 1}, "SMACreateCallArgumentsMap": {"type": "map", "key": {"shape": "SensitiveString"}, "value": {"shape": "SensitiveString"}, "max": 20, "min": 0}, "SMAUpdateCallArgumentsMap": {"type": "map", "key": {"shape": "SensitiveString"}, "value": {"shape": "SensitiveString"}, "max": 20, "min": 0}, "SearchAvailablePhoneNumbersRequest": {"type": "structure", "members": {"AreaCode": {"shape": "String", "location": "querystring", "locationName": "area-code"}, "City": {"shape": "String", "location": "querystring", "locationName": "city"}, "Country": {"shape": "Alpha2CountryCode", "location": "querystring", "locationName": "country"}, "State": {"shape": "String", "location": "querystring", "locationName": "state"}, "TollFreePrefix": {"shape": "TollFreePrefix", "location": "querystring", "locationName": "toll-free-prefix"}, "PhoneNumberType": {"shape": "PhoneNumberType", "location": "querystring", "locationName": "phone-number-type"}, "MaxResults": {"shape": "PhoneNumberMaxResults", "location": "querystring", "locationName": "max-results"}, "NextToken": {"shape": "String", "location": "querystring", "locationName": "next-token"}}}, "SearchAvailablePhoneNumbersResponse": {"type": "structure", "members": {"E164PhoneNumbers": {"shape": "E164PhoneNumberList"}, "NextToken": {"shape": "String"}}}, "SensitiveNonEmptyString": {"type": "string", "pattern": ".*\\S.*", "sensitive": true}, "SensitiveString": {"type": "string", "sensitive": true}, "SensitiveStringList": {"type": "list", "member": {"shape": "SensitiveString"}}, "ServiceFailureException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ServiceUnavailableException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 503}, "exception": true, "fault": true}, "SipApplicationPriority": {"type": "integer", "min": 1}, "SipHeadersMap": {"type": "map", "key": {"shape": "SensitiveString"}, "value": {"shape": "SensitiveString"}, "max": 20, "min": 0}, "SipMediaApplication": {"type": "structure", "members": {"SipMediaApplicationId": {"shape": "NonEmptyString"}, "AwsRegion": {"shape": "String"}, "Name": {"shape": "SipMediaApplicationName"}, "Endpoints": {"shape": "SipMediaApplicationEndpointList"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp"}}}, "SipMediaApplicationAlexaSkillConfiguration": {"type": "structure", "required": ["AlexaSkillStatus", "AlexaSkillIds"], "members": {"AlexaSkillStatus": {"shape": "AlexaSkillStatus"}, "AlexaSkillIds": {"shape": "AlexaSkillIdList"}}}, "SipMediaApplicationCall": {"type": "structure", "members": {"TransactionId": {"shape": "GuidString"}}}, "SipMediaApplicationEndpoint": {"type": "structure", "members": {"LambdaArn": {"shape": "FunctionArn"}}}, "SipMediaApplicationEndpointList": {"type": "list", "member": {"shape": "SipMediaApplicationEndpoint"}, "max": 1, "min": 1}, "SipMediaApplicationList": {"type": "list", "member": {"shape": "SipMediaApplication"}}, "SipMediaApplicationLoggingConfiguration": {"type": "structure", "members": {"EnableSipMediaApplicationMessageLogs": {"shape": "Boolean"}}}, "SipMediaApplicationName": {"type": "string", "max": 256, "min": 1}, "SipRule": {"type": "structure", "members": {"SipRuleId": {"shape": "NonEmptyString"}, "Name": {"shape": "SipRuleName"}, "Disabled": {"shape": "Boolean"}, "TriggerType": {"shape": "SipRuleTriggerType"}, "TriggerValue": {"shape": "NonEmptyString"}, "TargetApplications": {"shape": "SipRuleTargetApplicationList"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp"}}}, "SipRuleList": {"type": "list", "member": {"shape": "SipRule"}}, "SipRuleName": {"type": "string", "max": 256, "min": 1}, "SipRuleTargetApplication": {"type": "structure", "members": {"SipMediaApplicationId": {"shape": "NonEmptyString"}, "Priority": {"shape": "SipApplicationPriority"}, "AwsRegion": {"shape": "String"}}}, "SipRuleTargetApplicationList": {"type": "list", "member": {"shape": "SipRuleTargetApplication"}, "max": 25, "min": 1}, "SipRuleTriggerType": {"type": "string", "enum": ["ToPhoneNumber", "RequestUriHostname"]}, "StreamingConfiguration": {"type": "structure", "required": ["DataRetentionInHours", "Disabled"], "members": {"DataRetentionInHours": {"shape": "DataRetentionInHours"}, "Disabled": {"shape": "Boolean"}, "StreamingNotificationTargets": {"shape": "StreamingNotificationTargetList"}}}, "StreamingNotificationTarget": {"type": "structure", "members": {"NotificationTarget": {"shape": "NotificationTarget"}}}, "StreamingNotificationTargetList": {"type": "list", "member": {"shape": "StreamingNotificationTarget"}, "max": 3, "min": 1}, "String": {"type": "string"}, "String128": {"type": "string", "max": 128}, "StringList": {"type": "list", "member": {"shape": "String"}}, "Termination": {"type": "structure", "members": {"CpsLimit": {"shape": "CpsLimit"}, "DefaultPhoneNumber": {"shape": "E164PhoneNumber"}, "CallingRegions": {"shape": "CallingRegionList"}, "CidrAllowedList": {"shape": "StringList"}, "Disabled": {"shape": "Boolean"}}}, "TerminationHealth": {"type": "structure", "members": {"Timestamp": {"shape": "Iso8601Timestamp"}, "Source": {"shape": "String"}}}, "ThrottledClientException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 429}, "exception": true}, "TollFreePrefix": {"type": "string", "max": 3, "min": 3, "pattern": "^8(00|33|44|55|66|77|88)$"}, "UnauthorizedClientException": {"type": "structure", "members": {}, "error": {"httpStatusCode": 401}, "exception": true}, "UpdateGlobalSettingsRequest": {"type": "structure", "members": {"VoiceConnector": {"shape": "VoiceConnectorSettings"}}}, "UpdatePhoneNumberRequest": {"type": "structure", "required": ["PhoneNumberId"], "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString", "location": "uri", "locationName": "phoneNumberId"}, "ProductType": {"shape": "PhoneNumberProductType"}, "CallingName": {"shape": "CallingName"}}}, "UpdatePhoneNumberRequestItem": {"type": "structure", "required": ["PhoneNumberId"], "members": {"PhoneNumberId": {"shape": "SensitiveNonEmptyString"}, "ProductType": {"shape": "PhoneNumberProductType"}, "CallingName": {"shape": "CallingName"}}}, "UpdatePhoneNumberRequestItemList": {"type": "list", "member": {"shape": "UpdatePhoneNumberRequestItem"}}, "UpdatePhoneNumberResponse": {"type": "structure", "members": {"PhoneNumber": {"shape": "PhoneNumber"}}}, "UpdatePhoneNumberSettingsRequest": {"type": "structure", "required": ["CallingName"], "members": {"CallingName": {"shape": "CallingName"}}}, "UpdateProxySessionRequest": {"type": "structure", "required": ["Capabilities", "VoiceConnectorId", "ProxySessionId"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString128", "location": "uri", "locationName": "voiceConnectorId"}, "ProxySessionId": {"shape": "NonEmptyString128", "location": "uri", "locationName": "proxySessionId"}, "Capabilities": {"shape": "CapabilityList"}, "ExpiryMinutes": {"shape": "PositiveInteger"}}}, "UpdateProxySessionResponse": {"type": "structure", "members": {"ProxySession": {"shape": "ProxySession"}}}, "UpdateSipMediaApplicationCallRequest": {"type": "structure", "required": ["SipMediaApplicationId", "TransactionId", "Arguments"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "location": "uri", "locationName": "sipMediaApplicationId"}, "TransactionId": {"shape": "NonEmptyString", "location": "uri", "locationName": "transactionId"}, "Arguments": {"shape": "SMAUpdateCallArgumentsMap"}}}, "UpdateSipMediaApplicationCallResponse": {"type": "structure", "members": {"SipMediaApplicationCall": {"shape": "SipMediaApplicationCall"}}}, "UpdateSipMediaApplicationRequest": {"type": "structure", "required": ["SipMediaApplicationId"], "members": {"SipMediaApplicationId": {"shape": "NonEmptyString", "location": "uri", "locationName": "sipMediaApplicationId"}, "Name": {"shape": "SipMediaApplicationName"}, "Endpoints": {"shape": "SipMediaApplicationEndpointList"}}}, "UpdateSipMediaApplicationResponse": {"type": "structure", "members": {"SipMediaApplication": {"shape": "SipMediaApplication"}}}, "UpdateSipRuleRequest": {"type": "structure", "required": ["SipRuleId", "Name"], "members": {"SipRuleId": {"shape": "NonEmptyString", "location": "uri", "locationName": "sipRuleId"}, "Name": {"shape": "SipRuleName"}, "Disabled": {"shape": "NullableBoolean"}, "TargetApplications": {"shape": "SipRuleTargetApplicationList"}}}, "UpdateSipRuleResponse": {"type": "structure", "members": {"SipRule": {"shape": "SipRule"}}}, "UpdateVoiceConnectorGroupRequest": {"type": "structure", "required": ["VoiceConnectorGroupId", "Name", "VoiceConnectorItems"], "members": {"VoiceConnectorGroupId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorGroupId"}, "Name": {"shape": "VoiceConnectorGroupName"}, "VoiceConnectorItems": {"shape": "VoiceConnectorItemList"}}}, "UpdateVoiceConnectorGroupResponse": {"type": "structure", "members": {"VoiceConnectorGroup": {"shape": "VoiceConnectorGroup"}}}, "UpdateVoiceConnectorRequest": {"type": "structure", "required": ["VoiceConnectorId", "Name", "RequireEncryption"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString", "location": "uri", "locationName": "voiceConnectorId"}, "Name": {"shape": "VoiceConnectorName"}, "RequireEncryption": {"shape": "Boolean"}}}, "UpdateVoiceConnectorResponse": {"type": "structure", "members": {"VoiceConnector": {"shape": "VoiceConnector"}}}, "ValidateE911AddressRequest": {"type": "structure", "required": ["AwsAccountId", "StreetNumber", "StreetInfo", "City", "State", "Country", "PostalCode"], "members": {"AwsAccountId": {"shape": "NonEmptyString"}, "StreetNumber": {"shape": "SensitiveNonEmptyString"}, "StreetInfo": {"shape": "SensitiveNonEmptyString"}, "City": {"shape": "SensitiveNonEmptyString"}, "State": {"shape": "SensitiveNonEmptyString"}, "Country": {"shape": "SensitiveNonEmptyString"}, "PostalCode": {"shape": "SensitiveNonEmptyString"}}}, "ValidateE911AddressResponse": {"type": "structure", "members": {"ValidationResult": {"shape": "ValidationResult"}, "AddressExternalId": {"shape": "String"}, "Address": {"shape": "Address"}, "CandidateAddressList": {"shape": "CandidateAddressList"}}}, "ValidationResult": {"type": "integer", "max": 2, "min": 0}, "VoiceConnector": {"type": "structure", "members": {"VoiceConnectorId": {"shape": "NonEmptyString"}, "AwsRegion": {"shape": "VoiceConnectorAwsRegion"}, "Name": {"shape": "VoiceConnectorName"}, "OutboundHostName": {"shape": "String"}, "RequireEncryption": {"shape": "Boolean"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp"}, "VoiceConnectorArn": {"shape": "NonEmptyString"}}}, "VoiceConnectorAwsRegion": {"type": "string", "enum": ["us-east-1", "us-west-2", "ca-central-1", "eu-central-1", "eu-west-1", "eu-west-2", "ap-northeast-2", "ap-northeast-1", "ap-southeast-1", "ap-southeast-2"]}, "VoiceConnectorAwsRegionList": {"type": "list", "member": {"shape": "VoiceConnectorAwsRegion"}}, "VoiceConnectorGroup": {"type": "structure", "members": {"VoiceConnectorGroupId": {"shape": "NonEmptyString"}, "Name": {"shape": "VoiceConnectorGroupName"}, "VoiceConnectorItems": {"shape": "VoiceConnectorItemList"}, "CreatedTimestamp": {"shape": "Iso8601Timestamp"}, "UpdatedTimestamp": {"shape": "Iso8601Timestamp"}, "VoiceConnectorGroupArn": {"shape": "NonEmptyString"}}}, "VoiceConnectorGroupList": {"type": "list", "member": {"shape": "VoiceConnectorGroup"}}, "VoiceConnectorGroupName": {"type": "string", "max": 256, "min": 1}, "VoiceConnectorItem": {"type": "structure", "required": ["VoiceConnectorId", "Priority"], "members": {"VoiceConnectorId": {"shape": "NonEmptyString"}, "Priority": {"shape": "VoiceConnectorItemPriority"}}}, "VoiceConnectorItemList": {"type": "list", "member": {"shape": "VoiceConnectorItem"}}, "VoiceConnectorItemPriority": {"type": "integer", "max": 99, "min": 1}, "VoiceConnectorList": {"type": "list", "member": {"shape": "VoiceConnector"}}, "VoiceConnectorName": {"type": "string", "max": 256, "min": 1}, "VoiceConnectorSettings": {"type": "structure", "members": {"CdrBucket": {"shape": "String"}}}}}