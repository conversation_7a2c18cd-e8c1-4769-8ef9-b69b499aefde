{"version": "2.0", "metadata": {"apiVersion": "2020-08-23", "endpointPrefix": "appflow", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon Appflow", "serviceId": "Appflow", "signatureVersion": "v4", "signingName": "appflow", "uid": "appflow-2020-08-23"}, "operations": {"CreateConnectorProfile": {"name": "CreateConnectorProfile", "http": {"method": "POST", "requestUri": "/create-connector-profile"}, "input": {"shape": "CreateConnectorProfileRequest"}, "output": {"shape": "CreateConnectorProfileResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConnectorAuthenticationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Creates a new connector profile associated with your Amazon Web Services account. There is a soft quota of 100 connector profiles per Amazon Web Services account. If you need more connector profiles than this quota allows, you can submit a request to the Amazon AppFlow team through the Amazon AppFlow support channel. In each connector profile that you create, you can provide the credentials and properties for only one connector.</p>"}, "CreateFlow": {"name": "CreateFlow", "http": {"method": "POST", "requestUri": "/create-flow"}, "input": {"shape": "CreateFlowRequest"}, "output": {"shape": "CreateFlowResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ConnectorAuthenticationException"}, {"shape": "ConnectorServerException"}], "documentation": "<p> Enables your application to create a new flow using Amazon AppFlow. You must create a connector profile before calling this API. Please note that the Request Syntax below shows syntax for multiple destinations, however, you can only transfer data to one item in this list at a time. Amazon AppFlow does not currently support flows to multiple destinations at once. </p>"}, "DeleteConnectorProfile": {"name": "DeleteConnectorProfile", "http": {"method": "POST", "requestUri": "/delete-connector-profile"}, "input": {"shape": "DeleteConnectorProfileRequest"}, "output": {"shape": "DeleteConnectorProfileResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p> Enables you to delete an existing connector profile. </p>"}, "DeleteFlow": {"name": "DeleteFlow", "http": {"method": "POST", "requestUri": "/delete-flow"}, "input": {"shape": "DeleteFlowRequest"}, "output": {"shape": "DeleteFlowResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p> Enables your application to delete an existing flow. Before deleting the flow, Amazon AppFlow validates the request by checking the flow configuration and status. You can delete flows one at a time. </p>"}, "DescribeConnector": {"name": "DescribeConnector", "http": {"method": "POST", "requestUri": "/describe-connector"}, "input": {"shape": "DescribeConnectorRequest"}, "output": {"shape": "DescribeConnectorResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Describes the given custom connector registered in your Amazon Web Services account. This API can be used for custom connectors that are registered in your account and also for Amazon authored connectors.</p>"}, "DescribeConnectorEntity": {"name": "DescribeConnectorEntity", "http": {"method": "POST", "requestUri": "/describe-connector-entity"}, "input": {"shape": "DescribeConnectorEntityRequest"}, "output": {"shape": "DescribeConnectorEntityResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConnectorAuthenticationException"}, {"shape": "ConnectorServerException"}, {"shape": "InternalServerException"}], "documentation": "<p> Provides details regarding the entity used with the connector, with a description of the data model for each field in that entity. </p>"}, "DescribeConnectorProfiles": {"name": "DescribeConnectorProfiles", "http": {"method": "POST", "requestUri": "/describe-connector-profiles"}, "input": {"shape": "DescribeConnectorProfilesRequest"}, "output": {"shape": "DescribeConnectorProfilesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Returns a list of <code>connector-profile</code> details matching the provided <code>connector-profile</code> names and <code>connector-types</code>. Both input lists are optional, and you can use them to filter the result. </p> <p>If no names or <code>connector-types</code> are provided, returns all connector profiles in a paginated form. If there is no match, this operation returns an empty list.</p>"}, "DescribeConnectors": {"name": "DescribeConnectors", "http": {"method": "POST", "requestUri": "/describe-connectors"}, "input": {"shape": "DescribeConnectorsRequest"}, "output": {"shape": "DescribeConnectorsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Describes the connectors vended by Amazon AppFlow for specified connector types. If you don't specify a connector type, this operation describes all connectors vended by Amazon AppFlow. If there are more connectors than can be returned in one page, the response contains a <code>nextToken</code> object, which can be be passed in to the next call to the <code>DescribeConnectors</code> API operation to retrieve the next page. </p>"}, "DescribeFlow": {"name": "DescribeFlow", "http": {"method": "POST", "requestUri": "/describe-flow"}, "input": {"shape": "DescribeFlowRequest"}, "output": {"shape": "DescribeFlowResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p> Provides a description of the specified flow. </p>"}, "DescribeFlowExecutionRecords": {"name": "DescribeFlowExecutionRecords", "http": {"method": "POST", "requestUri": "/describe-flow-execution-records"}, "input": {"shape": "DescribeFlowExecutionRecordsRequest"}, "output": {"shape": "DescribeFlowExecutionRecordsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}], "documentation": "<p> Fetches the execution history of the flow. </p>"}, "ListConnectorEntities": {"name": "ListConnectorEntities", "http": {"method": "POST", "requestUri": "/list-connector-entities"}, "input": {"shape": "ListConnectorEntitiesRequest"}, "output": {"shape": "ListConnectorEntitiesResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConnectorAuthenticationException"}, {"shape": "ConnectorServerException"}, {"shape": "InternalServerException"}], "documentation": "<p> Returns the list of available connector entities supported by Amazon AppFlow. For example, you can query Salesforce for <i>Account</i> and <i>Opportunity</i> entities, or query ServiceNow for the <i>Incident</i> entity. </p>"}, "ListConnectors": {"name": "ListConnectors", "http": {"method": "POST", "requestUri": "/list-connectors"}, "input": {"shape": "ListConnectorsRequest"}, "output": {"shape": "ListConnectorsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns the list of all registered custom connectors in your Amazon Web Services account. This API lists only custom connectors registered in this account, not the Amazon Web Services authored connectors. </p>"}, "ListFlows": {"name": "ListFlows", "http": {"method": "POST", "requestUri": "/list-flows"}, "input": {"shape": "ListFlowsRequest"}, "output": {"shape": "ListFlowsResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Lists all of the flows associated with your account. </p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Retrieves the tags that are associated with a specified flow. </p>"}, "RegisterConnector": {"name": "RegisterConnector", "http": {"method": "POST", "requestUri": "/register-connector"}, "input": {"shape": "RegisterConnectorRequest"}, "output": {"shape": "RegisterConnectorResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ConnectorServerException"}, {"shape": "ConnectorAuthenticationException"}], "documentation": "<p>Registers a new custom connector with your Amazon Web Services account. Before you can register the connector, you must deploy the associated AWS lambda function in your account.</p>"}, "StartFlow": {"name": "StartFlow", "http": {"method": "POST", "requestUri": "/start-flow"}, "input": {"shape": "StartFlowRequest"}, "output": {"shape": "StartFlowResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}], "documentation": "<p> Activates an existing flow. For on-demand flows, this operation runs the flow immediately. For schedule and event-triggered flows, this operation activates the flow. </p>"}, "StopFlow": {"name": "StopFlow", "http": {"method": "POST", "requestUri": "/stop-flow"}, "input": {"shape": "StopFlowRequest"}, "output": {"shape": "StopFlowResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ResourceNotFoundException"}, {"shape": "UnsupportedOperationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Deactivates the existing flow. For on-demand flows, this operation returns an <code>unsupportedOperationException</code> error message. For schedule and event-triggered flows, this operation deactivates the flow. </p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Applies a tag to the specified flow. </p>"}, "UnregisterConnector": {"name": "UnregisterConnector", "http": {"method": "POST", "requestUri": "/unregister-connector"}, "input": {"shape": "UnregisterConnectorRequest"}, "output": {"shape": "UnregisterConnectorResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "InternalServerException"}], "documentation": "<p>Unregisters the custom connector registered in your account that matches the connector label provided in the request.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p> Removes a tag from the specified flow. </p>"}, "UpdateConnectorProfile": {"name": "UpdateConnectorProfile", "http": {"method": "POST", "requestUri": "/update-connector-profile"}, "input": {"shape": "UpdateConnectorProfileRequest"}, "output": {"shape": "UpdateConnectorProfileResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConflictException"}, {"shape": "ConnectorAuthenticationException"}, {"shape": "InternalServerException"}], "documentation": "<p> Updates a given connector profile associated with your account. </p>"}, "UpdateConnectorRegistration": {"name": "UpdateConnectorRegistration", "http": {"method": "POST", "requestUri": "/update-connector-registration"}, "input": {"shape": "UpdateConnectorRegistrationRequest"}, "output": {"shape": "UpdateConnectorRegistrationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}, {"shape": "ConnectorServerException"}, {"shape": "ConnectorAuthenticationException"}], "documentation": "<p>Updates a custom connector that you've previously registered. This operation updates the connector with one of the following:</p> <ul> <li> <p>The latest version of the AWS Lambda function that's assigned to the connector</p> </li> <li> <p>A new AWS Lambda function that you specify</p> </li> </ul>"}, "UpdateFlow": {"name": "UpdateFlow", "http": {"method": "POST", "requestUri": "/update-flow"}, "input": {"shape": "UpdateFlowRequest"}, "output": {"shape": "UpdateFlowResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ConflictException"}, {"shape": "ConnectorAuthenticationException"}, {"shape": "ConnectorServerException"}, {"shape": "InternalServerException"}], "documentation": "<p> Updates an existing flow. </p>"}}, "shapes": {"ARN": {"type": "string", "max": 512, "pattern": "arn:aws:.*:.*:[0-9]+:.*"}, "AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>AppFlow/Requester has invalid or missing permissions.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "AccessKeyId": {"type": "string", "max": 256, "pattern": "\\S+", "sensitive": true}, "AccessToken": {"type": "string", "max": 4096, "pattern": "\\S+", "sensitive": true}, "AccountName": {"type": "string", "max": 512, "pattern": "\\S+"}, "AggregationConfig": {"type": "structure", "members": {"aggregationType": {"shape": "AggregationType", "documentation": "<p> Specifies whether Amazon AppFlow aggregates the flow records into a single file, or leave them unaggregated. </p>"}, "targetFileSize": {"shape": "<PERSON>", "documentation": "<p>The desired file size, in MB, for each output file that Amazon AppFlow writes to the flow destination. For each file, Amazon AppFlow attempts to achieve the size that you specify. The actual file sizes might differ from this target based on the number and size of the records that each file contains.</p>"}}, "documentation": "<p> The aggregation settings that you can use to customize the output format of your flow data. </p>"}, "AggregationType": {"type": "string", "enum": ["None", "SingleFile"]}, "AmplitudeConnectorOperator": {"type": "string", "enum": ["BETWEEN"]}, "AmplitudeConnectorProfileCredentials": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "secret<PERSON>ey"], "members": {"apiKey": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p> A unique alphanumeric identifier used to authenticate a user, developer, or calling program to your API. </p>"}, "secretKey": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p> The Secret Access Key portion of the credentials. </p>"}}, "documentation": "<p> The connector-specific credentials required when using Amplitude. </p>"}, "AmplitudeConnectorProfileProperties": {"type": "structure", "members": {}, "documentation": "<p> The connector-specific profile properties required when using Amplitude. </p>"}, "AmplitudeMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to Amplitude. </p>"}, "AmplitudeSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Amplitude flow source. </p>"}}, "documentation": "<p> The properties that are applied when Amplitude is being used as a source. </p>"}, "ApiKey": {"type": "string", "max": 256, "pattern": "\\S+", "sensitive": true}, "ApiKeyCredentials": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"apiKey": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The API key required for API key authentication.</p>"}, "apiSecretKey": {"shape": "ApiSecret<PERSON>ey", "documentation": "<p>The API secret key required for API key authentication.</p>"}}, "documentation": "<p>The API key credentials required for API key authentication.</p>"}, "ApiSecretKey": {"type": "string", "max": 256, "pattern": "\\S+", "sensitive": true}, "ApiToken": {"type": "string", "max": 256, "pattern": "\\S+"}, "ApiVersion": {"type": "string", "max": 256, "pattern": "\\S+"}, "ApplicationHostUrl": {"type": "string", "max": 256, "pattern": "^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]"}, "ApplicationKey": {"type": "string", "max": 512, "pattern": "\\S+"}, "ApplicationServicePath": {"type": "string", "max": 512, "pattern": "\\S+"}, "ApplicationType": {"type": "string", "max": 512, "pattern": "\\S+"}, "AuthCode": {"type": "string", "max": 2048, "pattern": "\\S+"}, "AuthCodeUrl": {"type": "string", "max": 256, "pattern": "^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]"}, "AuthCodeUrlList": {"type": "list", "member": {"shape": "AuthCodeUrl"}}, "AuthParameter": {"type": "structure", "members": {"key": {"shape": "Key", "documentation": "<p>The authentication key required to authenticate with the connector.</p>"}, "isRequired": {"shape": "Boolean", "documentation": "<p>Indicates whether this authentication parameter is required.</p>"}, "label": {"shape": "Label", "documentation": "<p>Label used for authentication parameter.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description about the authentication parameter.</p>"}, "isSensitiveField": {"shape": "Boolean", "documentation": "<p>Indicates whether this authentication parameter is a sensitive field.</p>"}, "connectorSuppliedValues": {"shape": "ConnectorSuppliedValueList", "documentation": "<p>Contains default values for this authentication parameter that are supplied by the connector.</p>"}}, "documentation": "<p>Information about required authentication parameters.</p>"}, "AuthParameterList": {"type": "list", "member": {"shape": "AuthParameter"}}, "AuthenticationConfig": {"type": "structure", "members": {"isBasicAuthSupported": {"shape": "Boolean", "documentation": "<p>Indicates whether basic authentication is supported by the connector.</p>"}, "isApiKeyAuthSupported": {"shape": "Boolean", "documentation": "<p>Indicates whether API key authentication is supported by the connector</p>"}, "isOAuth2Supported": {"shape": "Boolean", "documentation": "<p>Indicates whether OAuth 2.0 authentication is supported by the connector.</p>"}, "isCustomAuthSupported": {"shape": "Boolean", "documentation": "<p>Indicates whether custom authentication is supported by the connector</p>"}, "oAuth2Defaults": {"shape": "OAuth2Defaults", "documentation": "<p>Contains the default values required for OAuth 2.0 authentication.</p>"}, "customAuthConfigs": {"shape": "CustomAuthConfigList", "documentation": "<p>Contains information required for custom authentication.</p>"}}, "documentation": "<p>Contains information about the authentication config that the connector supports.</p>"}, "AuthenticationType": {"type": "string", "enum": ["OAUTH2", "APIKEY", "BASIC", "CUSTOM"]}, "BasicAuthCredentials": {"type": "structure", "required": ["username", "password"], "members": {"username": {"shape": "Username", "documentation": "<p> The username to use to connect to a resource. </p>"}, "password": {"shape": "Password", "documentation": "<p> The password to use to connect to a resource.</p>"}}, "documentation": "<p> The basic auth credentials required for basic authentication. </p>"}, "Boolean": {"type": "boolean"}, "BucketName": {"type": "string", "max": 63, "min": 3, "pattern": "\\S+"}, "BucketPrefix": {"type": "string", "max": 512, "pattern": ".*"}, "BusinessUnitId": {"type": "string", "max": 18, "pattern": "\\S+"}, "CatalogType": {"type": "string", "enum": ["GLUE"]}, "ClientCredentialsArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws:secretsmanager:.*:[0-9]+:.*", "sensitive": true}, "ClientId": {"type": "string", "max": 512, "pattern": "\\S+"}, "ClientNumber": {"type": "string", "max": 3, "min": 3, "pattern": "^\\d{3}$"}, "ClientSecret": {"type": "string", "max": 512, "pattern": "\\S+", "sensitive": true}, "ClusterIdentifier": {"type": "string", "max": 512, "pattern": "\\S+"}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p> There was a conflict when processing the request (for example, a flow with the given name already exists within the account. Check for conflicting resource names and try again. </p>", "error": {"httpStatusCode": 409}, "exception": true}, "ConnectionMode": {"type": "string", "enum": ["Public", "Private"]}, "ConnectorAuthenticationException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p> An error occurred when authenticating with the connector endpoint. </p>", "error": {"httpStatusCode": 401}, "exception": true}, "ConnectorConfiguration": {"type": "structure", "members": {"canUseAsSource": {"shape": "Boolean", "documentation": "<p> Specifies whether the connector can be used as a source. </p>"}, "canUseAsDestination": {"shape": "Boolean", "documentation": "<p> Specifies whether the connector can be used as a destination. </p>"}, "supportedDestinationConnectors": {"shape": "ConnectorTypeList", "documentation": "<p> Lists the connectors that are available for use as destinations. </p>"}, "supportedSchedulingFrequencies": {"shape": "SchedulingFrequencyTypeList", "documentation": "<p> Specifies the supported flow frequency for that connector. </p>"}, "isPrivateLinkEnabled": {"shape": "Boolean", "documentation": "<p> Specifies if PrivateLink is enabled for that connector. </p>"}, "isPrivateLinkEndpointUrlRequired": {"shape": "Boolean", "documentation": "<p> Specifies if a PrivateLink endpoint URL is required. </p>"}, "supportedTriggerTypes": {"shape": "TriggerTypeList", "documentation": "<p> Specifies the supported trigger types for the flow. </p>"}, "connectorMetadata": {"shape": "ConnectorMetadata", "documentation": "<p> Specifies connector-specific metadata such as <code>oAuthScopes</code>, <code>supportedRegions</code>, <code>privateLinkServiceUrl</code>, and so on. </p>"}, "connectorType": {"shape": "ConnectorType", "documentation": "<p>The connector type.</p>"}, "connectorLabel": {"shape": "ConnectorLabel", "documentation": "<p>The label used for registering the connector.</p>"}, "connectorDescription": {"shape": "ConnectorDescription", "documentation": "<p>A description about the connector.</p>"}, "connectorOwner": {"shape": "ConnectorOwner", "documentation": "<p>The owner who developed the connector.</p>"}, "connectorName": {"shape": "ConnectorName", "documentation": "<p>The connector name.</p>"}, "connectorVersion": {"shape": "ConnectorVersion", "documentation": "<p>The connector version.</p>"}, "connectorArn": {"shape": "ARN", "documentation": "<p>The Amazon Resource Name (ARN) for the registered connector.</p>"}, "connectorModes": {"shape": "ConnectorModeList", "documentation": "<p>The connection modes that the connector supports.</p>"}, "authenticationConfig": {"shape": "AuthenticationConfig", "documentation": "<p>The authentication config required for the connector.</p>"}, "connectorRuntimeSettings": {"shape": "ConnectorRuntimeSettingList", "documentation": "<p>The required connector runtime settings.</p>"}, "supportedApiVersions": {"shape": "SupportedApiVersionList", "documentation": "<p>A list of API versions that are supported by the connector.</p>"}, "supportedOperators": {"shape": "SupportedOperatorList", "documentation": "<p>A list of operators supported by the connector.</p>"}, "supportedWriteOperations": {"shape": "SupportedWriteOperationList", "documentation": "<p>A list of write operations supported by the connector.</p>"}, "connectorProvisioningType": {"shape": "ConnectorProvisioningType", "documentation": "<p>The provisioning type used to register the connector.</p>"}, "connectorProvisioningConfig": {"shape": "ConnectorProvisioningConfig", "documentation": "<p>The configuration required for registering the connector.</p>"}, "logoURL": {"shape": "LogoURL", "documentation": "<p>Logo URL of the connector.</p>"}, "registeredAt": {"shape": "Date", "documentation": "<p>The date on which the connector was registered.</p>"}, "registeredBy": {"shape": "RegisteredBy", "documentation": "<p>Information about who registered the connector.</p>"}}, "documentation": "<p> The configuration settings related to a given connector. </p>"}, "ConnectorConfigurationsMap": {"type": "map", "key": {"shape": "ConnectorType"}, "value": {"shape": "ConnectorConfiguration"}}, "ConnectorDescription": {"type": "string", "max": 2048, "pattern": "[\\w!@#\\-.?,\\s]*"}, "ConnectorDetail": {"type": "structure", "members": {"connectorDescription": {"shape": "ConnectorDescription", "documentation": "<p>A description about the registered connector.</p>"}, "connectorName": {"shape": "ConnectorName", "documentation": "<p>The name of the connector.</p>"}, "connectorOwner": {"shape": "ConnectorOwner", "documentation": "<p>The owner of the connector.</p>"}, "connectorVersion": {"shape": "ConnectorVersion", "documentation": "<p>The connector version.</p>"}, "applicationType": {"shape": "ApplicationType", "documentation": "<p>The application type of the connector.</p>"}, "connectorType": {"shape": "ConnectorType", "documentation": "<p>The connector type.</p>"}, "connectorLabel": {"shape": "ConnectorLabel", "documentation": "<p>A label used for the connector.</p>"}, "registeredAt": {"shape": "Date", "documentation": "<p>The time at which the connector was registered.</p>"}, "registeredBy": {"shape": "RegisteredBy", "documentation": "<p>The user who registered the connector.</p>"}, "connectorProvisioningType": {"shape": "ConnectorProvisioningType", "documentation": "<p>The provisioning type that the connector uses.</p>"}, "connectorModes": {"shape": "ConnectorModeList", "documentation": "<p>The connection mode that the connector supports.</p>"}}, "documentation": "<p>Information about the registered connector.</p>"}, "ConnectorEntity": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "Name", "documentation": "<p> The name of the connector entity. </p>"}, "label": {"shape": "Label", "documentation": "<p> The label applied to the connector entity. </p>"}, "hasNestedEntities": {"shape": "Boolean", "documentation": "<p> Specifies whether the connector entity is a parent or a category and has more entities nested underneath it. If another call is made with <code>entitiesPath = \"the_current_entity_name_with_hasNestedEntities_true\"</code>, then it returns the nested entities underneath it. This provides a way to retrieve all supported entities in a recursive fashion. </p>"}}, "documentation": "<p> The high-level entity that can be queried in Amazon AppFlow. For example, a Salesforce entity might be an <i>Account</i> or <i>Opportunity</i>, whereas a ServiceNow entity might be an <i>Incident</i>. </p>"}, "ConnectorEntityField": {"type": "structure", "required": ["identifier"], "members": {"identifier": {"shape": "Identifier", "documentation": "<p> The unique identifier of the connector field. </p>"}, "parentIdentifier": {"shape": "Identifier", "documentation": "<p>The parent identifier of the connector field.</p>"}, "label": {"shape": "Label", "documentation": "<p> The label applied to a connector entity field. </p>"}, "isPrimaryKey": {"shape": "Boolean", "documentation": "<p>Booelan value that indicates whether this field can be used as a primary key.</p>"}, "defaultValue": {"shape": "String", "documentation": "<p>Default value that can be assigned to this field.</p>"}, "isDeprecated": {"shape": "Boolean", "documentation": "<p>Booelan value that indicates whether this field is deprecated or not.</p>"}, "supportedFieldTypeDetails": {"shape": "SupportedFieldTypeDetails", "documentation": "<p> Contains details regarding the supported <code>FieldType</code>, including the corresponding <code>filterOperators</code> and <code>supportedValues</code>. </p>"}, "description": {"shape": "Description", "documentation": "<p> A description of the connector entity field. </p>"}, "sourceProperties": {"shape": "SourceFieldProperties", "documentation": "<p> The properties that can be applied to a field when the connector is being used as a source. </p>"}, "destinationProperties": {"shape": "DestinationFieldProperties", "documentation": "<p> The properties applied to a field when the connector is being used as a destination. </p>"}, "customProperties": {"shape": "CustomProperties", "documentation": "<p>A map that has specific properties related to the ConnectorEntityField.</p>"}}, "documentation": "<p> Describes the data model of a connector field. For example, for an <i>account</i> entity, the fields would be <i>account name</i>, <i>account ID</i>, and so on. </p>"}, "ConnectorEntityFieldList": {"type": "list", "member": {"shape": "ConnectorEntityField"}}, "ConnectorEntityList": {"type": "list", "member": {"shape": "ConnectorEntity"}}, "ConnectorEntityMap": {"type": "map", "key": {"shape": "Group"}, "value": {"shape": "ConnectorEntityList"}}, "ConnectorLabel": {"type": "string", "max": 256, "pattern": "[a-zA-Z0-9][\\w!@#.-]+"}, "ConnectorList": {"type": "list", "member": {"shape": "ConnectorDetail"}}, "ConnectorMetadata": {"type": "structure", "members": {"Amplitude": {"shape": "AmplitudeMetadata", "documentation": "<p> The connector metadata specific to Amplitude. </p>"}, "Datadog": {"shape": "DatadogMetadata", "documentation": "<p> The connector metadata specific to Datadog. </p>"}, "Dynatrace": {"shape": "DynatraceMetadata", "documentation": "<p> The connector metadata specific to Dynatrace. </p>"}, "GoogleAnalytics": {"shape": "GoogleAnalyticsMetadata", "documentation": "<p> The connector metadata specific to Google Analytics. </p>"}, "InforNexus": {"shape": "InforNexusMetadata", "documentation": "<p> The connector metadata specific to Infor Nexus. </p>"}, "Marketo": {"shape": "MarketoMetadata", "documentation": "<p> The connector metadata specific to Marketo. </p>"}, "Redshift": {"shape": "RedshiftMetadata", "documentation": "<p> The connector metadata specific to Amazon Redshift. </p>"}, "S3": {"shape": "S3Metadata", "documentation": "<p> The connector metadata specific to Amazon S3. </p>"}, "Salesforce": {"shape": "SalesforceMetadata", "documentation": "<p> The connector metadata specific to Salesforce. </p>"}, "ServiceNow": {"shape": "ServiceNowMetadata", "documentation": "<p> The connector metadata specific to ServiceNow. </p>"}, "Singular": {"shape": "SingularMetadata", "documentation": "<p> The connector metadata specific to Singular. </p>"}, "Slack": {"shape": "SlackMetadata", "documentation": "<p> The connector metadata specific to Slack. </p>"}, "Snowflake": {"shape": "SnowflakeMetadata", "documentation": "<p> The connector metadata specific to Snowflake. </p>"}, "Trendmicro": {"shape": "TrendmicroMetadata", "documentation": "<p> The connector metadata specific to Trend Micro. </p>"}, "Veeva": {"shape": "VeevaMetadata", "documentation": "<p> The connector metadata specific to <PERSON><PERSON><PERSON>. </p>"}, "Zendesk": {"shape": "ZendeskMetadata", "documentation": "<p> The connector metadata specific to Zendesk. </p>"}, "EventBridge": {"shape": "EventBridgeMetadata", "documentation": "<p> The connector metadata specific to Amazon EventBridge. </p>"}, "Upsolver": {"shape": "UpsolverMetadata", "documentation": "<p> The connector metadata specific to Upsolver. </p>"}, "CustomerProfiles": {"shape": "CustomerProfilesMetadata", "documentation": "<p> The connector metadata specific to Amazon Connect Customer Profiles. </p>"}, "Honeycode": {"shape": "HoneycodeMetadata", "documentation": "<p> The connector metadata specific to Amazon Honeycode. </p>"}, "SAPOData": {"shape": "SAPODataMetadata"}, "Pardot": {"shape": "PardotMetadata", "documentation": "<p>The connector metadata specific to Salesforce Pardot.</p>"}}, "documentation": "<p> A structure to specify connector-specific metadata such as <code>oAuthScopes</code>, <code>supportedRegions</code>, <code>privateLinkServiceUrl</code>, and so on. </p>"}, "ConnectorMode": {"type": "string", "max": 256, "pattern": "\\S+"}, "ConnectorModeList": {"type": "list", "member": {"shape": "ConnectorMode"}}, "ConnectorName": {"type": "string", "max": 256, "pattern": ".*"}, "ConnectorOAuthRequest": {"type": "structure", "members": {"authCode": {"shape": "AuthCode", "documentation": "<p> The code provided by the connector when it has been authenticated via the connected app. </p>"}, "redirectUri": {"shape": "RedirectUri", "documentation": "<p> The URL to which the authentication server redirects the browser after authorization has been granted. </p>"}}, "documentation": "<p> Used by select connectors for which the OAuth workflow is supported, such as Salesforce, Google Analytics, Marketo, Zendesk, and Slack. </p>"}, "ConnectorOperator": {"type": "structure", "members": {"Amplitude": {"shape": "AmplitudeConnectorOperator", "documentation": "<p> The operation to be performed on the provided Amplitude source fields. </p>"}, "Datadog": {"shape": "DatadogConnectorOperator", "documentation": "<p> The operation to be performed on the provided Datadog source fields. </p>"}, "Dynatrace": {"shape": "DynatraceConnectorOperator", "documentation": "<p> The operation to be performed on the provided Dynatrace source fields. </p>"}, "GoogleAnalytics": {"shape": "GoogleAnalyticsConnectorOperator", "documentation": "<p> The operation to be performed on the provided Google Analytics source fields. </p>"}, "InforNexus": {"shape": "InforNexusConnectorOperator", "documentation": "<p> The operation to be performed on the provided Infor Nexus source fields. </p>"}, "Marketo": {"shape": "MarketoConnectorOperator", "documentation": "<p> The operation to be performed on the provided Marketo source fields. </p>"}, "S3": {"shape": "S3ConnectorOperator", "documentation": "<p> The operation to be performed on the provided Amazon S3 source fields. </p>"}, "Salesforce": {"shape": "SalesforceConnectorOperator", "documentation": "<p> The operation to be performed on the provided Salesforce source fields. </p>"}, "ServiceNow": {"shape": "ServiceNowConnectorOperator", "documentation": "<p> The operation to be performed on the provided ServiceNow source fields. </p>"}, "Singular": {"shape": "SingularConnectorOperator", "documentation": "<p> The operation to be performed on the provided Singular source fields. </p>"}, "Slack": {"shape": "SlackConnectorOperator", "documentation": "<p> The operation to be performed on the provided Slack source fields. </p>"}, "Trendmicro": {"shape": "TrendmicroConnectorOperator", "documentation": "<p> The operation to be performed on the provided Trend Micro source fields. </p>"}, "Veeva": {"shape": "VeevaConnectorOperator", "documentation": "<p> The operation to be performed on the provided Veeva source fields. </p>"}, "Zendesk": {"shape": "ZendeskConnectorOperator", "documentation": "<p> The operation to be performed on the provided Zendesk source fields. </p>"}, "SAPOData": {"shape": "SAPODataConnectorOperator", "documentation": "<p> The operation to be performed on the provided SAPOData source fields. </p>"}, "CustomConnector": {"shape": "Operator", "documentation": "<p>Operators supported by the custom connector.</p>"}, "Pardot": {"shape": "PardotConnectorOperator", "documentation": "<p>The operation to be performed on the provided Salesforce Pardot source fields.</p>"}}, "documentation": "<p> The operation to be performed on the provided source fields. </p>"}, "ConnectorOwner": {"type": "string", "max": 256, "pattern": ".*"}, "ConnectorProfile": {"type": "structure", "members": {"connectorProfileArn": {"shape": "ConnectorProfileArn", "documentation": "<p> The Amazon Resource Name (ARN) of the connector profile. </p>"}, "connectorProfileName": {"shape": "ConnectorProfileName", "documentation": "<p> The name of the connector profile. The name is unique for each <code>ConnectorProfile</code> in the Amazon Web Services account. </p>"}, "connectorType": {"shape": "ConnectorType", "documentation": "<p> The type of connector, such as Salesforce, Amplitude, and so on. </p>"}, "connectorLabel": {"shape": "ConnectorLabel", "documentation": "<p>The label for the connector profile being created.</p>"}, "connectionMode": {"shape": "ConnectionMode", "documentation": "<p> Indicates the connection mode and if it is public or private. </p>"}, "credentialsArn": {"shape": "ARN", "documentation": "<p> The Amazon Resource Name (ARN) of the connector profile credentials. </p>"}, "connectorProfileProperties": {"shape": "ConnectorProfileProperties", "documentation": "<p> The connector-specific properties of the profile configuration. </p>"}, "createdAt": {"shape": "Date", "documentation": "<p> Specifies when the connector profile was created. </p>"}, "lastUpdatedAt": {"shape": "Date", "documentation": "<p> Specifies when the connector profile was last updated. </p>"}, "privateConnectionProvisioningState": {"shape": "PrivateConnectionProvisioningState", "documentation": "<p> Specifies the private connection provisioning state. </p>"}}, "documentation": "<p> Describes an instance of a connector. This includes the provided name, credentials ARN, connection-mode, and so on. To keep the API intuitive and extensible, the fields that are common to all types of connector profiles are explicitly specified at the top level. The rest of the connector-specific properties are available via the <code>connectorProfileProperties</code> field. </p>"}, "ConnectorProfileArn": {"type": "string", "max": 512, "pattern": "arn:aws:appflow:.*:[0-9]+:.*"}, "ConnectorProfileConfig": {"type": "structure", "required": ["connectorProfileProperties"], "members": {"connectorProfileProperties": {"shape": "ConnectorProfileProperties", "documentation": "<p> The connector-specific properties of the profile configuration. </p>"}, "connectorProfileCredentials": {"shape": "ConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required by each connector. </p>"}}, "documentation": "<p> Defines the connector-specific configuration and credentials for the connector profile. </p>"}, "ConnectorProfileCredentials": {"type": "structure", "members": {"Amplitude": {"shape": "AmplitudeConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Amplitude. </p>"}, "Datadog": {"shape": "DatadogConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Datadog. </p>"}, "Dynatrace": {"shape": "DynatraceConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Dynatrace. </p>"}, "GoogleAnalytics": {"shape": "GoogleAnalyticsConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Google Analytics. </p>"}, "Honeycode": {"shape": "HoneycodeConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Amazon Honeycode. </p>"}, "InforNexus": {"shape": "InforNexusConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Infor Nexus. </p>"}, "Marketo": {"shape": "MarketoConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Marketo. </p>"}, "Redshift": {"shape": "RedshiftConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Amazon Redshift. </p>"}, "Salesforce": {"shape": "SalesforceConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Salesforce. </p>"}, "ServiceNow": {"shape": "ServiceNowConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using ServiceNow. </p>"}, "Singular": {"shape": "SingularConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Singular. </p>"}, "Slack": {"shape": "SlackConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Slack. </p>"}, "Snowflake": {"shape": "SnowflakeConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Snowflake. </p>"}, "Trendmicro": {"shape": "TrendmicroConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Trend Micro. </p>"}, "Veeva": {"shape": "VeevaConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Veeva. </p>"}, "Zendesk": {"shape": "ZendeskConnectorProfileCredentials", "documentation": "<p> The connector-specific credentials required when using Zendesk. </p>"}, "SAPOData": {"shape": "SAPODataConnectorProfileCredentials"}, "CustomConnector": {"shape": "CustomConnectorProfileCredentials"}, "Pardot": {"shape": "PardotConnectorProfileCredentials", "documentation": "<p>The connector-specific credentials required when using Salesforce Pardot.</p>"}}, "documentation": "<p> The connector-specific credentials required by a connector. </p>"}, "ConnectorProfileDetailList": {"type": "list", "member": {"shape": "ConnectorProfile"}}, "ConnectorProfileName": {"type": "string", "max": 256, "pattern": "[\\w/!@#+=.-]+"}, "ConnectorProfileNameList": {"type": "list", "member": {"shape": "ConnectorProfileName"}, "max": 100, "min": 0}, "ConnectorProfileProperties": {"type": "structure", "members": {"Amplitude": {"shape": "AmplitudeConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Amplitude. </p>"}, "Datadog": {"shape": "DatadogConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Datadog. </p>"}, "Dynatrace": {"shape": "DynatraceConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Dynatrace. </p>"}, "GoogleAnalytics": {"shape": "GoogleAnalyticsConnectorProfileProperties", "documentation": "<p> The connector-specific properties required Google Analytics. </p>"}, "Honeycode": {"shape": "HoneycodeConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Amazon Honeycode. </p>"}, "InforNexus": {"shape": "InforNexusConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Infor Nexus. </p>"}, "Marketo": {"shape": "MarketoConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Marketo. </p>"}, "Redshift": {"shape": "RedshiftConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Amazon Redshift. </p>"}, "Salesforce": {"shape": "SalesforceConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Salesforce. </p>"}, "ServiceNow": {"shape": "ServiceNowConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by serviceNow. </p>"}, "Singular": {"shape": "SingularConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Singular. </p>"}, "Slack": {"shape": "SlackConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Slack. </p>"}, "Snowflake": {"shape": "SnowflakeConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Snowflake. </p>"}, "Trendmicro": {"shape": "TrendmicroConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Trend Micro. </p>"}, "Veeva": {"shape": "VeevaConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by <PERSON><PERSON><PERSON>. </p>"}, "Zendesk": {"shape": "ZendeskConnectorProfileProperties", "documentation": "<p> The connector-specific properties required by Zendesk. </p>"}, "SAPOData": {"shape": "SAPODataConnectorProfileProperties"}, "CustomConnector": {"shape": "CustomConnectorProfileProperties", "documentation": "<p>The properties required by the custom connector.</p>"}, "Pardot": {"shape": "PardotConnectorProfileProperties", "documentation": "<p>The connector-specific properties required by Salesforce Pardot.</p>"}}, "documentation": "<p> The connector-specific profile properties required by each connector. </p>"}, "ConnectorProvisioningConfig": {"type": "structure", "members": {"lambda": {"shape": "LambdaConnectorProvisioningConfig", "documentation": "<p>Contains information about the configuration of the lambda which is being registered as the connector.</p>"}}, "documentation": "<p>Contains information about the configuration of the connector being registered.</p>"}, "ConnectorProvisioningType": {"type": "string", "documentation": "<p>The type of provisioning that the connector supports, such as Lambda.</p>", "enum": ["LAMBDA"]}, "ConnectorRuntimeSetting": {"type": "structure", "members": {"key": {"shape": "Key", "documentation": "<p>Contains value information about the connector runtime setting.</p>"}, "dataType": {"shape": "ConnectorRuntimeSettingDataType", "documentation": "<p>Data type of the connector runtime setting.</p>"}, "isRequired": {"shape": "Boolean", "documentation": "<p>Indicates whether this connector runtime setting is required.</p>"}, "label": {"shape": "Label", "documentation": "<p>A label used for connector runtime setting.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description about the connector runtime setting.</p>"}, "scope": {"shape": "ConnectorRuntimeSettingScope", "documentation": "<p>Indicates the scope of the connector runtime setting.</p>"}, "connectorSuppliedValueOptions": {"shape": "ConnectorSuppliedValueOptionList", "documentation": "<p>Contains default values for the connector runtime setting that are supplied by the connector.</p>"}}, "documentation": "<p>Contains information about the connector runtime settings that are required for flow execution.</p>"}, "ConnectorRuntimeSettingDataType": {"type": "string", "max": 256, "pattern": "\\S+"}, "ConnectorRuntimeSettingList": {"type": "list", "member": {"shape": "ConnectorRuntimeSetting"}}, "ConnectorRuntimeSettingScope": {"type": "string", "max": 256, "pattern": "\\S+"}, "ConnectorServerException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p> An error occurred when retrieving data from the connector endpoint. </p>", "error": {"httpStatusCode": 400}, "exception": true}, "ConnectorSuppliedValue": {"type": "string", "max": 256, "pattern": "\\S+"}, "ConnectorSuppliedValueList": {"type": "list", "member": {"shape": "ConnectorSuppliedValue"}}, "ConnectorSuppliedValueOptionList": {"type": "list", "member": {"shape": "ConnectorSuppliedValue"}}, "ConnectorType": {"type": "string", "enum": ["Salesforce", "Singular", "<PERSON><PERSON>ck", "Redshift", "S3", "Marketo", "Googleanalytics", "Zendesk", "Servicenow", "Datadog", "Trendmicro", "Snowflake", "Dynatrace", "Infornexus", "Amplitude", "<PERSON><PERSON><PERSON>", "EventBridge", "LookoutMetrics", "Upsolver", "Honeycode", "CustomerProfiles", "SAPOData", "CustomConnector", "<PERSON><PERSON><PERSON>"]}, "ConnectorTypeList": {"type": "list", "member": {"shape": "ConnectorType"}, "max": 100, "min": 0}, "ConnectorVersion": {"type": "string", "max": 256, "pattern": "\\S+"}, "CreateConnectorProfileRequest": {"type": "structure", "required": ["connectorProfileName", "connectorType", "connectionMode", "connectorProfileConfig"], "members": {"connectorProfileName": {"shape": "ConnectorProfileName", "documentation": "<p> The name of the connector profile. The name is unique for each <code>ConnectorProfile</code> in your Amazon Web Services account. </p>"}, "kmsArn": {"shape": "KMSArn", "documentation": "<p> The ARN (Amazon Resource Name) of the Key Management Service (KMS) key you provide for encryption. This is required if you do not want to use the Amazon AppFlow-managed KMS key. If you don't provide anything here, Amazon AppFlow uses the Amazon AppFlow-managed KMS key. </p>"}, "connectorType": {"shape": "ConnectorType", "documentation": "<p> The type of connector, such as Salesforce, Amplitude, and so on. </p>"}, "connectorLabel": {"shape": "ConnectorLabel", "documentation": "<p>The label of the connector. The label is unique for each <code>ConnectorRegistration</code> in your Amazon Web Services account. Only needed if calling for CUSTOMCONNECTOR connector type/.</p>"}, "connectionMode": {"shape": "ConnectionMode", "documentation": "<p> Indicates the connection mode and specifies whether it is public or private. Private flows use Amazon Web Services PrivateLink to route data over Amazon Web Services infrastructure without exposing it to the public internet. </p>"}, "connectorProfileConfig": {"shape": "ConnectorProfileConfig", "documentation": "<p> Defines the connector-specific configuration and credentials. </p>"}}}, "CreateConnectorProfileResponse": {"type": "structure", "members": {"connectorProfileArn": {"shape": "ConnectorProfileArn", "documentation": "<p> The Amazon Resource Name (ARN) of the connector profile. </p>"}}}, "CreateFlowRequest": {"type": "structure", "required": ["flowName", "triggerConfig", "sourceFlowConfig", "destinationFlowConfigList", "tasks"], "members": {"flowName": {"shape": "FlowName", "documentation": "<p> The specified name of the flow. Spaces are not allowed. Use underscores (_) or hyphens (-) only. </p>"}, "description": {"shape": "FlowDescription", "documentation": "<p> A description of the flow you want to create. </p>"}, "kmsArn": {"shape": "KMSArn", "documentation": "<p> The ARN (Amazon Resource Name) of the Key Management Service (KMS) key you provide for encryption. This is required if you do not want to use the Amazon AppFlow-managed KMS key. If you don't provide anything here, Amazon AppFlow uses the Amazon AppFlow-managed KMS key. </p>"}, "triggerConfig": {"shape": "TriggerConfig", "documentation": "<p> The trigger settings that determine how and when the flow runs. </p>"}, "sourceFlowConfig": {"shape": "SourceFlowConfig", "documentation": "<p> The configuration that controls how Amazon AppFlow retrieves data from the source connector. </p>"}, "destinationFlowConfigList": {"shape": "DestinationFlowConfigList", "documentation": "<p> The configuration that controls how Amazon AppFlow places data in the destination connector. </p>"}, "tasks": {"shape": "Tasks", "documentation": "<p> A list of tasks that Amazon AppFlow performs while transferring the data in the flow run. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags used to organize, track, or control access for your flow. </p>"}, "metadataCatalogConfig": {"shape": "MetadataCatalogConfig", "documentation": "<p>Specifies the configuration that Amazon AppFlow uses when it catalogs the data that's transferred by the associated flow. When Amazon AppFlow catalogs the data from a flow, it stores metadata in a data catalog.</p>"}}}, "CreateFlowResponse": {"type": "structure", "members": {"flowArn": {"shape": "FlowArn", "documentation": "<p> The flow's Amazon Resource Name (ARN). </p>"}, "flowStatus": {"shape": "FlowStatus", "documentation": "<p> Indicates the current status of the flow. </p>"}}}, "CreatedBy": {"type": "string", "max": 256, "pattern": "\\S+"}, "CredentialsMap": {"type": "map", "key": {"shape": "CredentialsMapKey"}, "value": {"shape": "CredentialsMapValue"}, "max": 50, "min": 0}, "CredentialsMapKey": {"type": "string", "max": 128, "min": 1, "pattern": "[\\w]+", "sensitive": true}, "CredentialsMapValue": {"type": "string", "max": 2048, "pattern": "\\S+", "sensitive": true}, "CustomAuthConfig": {"type": "structure", "members": {"customAuthenticationType": {"shape": "CustomAuthenticationType", "documentation": "<p>The authentication type that the custom connector uses.</p>"}, "authParameters": {"shape": "AuthParameterList", "documentation": "<p>Information about authentication parameters required for authentication.</p>"}}, "documentation": "<p>Configuration information required for custom authentication.</p>"}, "CustomAuthConfigList": {"type": "list", "member": {"shape": "CustomAuthConfig"}}, "CustomAuthCredentials": {"type": "structure", "required": ["customAuthenticationType"], "members": {"customAuthenticationType": {"shape": "CustomAuthenticationType", "documentation": "<p>The custom authentication type that the connector uses.</p>"}, "credentialsMap": {"shape": "CredentialsMap", "documentation": "<p>A map that holds custom authentication credentials.</p>"}}, "documentation": "<p>The custom credentials required for custom authentication.</p>"}, "CustomAuthenticationType": {"type": "string", "max": 256, "pattern": "\\S+"}, "CustomConnectorDestinationProperties": {"type": "structure", "required": ["entityName"], "members": {"entityName": {"shape": "EntityName", "documentation": "<p>The entity specified in the custom connector as a destination in the flow.</p>"}, "errorHandlingConfig": {"shape": "ErrorHandlingConfig", "documentation": "<p>The settings that determine how Amazon AppFlow handles an error when placing data in the custom connector as destination.</p>"}, "writeOperationType": {"shape": "WriteOperationType", "documentation": "<p>Specifies the type of write operation to be performed in the custom connector when it's used as destination.</p>"}, "idFieldNames": {"shape": "IdFieldNameList", "documentation": "<p>The name of the field that Amazon AppFlow uses as an ID when performing a write operation such as update, delete, or upsert.</p>"}, "customProperties": {"shape": "CustomProperties", "documentation": "<p>The custom properties that are specific to the connector when it's used as a destination in the flow.</p>"}}, "documentation": "<p>The properties that are applied when the custom connector is being used as a destination.</p>"}, "CustomConnectorProfileCredentials": {"type": "structure", "required": ["authenticationType"], "members": {"authenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type that the custom connector uses for authenticating while creating a connector profile.</p>"}, "basic": {"shape": "BasicAuthCredentials", "documentation": "<p>The basic credentials that are required for the authentication of the user.</p>"}, "oauth2": {"shape": "OAuth2Credentials", "documentation": "<p>The OAuth 2.0 credentials required for the authentication of the user.</p>"}, "apiKey": {"shape": "ApiKeyCredentials", "documentation": "<p>The API keys required for the authentication of the user.</p>"}, "custom": {"shape": "CustomAuthCredentials", "documentation": "<p>If the connector uses the custom authentication mechanism, this holds the required credentials.</p>"}}, "documentation": "<p>The connector-specific profile credentials that are required when using the custom connector.</p>"}, "CustomConnectorProfileProperties": {"type": "structure", "members": {"profileProperties": {"shape": "ProfilePropertiesMap", "documentation": "<p>A map of properties that are required to create a profile for the custom connector.</p>"}, "oAuth2Properties": {"shape": "OAuth2Properties"}}, "documentation": "<p>The profile properties required by the custom connector.</p>"}, "CustomConnectorSourceProperties": {"type": "structure", "required": ["entityName"], "members": {"entityName": {"shape": "EntityName", "documentation": "<p>The entity specified in the custom connector as a source in the flow.</p>"}, "customProperties": {"shape": "CustomProperties", "documentation": "<p>Custom properties that are required to use the custom connector as a source.</p>"}}, "documentation": "<p>The properties that are applied when the custom connector is being used as a source.</p>"}, "CustomProperties": {"type": "map", "key": {"shape": "CustomPropertyKey"}, "value": {"shape": "CustomPropertyValue"}, "max": 50, "min": 0}, "CustomPropertyKey": {"type": "string", "max": 128, "min": 1, "pattern": "[\\w]+"}, "CustomPropertyValue": {"type": "string", "max": 2048, "pattern": "\\S+"}, "CustomerProfilesDestinationProperties": {"type": "structure", "required": ["domainName"], "members": {"domainName": {"shape": "DomainName", "documentation": "<p> The unique name of the Amazon Connect Customer Profiles domain. </p>"}, "objectTypeName": {"shape": "ObjectTypeName", "documentation": "<p> The object specified in the Amazon Connect Customer Profiles flow destination. </p>"}}, "documentation": "<p> The properties that are applied when Amazon Connect Customer Profiles is used as a destination. </p>"}, "CustomerProfilesMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to Amazon Connect Customer Profiles. </p>"}, "DataApiRoleArn": {"type": "string", "max": 512, "pattern": "arn:aws:iam:.*:[0-9]+:.*"}, "DataPullMode": {"type": "string", "enum": ["Incremental", "Complete"]}, "DatabaseName": {"type": "string", "max": 512, "pattern": "\\S+"}, "DatabaseUrl": {"type": "string", "max": 512, "pattern": "\\S+"}, "DatadogConnectorOperator": {"type": "string", "enum": ["PROJECTION", "BETWEEN", "EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "DatadogConnectorProfileCredentials": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "applicationKey"], "members": {"apiKey": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p> A unique alphanumeric identifier used to authenticate a user, developer, or calling program to your API. </p>"}, "applicationKey": {"shape": "ApplicationKey", "documentation": "<p> Application keys, in conjunction with your API key, give you full access to Datadog’s programmatic API. Application keys are associated with the user account that created them. The application key is used to log all requests made to the API. </p>"}}, "documentation": "<p> The connector-specific credentials required by Datadog. </p>"}, "DatadogConnectorProfileProperties": {"type": "structure", "required": ["instanceUrl"], "members": {"instanceUrl": {"shape": "InstanceUrl", "documentation": "<p> The location of the Datadog resource. </p>"}}, "documentation": "<p> The connector-specific profile properties required by Datadog. </p>"}, "DatadogMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to Datadog. </p>"}, "DatadogSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Datadog flow source. </p>"}}, "documentation": "<p> The properties that are applied when Datadog is being used as a source. </p>"}, "Date": {"type": "timestamp"}, "DatetimeTypeFieldName": {"type": "string", "max": 256, "pattern": ".*"}, "DeleteConnectorProfileRequest": {"type": "structure", "required": ["connectorProfileName"], "members": {"connectorProfileName": {"shape": "ConnectorProfileName", "documentation": "<p> The name of the connector profile. The name is unique for each <code>ConnectorProfile</code> in your account. </p>"}, "forceDelete": {"shape": "Boolean", "documentation": "<p> Indicates whether Amazon AppFlow should delete the profile, even if it is currently in use in one or more flows. </p>"}}}, "DeleteConnectorProfileResponse": {"type": "structure", "members": {}}, "DeleteFlowRequest": {"type": "structure", "required": ["flowName"], "members": {"flowName": {"shape": "FlowName", "documentation": "<p> The specified name of the flow. Spaces are not allowed. Use underscores (_) or hyphens (-) only. </p>"}, "forceDelete": {"shape": "Boolean", "documentation": "<p> Indicates whether Amazon AppFlow should delete the flow, even if it is currently in use. </p>"}}}, "DeleteFlowResponse": {"type": "structure", "members": {}}, "DescribeConnectorEntityRequest": {"type": "structure", "required": ["connectorEntityName"], "members": {"connectorEntityName": {"shape": "EntityName", "documentation": "<p> The entity name for that connector. </p>"}, "connectorType": {"shape": "ConnectorType", "documentation": "<p> The type of connector application, such as Salesforce, Amplitude, and so on. </p>"}, "connectorProfileName": {"shape": "ConnectorProfileName", "documentation": "<p> The name of the connector profile. The name is unique for each <code>ConnectorProfile</code> in the Amazon Web Services account. </p>"}, "apiVersion": {"shape": "ApiVersion", "documentation": "<p>The version of the API that's used by the connector.</p>"}}}, "DescribeConnectorEntityResponse": {"type": "structure", "required": ["connectorEntityFields"], "members": {"connectorEntityFields": {"shape": "ConnectorEntityFieldList", "documentation": "<p> Describes the fields for that connector entity. For example, for an <i>account</i> entity, the fields would be <i>account name</i>, <i>account ID</i>, and so on. </p>"}}}, "DescribeConnectorProfilesRequest": {"type": "structure", "members": {"connectorProfileNames": {"shape": "ConnectorProfileNameList", "documentation": "<p> The name of the connector profile. The name is unique for each <code>ConnectorProfile</code> in the Amazon Web Services account. </p>"}, "connectorType": {"shape": "ConnectorType", "documentation": "<p> The type of connector, such as Salesforce, Amplitude, and so on. </p>"}, "connectorLabel": {"shape": "ConnectorLabel", "documentation": "<p>The name of the connector. The name is unique for each <code>ConnectorRegistration</code> in your Amazon Web Services account. Only needed if calling for CUSTOMCONNECTOR connector type/.</p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> Specifies the maximum number of items that should be returned in the result set. The default for <code>maxResults</code> is 20 (for all paginated API operations). </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The pagination token for the next page of data. </p>"}}}, "DescribeConnectorProfilesResponse": {"type": "structure", "members": {"connectorProfileDetails": {"shape": "ConnectorProfileDetailList", "documentation": "<p> Returns information about the connector profiles associated with the flow. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The pagination token for the next page of data. If <code>nextToken=null</code>, this means that all records have been fetched. </p>"}}}, "DescribeConnectorRequest": {"type": "structure", "required": ["connectorType"], "members": {"connectorType": {"shape": "ConnectorType", "documentation": "<p>The connector type, such as CUSTOMCONNECTOR, Saleforce, Marketo. Please choose CUSTOMCONNECTOR for Lambda based custom connectors.</p>"}, "connectorLabel": {"shape": "ConnectorLabel", "documentation": "<p>The label of the connector. The label is unique for each <code>ConnectorRegistration</code> in your Amazon Web Services account. Only needed if calling for CUSTOMCONNECTOR connector type/.</p>"}}}, "DescribeConnectorResponse": {"type": "structure", "members": {"connectorConfiguration": {"shape": "ConnectorConfiguration", "documentation": "<p>Configuration info of all the connectors that the user requested.</p>"}}}, "DescribeConnectorsRequest": {"type": "structure", "members": {"connectorTypes": {"shape": "ConnectorTypeList", "documentation": "<p> The type of connector, such as Salesforce, Amplitude, and so on. </p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of items that should be returned in the result set. The default is 20.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The pagination token for the next page of data. </p>"}}}, "DescribeConnectorsResponse": {"type": "structure", "members": {"connectorConfigurations": {"shape": "ConnectorConfigurationsMap", "documentation": "<p> The configuration that is applied to the connectors used in the flow. </p>"}, "connectors": {"shape": "ConnectorList", "documentation": "<p>Information about the connectors supported in Amazon AppFlow.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The pagination token for the next page of data. </p>"}}}, "DescribeFlowExecutionRecordsRequest": {"type": "structure", "required": ["flowName"], "members": {"flowName": {"shape": "FlowName", "documentation": "<p> The specified name of the flow. Spaces are not allowed. Use underscores (_) or hyphens (-) only. </p>"}, "maxResults": {"shape": "MaxResults", "documentation": "<p> Specifies the maximum number of items that should be returned in the result set. The default for <code>maxResults</code> is 20 (for all paginated API operations). </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The pagination token for the next page of data. </p>"}}}, "DescribeFlowExecutionRecordsResponse": {"type": "structure", "members": {"flowExecutions": {"shape": "FlowExecutionList", "documentation": "<p> Returns a list of all instances when this flow was run. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The pagination token for the next page of data. </p>"}}}, "DescribeFlowRequest": {"type": "structure", "required": ["flowName"], "members": {"flowName": {"shape": "FlowName", "documentation": "<p> The specified name of the flow. Spaces are not allowed. Use underscores (_) or hyphens (-) only. </p>"}}}, "DescribeFlowResponse": {"type": "structure", "members": {"flowArn": {"shape": "FlowArn", "documentation": "<p> The flow's Amazon Resource Name (ARN). </p>"}, "description": {"shape": "FlowDescription", "documentation": "<p> A description of the flow. </p>"}, "flowName": {"shape": "FlowName", "documentation": "<p> The specified name of the flow. Spaces are not allowed. Use underscores (_) or hyphens (-) only. </p>"}, "kmsArn": {"shape": "KMSArn", "documentation": "<p> The ARN (Amazon Resource Name) of the Key Management Service (KMS) key you provide for encryption. This is required if you do not want to use the Amazon AppFlow-managed KMS key. If you don't provide anything here, Amazon AppFlow uses the Amazon AppFlow-managed KMS key. </p>"}, "flowStatus": {"shape": "FlowStatus", "documentation": "<p> Indicates the current status of the flow. </p>"}, "flowStatusMessage": {"shape": "FlowStatusMessage", "documentation": "<p> Contains an error message if the flow status is in a suspended or error state. This applies only to scheduled or event-triggered flows. </p>"}, "sourceFlowConfig": {"shape": "SourceFlowConfig", "documentation": "<p> The configuration that controls how Amazon AppFlow retrieves data from the source connector. </p>"}, "destinationFlowConfigList": {"shape": "DestinationFlowConfigList", "documentation": "<p> The configuration that controls how Amazon AppFlow transfers data to the destination connector. </p>"}, "lastRunExecutionDetails": {"shape": "ExecutionDetails", "documentation": "<p> Describes the details of the most recent flow run. </p>"}, "triggerConfig": {"shape": "TriggerConfig", "documentation": "<p> The trigger settings that determine how and when the flow runs. </p>"}, "tasks": {"shape": "Tasks", "documentation": "<p> A list of tasks that Amazon AppFlow performs while transferring the data in the flow run. </p>"}, "createdAt": {"shape": "Date", "documentation": "<p> Specifies when the flow was created. </p>"}, "lastUpdatedAt": {"shape": "Date", "documentation": "<p> Specifies when the flow was last updated. </p>"}, "createdBy": {"shape": "CreatedBy", "documentation": "<p> The ARN of the user who created the flow. </p>"}, "lastUpdatedBy": {"shape": "UpdatedBy", "documentation": "<p> Specifies the user name of the account that performed the most recent update. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags used to organize, track, or control access for your flow. </p>"}, "metadataCatalogConfig": {"shape": "MetadataCatalogConfig", "documentation": "<p>Specifies the configuration that Amazon AppFlow uses when it catalogs the data that's transferred by the associated flow. When Amazon AppFlow catalogs the data from a flow, it stores metadata in a data catalog.</p>"}, "lastRunMetadataCatalogDetails": {"shape": "MetadataCatalogDetails", "documentation": "<p>Describes the metadata catalog, metadata table, and data partitions that Amazon AppFlow used for the associated flow run.</p>"}, "schemaVersion": {"shape": "<PERSON>", "documentation": "<p>The version number of your data schema. Amazon AppFlow assigns this version number. The version number increases by one when you change any of the following settings in your flow configuration:</p> <ul> <li> <p>Source-to-destination field mappings</p> </li> <li> <p>Field data types</p> </li> <li> <p>Partition keys</p> </li> </ul>"}}}, "Description": {"type": "string", "max": 1024, "pattern": "[\\s\\w/!@#+=.-]*"}, "DestinationConnectorProperties": {"type": "structure", "members": {"Redshift": {"shape": "RedshiftDestinationProperties", "documentation": "<p> The properties required to query Amazon Redshift. </p>"}, "S3": {"shape": "S3DestinationProperties", "documentation": "<p> The properties required to query Amazon S3. </p>"}, "Salesforce": {"shape": "SalesforceDestinationProperties", "documentation": "<p> The properties required to query Salesforce. </p>"}, "Snowflake": {"shape": "SnowflakeDestinationProperties", "documentation": "<p> The properties required to query Snowflake. </p>"}, "EventBridge": {"shape": "EventBridgeDestinationProperties", "documentation": "<p> The properties required to query Amazon EventBridge. </p>"}, "LookoutMetrics": {"shape": "LookoutMetricsDestinationProperties", "documentation": "<p> The properties required to query Amazon Lookout for Metrics. </p>"}, "Upsolver": {"shape": "UpsolverDestinationProperties", "documentation": "<p> The properties required to query Upsolver. </p>"}, "Honeycode": {"shape": "HoneycodeDestinationProperties", "documentation": "<p> The properties required to query Amazon Honeycode. </p>"}, "CustomerProfiles": {"shape": "CustomerProfilesDestinationProperties", "documentation": "<p> The properties required to query Amazon Connect Customer Profiles. </p>"}, "Zendesk": {"shape": "ZendeskDestinationProperties", "documentation": "<p>The properties required to query Zendesk.</p>"}, "Marketo": {"shape": "MarketoDestinationProperties", "documentation": "<p>The properties required to query <PERSON><PERSON>.</p>"}, "CustomConnector": {"shape": "CustomConnectorDestinationProperties", "documentation": "<p>The properties that are required to query the custom Connector.</p>"}, "SAPOData": {"shape": "SAPODataDestinationProperties", "documentation": "<p>The properties required to query SAPOData.</p>"}}, "documentation": "<p> This stores the information that is required to query a particular connector. </p>"}, "DestinationField": {"type": "string", "max": 256, "pattern": ".*"}, "DestinationFieldProperties": {"type": "structure", "members": {"isCreatable": {"shape": "Boolean", "documentation": "<p> Specifies if the destination field can be created by the current user. </p>"}, "isNullable": {"shape": "Boolean", "documentation": "<p> Specifies if the destination field can have a null value. </p>"}, "isUpsertable": {"shape": "Boolean", "documentation": "<p> Specifies if the flow run can either insert new rows in the destination field if they do not already exist, or update them if they do. </p>"}, "isUpdatable": {"shape": "Boolean", "documentation": "<p> Specifies whether the field can be updated during an <code>UPDATE</code> or <code>UPSERT</code> write operation. </p>"}, "isDefaultedOnCreate": {"shape": "Boolean", "documentation": "<p>Specifies whether the field can use the default value during a Create operation.</p>"}, "supportedWriteOperations": {"shape": "SupportedWriteOperationList", "documentation": "<p> A list of supported write operations. For each write operation listed, this field can be used in <code>idFieldNames</code> when that write operation is present as a destination option. </p>"}}, "documentation": "<p> The properties that can be applied to a field when connector is being used as a destination. </p>"}, "DestinationFlowConfig": {"type": "structure", "required": ["connectorType", "destinationConnectorProperties"], "members": {"connectorType": {"shape": "ConnectorType", "documentation": "<p> The type of connector, such as Salesforce, Amplitude, and so on. </p>"}, "apiVersion": {"shape": "ApiVersion", "documentation": "<p>The API version that the destination connector uses.</p>"}, "connectorProfileName": {"shape": "ConnectorProfileName", "documentation": "<p> The name of the connector profile. This name must be unique for each connector profile in the Amazon Web Services account. </p>"}, "destinationConnectorProperties": {"shape": "DestinationConnectorProperties", "documentation": "<p> This stores the information that is required to query a particular connector. </p>"}}, "documentation": "<p> Contains information about the configuration of destination connectors present in the flow. </p>"}, "DestinationFlowConfigList": {"type": "list", "member": {"shape": "DestinationFlowConfig"}}, "DocumentType": {"type": "string", "max": 512, "pattern": "[\\s\\w_-]+"}, "DomainName": {"type": "string", "max": 64, "pattern": "\\S+"}, "Double": {"type": "double"}, "DynatraceConnectorOperator": {"type": "string", "enum": ["PROJECTION", "BETWEEN", "EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "DynatraceConnectorProfileCredentials": {"type": "structure", "required": ["apiToken"], "members": {"apiToken": {"shape": "ApiToken", "documentation": "<p> The API tokens used by Dynatrace API to authenticate various API calls. </p>"}}, "documentation": "<p> The connector-specific profile credentials required by Dynatrace. </p>"}, "DynatraceConnectorProfileProperties": {"type": "structure", "required": ["instanceUrl"], "members": {"instanceUrl": {"shape": "InstanceUrl", "documentation": "<p> The location of the Dynatrace resource. </p>"}}, "documentation": "<p> The connector-specific profile properties required by Dynatrace. </p>"}, "DynatraceMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to Dynatrace. </p>"}, "DynatraceSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Dynatrace flow source. </p>"}}, "documentation": "<p> The properties that are applied when Dynatrace is being used as a source. </p>"}, "EntitiesPath": {"type": "string", "max": 256, "pattern": "[\\s\\w/!@#+=,.-]*"}, "EntityName": {"type": "string", "max": 1024, "pattern": "\\S+"}, "ErrorHandlingConfig": {"type": "structure", "members": {"failOnFirstDestinationError": {"shape": "Boolean", "documentation": "<p> Specifies if the flow should fail after the first instance of a failure when attempting to place data in the destination. </p>"}, "bucketPrefix": {"shape": "BucketPrefix", "documentation": "<p> Specifies the Amazon S3 bucket prefix. </p>"}, "bucketName": {"shape": "BucketName", "documentation": "<p> Specifies the name of the Amazon S3 bucket. </p>"}}, "documentation": "<p> The settings that determine how Amazon AppFlow handles an error when placing data in the destination. For example, this setting would determine if the flow should fail after one insertion error, or continue and attempt to insert every record regardless of the initial failure. <code>ErrorHandlingConfig</code> is a part of the destination connector details. </p>"}, "ErrorInfo": {"type": "structure", "members": {"putFailuresCount": {"shape": "<PERSON>", "documentation": "<p> Specifies the failure count for the attempted flow. </p>"}, "executionMessage": {"shape": "ExecutionMessage", "documentation": "<p> Specifies the error message that appears if a flow fails. </p>"}}, "documentation": "<p> Provides details in the event of a failed flow, including the failure count and the related error messages. </p>"}, "ErrorMessage": {"type": "string", "max": 2048, "pattern": "[\\s\\w/!@#+=.-]*"}, "EventBridgeDestinationProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Amazon EventBridge flow destination. </p>"}, "errorHandlingConfig": {"shape": "ErrorHandlingConfig"}}, "documentation": "<p> The properties that are applied when Amazon EventBridge is being used as a destination. </p>"}, "EventBridgeMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to Amazon EventBridge. </p>"}, "ExecutionDetails": {"type": "structure", "members": {"mostRecentExecutionMessage": {"shape": "MostRecentExecutionMessage", "documentation": "<p> Describes the details of the most recent flow run. </p>"}, "mostRecentExecutionTime": {"shape": "Date", "documentation": "<p> Specifies the time of the most recent flow run. </p>"}, "mostRecentExecutionStatus": {"shape": "ExecutionStatus", "documentation": "<p> Specifies the status of the most recent flow run. </p>"}}, "documentation": "<p> Describes the details of the flow run, including the timestamp, status, and message. </p>"}, "ExecutionId": {"type": "string", "max": 256, "pattern": "\\S+"}, "ExecutionMessage": {"type": "string", "max": 2048, "pattern": "[\\s\\w/!@#+=.-]*"}, "ExecutionRecord": {"type": "structure", "members": {"executionId": {"shape": "ExecutionId", "documentation": "<p> Specifies the identifier of the given flow run. </p>"}, "executionStatus": {"shape": "ExecutionStatus", "documentation": "<p> Specifies the flow run status and whether it is in progress, has completed successfully, or has failed. </p>"}, "executionResult": {"shape": "ExecutionResult", "documentation": "<p> Describes the result of the given flow run. </p>"}, "startedAt": {"shape": "Date", "documentation": "<p> Specifies the start time of the flow run. </p>"}, "lastUpdatedAt": {"shape": "Date", "documentation": "<p> Specifies the time of the most recent update. </p>"}, "dataPullStartTime": {"shape": "Date", "documentation": "<p> The timestamp that determines the first new or updated record to be transferred in the flow run. </p>"}, "dataPullEndTime": {"shape": "Date", "documentation": "<p> The timestamp that indicates the last new or updated record to be transferred in the flow run. </p>"}, "metadataCatalogDetails": {"shape": "MetadataCatalogDetails", "documentation": "<p>Describes the metadata catalog, metadata table, and data partitions that Amazon AppFlow used for the associated flow run.</p>"}}, "documentation": "<p> Specifies information about the past flow run instances for a given flow. </p>"}, "ExecutionResult": {"type": "structure", "members": {"errorInfo": {"shape": "ErrorInfo", "documentation": "<p> Provides any error message information related to the flow run. </p>"}, "bytesProcessed": {"shape": "<PERSON>", "documentation": "<p> The total number of bytes processed by the flow run. </p>"}, "bytesWritten": {"shape": "<PERSON>", "documentation": "<p> The total number of bytes written as a result of the flow run. </p>"}, "recordsProcessed": {"shape": "<PERSON>", "documentation": "<p> The number of records processed in the flow run. </p>"}}, "documentation": "<p> Specifies the end result of the flow run. </p>"}, "ExecutionStatus": {"type": "string", "enum": ["InProgress", "Successful", "Error"]}, "FieldType": {"type": "string"}, "FieldTypeDetails": {"type": "structure", "required": ["fieldType", "filterOperators"], "members": {"fieldType": {"shape": "FieldType", "documentation": "<p> The type of field, such as string, integer, date, and so on. </p>"}, "filterOperators": {"shape": "FilterOperatorList", "documentation": "<p> The list of operators supported by a field. </p>"}, "supportedValues": {"shape": "SupportedValueList", "documentation": "<p> The list of values that a field can contain. For example, a Boolean <code>fieldType</code> can have two values: \"true\" and \"false\". </p>"}, "valueRegexPattern": {"shape": "String", "documentation": "<p>The regular expression pattern for the field name.</p>"}, "supportedDateFormat": {"shape": "String", "documentation": "<p>The date format that the field supports.</p>"}, "fieldValueRange": {"shape": "Range", "documentation": "<p>The range of values this field can hold.</p>"}, "fieldLengthRange": {"shape": "Range", "documentation": "<p>This is the allowable length range for this field's value.</p>"}}, "documentation": "<p> Contains details regarding the supported field type and the operators that can be applied for filtering. </p>"}, "FileType": {"type": "string", "enum": ["CSV", "JSON", "PARQUET"]}, "FilterOperatorList": {"type": "list", "member": {"shape": "Operator"}}, "FlowArn": {"type": "string", "max": 512, "pattern": "arn:aws:appflow:.*:[0-9]+:.*"}, "FlowDefinition": {"type": "structure", "members": {"flowArn": {"shape": "FlowArn", "documentation": "<p> The flow's Amazon Resource Name (ARN). </p>"}, "description": {"shape": "FlowDescription", "documentation": "<p> A user-entered description of the flow. </p>"}, "flowName": {"shape": "FlowName", "documentation": "<p> The specified name of the flow. Spaces are not allowed. Use underscores (_) or hyphens (-) only. </p>"}, "flowStatus": {"shape": "FlowStatus", "documentation": "<p> Indicates the current status of the flow. </p>"}, "sourceConnectorType": {"shape": "ConnectorType", "documentation": "<p> Specifies the source connector type, such as Salesforce, Amazon S3, Amplitude, and so on. </p>"}, "sourceConnectorLabel": {"shape": "ConnectorLabel", "documentation": "<p>The label of the source connector in the flow.</p>"}, "destinationConnectorType": {"shape": "ConnectorType", "documentation": "<p> Specifies the destination connector type, such as Salesforce, Amazon S3, Amplitude, and so on. </p>"}, "destinationConnectorLabel": {"shape": "ConnectorLabel", "documentation": "<p>The label of the destination connector in the flow.</p>"}, "triggerType": {"shape": "TriggerType", "documentation": "<p> Specifies the type of flow trigger. This can be <code>OnDemand</code>, <code>Scheduled</code>, or <code>Event</code>. </p>"}, "createdAt": {"shape": "Date", "documentation": "<p> Specifies when the flow was created. </p>"}, "lastUpdatedAt": {"shape": "Date", "documentation": "<p> Specifies when the flow was last updated. </p>"}, "createdBy": {"shape": "CreatedBy", "documentation": "<p> The ARN of the user who created the flow. </p>"}, "lastUpdatedBy": {"shape": "UpdatedBy", "documentation": "<p> Specifies the account user name that most recently updated the flow. </p>"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags used to organize, track, or control access for your flow. </p>"}, "lastRunExecutionDetails": {"shape": "ExecutionDetails", "documentation": "<p> Describes the details of the most recent flow run. </p>"}}, "documentation": "<p> The properties of the flow, such as its source, destination, trigger type, and so on. </p>"}, "FlowDescription": {"type": "string", "max": 2048, "pattern": "[\\w!@#\\-.?,\\s]*"}, "FlowErrorDeactivationThreshold": {"type": "integer", "max": 100, "min": 1}, "FlowExecutionList": {"type": "list", "member": {"shape": "ExecutionRecord"}}, "FlowList": {"type": "list", "member": {"shape": "FlowDefinition"}}, "FlowName": {"type": "string", "max": 256, "pattern": "[a-zA-Z0-9][\\w!@#.-]+"}, "FlowStatus": {"type": "string", "enum": ["Active", "Deprecated", "Deleted", "Draft", "Errored", "Suspended"]}, "FlowStatusMessage": {"type": "string", "max": 2048, "pattern": "[\\s\\w/!@#+=.-]*"}, "GlueDataCatalogConfig": {"type": "structure", "required": ["roleArn", "databaseName", "tablePrefix"], "members": {"roleArn": {"shape": "GlueDataCatalogIAMRole", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role that grants Amazon AppFlow the permissions it needs to create Data Catalog tables, databases, and partitions.</p> <p>For an example IAM policy that has the required permissions, see <a href=\"https://docs.aws.amazon.com/appflow/latest/userguide/security_iam_id-based-policy-examples.html\">Identity-based policy examples for Amazon AppFlow</a>.</p>"}, "databaseName": {"shape": "GlueDataCatalogDatabaseName", "documentation": "<p>The name of the Data Catalog database that stores the metadata tables that Amazon AppFlow creates in your Amazon Web Services account. These tables contain metadata for the data that's transferred by the flow that you configure with this parameter.</p> <note> <p>When you configure a new flow with this parameter, you must specify an existing database.</p> </note>"}, "tablePrefix": {"shape": "GlueDataCatalogTablePrefix", "documentation": "<p>A naming prefix for each Data Catalog table that Amazon AppFlow creates for the flow that you configure with this setting. Amazon AppFlow adds the prefix to the beginning of the each table name.</p>"}}, "documentation": "<p>Specifies the configuration that Amazon AppFlow uses when it catalogs your data with the Glue Data Catalog. When Amazon AppFlow catalogs your data, it stores metadata in Data Catalog tables. This metadata represents the data that's transferred by the flow that you configure with these settings.</p> <note> <p>You can configure a flow with these settings only when the flow destination is Amazon S3.</p> </note>"}, "GlueDataCatalogDatabaseName": {"type": "string", "max": 255, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*"}, "GlueDataCatalogIAMRole": {"type": "string", "max": 512, "pattern": "arn:aws:iam:.*:[0-9]+:.*"}, "GlueDataCatalogTablePrefix": {"type": "string", "max": 128, "pattern": "[\\u0020-\\uD7FF\\uE000-\\uFFFD\\uD800\\uDC00-\\uDBFF\\uDFFF\\t]*"}, "GoogleAnalyticsConnectorOperator": {"type": "string", "enum": ["PROJECTION", "BETWEEN"]}, "GoogleAnalyticsConnectorProfileCredentials": {"type": "structure", "required": ["clientId", "clientSecret"], "members": {"clientId": {"shape": "ClientId", "documentation": "<p> The identifier for the desired client. </p>"}, "clientSecret": {"shape": "ClientSecret", "documentation": "<p> The client secret used by the OAuth client to authenticate to the authorization server. </p>"}, "accessToken": {"shape": "AccessToken", "documentation": "<p> The credentials used to access protected Google Analytics resources. </p>"}, "refreshToken": {"shape": "RefreshToken", "documentation": "<p> The credentials used to acquire new access tokens. This is required only for OAuth2 access tokens, and is not required for OAuth1 access tokens. </p>"}, "oAuthRequest": {"shape": "ConnectorOAuthRequest", "documentation": "<p> The OAuth requirement needed to request security tokens from the connector endpoint. </p>"}}, "documentation": "<p> The connector-specific profile credentials required by Google Analytics. </p>"}, "GoogleAnalyticsConnectorProfileProperties": {"type": "structure", "members": {}, "documentation": "<p> The connector-specific profile properties required by Google Analytics. </p>"}, "GoogleAnalyticsMetadata": {"type": "structure", "members": {"oAuthScopes": {"shape": "OAuthScopeList", "documentation": "<p> The desired authorization scope for the Google Analytics account. </p>"}}, "documentation": "<p> The connector metadata specific to Google Analytics. </p>"}, "GoogleAnalyticsSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Google Analytics flow source. </p>"}}, "documentation": "<p> The properties that are applied when Google Analytics is being used as a source. </p>"}, "Group": {"type": "string", "max": 128, "pattern": "\\S+"}, "HoneycodeConnectorProfileCredentials": {"type": "structure", "members": {"accessToken": {"shape": "AccessToken", "documentation": "<p> The credentials used to access protected Amazon Honeycode resources. </p>"}, "refreshToken": {"shape": "RefreshToken", "documentation": "<p> The credentials used to acquire new access tokens. </p>"}, "oAuthRequest": {"shape": "ConnectorOAuthRequest"}}, "documentation": "<p> The connector-specific credentials required when using Amazon Honeycode. </p>"}, "HoneycodeConnectorProfileProperties": {"type": "structure", "members": {}, "documentation": "<p> The connector-specific properties required when using Amazon Honeycode. </p>"}, "HoneycodeDestinationProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Amazon Honeycode flow destination. </p>"}, "errorHandlingConfig": {"shape": "ErrorHandlingConfig"}}, "documentation": "<p> The properties that are applied when Amazon Honeycode is used as a destination. </p>"}, "HoneycodeMetadata": {"type": "structure", "members": {"oAuthScopes": {"shape": "OAuthScopeList", "documentation": "<p> The desired authorization scope for the Amazon Honeycode account. </p>"}}, "documentation": "<p> The connector metadata specific to Amazon Honeycode. </p>"}, "IdFieldNameList": {"type": "list", "member": {"shape": "Name"}, "documentation": "<p> A list of field names that can be used as an ID field when performing a write operation. </p>", "min": 0}, "Identifier": {"type": "string", "max": 128, "pattern": "\\S+"}, "IncrementalPullConfig": {"type": "structure", "members": {"datetimeTypeFieldName": {"shape": "DatetimeTypeFieldName", "documentation": "<p> A field that specifies the date time or timestamp field as the criteria to use when importing incremental records from the source. </p>"}}, "documentation": "<p> Specifies the configuration used when importing incremental records from the source. </p>"}, "InforNexusConnectorOperator": {"type": "string", "enum": ["PROJECTION", "BETWEEN", "EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "InforNexusConnectorProfileCredentials": {"type": "structure", "required": ["accessKeyId", "userId", "secretAccessKey", "datakey"], "members": {"accessKeyId": {"shape": "AccessKeyId", "documentation": "<p> The Access Key portion of the credentials. </p>"}, "userId": {"shape": "Username", "documentation": "<p> The identifier for the user. </p>"}, "secretAccessKey": {"shape": "Key", "documentation": "<p> The secret key used to sign requests. </p>"}, "datakey": {"shape": "Key", "documentation": "<p> The encryption keys used to encrypt data. </p>"}}, "documentation": "<p> The connector-specific profile credentials required by Infor Nexus. </p>"}, "InforNexusConnectorProfileProperties": {"type": "structure", "required": ["instanceUrl"], "members": {"instanceUrl": {"shape": "InstanceUrl", "documentation": "<p> The location of the Infor Nexus resource. </p>"}}, "documentation": "<p> The connector-specific profile properties required by Infor Nexus. </p>"}, "InforNexusMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to Infor Nexus. </p>"}, "InforNexusSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Infor Nexus flow source. </p>"}}, "documentation": "<p> The properties that are applied when Infor Nexus is being used as a source. </p>"}, "InstanceUrl": {"type": "string", "max": 256, "pattern": "\\S+"}, "InternalServerException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p> An internal service error occurred during the processing of your request. Try again later. </p>", "error": {"httpStatusCode": 500}, "exception": true}, "JavaBoolean": {"type": "boolean"}, "KMSArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws:kms:.*:[0-9]+:.*"}, "Key": {"type": "string", "max": 512, "pattern": "\\S+"}, "Label": {"type": "string", "max": 128, "pattern": ".*"}, "LambdaConnectorProvisioningConfig": {"type": "structure", "required": ["lambdaArn"], "members": {"lambdaArn": {"shape": "ARN", "documentation": "<p>Lambda ARN of the connector being registered.</p>"}}, "documentation": "<p>Contains information about the configuration of the lambda which is being registered as the connector.</p>"}, "ListConnectorEntitiesRequest": {"type": "structure", "members": {"connectorProfileName": {"shape": "ConnectorProfileName", "documentation": "<p> The name of the connector profile. The name is unique for each <code>ConnectorProfile</code> in the Amazon Web Services account, and is used to query the downstream connector. </p>"}, "connectorType": {"shape": "ConnectorType", "documentation": "<p> The type of connector, such as Salesforce, Amplitude, and so on. </p>"}, "entitiesPath": {"shape": "EntitiesPath", "documentation": "<p> This optional parameter is specific to connector implementation. Some connectors support multiple levels or categories of entities. You can find out the list of roots for such providers by sending a request without the <code>entitiesPath</code> parameter. If the connector supports entities at different roots, this initial request returns the list of roots. Otherwise, this request returns all entities supported by the provider. </p>"}, "apiVersion": {"shape": "ApiVersion", "documentation": "<p>The version of the API that's used by the connector.</p>"}, "maxResults": {"shape": "ListEntitiesMaxResults", "documentation": "<p>The maximum number of items that the operation returns in the response.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that was provided by your prior <code>ListConnectorEntities</code> operation if the response was too big for the page size. You specify this token to get the next page of results in paginated response.</p>"}}}, "ListConnectorEntitiesResponse": {"type": "structure", "required": ["connectorEntityMap"], "members": {"connectorEntityMap": {"shape": "ConnectorEntityMap", "documentation": "<p> The response of <code>ListConnectorEntities</code> lists entities grouped by category. This map's key represents the group name, and its value contains the list of entities belonging to that group. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>A token that you specify in your next <code>ListConnectorEntities</code> operation to get the next page of results in paginated response. The <code>ListConnectorEntities</code> operation provides this token if the response is too big for the page size.</p>"}}}, "ListConnectorsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p>Specifies the maximum number of items that should be returned in the result set. The default for <code>maxResults</code> is 20 (for all paginated API operations).</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token for the next page of data.</p>"}}}, "ListConnectorsResponse": {"type": "structure", "members": {"connectors": {"shape": "ConnectorList", "documentation": "<p>Contains information about the connectors supported by Amazon AppFlow.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The pagination token for the next page of data. If nextToken=null, this means that all records have been fetched.</p>"}}}, "ListEntitiesMaxResults": {"type": "integer", "max": 10000, "min": 1}, "ListFlowsRequest": {"type": "structure", "members": {"maxResults": {"shape": "MaxResults", "documentation": "<p> Specifies the maximum number of items that should be returned in the result set. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The pagination token for next page of data. </p>"}}}, "ListFlowsResponse": {"type": "structure", "members": {"flows": {"shape": "FlowList", "documentation": "<p> The list of flows associated with your account. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p> The pagination token for next page of data. </p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "ARN", "documentation": "<p> The Amazon Resource Name (ARN) of the specified flow. </p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p> The tags used to organize, track, or control access for your flow. </p>"}}}, "LogoURL": {"type": "string", "max": 256, "pattern": "^(https?|ftp|file)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]"}, "LogonLanguage": {"type": "string", "max": 2, "pattern": "^[a-zA-Z0-9_]*$"}, "Long": {"type": "long"}, "LookoutMetricsDestinationProperties": {"type": "structure", "members": {}, "documentation": "<p> The properties that are applied when Amazon Lookout for Metrics is used as a destination. </p>"}, "MarketoConnectorOperator": {"type": "string", "enum": ["PROJECTION", "LESS_THAN", "GREATER_THAN", "BETWEEN", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "MarketoConnectorProfileCredentials": {"type": "structure", "required": ["clientId", "clientSecret"], "members": {"clientId": {"shape": "ClientId", "documentation": "<p> The identifier for the desired client. </p>"}, "clientSecret": {"shape": "ClientSecret", "documentation": "<p> The client secret used by the OAuth client to authenticate to the authorization server. </p>"}, "accessToken": {"shape": "AccessToken", "documentation": "<p> The credentials used to access protected Marketo resources. </p>"}, "oAuthRequest": {"shape": "ConnectorOAuthRequest", "documentation": "<p> The OAuth requirement needed to request security tokens from the connector endpoint. </p>"}}, "documentation": "<p> The connector-specific profile credentials required by Marketo. </p>"}, "MarketoConnectorProfileProperties": {"type": "structure", "required": ["instanceUrl"], "members": {"instanceUrl": {"shape": "InstanceUrl", "documentation": "<p> The location of the Marketo resource. </p>"}}, "documentation": "<p> The connector-specific profile properties required when using Marketo. </p>"}, "MarketoDestinationProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p>The object specified in the Marketo flow destination.</p>"}, "errorHandlingConfig": {"shape": "ErrorHandlingConfig"}}, "documentation": "<p>The properties that Amazon AppFlow applies when you use Marketo as a flow destination.</p>"}, "MarketoMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to Marketo. </p>"}, "MarketoSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Marketo flow source. </p>"}}, "documentation": "<p> The properties that are applied when Marketo is being used as a source. </p>"}, "MaxResults": {"type": "integer", "max": 100, "min": 1}, "MetadataCatalogConfig": {"type": "structure", "members": {"glueDataCatalog": {"shape": "GlueDataCatalogConfig", "documentation": "<p>Specifies the configuration that Amazon AppFlow uses when it catalogs your data with the Glue Data Catalog.</p>"}}, "documentation": "<p>Specifies the configuration that Amazon AppFlow uses when it catalogs your data. When Amazon AppFlow catalogs your data, it stores metadata in a data catalog.</p>"}, "MetadataCatalogDetail": {"type": "structure", "members": {"catalogType": {"shape": "CatalogType", "documentation": "<p>The type of metadata catalog that Amazon AppFlow used for the associated flow run. This parameter returns the following value:</p> <dl> <dt>GLUE</dt> <dd> <p>The metadata catalog is provided by the Glue Data Catalog. Glue includes the Glue Data Catalog as a component.</p> </dd> </dl>"}, "tableName": {"shape": "String", "documentation": "<p>The name of the table that stores the metadata for the associated flow run. The table stores metadata that represents the data that the flow transferred. Amazon AppFlow stores the table in the metadata catalog.</p>"}, "tableRegistrationOutput": {"shape": "RegistrationOutput", "documentation": "<p>Describes the status of the attempt from Amazon AppFlow to register the metadata table with the metadata catalog. Amazon AppFlow creates or updates this table for the associated flow run.</p>"}, "partitionRegistrationOutput": {"shape": "RegistrationOutput", "documentation": "<p>Describes the status of the attempt from Amazon AppFlow to register the data partitions with the metadata catalog. The data partitions organize the flow output into a hierarchical path, such as a folder path in an S3 bucket. Amazon AppFlow creates the partitions (if they don't already exist) based on your flow configuration.</p>"}}, "documentation": "<p>Describes the metadata catalog, metadata table, and data partitions that Amazon AppFlow used for the associated flow run.</p>"}, "MetadataCatalogDetails": {"type": "list", "member": {"shape": "MetadataCatalogDetail"}}, "MostRecentExecutionMessage": {"type": "string", "max": 2048, "pattern": "[\\w!@#\\-.?,\\s]*"}, "Name": {"type": "string", "max": 128, "pattern": "\\S+"}, "NextToken": {"type": "string", "max": 2048, "pattern": "\\S+"}, "OAuth2Credentials": {"type": "structure", "members": {"clientId": {"shape": "ClientId", "documentation": "<p>The identifier for the desired client.</p>"}, "clientSecret": {"shape": "ClientSecret", "documentation": "<p>The client secret used by the OAuth client to authenticate to the authorization server.</p>"}, "accessToken": {"shape": "AccessToken", "documentation": "<p>The access token used to access the connector on your behalf.</p>"}, "refreshToken": {"shape": "RefreshToken", "documentation": "<p>The refresh token used to refresh an expired access token.</p>"}, "oAuthRequest": {"shape": "ConnectorOAuthRequest"}}, "documentation": "<p>The OAuth 2.0 credentials required for OAuth 2.0 authentication.</p>"}, "OAuth2CustomParameter": {"type": "structure", "members": {"key": {"shape": "Key", "documentation": "<p>The key of the custom parameter required for OAuth 2.0 authentication.</p>"}, "isRequired": {"shape": "Boolean", "documentation": "<p>Indicates whether the custom parameter for OAuth 2.0 authentication is required.</p>"}, "label": {"shape": "Label", "documentation": "<p>The label of the custom parameter used for OAuth 2.0 authentication.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description about the custom parameter used for OAuth 2.0 authentication.</p>"}, "isSensitiveField": {"shape": "Boolean", "documentation": "<p>Indicates whether this authentication custom parameter is a sensitive field.</p>"}, "connectorSuppliedValues": {"shape": "ConnectorSuppliedValueList", "documentation": "<p>Contains default values for this authentication parameter that are supplied by the connector.</p>"}, "type": {"shape": "OAuth2CustomPropType", "documentation": "<p>Indicates whether custom parameter is used with TokenUrl or AuthUrl.</p>"}}, "documentation": "<p>Custom parameter required for OAuth 2.0 authentication.</p>"}, "OAuth2CustomPropType": {"type": "string", "enum": ["TOKEN_URL", "AUTH_URL"]}, "OAuth2CustomPropertiesList": {"type": "list", "member": {"shape": "OAuth2CustomParameter"}}, "OAuth2Defaults": {"type": "structure", "members": {"oauthScopes": {"shape": "OAuthScopeList", "documentation": "<p>OAuth 2.0 scopes that the connector supports.</p>"}, "tokenUrls": {"shape": "TokenUrlList", "documentation": "<p>Token URLs that can be used for OAuth 2.0 authentication.</p>"}, "authCodeUrls": {"shape": "AuthCodeUrlList", "documentation": "<p>Auth code URLs that can be used for OAuth 2.0 authentication.</p>"}, "oauth2GrantTypesSupported": {"shape": "OAuth2GrantTypeSupportedList", "documentation": "<p>OAuth 2.0 grant types supported by the connector.</p>"}, "oauth2CustomProperties": {"shape": "OAuth2CustomPropertiesList", "documentation": "<p>List of custom parameters required for OAuth 2.0 authentication.</p>"}}, "documentation": "<p>Contains the default values required for OAuth 2.0 authentication.</p>"}, "OAuth2GrantType": {"type": "string", "enum": ["CLIENT_CREDENTIALS", "AUTHORIZATION_CODE"]}, "OAuth2GrantTypeSupportedList": {"type": "list", "member": {"shape": "OAuth2GrantType"}}, "OAuth2Properties": {"type": "structure", "required": ["tokenUrl", "oAuth2GrantType"], "members": {"tokenUrl": {"shape": "TokenUrl", "documentation": "<p>The token URL required for OAuth 2.0 authentication.</p>"}, "oAuth2GrantType": {"shape": "OAuth2GrantType", "documentation": "<p>The OAuth 2.0 grant type used by connector for OAuth 2.0 authentication.</p>"}, "tokenUrlCustomProperties": {"shape": "TokenUrlCustomProperties", "documentation": "<p>Associates your token URL with a map of properties that you define. Use this parameter to provide any additional details that the connector requires to authenticate your request.</p>"}}, "documentation": "<p>The OAuth 2.0 properties required for OAuth 2.0 authentication.</p>"}, "OAuthCredentials": {"type": "structure", "required": ["clientId", "clientSecret"], "members": {"clientId": {"shape": "ClientId", "documentation": "<p> The identifier for the desired client. </p>"}, "clientSecret": {"shape": "ClientSecret", "documentation": "<p> The client secret used by the OAuth client to authenticate to the authorization server. </p>"}, "accessToken": {"shape": "AccessToken", "documentation": "<p> The access token used to access protected SAPOData resources. </p>"}, "refreshToken": {"shape": "RefreshToken", "documentation": "<p> The refresh token used to refresh expired access token. </p>"}, "oAuthRequest": {"shape": "ConnectorOAuthRequest", "documentation": "<p> The OAuth requirement needed to request security tokens from the connector endpoint. </p>"}}, "documentation": "<p> The OAuth credentials required for OAuth type authentication. </p>"}, "OAuthProperties": {"type": "structure", "required": ["tokenUrl", "authCodeUrl", "oAuthScopes"], "members": {"tokenUrl": {"shape": "TokenUrl", "documentation": "<p> The token url required to fetch access/refresh tokens using authorization code and also to refresh expired access token using refresh token.</p>"}, "authCodeUrl": {"shape": "AuthCodeUrl", "documentation": "<p> The authorization code url required to redirect to SAP Login Page to fetch authorization code for OAuth type authentication. </p>"}, "oAuthScopes": {"shape": "OAuthScopeList", "documentation": "<p> The OAuth scopes required for OAuth type authentication. </p>"}}, "documentation": "<p> The OAuth properties required for OAuth type authentication. </p>"}, "OAuthScope": {"type": "string", "max": 128, "pattern": "\\S+"}, "OAuthScopeList": {"type": "list", "member": {"shape": "OAuthScope"}}, "Object": {"type": "string", "max": 512, "pattern": "\\S+"}, "ObjectTypeName": {"type": "string", "max": 255, "pattern": "\\S+"}, "Operator": {"type": "string", "enum": ["PROJECTION", "LESS_THAN", "GREATER_THAN", "CONTAINS", "BETWEEN", "LESS_THAN_OR_EQUAL_TO", "GREATER_THAN_OR_EQUAL_TO", "EQUAL_TO", "NOT_EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "OperatorPropertiesKeys": {"type": "string", "enum": ["VALUE", "VALUES", "DATA_TYPE", "UPPER_BOUND", "LOWER_BOUND", "SOURCE_DATA_TYPE", "DESTINATION_DATA_TYPE", "VALIDATION_ACTION", "MASK_VALUE", "MASK_LENGTH", "TRUNCATE_LENGTH", "MATH_OPERATION_FIELDS_ORDER", "CONCAT_FORMAT", "SUBFIELD_CATEGORY_MAP", "EXCLUDE_SOURCE_FIELDS_LIST", "INCLUDE_NEW_FIELDS", "ORDERED_PARTITION_KEYS_LIST"]}, "Operators": {"type": "string", "enum": ["PROJECTION", "LESS_THAN", "GREATER_THAN", "CONTAINS", "BETWEEN", "LESS_THAN_OR_EQUAL_TO", "GREATER_THAN_OR_EQUAL_TO", "EQUAL_TO", "NOT_EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "PardotConnectorOperator": {"type": "string", "enum": ["PROJECTION", "EQUAL_TO", "NO_OP", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC"]}, "PardotConnectorProfileCredentials": {"type": "structure", "members": {"accessToken": {"shape": "AccessToken", "documentation": "<p>The credentials used to access protected Salesforce Pardot resources.</p>"}, "refreshToken": {"shape": "RefreshToken", "documentation": "<p>The credentials used to acquire new access tokens.</p>"}, "oAuthRequest": {"shape": "ConnectorOAuthRequest"}, "clientCredentialsArn": {"shape": "ClientCredentialsArn", "documentation": "<p>The secret manager ARN, which contains the client ID and client secret of the connected app.</p>"}}, "documentation": "<p>The connector-specific profile credentials required when using Salesforce Pardot.</p>"}, "PardotConnectorProfileProperties": {"type": "structure", "members": {"instanceUrl": {"shape": "InstanceUrl", "documentation": "<p>The location of the Salesforce Pardot resource.</p>"}, "isSandboxEnvironment": {"shape": "Boolean", "documentation": "<p>Indicates whether the connector profile applies to a sandbox or production environment.</p>"}, "businessUnitId": {"shape": "BusinessUnitId", "documentation": "<p>The business unit id of Salesforce Pardot instance.</p>"}}, "documentation": "<p>The connector-specific profile properties required when using Salesforce Pardot.</p>"}, "PardotMetadata": {"type": "structure", "members": {}, "documentation": "<p>The connector metadata specific to Salesforce Pardot.</p>"}, "PardotSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p>The object specified in the Salesforce Pardot flow source.</p>"}}, "documentation": "<p>The properties that are applied when Salesforce Pardot is being used as a source.</p>"}, "Password": {"type": "string", "max": 512, "pattern": ".*", "sensitive": true}, "PathPrefix": {"type": "string", "enum": ["EXECUTION_ID", "SCHEMA_VERSION"]}, "PathPrefixHierarchy": {"type": "list", "member": {"shape": "PathPrefix"}}, "PortNumber": {"type": "integer", "max": 65535, "min": 1}, "PrefixConfig": {"type": "structure", "members": {"prefixType": {"shape": "PrefixType", "documentation": "<p>Determines the format of the prefix, and whether it applies to the file name, file path, or both. </p>"}, "prefixFormat": {"shape": "PrefixFormat", "documentation": "<p>Determines the level of granularity for the date and time that's included in the prefix. </p>"}, "pathPrefixHierarchy": {"shape": "PathPrefixHierarchy", "documentation": "<p>Specifies whether the destination file path includes either or both of the following elements:</p> <dl> <dt>EXECUTION_ID</dt> <dd> <p>The ID that Amazon AppFlow assigns to the flow run.</p> </dd> <dt>SCHEMA_VERSION</dt> <dd> <p>The version number of your data schema. Amazon AppFlow assigns this version number. The version number increases by one when you change any of the following settings in your flow configuration:</p> <ul> <li> <p>Source-to-destination field mappings</p> </li> <li> <p>Field data types</p> </li> <li> <p>Partition keys</p> </li> </ul> </dd> </dl>"}}, "documentation": "<p>Specifies elements that Amazon AppFlow includes in the file and folder names in the flow destination.</p>"}, "PrefixFormat": {"type": "string", "enum": ["YEAR", "MONTH", "DAY", "HOUR", "MINUTE"]}, "PrefixType": {"type": "string", "enum": ["FILENAME", "PATH", "PATH_AND_FILENAME"]}, "PrivateConnectionProvisioningFailureCause": {"type": "string", "enum": ["CONNECTOR_AUTHENTICATION", "CONNECTOR_SERVER", "INTERNAL_SERVER", "ACCESS_DENIED", "VALIDATION"]}, "PrivateConnectionProvisioningFailureMessage": {"type": "string", "max": 2048, "pattern": "[\\s\\w/!@#+=.-]*"}, "PrivateConnectionProvisioningState": {"type": "structure", "members": {"status": {"shape": "PrivateConnectionProvisioningStatus", "documentation": "<p> Specifies the private connection provisioning status. </p>"}, "failureMessage": {"shape": "PrivateConnectionProvisioningFailureMessage", "documentation": "<p> Specifies the private connection provisioning failure reason. </p>"}, "failureCause": {"shape": "PrivateConnectionProvisioningFailureCause", "documentation": "<p> Specifies the private connection provisioning failure cause. </p>"}}, "documentation": "<p> Specifies the private connection provisioning state. </p>"}, "PrivateConnectionProvisioningStatus": {"type": "string", "enum": ["FAILED", "PENDING", "CREATED"]}, "PrivateLinkServiceName": {"type": "string", "max": 512, "pattern": "^$|com.amazonaws.vpce.[\\w/!:@#.\\-]+"}, "ProfilePropertiesMap": {"type": "map", "key": {"shape": "ProfileProperty<PERSON>ey"}, "value": {"shape": "ProfilePropertyValue"}, "max": 50, "min": 0}, "ProfilePropertyKey": {"type": "string", "max": 128, "min": 1, "pattern": "[\\w]+"}, "ProfilePropertyValue": {"type": "string", "max": 2048, "pattern": "\\S+"}, "Property": {"type": "string", "max": 2048, "pattern": ".+"}, "Range": {"type": "structure", "members": {"maximum": {"shape": "Double", "documentation": "<p>Maximum value supported by the field.</p>"}, "minimum": {"shape": "Double", "documentation": "<p>Minimum value supported by the field.</p>"}}, "documentation": "<p>The range of values that the property supports.</p>"}, "RedirectUri": {"type": "string", "max": 512, "pattern": "\\S+"}, "RedshiftConnectorProfileCredentials": {"type": "structure", "members": {"username": {"shape": "String", "documentation": "<p> The name of the user. </p>"}, "password": {"shape": "Password", "documentation": "<p> The password that corresponds to the user name. </p>"}}, "documentation": "<p> The connector-specific profile credentials required when using Amazon Redshift. </p>"}, "RedshiftConnectorProfileProperties": {"type": "structure", "required": ["bucketName", "roleArn"], "members": {"databaseUrl": {"shape": "DatabaseUrl", "documentation": "<p> The JDBC URL of the Amazon Redshift cluster. </p>"}, "bucketName": {"shape": "BucketName", "documentation": "<p> A name for the associated Amazon S3 bucket. </p>"}, "bucketPrefix": {"shape": "BucketPrefix", "documentation": "<p> The object key for the destination bucket in which Amazon AppFlow places the files. </p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p> The Amazon Resource Name (ARN) of IAM role that grants Amazon Redshift read-only access to Amazon S3. For more information, and for the polices that you attach to this role, see <a href=\"https://docs.aws.amazon.com/appflow/latest/userguide/security_iam_service-role-policies.html#redshift-access-s3\">Allow Amazon Redshift to access your Amazon AppFlow data in Amazon S3</a>.</p>"}, "dataApiRoleArn": {"shape": "DataApiRoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role that permits Amazon AppFlow to access your Amazon Redshift database through the Data API. For more information, and for the polices that you attach to this role, see <a href=\"https://docs.aws.amazon.com/appflow/latest/userguide/security_iam_service-role-policies.html#access-redshift\">Allow Amazon AppFlow to access Amazon Redshift databases with the Data API</a>.</p>"}, "isRedshiftServerless": {"shape": "Boolean", "documentation": "<p>Indicates whether the connector profile defines a connection to an Amazon Redshift Serverless data warehouse.</p>"}, "clusterIdentifier": {"shape": "ClusterIdentifier", "documentation": "<p>The unique ID that's assigned to an Amazon Redshift cluster.</p>"}, "workgroupName": {"shape": "WorkgroupName", "documentation": "<p>The name of an Amazon Redshift workgroup.</p>"}, "databaseName": {"shape": "DatabaseName", "documentation": "<p>The name of an Amazon Redshift database.</p>"}}, "documentation": "<p> The connector-specific profile properties when using Amazon Redshift. </p>"}, "RedshiftDestinationProperties": {"type": "structure", "required": ["object", "intermediateBucketName"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Amazon Redshift flow destination. </p>"}, "intermediateBucketName": {"shape": "BucketName", "documentation": "<p> The intermediate bucket that Amazon AppFlow uses when moving data into Amazon Redshift. </p>"}, "bucketPrefix": {"shape": "BucketPrefix", "documentation": "<p> The object key for the bucket in which Amazon AppFlow places the destination files. </p>"}, "errorHandlingConfig": {"shape": "ErrorHandlingConfig", "documentation": "<p> The settings that determine how Amazon AppFlow handles an error when placing data in the Amazon Redshift destination. For example, this setting would determine if the flow should fail after one insertion error, or continue and attempt to insert every record regardless of the initial failure. <code>ErrorHandlingConfig</code> is a part of the destination connector details. </p>"}}, "documentation": "<p> The properties that are applied when Amazon Redshift is being used as a destination. </p>"}, "RedshiftMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to Amazon Redshift. </p>"}, "RefreshToken": {"type": "string", "max": 2048, "pattern": "\\S+"}, "Region": {"type": "string", "max": 64, "pattern": "\\S+"}, "RegionList": {"type": "list", "member": {"shape": "Region"}}, "RegisterConnectorRequest": {"type": "structure", "members": {"connectorLabel": {"shape": "ConnectorLabel", "documentation": "<p> The name of the connector. The name is unique for each <code>ConnectorRegistration</code> in your Amazon Web Services account.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description about the connector that's being registered.</p>"}, "connectorProvisioningType": {"shape": "ConnectorProvisioningType", "documentation": "<p>The provisioning type of the connector. Currently the only supported value is LAMBDA. </p>"}, "connectorProvisioningConfig": {"shape": "ConnectorProvisioningConfig", "documentation": "<p>The provisioning type of the connector. Currently the only supported value is LAMBDA.</p>"}}}, "RegisterConnectorResponse": {"type": "structure", "members": {"connectorArn": {"shape": "ARN", "documentation": "<p>The ARN of the connector being registered.</p>"}}}, "RegisteredBy": {"type": "string", "max": 512, "pattern": "\\S+"}, "RegistrationOutput": {"type": "structure", "members": {"message": {"shape": "String", "documentation": "<p>Explains the status of the registration attempt from Amazon AppFlow. If the attempt fails, the message explains why.</p>"}, "result": {"shape": "String", "documentation": "<p>Indicates the number of resources that Amazon AppFlow created or updated. Possible resources include metadata tables and data partitions.</p>"}, "status": {"shape": "ExecutionStatus", "documentation": "<p>Indicates the status of the registration attempt from Amazon AppFlow.</p>"}}, "documentation": "<p>Describes the status of an attempt from Amazon AppFlow to register a resource.</p> <p>When you run a flow that you've configured to use a metadata catalog, Amazon AppFlow registers a metadata table and data partitions with that catalog. This operation provides the status of that registration attempt. The operation also indicates how many related resources Amazon AppFlow created or updated.</p>"}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p> The resource specified in the request (such as the source or destination connector profile) is not found. </p>", "error": {"httpStatusCode": 404}, "exception": true}, "RoleArn": {"type": "string", "max": 512, "pattern": "arn:aws:iam:.*:[0-9]+:.*"}, "S3ConnectorOperator": {"type": "string", "enum": ["PROJECTION", "LESS_THAN", "GREATER_THAN", "BETWEEN", "LESS_THAN_OR_EQUAL_TO", "GREATER_THAN_OR_EQUAL_TO", "EQUAL_TO", "NOT_EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "S3DestinationProperties": {"type": "structure", "required": ["bucketName"], "members": {"bucketName": {"shape": "BucketName", "documentation": "<p> The Amazon S3 bucket name in which Amazon AppFlow places the transferred data. </p>"}, "bucketPrefix": {"shape": "BucketPrefix", "documentation": "<p> The object key for the destination bucket in which Amazon AppFlow places the files. </p>"}, "s3OutputFormatConfig": {"shape": "S3OutputFormatConfig"}}, "documentation": "<p> The properties that are applied when Amazon S3 is used as a destination. </p>"}, "S3InputFileType": {"type": "string", "enum": ["CSV", "JSON"]}, "S3InputFormatConfig": {"type": "structure", "members": {"s3InputFileType": {"shape": "S3InputFileType", "documentation": "<p> The file type that Amazon AppFlow gets from your Amazon S3 bucket. </p>"}}, "documentation": "<p> When you use Amazon S3 as the source, the configuration format that you provide the flow input data. </p>"}, "S3Metadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to Amazon S3. </p>"}, "S3OutputFormatConfig": {"type": "structure", "members": {"fileType": {"shape": "FileType", "documentation": "<p> Indicates the file type that Amazon AppFlow places in the Amazon S3 bucket. </p>"}, "prefixConfig": {"shape": "PrefixConfig", "documentation": "<p> Determines the prefix that Amazon AppFlow applies to the folder name in the Amazon S3 bucket. You can name folders according to the flow frequency and date. </p>"}, "aggregationConfig": {"shape": "AggregationConfig"}, "preserveSourceDataTyping": {"shape": "JavaBoolean", "documentation": "<p>If your file output format is <PERSON>rquet, use this parameter to set whether Amazon AppFlow preserves the data types in your source data when it writes the output to Amazon S3. </p> <ul> <li> <p> <code>true</code>: Amazon AppFlow preserves the data types when it writes to Amazon S3. For example, an integer or <code>1</code> in your source data is still an integer in your output.</p> </li> <li> <p> <code>false</code>: Amazon AppFlow converts all of the source data into strings when it writes to Amazon S3. For example, an integer of <code>1</code> in your source data becomes the string <code>\"1\"</code> in the output.</p> </li> </ul>"}}, "documentation": "<p> The configuration that determines how Amazon AppFlow should format the flow output data when Amazon S3 is used as the destination. </p>"}, "S3SourceProperties": {"type": "structure", "required": ["bucketName"], "members": {"bucketName": {"shape": "BucketName", "documentation": "<p> The Amazon S3 bucket name where the source files are stored. </p>"}, "bucketPrefix": {"shape": "BucketPrefix", "documentation": "<p> The object key for the Amazon S3 bucket in which the source files are stored. </p>"}, "s3InputFormatConfig": {"shape": "S3InputFormatConfig"}}, "documentation": "<p> The properties that are applied when Amazon S3 is being used as the flow source. </p>"}, "SAPODataConnectorOperator": {"type": "string", "enum": ["PROJECTION", "LESS_THAN", "CONTAINS", "GREATER_THAN", "BETWEEN", "LESS_THAN_OR_EQUAL_TO", "GREATER_THAN_OR_EQUAL_TO", "EQUAL_TO", "NOT_EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "SAPODataConnectorProfileCredentials": {"type": "structure", "members": {"basicAuthCredentials": {"shape": "BasicAuthCredentials", "documentation": "<p> The SAPOData basic authentication credentials. </p>"}, "oAuthCredentials": {"shape": "OAuthCredentials", "documentation": "<p> The SAPOData OAuth type authentication credentials. </p>"}}, "documentation": "<p> The connector-specific profile credentials required when using SAPOData. </p>"}, "SAPODataConnectorProfileProperties": {"type": "structure", "required": ["applicationHostUrl", "applicationServicePath", "portNumber", "clientNumber"], "members": {"applicationHostUrl": {"shape": "ApplicationHostUrl", "documentation": "<p> The location of the SAPOData resource. </p>"}, "applicationServicePath": {"shape": "ApplicationServicePath", "documentation": "<p> The application path to catalog service. </p>"}, "portNumber": {"shape": "PortNumber", "documentation": "<p> The port number of the SAPOData instance. </p>", "box": true}, "clientNumber": {"shape": "ClientNumber", "documentation": "<p> The client number for the client creating the connection. </p>"}, "logonLanguage": {"shape": "LogonLanguage", "documentation": "<p> The logon language of SAPOData instance. </p>"}, "privateLinkServiceName": {"shape": "PrivateLinkServiceName", "documentation": "<p> The SAPOData Private Link service name to be used for private data transfers. </p>"}, "oAuthProperties": {"shape": "OAuthProperties", "documentation": "<p> The SAPOData OAuth properties required for OAuth type authentication. </p>"}}, "documentation": "<p> The connector-specific profile properties required when using SAPOData. </p>"}, "SAPODataDestinationProperties": {"type": "structure", "required": ["objectPath"], "members": {"objectPath": {"shape": "Object", "documentation": "<p>The object path specified in the SAPOData flow destination.</p>"}, "successResponseHandlingConfig": {"shape": "SuccessResponseHandlingConfig", "documentation": "<p>Determines how Amazon AppFlow handles the success response that it gets from the connector after placing data.</p> <p>For example, this setting would determine where to write the response from a destination connector upon a successful insert operation.</p>"}, "idFieldNames": {"shape": "IdFieldNameList"}, "errorHandlingConfig": {"shape": "ErrorHandlingConfig"}, "writeOperationType": {"shape": "WriteOperationType"}}, "documentation": "<p>The properties that are applied when using SAPOData as a flow destination</p>"}, "SAPODataMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to SAPOData. </p>"}, "SAPODataSourceProperties": {"type": "structure", "members": {"objectPath": {"shape": "Object", "documentation": "<p> The object path specified in the SAPOData flow source. </p>"}}, "documentation": "<p> The properties that are applied when using SAPOData as a flow source. </p>"}, "SalesforceConnectorOperator": {"type": "string", "enum": ["PROJECTION", "LESS_THAN", "CONTAINS", "GREATER_THAN", "BETWEEN", "LESS_THAN_OR_EQUAL_TO", "GREATER_THAN_OR_EQUAL_TO", "EQUAL_TO", "NOT_EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "SalesforceConnectorProfileCredentials": {"type": "structure", "members": {"accessToken": {"shape": "AccessToken", "documentation": "<p> The credentials used to access protected Salesforce resources. </p>"}, "refreshToken": {"shape": "RefreshToken", "documentation": "<p> The credentials used to acquire new access tokens. </p>"}, "oAuthRequest": {"shape": "ConnectorOAuthRequest", "documentation": "<p> The OAuth requirement needed to request security tokens from the connector endpoint. </p>"}, "clientCredentialsArn": {"shape": "ClientCredentialsArn", "documentation": "<p> The secret manager ARN, which contains the client ID and client secret of the connected app. </p>"}}, "documentation": "<p> The connector-specific profile credentials required when using Salesforce. </p>"}, "SalesforceConnectorProfileProperties": {"type": "structure", "members": {"instanceUrl": {"shape": "InstanceUrl", "documentation": "<p> The location of the Salesforce resource. </p>"}, "isSandboxEnvironment": {"shape": "Boolean", "documentation": "<p> Indicates whether the connector profile applies to a sandbox or production environment. </p>"}}, "documentation": "<p> The connector-specific profile properties required when using Salesforce. </p>"}, "SalesforceDataTransferApi": {"type": "string", "enum": ["AUTOMATIC", "BULKV2", "REST_SYNC"]}, "SalesforceDataTransferApiList": {"type": "list", "member": {"shape": "SalesforceDataTransferApi"}}, "SalesforceDestinationProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Salesforce flow destination. </p>"}, "idFieldNames": {"shape": "IdFieldNameList", "documentation": "<p> The name of the field that Amazon AppFlow uses as an ID when performing a write operation such as update or delete. </p>"}, "errorHandlingConfig": {"shape": "ErrorHandlingConfig", "documentation": "<p> The settings that determine how Amazon AppFlow handles an error when placing data in the Salesforce destination. For example, this setting would determine if the flow should fail after one insertion error, or continue and attempt to insert every record regardless of the initial failure. <code>ErrorHandlingConfig</code> is a part of the destination connector details. </p>"}, "writeOperationType": {"shape": "WriteOperationType", "documentation": "<p> This specifies the type of write operation to be performed in Salesforce. When the value is <code>UPSERT</code>, then <code>idFieldNames</code> is required. </p>"}, "dataTransferApi": {"shape": "SalesforceDataTransferApi", "documentation": "<p>Specifies which Salesforce API is used by Amazon AppFlow when your flow transfers data to Salesforce.</p> <dl> <dt>AUTOMATIC</dt> <dd> <p>The default. Amazon AppFlow selects which API to use based on the number of records that your flow transfers to Salesforce. If your flow transfers fewer than 1,000 records, Amazon AppFlow uses Salesforce REST API. If your flow transfers 1,000 records or more, Amazon AppFlow uses Salesforce Bulk API 2.0.</p> <p>Each of these Salesforce APIs structures data differently. If Amazon AppFlow selects the API automatically, be aware that, for recurring flows, the data output might vary from one flow run to the next. For example, if a flow runs daily, it might use REST API on one day to transfer 900 records, and it might use Bulk API 2.0 on the next day to transfer 1,100 records. For each of these flow runs, the respective Salesforce API formats the data differently. Some of the differences include how dates are formatted and null values are represented. Also, Bulk API 2.0 doesn't transfer Salesforce compound fields.</p> <p>By choosing this option, you optimize flow performance for both small and large data transfers, but the tradeoff is inconsistent formatting in the output.</p> </dd> <dt>BULKV2</dt> <dd> <p>Amazon AppFlow uses only Salesforce Bulk API 2.0. This API runs asynchronous data transfers, and it's optimal for large sets of data. By choosing this option, you ensure that your flow writes consistent output, but you optimize performance only for large data transfers.</p> <p>Note that Bulk API 2.0 does not transfer Salesforce compound fields.</p> </dd> <dt>REST_SYNC</dt> <dd> <p>Amazon AppFlow uses only Salesforce REST API. By choosing this option, you ensure that your flow writes consistent output, but you decrease performance for large data transfers that are better suited for Bulk API 2.0. In some cases, if your flow attempts to transfer a vary large set of data, it might fail with a timed out error.</p> </dd> </dl>"}}, "documentation": "<p> The properties that are applied when Salesforce is being used as a destination. </p>"}, "SalesforceMetadata": {"type": "structure", "members": {"oAuthScopes": {"shape": "OAuthScopeList", "documentation": "<p> The desired authorization scope for the Salesforce account. </p>"}, "dataTransferApis": {"shape": "SalesforceDataTransferApiList", "documentation": "<p>The Salesforce APIs that you can have Amazon AppFlow use when your flows transfers data to or from Salesforce.</p>"}}, "documentation": "<p> The connector metadata specific to Salesforce. </p>"}, "SalesforceSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Salesforce flow source. </p>"}, "enableDynamicFieldUpdate": {"shape": "Boolean", "documentation": "<p> The flag that enables dynamic fetching of new (recently added) fields in the Salesforce objects while running a flow. </p>"}, "includeDeletedRecords": {"shape": "Boolean", "documentation": "<p> Indicates whether Amazon AppFlow includes deleted files in the flow run. </p>"}, "dataTransferApi": {"shape": "SalesforceDataTransferApi", "documentation": "<p>Specifies which Salesforce API is used by Amazon AppFlow when your flow transfers data from Salesforce.</p> <dl> <dt>AUTOMATIC</dt> <dd> <p>The default. Amazon AppFlow selects which API to use based on the number of records that your flow transfers from Salesforce. If your flow transfers fewer than 1,000,000 records, Amazon AppFlow uses Salesforce REST API. If your flow transfers 1,000,000 records or more, Amazon AppFlow uses Salesforce Bulk API 2.0.</p> <p>Each of these Salesforce APIs structures data differently. If Amazon AppFlow selects the API automatically, be aware that, for recurring flows, the data output might vary from one flow run to the next. For example, if a flow runs daily, it might use REST API on one day to transfer 900,000 records, and it might use Bulk API 2.0 on the next day to transfer 1,100,000 records. For each of these flow runs, the respective Salesforce API formats the data differently. Some of the differences include how dates are formatted and null values are represented. Also, Bulk API 2.0 doesn't transfer Salesforce compound fields.</p> <p>By choosing this option, you optimize flow performance for both small and large data transfers, but the tradeoff is inconsistent formatting in the output.</p> </dd> <dt>BULKV2</dt> <dd> <p>Amazon AppFlow uses only Salesforce Bulk API 2.0. This API runs asynchronous data transfers, and it's optimal for large sets of data. By choosing this option, you ensure that your flow writes consistent output, but you optimize performance only for large data transfers.</p> <p>Note that Bulk API 2.0 does not transfer Salesforce compound fields.</p> </dd> <dt>REST_SYNC</dt> <dd> <p>Amazon AppFlow uses only Salesforce REST API. By choosing this option, you ensure that your flow writes consistent output, but you decrease performance for large data transfers that are better suited for Bulk API 2.0. In some cases, if your flow attempts to transfer a vary large set of data, it might fail wituh a timed out error.</p> </dd> </dl>"}}, "documentation": "<p> The properties that are applied when Salesforce is being used as a source. </p>"}, "ScheduleExpression": {"type": "string", "max": 256, "pattern": ".*"}, "ScheduleFrequencyType": {"type": "string", "enum": ["BYMINUTE", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "ONCE"]}, "ScheduleOffset": {"type": "long", "max": 36000, "min": 0}, "ScheduledTriggerProperties": {"type": "structure", "required": ["scheduleExpression"], "members": {"scheduleExpression": {"shape": "ScheduleExpression", "documentation": "<p> The scheduling expression that determines the rate at which the schedule will run, for example <code>rate(5minutes)</code>. </p>"}, "dataPullMode": {"shape": "DataPullMode", "documentation": "<p> Specifies whether a scheduled flow has an incremental data transfer or a complete data transfer for each flow run. </p>"}, "scheduleStartTime": {"shape": "Date", "documentation": "<p>The time at which the scheduled flow starts. The time is formatted as a timestamp that follows the ISO 8601 standard, such as <code>2022-04-26T13:00:00-07:00</code>.</p>"}, "scheduleEndTime": {"shape": "Date", "documentation": "<p>The time at which the scheduled flow ends. The time is formatted as a timestamp that follows the ISO 8601 standard, such as <code>2022-04-27T13:00:00-07:00</code>.</p>"}, "timezone": {"shape": "Timezone", "documentation": "<p>Specifies the time zone used when referring to the dates and times of a scheduled flow, such as <code>America/New_York</code>. This time zone is only a descriptive label. It doesn't affect how Amazon AppFlow interprets the timestamps that you specify to schedule the flow.</p> <p>If you want to schedule a flow by using times in a particular time zone, indicate the time zone as a UTC offset in your timestamps. For example, the UTC offsets for the <code>America/New_York</code> timezone are <code>-04:00</code> EDT and <code>-05:00 EST</code>.</p>"}, "scheduleOffset": {"shape": "ScheduleOffset", "documentation": "<p> Specifies the optional offset that is added to the time interval for a schedule-triggered flow. </p>", "box": true}, "firstExecutionFrom": {"shape": "Date", "documentation": "<p> Specifies the date range for the records to import from the connector in the first flow run. </p>"}, "flowErrorDeactivationThreshold": {"shape": "FlowErrorDeactivationThreshold", "documentation": "<p>Defines how many times a scheduled flow fails consecutively before Amazon AppFlow deactivates it.</p>", "box": true}}, "documentation": "<p> Specifies the configuration details of a schedule-triggered flow as defined by the user. Currently, these settings only apply to the <code>Scheduled</code> trigger type. </p>"}, "SchedulingFrequencyTypeList": {"type": "list", "member": {"shape": "ScheduleFrequencyType"}}, "SecretKey": {"type": "string", "max": 256, "pattern": "\\S+", "sensitive": true}, "ServiceNowConnectorOperator": {"type": "string", "enum": ["PROJECTION", "CONTAINS", "LESS_THAN", "GREATER_THAN", "BETWEEN", "LESS_THAN_OR_EQUAL_TO", "GREATER_THAN_OR_EQUAL_TO", "EQUAL_TO", "NOT_EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "ServiceNowConnectorProfileCredentials": {"type": "structure", "required": ["username", "password"], "members": {"username": {"shape": "Username", "documentation": "<p> The name of the user. </p>"}, "password": {"shape": "Password", "documentation": "<p> The password that corresponds to the user name. </p>"}}, "documentation": "<p> The connector-specific profile credentials required when using ServiceNow. </p>"}, "ServiceNowConnectorProfileProperties": {"type": "structure", "required": ["instanceUrl"], "members": {"instanceUrl": {"shape": "InstanceUrl", "documentation": "<p> The location of the ServiceNow resource. </p>"}}, "documentation": "<p> The connector-specific profile properties required when using ServiceNow. </p>"}, "ServiceNowMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to ServiceNow. </p>"}, "ServiceNowSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the ServiceNow flow source. </p>"}}, "documentation": "<p> The properties that are applied when ServiceNow is being used as a source. </p>"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p> The request would cause a service quota (such as the number of flows) to be exceeded. </p>", "error": {"httpStatusCode": 402}, "exception": true}, "SingularConnectorOperator": {"type": "string", "enum": ["PROJECTION", "EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "SingularConnectorProfileCredentials": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"apiKey": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p> A unique alphanumeric identifier used to authenticate a user, developer, or calling program to your API. </p>"}}, "documentation": "<p> The connector-specific profile credentials required when using Singular. </p>"}, "SingularConnectorProfileProperties": {"type": "structure", "members": {}, "documentation": "<p> The connector-specific profile properties required when using Singular. </p>"}, "SingularMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to Singular. </p>"}, "SingularSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Singular flow source. </p>"}}, "documentation": "<p> The properties that are applied when Singular is being used as a source. </p>"}, "SlackConnectorOperator": {"type": "string", "enum": ["PROJECTION", "LESS_THAN", "GREATER_THAN", "BETWEEN", "LESS_THAN_OR_EQUAL_TO", "GREATER_THAN_OR_EQUAL_TO", "EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "SlackConnectorProfileCredentials": {"type": "structure", "required": ["clientId", "clientSecret"], "members": {"clientId": {"shape": "ClientId", "documentation": "<p> The identifier for the client. </p>"}, "clientSecret": {"shape": "ClientSecret", "documentation": "<p> The client secret used by the OAuth client to authenticate to the authorization server. </p>"}, "accessToken": {"shape": "AccessToken", "documentation": "<p> The credentials used to access protected Slack resources. </p>"}, "oAuthRequest": {"shape": "ConnectorOAuthRequest", "documentation": "<p> The OAuth requirement needed to request security tokens from the connector endpoint. </p>"}}, "documentation": "<p> The connector-specific profile credentials required when using Slack. </p>"}, "SlackConnectorProfileProperties": {"type": "structure", "required": ["instanceUrl"], "members": {"instanceUrl": {"shape": "InstanceUrl", "documentation": "<p> The location of the Slack resource. </p>"}}, "documentation": "<p> The connector-specific profile properties required when using Slack. </p>"}, "SlackMetadata": {"type": "structure", "members": {"oAuthScopes": {"shape": "OAuthScopeList", "documentation": "<p> The desired authorization scope for the Slack account. </p>"}}, "documentation": "<p> The connector metadata specific to Slack. </p>"}, "SlackSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Slack flow source. </p>"}}, "documentation": "<p> The properties that are applied when Slack is being used as a source. </p>"}, "SnowflakeConnectorProfileCredentials": {"type": "structure", "required": ["username", "password"], "members": {"username": {"shape": "Username", "documentation": "<p> The name of the user. </p>"}, "password": {"shape": "Password", "documentation": "<p> The password that corresponds to the user name. </p>"}}, "documentation": "<p> The connector-specific profile credentials required when using Snowflake. </p>"}, "SnowflakeConnectorProfileProperties": {"type": "structure", "required": ["warehouse", "stage", "bucketName"], "members": {"warehouse": {"shape": "Warehouse", "documentation": "<p> The name of the Snowflake warehouse. </p>"}, "stage": {"shape": "Stage", "documentation": "<p> The name of the Amazon S3 stage that was created while setting up an Amazon S3 stage in the Snowflake account. This is written in the following format: &lt; Database&gt;&lt; Schema&gt;&lt;Stage Name&gt;. </p>"}, "bucketName": {"shape": "BucketName", "documentation": "<p> The name of the Amazon S3 bucket associated with Snowflake. </p>"}, "bucketPrefix": {"shape": "BucketPrefix", "documentation": "<p> The bucket path that refers to the Amazon S3 bucket associated with Snowflake. </p>"}, "privateLinkServiceName": {"shape": "PrivateLinkServiceName", "documentation": "<p> The Snowflake Private Link service name to be used for private data transfers. </p>"}, "accountName": {"shape": "Account<PERSON><PERSON>", "documentation": "<p> The name of the account. </p>"}, "region": {"shape": "Region", "documentation": "<p> The Amazon Web Services Region of the Snowflake account. </p>"}}, "documentation": "<p> The connector-specific profile properties required when using Snowflake. </p>"}, "SnowflakeDestinationProperties": {"type": "structure", "required": ["object", "intermediateBucketName"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Snowflake flow destination. </p>"}, "intermediateBucketName": {"shape": "BucketName", "documentation": "<p> The intermediate bucket that Amazon AppFlow uses when moving data into Snowflake. </p>"}, "bucketPrefix": {"shape": "BucketPrefix", "documentation": "<p> The object key for the destination bucket in which Amazon AppFlow places the files. </p>"}, "errorHandlingConfig": {"shape": "ErrorHandlingConfig", "documentation": "<p> The settings that determine how Amazon AppFlow handles an error when placing data in the Snowflake destination. For example, this setting would determine if the flow should fail after one insertion error, or continue and attempt to insert every record regardless of the initial failure. <code>ErrorHandlingConfig</code> is a part of the destination connector details. </p>"}}, "documentation": "<p> The properties that are applied when Snowflake is being used as a destination. </p>"}, "SnowflakeMetadata": {"type": "structure", "members": {"supportedRegions": {"shape": "RegionList", "documentation": "<p> Specifies the supported Amazon Web Services Regions when using Snowflake. </p>"}}, "documentation": "<p> The connector metadata specific to Snowflake. </p>"}, "SourceConnectorProperties": {"type": "structure", "members": {"Amplitude": {"shape": "AmplitudeSourceProperties", "documentation": "<p> Specifies the information that is required for querying Amplitude. </p>"}, "Datadog": {"shape": "DatadogSourceProperties", "documentation": "<p> Specifies the information that is required for querying Datadog. </p>"}, "Dynatrace": {"shape": "DynatraceSourceProperties", "documentation": "<p> Specifies the information that is required for querying Dynatrace. </p>"}, "GoogleAnalytics": {"shape": "GoogleAnalyticsSourceProperties", "documentation": "<p> Specifies the information that is required for querying Google Analytics. </p>"}, "InforNexus": {"shape": "InforNexusSourceProperties", "documentation": "<p> Specifies the information that is required for querying Infor Nexus. </p>"}, "Marketo": {"shape": "MarketoSourceProperties", "documentation": "<p> Specifies the information that is required for querying Marketo. </p>"}, "S3": {"shape": "S3SourceProperties", "documentation": "<p> Specifies the information that is required for querying Amazon S3. </p>"}, "Salesforce": {"shape": "SalesforceSourceProperties", "documentation": "<p> Specifies the information that is required for querying Salesforce. </p>"}, "ServiceNow": {"shape": "ServiceNowSourceProperties", "documentation": "<p> Specifies the information that is required for querying ServiceNow. </p>"}, "Singular": {"shape": "SingularSourceProperties", "documentation": "<p> Specifies the information that is required for querying Singular. </p>"}, "Slack": {"shape": "SlackSourceProperties", "documentation": "<p> Specifies the information that is required for querying Slack. </p>"}, "Trendmicro": {"shape": "TrendmicroSourceProperties", "documentation": "<p> Specifies the information that is required for querying Trend Micro. </p>"}, "Veeva": {"shape": "VeevaSourceProperties", "documentation": "<p> Specifies the information that is required for querying <PERSON><PERSON><PERSON>. </p>"}, "Zendesk": {"shape": "ZendeskSourceProperties", "documentation": "<p> Specifies the information that is required for querying Zendesk. </p>"}, "SAPOData": {"shape": "SAPODataSourceProperties"}, "CustomConnector": {"shape": "CustomConnectorSourceProperties"}, "Pardot": {"shape": "PardotSourceProperties", "documentation": "<p>Specifies the information that is required for querying Salesforce Pardot.</p>"}}, "documentation": "<p> Specifies the information that is required to query a particular connector. </p>"}, "SourceFieldProperties": {"type": "structure", "members": {"isRetrievable": {"shape": "Boolean", "documentation": "<p> Indicates whether the field can be returned in a search result. </p>"}, "isQueryable": {"shape": "Boolean", "documentation": "<p> Indicates if the field can be queried. </p>"}, "isTimestampFieldForIncrementalQueries": {"shape": "Boolean", "documentation": "<p>Indicates if this timestamp field can be used for incremental queries.</p>"}}, "documentation": "<p> The properties that can be applied to a field when the connector is being used as a source. </p>"}, "SourceFields": {"type": "list", "member": {"shape": "String"}}, "SourceFlowConfig": {"type": "structure", "required": ["connectorType", "sourceConnectorProperties"], "members": {"connectorType": {"shape": "ConnectorType", "documentation": "<p> The type of connector, such as Salesforce, Amplitude, and so on. </p>"}, "apiVersion": {"shape": "ApiVersion", "documentation": "<p>The API version of the connector when it's used as a source in the flow.</p>"}, "connectorProfileName": {"shape": "ConnectorProfileName", "documentation": "<p> The name of the connector profile. This name must be unique for each connector profile in the Amazon Web Services account. </p>"}, "sourceConnectorProperties": {"shape": "SourceConnectorProperties", "documentation": "<p> Specifies the information that is required to query a particular source connector. </p>"}, "incrementalPullConfig": {"shape": "IncrementalPullConfig", "documentation": "<p> Defines the configuration for a scheduled incremental data pull. If a valid configuration is provided, the fields specified in the configuration are used when querying for the incremental data pull. </p>"}}, "documentation": "<p> Contains information about the configuration of the source connector used in the flow. </p>"}, "Stage": {"type": "string", "max": 512, "pattern": "\\S+"}, "StartFlowRequest": {"type": "structure", "required": ["flowName"], "members": {"flowName": {"shape": "FlowName", "documentation": "<p> The specified name of the flow. Spaces are not allowed. Use underscores (_) or hyphens (-) only. </p>"}}}, "StartFlowResponse": {"type": "structure", "members": {"flowArn": {"shape": "FlowArn", "documentation": "<p> The flow's Amazon Resource Name (ARN). </p>"}, "flowStatus": {"shape": "FlowStatus", "documentation": "<p> Indicates the current status of the flow. </p>"}, "executionId": {"shape": "ExecutionId", "documentation": "<p> Returns the internal execution ID of an on-demand flow when the flow is started. For scheduled or event-triggered flows, this value is null. </p>"}}}, "StopFlowRequest": {"type": "structure", "required": ["flowName"], "members": {"flowName": {"shape": "FlowName", "documentation": "<p> The specified name of the flow. Spaces are not allowed. Use underscores (_) or hyphens (-) only. </p>"}}}, "StopFlowResponse": {"type": "structure", "members": {"flowArn": {"shape": "FlowArn", "documentation": "<p> The flow's Amazon Resource Name (ARN). </p>"}, "flowStatus": {"shape": "FlowStatus", "documentation": "<p> Indicates the current status of the flow. </p>"}}}, "String": {"type": "string", "max": 2048, "pattern": ".*"}, "SuccessResponseHandlingConfig": {"type": "structure", "members": {"bucketPrefix": {"shape": "BucketPrefix", "documentation": "<p>The Amazon S3 bucket prefix.</p>"}, "bucketName": {"shape": "BucketName", "documentation": "<p>The name of the Amazon S3 bucket.</p>"}}, "documentation": "<p>Determines how Amazon AppFlow handles the success response that it gets from the connector after placing data.</p> <p>For example, this setting would determine where to write the response from the destination connector upon a successful insert operation.</p>"}, "SupportedApiVersion": {"type": "string", "max": 256, "pattern": "\\S+"}, "SupportedApiVersionList": {"type": "list", "member": {"shape": "SupportedApiVersion"}}, "SupportedFieldTypeDetails": {"type": "structure", "required": ["v1"], "members": {"v1": {"shape": "FieldTypeDetails", "documentation": "<p> The initial supported version for <code>fieldType</code>. If this is later changed to a different version, v2 will be introduced. </p>"}}, "documentation": "<p> Contains details regarding all the supported <code>FieldTypes</code> and their corresponding <code>filterOperators</code> and <code>supportedValues</code>. </p>"}, "SupportedOperatorList": {"type": "list", "member": {"shape": "Operators"}}, "SupportedValueList": {"type": "list", "member": {"shape": "Value"}}, "SupportedWriteOperationList": {"type": "list", "member": {"shape": "WriteOperationType"}}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!aws:)[a-zA-Z+-=._:/]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "ARN", "documentation": "<p> The Amazon Resource Name (ARN) of the flow that you want to tag. </p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p> The tags used to organize, track, or control access for your flow. </p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "pattern": "[\\s\\w+-=\\.:/@]*"}, "Task": {"type": "structure", "required": ["sourceFields", "taskType"], "members": {"sourceFields": {"shape": "SourceFields", "documentation": "<p> The source fields to which a particular task is applied. </p>"}, "connectorOperator": {"shape": "ConnectorOperator", "documentation": "<p> The operation to be performed on the provided source fields. </p>"}, "destinationField": {"shape": "DestinationField", "documentation": "<p> A field in a destination connector, or a field value against which Amazon AppFlow validates a source field. </p>"}, "taskType": {"shape": "TaskType", "documentation": "<p> Specifies the particular task implementation that Amazon AppFlow performs. </p>"}, "taskProperties": {"shape": "TaskPropertiesMap", "documentation": "<p> A map used to store task-related information. The execution service looks for particular information based on the <code>TaskType</code>. </p>"}}, "documentation": "<p> A class for modeling different type of tasks. Task implementation varies based on the <code>TaskType</code>. </p>"}, "TaskPropertiesMap": {"type": "map", "key": {"shape": "OperatorPropertiesKeys"}, "value": {"shape": "Property"}}, "TaskType": {"type": "string", "enum": ["Arithmetic", "Filter", "Map", "Map_all", "Mask", "<PERSON><PERSON>", "Passthrough", "Truncate", "Validate", "Partition"]}, "Tasks": {"type": "list", "member": {"shape": "Task"}}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p>API calls have exceeded the maximum allowed API request rate per account and per Region. </p>", "error": {"httpStatusCode": 429}, "exception": true}, "Timezone": {"type": "string", "max": 256, "pattern": ".*"}, "TokenUrl": {"type": "string", "max": 256, "pattern": "^(https?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]"}, "TokenUrlCustomProperties": {"type": "map", "key": {"shape": "CustomPropertyKey"}, "value": {"shape": "CustomPropertyValue"}, "max": 50, "min": 0}, "TokenUrlList": {"type": "list", "member": {"shape": "TokenUrl"}}, "TrendmicroConnectorOperator": {"type": "string", "enum": ["PROJECTION", "EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "TrendmicroConnectorProfileCredentials": {"type": "structure", "required": ["apiSecret<PERSON>ey"], "members": {"apiSecretKey": {"shape": "ApiSecret<PERSON>ey", "documentation": "<p> The Secret Access Key portion of the credentials. </p>"}}, "documentation": "<p> The connector-specific profile credentials required when using Trend Micro. </p>"}, "TrendmicroConnectorProfileProperties": {"type": "structure", "members": {}, "documentation": "<p> The connector-specific profile properties required when using Trend Micro. </p>"}, "TrendmicroMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to Trend Micro. </p>"}, "TrendmicroSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Trend Micro flow source. </p>"}}, "documentation": "<p> The properties that are applied when using Trend Micro as a flow source. </p>"}, "TriggerConfig": {"type": "structure", "required": ["triggerType"], "members": {"triggerType": {"shape": "TriggerType", "documentation": "<p> Specifies the type of flow trigger. This can be <code>OnDemand</code>, <code>Scheduled</code>, or <code>Event</code>. </p>"}, "triggerProperties": {"shape": "TriggerProperties", "documentation": "<p> Specifies the configuration details of a schedule-triggered flow as defined by the user. Currently, these settings only apply to the <code>Scheduled</code> trigger type. </p>"}}, "documentation": "<p> The trigger settings that determine how and when Amazon AppFlow runs the specified flow. </p>"}, "TriggerProperties": {"type": "structure", "members": {"Scheduled": {"shape": "ScheduledTriggerProperties", "documentation": "<p> Specifies the configuration details of a schedule-triggered flow as defined by the user. </p>"}}, "documentation": "<p> Specifies the configuration details that control the trigger for a flow. Currently, these settings only apply to the <code>Scheduled</code> trigger type. </p>"}, "TriggerType": {"type": "string", "enum": ["Scheduled", "Event", "OnDemand"]}, "TriggerTypeList": {"type": "list", "member": {"shape": "TriggerType"}}, "UnregisterConnectorRequest": {"type": "structure", "required": ["connectorLabel"], "members": {"connectorLabel": {"shape": "ConnectorLabel", "documentation": "<p>The label of the connector. The label is unique for each <code>ConnectorRegistration</code> in your Amazon Web Services account.</p>"}, "forceDelete": {"shape": "Boolean", "documentation": "<p>Indicates whether Amazon AppFlow should unregister the connector, even if it is currently in use in one or more connector profiles. The default value is false.</p>"}}}, "UnregisterConnectorResponse": {"type": "structure", "members": {}}, "UnsupportedOperationException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p> The requested operation is not supported for the current flow. </p>", "error": {"httpStatusCode": 400}, "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "ARN", "documentation": "<p> The Amazon Resource Name (ARN) of the flow that you want to untag. </p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p> The tag keys associated with the tag that you want to remove from your flow. </p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateConnectorProfileRequest": {"type": "structure", "required": ["connectorProfileName", "connectionMode", "connectorProfileConfig"], "members": {"connectorProfileName": {"shape": "ConnectorProfileName", "documentation": "<p> The name of the connector profile and is unique for each <code>ConnectorProfile</code> in the Amazon Web Services account. </p>"}, "connectionMode": {"shape": "ConnectionMode", "documentation": "<p> Indicates the connection mode and if it is public or private. </p>"}, "connectorProfileConfig": {"shape": "ConnectorProfileConfig", "documentation": "<p> Defines the connector-specific profile configuration and credentials. </p>"}}}, "UpdateConnectorProfileResponse": {"type": "structure", "members": {"connectorProfileArn": {"shape": "ConnectorProfileArn", "documentation": "<p> The Amazon Resource Name (ARN) of the connector profile. </p>"}}}, "UpdateConnectorRegistrationRequest": {"type": "structure", "required": ["connectorLabel"], "members": {"connectorLabel": {"shape": "ConnectorLabel", "documentation": "<p>The name of the connector. The name is unique for each connector registration in your AWS account.</p>"}, "description": {"shape": "Description", "documentation": "<p>A description about the update that you're applying to the connector.</p>"}, "connectorProvisioningConfig": {"shape": "ConnectorProvisioningConfig"}}}, "UpdateConnectorRegistrationResponse": {"type": "structure", "members": {"connectorArn": {"shape": "ARN", "documentation": "<p>The ARN of the connector being updated.</p>"}}}, "UpdateFlowRequest": {"type": "structure", "required": ["flowName", "triggerConfig", "sourceFlowConfig", "destinationFlowConfigList", "tasks"], "members": {"flowName": {"shape": "FlowName", "documentation": "<p> The specified name of the flow. Spaces are not allowed. Use underscores (_) or hyphens (-) only. </p>"}, "description": {"shape": "FlowDescription", "documentation": "<p> A description of the flow. </p>"}, "triggerConfig": {"shape": "TriggerConfig", "documentation": "<p> The trigger settings that determine how and when the flow runs. </p>"}, "sourceFlowConfig": {"shape": "SourceFlowConfig"}, "destinationFlowConfigList": {"shape": "DestinationFlowConfigList", "documentation": "<p> The configuration that controls how Amazon AppFlow transfers data to the destination connector. </p>"}, "tasks": {"shape": "Tasks", "documentation": "<p> A list of tasks that Amazon AppFlow performs while transferring the data in the flow run. </p>"}, "metadataCatalogConfig": {"shape": "MetadataCatalogConfig", "documentation": "<p>Specifies the configuration that Amazon AppFlow uses when it catalogs the data that's transferred by the associated flow. When Amazon AppFlow catalogs the data from a flow, it stores metadata in a data catalog.</p>"}}}, "UpdateFlowResponse": {"type": "structure", "members": {"flowStatus": {"shape": "FlowStatus", "documentation": "<p>Indicates the current status of the flow. </p>"}}}, "UpdatedBy": {"type": "string", "max": 256, "pattern": "\\S+"}, "UpsolverBucketName": {"type": "string", "max": 63, "min": 16, "pattern": "^(upsolver-appflow)\\S*"}, "UpsolverDestinationProperties": {"type": "structure", "required": ["bucketName", "s3OutputFormatConfig"], "members": {"bucketName": {"shape": "UpsolverBucketName", "documentation": "<p> The Upsolver Amazon S3 bucket name in which Amazon AppFlow places the transferred data. </p>"}, "bucketPrefix": {"shape": "BucketPrefix", "documentation": "<p> The object key for the destination Upsolver Amazon S3 bucket in which Amazon AppFlow places the files. </p>"}, "s3OutputFormatConfig": {"shape": "UpsolverS3OutputFormatConfig", "documentation": "<p> The configuration that determines how data is formatted when Upsolver is used as the flow destination. </p>"}}, "documentation": "<p> The properties that are applied when Upsolver is used as a destination. </p>"}, "UpsolverMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to Upsolver. </p>"}, "UpsolverS3OutputFormatConfig": {"type": "structure", "required": ["prefixConfig"], "members": {"fileType": {"shape": "FileType", "documentation": "<p> Indicates the file type that Amazon AppFlow places in the Upsolver Amazon S3 bucket. </p>"}, "prefixConfig": {"shape": "PrefixConfig"}, "aggregationConfig": {"shape": "AggregationConfig"}}, "documentation": "<p> The configuration that determines how Amazon AppFlow formats the flow output data when Upsolver is used as the destination. </p>"}, "Username": {"type": "string", "max": 512, "pattern": "\\S+"}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "ErrorMessage"}}, "documentation": "<p> The request has invalid or missing parameters. </p>", "error": {"httpStatusCode": 400}, "exception": true}, "Value": {"type": "string", "max": 128, "pattern": "\\S+"}, "VeevaConnectorOperator": {"type": "string", "enum": ["PROJECTION", "LESS_THAN", "GREATER_THAN", "CONTAINS", "BETWEEN", "LESS_THAN_OR_EQUAL_TO", "GREATER_THAN_OR_EQUAL_TO", "EQUAL_TO", "NOT_EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "VeevaConnectorProfileCredentials": {"type": "structure", "required": ["username", "password"], "members": {"username": {"shape": "Username", "documentation": "<p> The name of the user. </p>"}, "password": {"shape": "Password", "documentation": "<p> The password that corresponds to the user name. </p>"}}, "documentation": "<p> The connector-specific profile credentials required when using Veeva. </p>"}, "VeevaConnectorProfileProperties": {"type": "structure", "required": ["instanceUrl"], "members": {"instanceUrl": {"shape": "InstanceUrl", "documentation": "<p> The location of the Veeva resource. </p>"}}, "documentation": "<p> The connector-specific profile properties required when using Veeva. </p>"}, "VeevaMetadata": {"type": "structure", "members": {}, "documentation": "<p> The connector metadata specific to <PERSON><PERSON><PERSON>. </p>"}, "VeevaSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Veeva flow source. </p>"}, "documentType": {"shape": "DocumentType", "documentation": "<p>The document type specified in the Veeva document extract flow.</p>"}, "includeSourceFiles": {"shape": "Boolean", "documentation": "<p>Boolean value to include source files in Veeva document extract flow.</p>"}, "includeRenditions": {"shape": "Boolean", "documentation": "<p>Boolean value to include file renditions in Veeva document extract flow.</p>"}, "includeAllVersions": {"shape": "Boolean", "documentation": "<p>Boolean value to include All Versions of files in Veeva document extract flow.</p>"}}, "documentation": "<p> The properties that are applied when using V<PERSON>va as a flow source. </p>"}, "Warehouse": {"type": "string", "max": 512, "pattern": "[\\s\\w/!@#+=.-]*"}, "WorkgroupName": {"type": "string", "max": 512, "pattern": "\\S+"}, "WriteOperationType": {"type": "string", "documentation": "<p> The possible write operations in the destination connector. When this value is not provided, this defaults to the <code>INSERT</code> operation. </p>", "enum": ["INSERT", "UPSERT", "UPDATE", "DELETE"]}, "ZendeskConnectorOperator": {"type": "string", "enum": ["PROJECTION", "GREATER_THAN", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "ZendeskConnectorProfileCredentials": {"type": "structure", "required": ["clientId", "clientSecret"], "members": {"clientId": {"shape": "ClientId", "documentation": "<p> The identifier for the desired client. </p>"}, "clientSecret": {"shape": "ClientSecret", "documentation": "<p> The client secret used by the OAuth client to authenticate to the authorization server. </p>"}, "accessToken": {"shape": "AccessToken", "documentation": "<p> The credentials used to access protected Zendesk resources. </p>"}, "oAuthRequest": {"shape": "ConnectorOAuthRequest", "documentation": "<p> The OAuth requirement needed to request security tokens from the connector endpoint. </p>"}}, "documentation": "<p> The connector-specific profile credentials required when using Zendesk. </p>"}, "ZendeskConnectorProfileProperties": {"type": "structure", "required": ["instanceUrl"], "members": {"instanceUrl": {"shape": "InstanceUrl", "documentation": "<p> The location of the Zendesk resource. </p>"}}, "documentation": "<p> The connector-specific profile properties required when using Zendesk. </p>"}, "ZendeskDestinationProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p>The object specified in the Zendesk flow destination.</p>"}, "idFieldNames": {"shape": "IdFieldNameList"}, "errorHandlingConfig": {"shape": "ErrorHandlingConfig"}, "writeOperationType": {"shape": "WriteOperationType"}}, "documentation": "<p>The properties that are applied when Zendesk is used as a destination.</p>"}, "ZendeskMetadata": {"type": "structure", "members": {"oAuthScopes": {"shape": "OAuthScopeList", "documentation": "<p> The desired authorization scope for the Zendesk account. </p>"}}, "documentation": "<p> The connector metadata specific to Zendesk. </p>"}, "ZendeskSourceProperties": {"type": "structure", "required": ["object"], "members": {"object": {"shape": "Object", "documentation": "<p> The object specified in the Zendesk flow source. </p>"}}, "documentation": "<p> The properties that are applied when using Zendesk as a flow source. </p>"}}, "documentation": "<p>Welcome to the Amazon AppFlow API reference. This guide is for developers who need detailed information about the Amazon AppFlow API operations, data types, and errors. </p> <p>Amazon AppFlow is a fully managed integration service that enables you to securely transfer data between software as a service (SaaS) applications like Salesforce, Marketo, Slack, and ServiceNow, and Amazon Web Services like Amazon S3 and Amazon Redshift. </p> <p>Use the following links to get started on the Amazon AppFlow API:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/appflow/1.0/APIReference/API_Operations.html\">Actions</a>: An alphabetical list of all Amazon AppFlow API operations.</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/appflow/1.0/APIReference/API_Types.html\">Data types</a>: An alphabetical list of all Amazon AppFlow data types.</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/appflow/1.0/APIReference/CommonParameters.html\">Common parameters</a>: Parameters that all Query operations can use.</p> </li> <li> <p> <a href=\"https://docs.aws.amazon.com/appflow/1.0/APIReference/CommonErrors.html\">Common errors</a>: Client and server errors that all operations can return.</p> </li> </ul> <p>If you're new to Amazon AppFlow, we recommend that you review the <a href=\"https://docs.aws.amazon.com/appflow/latest/userguide/what-is-appflow.html\">Amazon AppFlow User Guide</a>.</p> <p>Amazon AppFlow API users can use vendor-specific mechanisms for OAuth, and include applicable OAuth attributes (such as <code>auth-code</code> and <code>redirecturi</code>) with the connector-specific <code>ConnectorProfileProperties</code> when creating a new connector profile using Amazon AppFlow API operations. For example, Salesforce users can refer to the <a href=\"https://help.salesforce.com/articleView?id=remoteaccess_authenticate.htm\"> <i>Authorize Apps with OAuth</i> </a> documentation.</p>"}