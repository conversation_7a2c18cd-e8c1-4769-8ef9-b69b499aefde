{"version": "2.0", "metadata": {"apiVersion": "2020-05-15", "endpointPrefix": "apprunner", "jsonVersion": "1.0", "protocol": "json", "serviceFullName": "AWS App Runner", "serviceId": "A<PERSON><PERSON><PERSON><PERSON>", "signatureVersion": "v4", "signingName": "apprunner", "targetPrefix": "A<PERSON><PERSON><PERSON><PERSON>", "uid": "apprunner-2020-05-15"}, "operations": {"AssociateCustomDomain": {"name": "AssociateCustomDomain", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateCustomDomainRequest"}, "output": {"shape": "AssociateCustomDomainResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Associate your own domain name with the App Runner subdomain URL of your App Runner service.</p> <p>After you call <code>AssociateCustomDomain</code> and receive a successful response, use the information in the <a>CustomDomain</a> record that's returned to add CNAME records to your Domain Name System (DNS). For each mapped domain name, add a mapping to the target App Runner subdomain and one or more certificate validation records. App Runner then performs DNS validation to verify that you own or control the domain name that you associated. App Runner tracks domain validity in a certificate stored in <a href=\"https://docs.aws.amazon.com/acm/latest/userguide\">AWS Certificate Manager (ACM)</a>.</p>"}, "CreateAutoScalingConfiguration": {"name": "CreateAutoScalingConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAutoScalingConfigurationRequest"}, "output": {"shape": "CreateAutoScalingConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create an App Runner automatic scaling configuration resource. App <PERSON> requires this resource when you create or update App Runner services and you require non-default auto scaling settings. You can share an auto scaling configuration across multiple services.</p> <p>Create multiple revisions of a configuration by calling this action multiple times using the same <code>AutoScalingConfigurationName</code>. The call returns incremental <code>AutoScalingConfigurationRevision</code> values. When you create a service and configure an auto scaling configuration resource, the service uses the latest active revision of the auto scaling configuration by default. You can optionally configure the service to use a specific revision.</p> <p>Configure a higher <code>MinSize</code> to increase the spread of your App Runner service over more Availability Zones in the Amazon Web Services Region. The tradeoff is a higher minimal cost.</p> <p>Configure a lower <code>MaxSize</code> to control your cost. The tradeoff is lower responsiveness during peak demand.</p>"}, "CreateConnection": {"name": "CreateConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateConnectionRequest"}, "output": {"shape": "CreateConnectionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create an App Runner connection resource. App Runner requires a connection resource when you create App Runner services that access private repositories from certain third-party providers. You can share a connection across multiple services.</p> <p>A connection resource is needed to access GitHub repositories. GitHub requires a user interface approval process through the App Runner console before you can use the connection.</p>"}, "CreateObservabilityConfiguration": {"name": "CreateObservabilityConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateObservabilityConfigurationRequest"}, "output": {"shape": "CreateObservabilityConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create an App Runner observability configuration resource. App <PERSON> requires this resource when you create or update App Runner services and you want to enable non-default observability features. You can share an observability configuration across multiple services.</p> <p>Create multiple revisions of a configuration by calling this action multiple times using the same <code>ObservabilityConfigurationName</code>. The call returns incremental <code>ObservabilityConfigurationRevision</code> values. When you create a service and configure an observability configuration resource, the service uses the latest active revision of the observability configuration by default. You can optionally configure the service to use a specific revision.</p> <p>The observability configuration resource is designed to configure multiple features (currently one feature, tracing). This action takes optional parameters that describe the configuration of these features (currently one parameter, <code>TraceConfiguration</code>). If you don't specify a feature parameter, App Runner doesn't enable the feature.</p>"}, "CreateService": {"name": "CreateService", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateServiceRequest"}, "output": {"shape": "CreateServiceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create an App Runner service. After the service is created, the action also automatically starts a deployment.</p> <p>This is an asynchronous operation. On a successful call, you can use the returned <code>OperationId</code> and the <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_ListOperations.html\">ListOperations</a> call to track the operation's progress.</p>"}, "CreateVpcConnector": {"name": "CreateVpcConnector", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateVpcConnectorRequest"}, "output": {"shape": "CreateVpcConnectorResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create an App Runner VPC connector resource. App Runner requires this resource when you want to associate your App Runner service to a custom Amazon Virtual Private Cloud (Amazon VPC).</p>"}, "CreateVpcIngressConnection": {"name": "CreateVpcIngressConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateVpcIngressConnectionRequest"}, "output": {"shape": "CreateVpcIngressConnectionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InvalidStateException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create an App Runner VPC Ingress Connection resource. App Runner requires this resource when you want to associate your App Runner service with an Amazon VPC endpoint.</p>"}, "DeleteAutoScalingConfiguration": {"name": "DeleteAutoScalingConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAutoScalingConfigurationRequest"}, "output": {"shape": "DeleteAutoScalingConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Delete an App Runner automatic scaling configuration resource. You can delete a specific revision or the latest active revision. You can't delete a configuration that's used by one or more App Runner services.</p>"}, "DeleteConnection": {"name": "DeleteConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteConnectionRequest"}, "output": {"shape": "DeleteConnectionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Delete an App Runner connection. You must first ensure that there are no running App Runner services that use this connection. If there are any, the <code>DeleteConnection</code> action fails.</p>"}, "DeleteObservabilityConfiguration": {"name": "DeleteObservabilityConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteObservabilityConfigurationRequest"}, "output": {"shape": "DeleteObservabilityConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Delete an App Runner observability configuration resource. You can delete a specific revision or the latest active revision. You can't delete a configuration that's used by one or more App Runner services.</p>"}, "DeleteService": {"name": "DeleteService", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteServiceRequest"}, "output": {"shape": "DeleteServiceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidStateException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Delete an App Runner service.</p> <p>This is an asynchronous operation. On a successful call, you can use the returned <code>OperationId</code> and the <a>ListOperations</a> call to track the operation's progress.</p> <note> <p>Make sure that you don't have any active VPCIngressConnections associated with the service you want to delete. </p> </note>"}, "DeleteVpcConnector": {"name": "DeleteVpcConnector", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteVpcConnectorRequest"}, "output": {"shape": "DeleteVpcConnectorResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Delete an App Runner VPC connector resource. You can't delete a connector that's used by one or more App Runner services.</p>"}, "DeleteVpcIngressConnection": {"name": "DeleteVpcIngressConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteVpcIngressConnectionRequest"}, "output": {"shape": "DeleteVpcIngressConnectionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Delete an App Runner VPC Ingress Connection resource that's associated with an App Runner service. The VPC Ingress Connection must be in one of the following states to be deleted: </p> <ul> <li> <p> <code>AVAILABLE</code> </p> </li> <li> <p> <code>FAILED_CREATION</code> </p> </li> <li> <p> <code>FAILED_UPDATE</code> </p> </li> <li> <p> <code>FAILED_DELETION</code> </p> </li> </ul>"}, "DescribeAutoScalingConfiguration": {"name": "DescribeAutoScalingConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAutoScalingConfigurationRequest"}, "output": {"shape": "DescribeAutoScalingConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Return a full description of an App Runner automatic scaling configuration resource.</p>"}, "DescribeCustomDomains": {"name": "DescribeCustomDomains", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCustomDomainsRequest"}, "output": {"shape": "DescribeCustomDomainsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Return a description of custom domain names that are associated with an App Runner service.</p>"}, "DescribeObservabilityConfiguration": {"name": "DescribeObservabilityConfiguration", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeObservabilityConfigurationRequest"}, "output": {"shape": "DescribeObservabilityConfigurationResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Return a full description of an App Runner observability configuration resource.</p>"}, "DescribeService": {"name": "DescribeService", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeServiceRequest"}, "output": {"shape": "DescribeServiceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Return a full description of an App Runner service.</p>"}, "DescribeVpcConnector": {"name": "DescribeVpcConnector", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeVpcConnectorRequest"}, "output": {"shape": "DescribeVpcConnectorResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Return a description of an App Runner VPC connector resource.</p>"}, "DescribeVpcIngressConnection": {"name": "DescribeVpcIngressConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeVpcIngressConnectionRequest"}, "output": {"shape": "DescribeVpcIngressConnectionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Return a full description of an App Runner VPC Ingress Connection resource.</p>"}, "DisassociateCustomDomain": {"name": "DisassociateCustomDomain", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateCustomDomainRequest"}, "output": {"shape": "DisassociateCustomDomainResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Disassociate a custom domain name from an App Runner service.</p> <p>Certificates tracking domain validity are associated with a custom domain and are stored in <a href=\"https://docs.aws.amazon.com/acm/latest/userguide\">AWS Certificate Manager (ACM)</a>. These certificates aren't deleted as part of this action. App Runner delays certificate deletion for 30 days after a domain is disassociated from your service.</p>"}, "ListAutoScalingConfigurations": {"name": "ListAutoScalingConfigurations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAutoScalingConfigurationsRequest"}, "output": {"shape": "ListAutoScalingConfigurationsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Returns a list of active App Runner automatic scaling configurations in your Amazon Web Services account. You can query the revisions for a specific configuration name or the revisions for all active configurations in your account. You can optionally query only the latest revision of each requested name.</p> <p>To retrieve a full description of a particular configuration revision, call and provide one of the ARNs returned by <code>ListAutoScalingConfigurations</code>.</p>"}, "ListConnections": {"name": "ListConnections", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListConnectionsRequest"}, "output": {"shape": "ListConnectionsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Returns a list of App Runner connections that are associated with your Amazon Web Services account.</p>"}, "ListObservabilityConfigurations": {"name": "ListObservabilityConfigurations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListObservabilityConfigurationsRequest"}, "output": {"shape": "ListObservabilityConfigurationsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Returns a list of active App Runner observability configurations in your Amazon Web Services account. You can query the revisions for a specific configuration name or the revisions for all active configurations in your account. You can optionally query only the latest revision of each requested name.</p> <p>To retrieve a full description of a particular configuration revision, call and provide one of the ARNs returned by <code>ListObservabilityConfigurations</code>.</p>"}, "ListOperations": {"name": "ListOperations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListOperationsRequest"}, "output": {"shape": "ListOperationsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Return a list of operations that occurred on an App Runner service.</p> <p>The resulting list of <a>OperationSummary</a> objects is sorted in reverse chronological order. The first object on the list represents the last started operation.</p>"}, "ListServices": {"name": "ListServices", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListServicesRequest"}, "output": {"shape": "ListServicesResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Returns a list of running App Runner services in your Amazon Web Services account.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidStateException"}], "documentation": "<p>List tags that are associated with for an App Runner resource. The response contains a list of tag key-value pairs.</p>"}, "ListVpcConnectors": {"name": "ListVpcConnectors", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListVpcConnectorsRequest"}, "output": {"shape": "ListVpcConnectorsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Returns a list of App Runner VPC connectors in your Amazon Web Services account.</p>"}, "ListVpcIngressConnections": {"name": "ListVpcIngressConnections", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListVpcIngressConnectionsRequest"}, "output": {"shape": "ListVpcIngressConnectionsResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Return a list of App Runner VPC Ingress Connections in your Amazon Web Services account.</p>"}, "PauseService": {"name": "PauseService", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PauseServiceRequest"}, "output": {"shape": "PauseServiceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Pause an active App Runner service. App Runner reduces compute capacity for the service to zero and loses state (for example, ephemeral storage is removed).</p> <p>This is an asynchronous operation. On a successful call, you can use the returned <code>OperationId</code> and the <a>ListOperations</a> call to track the operation's progress.</p>"}, "ResumeService": {"name": "ResumeService", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ResumeServiceRequest"}, "output": {"shape": "ResumeServiceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Resume an active App Runner service. App Runner provisions compute capacity for the service.</p> <p>This is an asynchronous operation. On a successful call, you can use the returned <code>OperationId</code> and the <a>ListOperations</a> call to track the operation's progress.</p>"}, "StartDeployment": {"name": "StartDeployment", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartDeploymentRequest"}, "output": {"shape": "StartDeploymentResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Initiate a manual deployment of the latest commit in a source code repository or the latest image in a source image repository to an App Runner service.</p> <p>For a source code repository, <PERSON><PERSON> <PERSON> retrieves the commit and builds a Docker image. For a source image repository, <PERSON><PERSON> <PERSON> retrieves the latest Docker image. In both cases, <PERSON><PERSON> <PERSON> then deploys the new image to your service and starts a new container instance.</p> <p>This is an asynchronous operation. On a successful call, you can use the returned <code>OperationId</code> and the <a>ListOperations</a> call to track the operation's progress.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Add tags to, or update the tag values of, an App Runner resource. A tag is a key-value pair.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceErrorException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Remove tags from an App Runner resource.</p>"}, "UpdateService": {"name": "UpdateService", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateServiceRequest"}, "output": {"shape": "UpdateServiceResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidStateException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Update an App Runner service. You can update the source configuration and instance configuration of the service. You can also update the ARN of the auto scaling configuration resource that's associated with the service. However, you can't change the name or the encryption configuration of the service. These can be set only when you create the service.</p> <p>To update the tags applied to your service, use the separate actions <a>TagResource</a> and <a>UntagResource</a>.</p> <p>This is an asynchronous operation. On a successful call, you can use the returned <code>OperationId</code> and the <a>ListOperations</a> call to track the operation's progress.</p>"}, "UpdateVpcIngressConnection": {"name": "UpdateVpcIngressConnection", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateVpcIngressConnectionRequest"}, "output": {"shape": "UpdateVpcIngressConnectionResponse"}, "errors": [{"shape": "InvalidRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidStateException"}, {"shape": "InternalServiceErrorException"}], "documentation": "<p>Update an existing App Runner VPC Ingress Connection resource. The VPC Ingress Connection must be in one of the following states to be updated:</p> <ul> <li> <p> AVAILABLE </p> </li> <li> <p> FAILED_CREATION </p> </li> <li> <p> FAILED_UPDATE </p> </li> </ul>"}}, "shapes": {"ASConfigMaxConcurrency": {"type": "integer", "max": 200, "min": 1}, "ASConfigMaxSize": {"type": "integer", "min": 1}, "ASConfigMinSize": {"type": "integer", "max": 25, "min": 1}, "AppRunnerResourceArn": {"type": "string", "max": 1011, "min": 1, "pattern": "arn:aws(-[\\w]+)*:[a-z0-9-\\\\.]{0,63}:[a-z0-9-\\\\.]{0,63}:[0-9]{12}:(\\w|\\/|-){1,1011}"}, "AssociateCustomDomainRequest": {"type": "structure", "required": ["ServiceArn", "DomainName"], "members": {"ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service that you want to associate a custom domain name with.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>A custom domain endpoint to associate. Specify a root domain (for example, <code>example.com</code>), a subdomain (for example, <code>login.example.com</code> or <code>admin.login.example.com</code>), or a wildcard (for example, <code>*.example.com</code>).</p>"}, "EnableWWWSubdomain": {"shape": "NullableBoolean", "documentation": "<p>Set to <code>true</code> to associate the subdomain <code>www.<i>DomainName</i> </code> with the App Runner service in addition to the base domain.</p> <p>Default: <code>true</code> </p>"}}}, "AssociateCustomDomainResponse": {"type": "structure", "required": ["DNSTarget", "ServiceArn", "CustomDomain", "VpcDNSTargets"], "members": {"DNSTarget": {"shape": "String", "documentation": "<p>The App Runner subdomain of the App Runner service. The custom domain name is mapped to this target name.</p>"}, "ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service with which a custom domain name is associated.</p>"}, "CustomDomain": {"shape": "CustomDomain", "documentation": "<p>A description of the domain name that's being associated.</p>"}, "VpcDNSTargets": {"shape": "VpcDNSTargetList", "documentation": "<p>DNS Target records for the custom domains of this Amazon VPC. </p>"}}}, "AuthenticationConfiguration": {"type": "structure", "members": {"ConnectionArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner connection that enables the App Runner service to connect to a source repository. It's required for GitHub code repositories.</p>"}, "AccessRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role that grants the App Runner service access to a source repository. It's required for ECR image repositories (but not for ECR Public repositories).</p>"}}, "documentation": "<p>Describes resources needed to authenticate access to some source repositories. The specific resource depends on the repository provider.</p>"}, "AutoScalingConfiguration": {"type": "structure", "members": {"AutoScalingConfigurationArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this auto scaling configuration.</p>"}, "AutoScalingConfigurationName": {"shape": "AutoScalingConfigurationName", "documentation": "<p>The customer-provided auto scaling configuration name. It can be used in multiple revisions of a configuration.</p>"}, "AutoScalingConfigurationRevision": {"shape": "Integer", "documentation": "<p>The revision of this auto scaling configuration. It's unique among all the active configurations (<code>\"Status\": \"ACTIVE\"</code>) that share the same <code>AutoScalingConfigurationName</code>.</p>"}, "Latest": {"shape": "Boolean", "documentation": "<p>It's set to <code>true</code> for the configuration with the highest <code>Revision</code> among all configurations that share the same <code>AutoScalingConfigurationName</code>. It's set to <code>false</code> otherwise.</p>"}, "Status": {"shape": "AutoScalingConfigurationStatus", "documentation": "<p>The current state of the auto scaling configuration. If the status of a configuration revision is <code>INACTIVE</code>, it was deleted and can't be used. Inactive configuration revisions are permanently removed some time after they are deleted.</p>"}, "MaxConcurrency": {"shape": "Integer", "documentation": "<p>The maximum number of concurrent requests that an instance processes. If the number of concurrent requests exceeds this limit, <PERSON><PERSON> <PERSON> scales the service up.</p>"}, "MinSize": {"shape": "Integer", "documentation": "<p>The minimum number of instances that App <PERSON> provisions for a service. The service always has at least <code>MinSize</code> provisioned instances. Some of them actively serve traffic. The rest of them (provisioned and inactive instances) are a cost-effective compute capacity reserve and are ready to be quickly activated. You pay for memory usage of all the provisioned instances. You pay for CPU usage of only the active subset.</p> <p>App Runner temporarily doubles the number of provisioned instances during deployments, to maintain the same capacity for both old and new code.</p>"}, "MaxSize": {"shape": "Integer", "documentation": "<p>The maximum number of instances that a service scales up to. At most <code>MaxSize</code> instances actively serve traffic for your service.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The time when the auto scaling configuration was created. It's in Unix time stamp format.</p>"}, "DeletedAt": {"shape": "Timestamp", "documentation": "<p>The time when the auto scaling configuration was deleted. It's in Unix time stamp format.</p>"}}, "documentation": "<p>Describes an App Runner automatic scaling configuration resource.</p> <p>A higher <code>MinSize</code> increases the spread of your App Runner service over more Availability Zones in the Amazon Web Services Region. The tradeoff is a higher minimal cost.</p> <p>A lower <code>MaxSize</code> controls your cost. The tradeoff is lower responsiveness during peak demand.</p> <p>Multiple revisions of a configuration might have the same <code>AutoScalingConfigurationName</code> and different <code>AutoScalingConfigurationRevision</code> values.</p>"}, "AutoScalingConfigurationName": {"type": "string", "max": 32, "min": 4, "pattern": "[A-Za-z0-9][A-Za-z0-9\\-_]{3,31}"}, "AutoScalingConfigurationStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "AutoScalingConfigurationSummary": {"type": "structure", "members": {"AutoScalingConfigurationArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this auto scaling configuration.</p>"}, "AutoScalingConfigurationName": {"shape": "AutoScalingConfigurationName", "documentation": "<p>The customer-provided auto scaling configuration name. It can be used in multiple revisions of a configuration.</p>"}, "AutoScalingConfigurationRevision": {"shape": "Integer", "documentation": "<p>The revision of this auto scaling configuration. It's unique among all the active configurations (<code>\"Status\": \"ACTIVE\"</code>) with the same <code>AutoScalingConfigurationName</code>.</p>"}}, "documentation": "<p>Provides summary information about an App Runner automatic scaling configuration resource.</p> <p>This type contains limited information about an auto scaling configuration. It includes only identification information, without configuration details. It's returned by the <a>ListAutoScalingConfigurations</a> action. Complete configuration information is returned by the <a>CreateAutoScalingConfiguration</a>, <a>DescribeAutoScalingConfiguration</a>, and <a>DeleteAutoScalingConfiguration</a> actions using the <a>AutoScalingConfiguration</a> type.</p>"}, "AutoScalingConfigurationSummaryList": {"type": "list", "member": {"shape": "AutoScalingConfigurationSummary"}}, "Boolean": {"type": "boolean"}, "BuildCommand": {"type": "string", "pattern": "[^\\x0a\\x0d]+", "sensitive": true}, "CertificateValidationRecord": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The certificate CNAME record name.</p>"}, "Type": {"shape": "String", "documentation": "<p>The record type, always <code>CNAME</code>.</p>"}, "Value": {"shape": "String", "documentation": "<p>The certificate CNAME record value.</p>"}, "Status": {"shape": "CertificateValidationRecordStatus", "documentation": "<p>The current state of the certificate CNAME record validation. It should change to <code>SUCCESS</code> after A<PERSON> <PERSON> completes validation with your DNS.</p>"}}, "documentation": "<p>Describes a certificate CNAME record to add to your DNS. For more information, see <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_AssociateCustomDomain.html\">AssociateCustomDomain</a>.</p>"}, "CertificateValidationRecordList": {"type": "list", "member": {"shape": "CertificateValidationRecord"}}, "CertificateValidationRecordStatus": {"type": "string", "enum": ["PENDING_VALIDATION", "SUCCESS", "FAILED"]}, "CodeConfiguration": {"type": "structure", "required": ["ConfigurationSource"], "members": {"ConfigurationSource": {"shape": "ConfigurationSource", "documentation": "<p>The source of the App Runner configuration. Values are interpreted as follows:</p> <ul> <li> <p> <code>REPOSITORY</code> – App Runner reads configuration values from the <code>apprunner.yaml</code> file in the source code repository and ignores <code>CodeConfigurationValues</code>.</p> </li> <li> <p> <code>API</code> – App Runner uses configuration values provided in <code>CodeConfigurationValues</code> and ignores the <code>apprunner.yaml</code> file in the source code repository.</p> </li> </ul>"}, "CodeConfigurationValues": {"shape": "CodeConfigurationValues", "documentation": "<p>The basic configuration for building and running the App Runner service. Use it to quickly launch an App Runner service without providing a <code>apprunner.yaml</code> file in the source code repository (or ignoring the file if it exists).</p>"}}, "documentation": "<p>Describes the configuration that App Runner uses to build and run an App Runner service from a source code repository.</p>"}, "CodeConfigurationValues": {"type": "structure", "required": ["Runtime"], "members": {"Runtime": {"shape": "Runtime", "documentation": "<p>A runtime environment type for building and running an App Runner service. It represents a programming language runtime.</p>"}, "BuildCommand": {"shape": "BuildCommand", "documentation": "<p>The command App Runner runs to build your application.</p>"}, "StartCommand": {"shape": "StartCommand", "documentation": "<p>The command App Runner runs to start your application.</p>"}, "Port": {"shape": "String", "documentation": "<p>The port that your application listens to in the container.</p> <p>Default: <code>8080</code> </p>"}, "RuntimeEnvironmentVariables": {"shape": "RuntimeEnvironmentVariables", "documentation": "<p>The environment variables that are available to your running App Runner service. An array of key-value pairs.</p>"}, "RuntimeEnvironmentSecrets": {"shape": "RuntimeEnvironmentSecrets", "documentation": "<p>An array of key-value pairs representing the secrets and parameters that get referenced to your service as an environment variable. The supported values are either the full Amazon Resource Name (ARN) of the Secrets Manager secret or the full ARN of the parameter in the Amazon Web Services Systems Manager Parameter Store.</p> <note> <ul> <li> <p> If the Amazon Web Services Systems Manager Parameter Store parameter exists in the same Amazon Web Services Region as the service that you're launching, you can use either the full ARN or name of the secret. If the parameter exists in a different Region, then the full ARN must be specified. </p> </li> <li> <p> Currently, cross account referencing of Amazon Web Services Systems Manager Parameter Store parameter is not supported. </p> </li> </ul> </note>"}}, "documentation": "<p>Describes the basic configuration needed for building and running an App Runner service. This type doesn't support the full set of possible configuration options. Fur full configuration capabilities, use a <code>apprunner.yaml</code> file in the source code repository.</p>"}, "CodeRepository": {"type": "structure", "required": ["RepositoryUrl", "SourceCodeVersion"], "members": {"RepositoryUrl": {"shape": "String", "documentation": "<p>The location of the repository that contains the source code.</p>"}, "SourceCodeVersion": {"shape": "SourceCodeVersion", "documentation": "<p>The version that should be used within the source code repository.</p>"}, "CodeConfiguration": {"shape": "CodeConfiguration", "documentation": "<p>Configuration for building and running the service from a source code repository.</p> <note> <p> <code>CodeConfiguration</code> is required only for <code>CreateService</code> request.</p> </note>"}}, "documentation": "<p>Describes a source code repository.</p>"}, "ConfigurationSource": {"type": "string", "enum": ["REPOSITORY", "API"]}, "Connection": {"type": "structure", "members": {"ConnectionName": {"shape": "ConnectionName", "documentation": "<p>The customer-provided connection name.</p>"}, "ConnectionArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this connection.</p>"}, "ProviderType": {"shape": "ProviderType", "documentation": "<p>The source repository provider.</p>"}, "Status": {"shape": "ConnectionStatus", "documentation": "<p>The current state of the App Runner connection. When the state is <code>AVAILABLE</code>, you can use the connection to create an App Runner service.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The App Runner connection creation time, expressed as a Unix time stamp.</p>"}}, "documentation": "<p>Describes an App Runner connection resource.</p>"}, "ConnectionName": {"type": "string", "max": 32, "min": 4, "pattern": "[A-Za-z0-9][A-Za-z0-9\\-_]{3,31}"}, "ConnectionStatus": {"type": "string", "enum": ["PENDING_HANDSHAKE", "AVAILABLE", "ERROR", "DELETED"]}, "ConnectionSummary": {"type": "structure", "members": {"ConnectionName": {"shape": "ConnectionName", "documentation": "<p>The customer-provided connection name.</p>"}, "ConnectionArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this connection.</p>"}, "ProviderType": {"shape": "ProviderType", "documentation": "<p>The source repository provider.</p>"}, "Status": {"shape": "ConnectionStatus", "documentation": "<p>The current state of the App Runner connection. When the state is <code>AVAILABLE</code>, you can use the connection to create an App Runner service.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The App Runner connection creation time, expressed as a Unix time stamp.</p>"}}, "documentation": "<p>Provides summary information about an App Runner connection resource.</p>"}, "ConnectionSummaryList": {"type": "list", "member": {"shape": "ConnectionSummary"}}, "Cpu": {"type": "string", "max": 6, "min": 4, "pattern": "1024|2048|(1|2) vCPU"}, "CreateAutoScalingConfigurationRequest": {"type": "structure", "required": ["AutoScalingConfigurationName"], "members": {"AutoScalingConfigurationName": {"shape": "AutoScalingConfigurationName", "documentation": "<p>A name for the auto scaling configuration. When you use it for the first time in an Amazon Web Services Region, <PERSON><PERSON> <PERSON> creates revision number <code>1</code> of this name. When you use the same name in subsequent calls, <PERSON><PERSON> <PERSON> creates incremental revisions of the configuration.</p> <note> <p>The name <code>DefaultConfiguration</code> is reserved (it's the configuration that <PERSON><PERSON> <PERSON> uses if you don't provide a custome one). You can't use it to create a new auto scaling configuration, and you can't create a revision of it.</p> <p>When you want to use your own auto scaling configuration for your App Runner service, <i>create a configuration with a different name</i>, and then provide it when you create or update your service.</p> </note>"}, "MaxConcurrency": {"shape": "ASConfigMaxConcurrency", "documentation": "<p>The maximum number of concurrent requests that you want an instance to process. If the number of concurrent requests exceeds this limit, App <PERSON> scales up your service.</p> <p>Default: <code>100</code> </p>"}, "MinSize": {"shape": "ASConfigMinSize", "documentation": "<p>The minimum number of instances that App <PERSON> provisions for your service. The service always has at least <code>MinSize</code> provisioned instances. Some of them actively serve traffic. The rest of them (provisioned and inactive instances) are a cost-effective compute capacity reserve and are ready to be quickly activated. You pay for memory usage of all the provisioned instances. You pay for CPU usage of only the active subset.</p> <p>App Runner temporarily doubles the number of provisioned instances during deployments, to maintain the same capacity for both old and new code.</p> <p>Default: <code>1</code> </p>"}, "MaxSize": {"shape": "ASConfigMaxSize", "documentation": "<p>The maximum number of instances that your service scales up to. At most <code>MaxSize</code> instances actively serve traffic for your service.</p> <p>Default: <code>25</code> </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of metadata items that you can associate with your auto scaling configuration resource. A tag is a key-value pair.</p>"}}}, "CreateAutoScalingConfigurationResponse": {"type": "structure", "required": ["AutoScalingConfiguration"], "members": {"AutoScalingConfiguration": {"shape": "AutoScalingConfiguration", "documentation": "<p>A description of the App Runner auto scaling configuration that's created by this request.</p>"}}}, "CreateConnectionRequest": {"type": "structure", "required": ["ConnectionName", "ProviderType"], "members": {"ConnectionName": {"shape": "ConnectionName", "documentation": "<p>A name for the new connection. It must be unique across all App Runner connections for the Amazon Web Services account in the Amazon Web Services Region.</p>"}, "ProviderType": {"shape": "ProviderType", "documentation": "<p>The source repository provider.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of metadata items that you can associate with your connection resource. A tag is a key-value pair.</p>"}}}, "CreateConnectionResponse": {"type": "structure", "required": ["Connection"], "members": {"Connection": {"shape": "Connection", "documentation": "<p>A description of the App Runner connection that's created by this request.</p>"}}}, "CreateObservabilityConfigurationRequest": {"type": "structure", "required": ["ObservabilityConfigurationName"], "members": {"ObservabilityConfigurationName": {"shape": "ObservabilityConfigurationName", "documentation": "<p>A name for the observability configuration. When you use it for the first time in an Amazon Web Services Region, <PERSON><PERSON> Runner creates revision number <code>1</code> of this name. When you use the same name in subsequent calls, <PERSON><PERSON> <PERSON> creates incremental revisions of the configuration.</p> <note> <p>The name <code>DefaultConfiguration</code> is reserved. You can't use it to create a new observability configuration, and you can't create a revision of it.</p> <p>When you want to use your own observability configuration for your App Runner service, <i>create a configuration with a different name</i>, and then provide it when you create or update your service.</p> </note>"}, "TraceConfiguration": {"shape": "TraceConfiguration", "documentation": "<p>The configuration of the tracing feature within this observability configuration. If you don't specify it, App Runner doesn't enable tracing.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of metadata items that you can associate with your observability configuration resource. A tag is a key-value pair.</p>"}}}, "CreateObservabilityConfigurationResponse": {"type": "structure", "required": ["ObservabilityConfiguration"], "members": {"ObservabilityConfiguration": {"shape": "ObservabilityConfiguration", "documentation": "<p>A description of the App Runner observability configuration that's created by this request.</p>"}}}, "CreateServiceRequest": {"type": "structure", "required": ["ServiceName", "SourceConfiguration"], "members": {"ServiceName": {"shape": "ServiceName", "documentation": "<p>A name for the App Runner service. It must be unique across all the running App Runner services in your Amazon Web Services account in the Amazon Web Services Region.</p>"}, "SourceConfiguration": {"shape": "SourceConfiguration", "documentation": "<p>The source to deploy to the App Runner service. It can be a code or an image repository.</p>"}, "InstanceConfiguration": {"shape": "InstanceConfiguration", "documentation": "<p>The runtime configuration of instances (scaling units) of your service.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An optional list of metadata items that you can associate with the App Runner service resource. A tag is a key-value pair.</p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>An optional custom encryption key that App Runner uses to encrypt the copy of your source repository that it maintains and your service logs. By default, App Runner uses an Amazon Web Services managed key.</p>"}, "HealthCheckConfiguration": {"shape": "HealthCheckConfiguration", "documentation": "<p>The settings for the health check that App Runner performs to monitor the health of the App Runner service.</p>"}, "AutoScalingConfigurationArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of an App Runner automatic scaling configuration resource that you want to associate with your service. If not provided, <PERSON>pp <PERSON> associates the latest revision of a default auto scaling configuration.</p> <p>Specify an ARN with a name and a revision number to associate that revision. For example: <code>arn:aws:apprunner:us-east-1:123456789012:autoscalingconfiguration/high-availability/3</code> </p> <p>Specify just the name to associate the latest revision. For example: <code>arn:aws:apprunner:us-east-1:123456789012:autoscalingconfiguration/high-availability</code> </p>"}, "NetworkConfiguration": {"shape": "NetworkConfiguration", "documentation": "<p>Configuration settings related to network traffic of the web application that the App Runner service runs.</p>"}, "ObservabilityConfiguration": {"shape": "ServiceObservabilityConfiguration", "documentation": "<p>The observability configuration of your service.</p>"}}}, "CreateServiceResponse": {"type": "structure", "required": ["Service", "OperationId"], "members": {"Service": {"shape": "Service", "documentation": "<p>A description of the App Runner service that's created by this request.</p>"}, "OperationId": {"shape": "UUID", "documentation": "<p>The unique ID of the asynchronous operation that this request started. You can use it combined with the <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_ListOperations.html\">ListOperations</a> call to track the operation's progress.</p>"}}}, "CreateVpcConnectorRequest": {"type": "structure", "required": ["VpcConnectorName", "Subnets"], "members": {"VpcConnectorName": {"shape": "VpcConnectorName", "documentation": "<p>A name for the VPC connector.</p>"}, "Subnets": {"shape": "StringList", "documentation": "<p>A list of IDs of subnets that <PERSON>pp Runner should use when it associates your service with a custom Amazon VPC. Specify IDs of subnets of a single Amazon VPC. App Runner determines the Amazon VPC from the subnets you specify.</p> <note> <p> App Runner currently only provides support for IPv4. </p> </note>"}, "SecurityGroups": {"shape": "StringList", "documentation": "<p>A list of IDs of security groups that <PERSON><PERSON> <PERSON> should use for access to Amazon Web Services resources under the specified subnets. If not specified, <PERSON>pp <PERSON> uses the default security group of the Amazon VPC. The default security group allows all outbound traffic.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of metadata items that you can associate with your VPC connector resource. A tag is a key-value pair.</p>"}}}, "CreateVpcConnectorResponse": {"type": "structure", "required": ["VpcConnector"], "members": {"VpcConnector": {"shape": "VpcConnector", "documentation": "<p>A description of the App Runner VPC connector that's created by this request.</p>"}}}, "CreateVpcIngressConnectionRequest": {"type": "structure", "required": ["ServiceArn", "VpcIngressConnectionName", "IngressVpcConfiguration"], "members": {"ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) for this App Runner service that is used to create the VPC Ingress Connection resource.</p>"}, "VpcIngressConnectionName": {"shape": "VpcIngressConnectionName", "documentation": "<p>A name for the VPC Ingress Connection resource. It must be unique across all the active VPC Ingress Connections in your Amazon Web Services account in the Amazon Web Services Region. </p>"}, "IngressVpcConfiguration": {"shape": "IngressVpcConfiguration", "documentation": "<p>Specifications for the customer’s Amazon VPC and the related Amazon Web Services PrivateLink VPC endpoint that are used to create the VPC Ingress Connection resource.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>An optional list of metadata items that you can associate with the VPC Ingress Connection resource. A tag is a key-value pair.</p>"}}}, "CreateVpcIngressConnectionResponse": {"type": "structure", "required": ["VpcIngressConnection"], "members": {"VpcIngressConnection": {"shape": "VpcIngressConnection", "documentation": "<p>A description of the App Runner VPC Ingress Connection resource that's created by this request. </p>"}}}, "CustomDomain": {"type": "structure", "required": ["DomainName", "EnableWWWSubdomain", "Status"], "members": {"DomainName": {"shape": "DomainName", "documentation": "<p>An associated custom domain endpoint. It can be a root domain (for example, <code>example.com</code>), a subdomain (for example, <code>login.example.com</code> or <code>admin.login.example.com</code>), or a wildcard (for example, <code>*.example.com</code>).</p>"}, "EnableWWWSubdomain": {"shape": "NullableBoolean", "documentation": "<p>When <code>true</code>, the subdomain <code>www.<i>DomainName</i> </code> is associated with the App Runner service in addition to the base domain.</p>"}, "CertificateValidationRecords": {"shape": "CertificateValidationRecordList", "documentation": "<p>A list of certificate CNAME records that's used for this domain name.</p>"}, "Status": {"shape": "CustomDomainAssociationStatus", "documentation": "<p>The current state of the domain name association.</p>"}}, "documentation": "<p>Describes a custom domain that's associated with an App Runner service.</p>"}, "CustomDomainAssociationStatus": {"type": "string", "enum": ["CREATING", "CREATE_FAILED", "ACTIVE", "DELETING", "DELETE_FAILED", "PENDING_CERTIFICATE_DNS_VALIDATION", "BINDING_CERTIFICATE"]}, "CustomDomainList": {"type": "list", "member": {"shape": "CustomDomain"}}, "CustomerAccountId": {"type": "string", "max": 12, "min": 12, "pattern": "[0-9]{12}"}, "DeleteAutoScalingConfigurationRequest": {"type": "structure", "required": ["AutoScalingConfigurationArn"], "members": {"AutoScalingConfigurationArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner auto scaling configuration that you want to delete.</p> <p>The ARN can be a full auto scaling configuration ARN, or a partial ARN ending with either <code>.../<i>name</i> </code> or <code>.../<i>name</i>/<i>revision</i> </code>. If a revision isn't specified, the latest active revision is deleted.</p>"}}}, "DeleteAutoScalingConfigurationResponse": {"type": "structure", "required": ["AutoScalingConfiguration"], "members": {"AutoScalingConfiguration": {"shape": "AutoScalingConfiguration", "documentation": "<p>A description of the App Runner auto scaling configuration that this request just deleted.</p>"}}}, "DeleteConnectionRequest": {"type": "structure", "required": ["ConnectionArn"], "members": {"ConnectionArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner connection that you want to delete.</p>"}}}, "DeleteConnectionResponse": {"type": "structure", "members": {"Connection": {"shape": "Connection", "documentation": "<p>A description of the App Runner connection that this request just deleted.</p>"}}}, "DeleteObservabilityConfigurationRequest": {"type": "structure", "required": ["ObservabilityConfigurationArn"], "members": {"ObservabilityConfigurationArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner observability configuration that you want to delete.</p> <p>The ARN can be a full observability configuration ARN, or a partial ARN ending with either <code>.../<i>name</i> </code> or <code>.../<i>name</i>/<i>revision</i> </code>. If a revision isn't specified, the latest active revision is deleted.</p>"}}}, "DeleteObservabilityConfigurationResponse": {"type": "structure", "required": ["ObservabilityConfiguration"], "members": {"ObservabilityConfiguration": {"shape": "ObservabilityConfiguration", "documentation": "<p>A description of the App Runner observability configuration that this request just deleted.</p>"}}}, "DeleteServiceRequest": {"type": "structure", "required": ["ServiceArn"], "members": {"ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service that you want to delete.</p>"}}}, "DeleteServiceResponse": {"type": "structure", "required": ["Service", "OperationId"], "members": {"Service": {"shape": "Service", "documentation": "<p>A description of the App Runner service that this request just deleted.</p>"}, "OperationId": {"shape": "UUID", "documentation": "<p>The unique ID of the asynchronous operation that this request started. You can use it combined with the <a>ListOperations</a> call to track the operation's progress.</p>"}}}, "DeleteVpcConnectorRequest": {"type": "structure", "required": ["VpcConnectorArn"], "members": {"VpcConnectorArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner VPC connector that you want to delete.</p> <p>The ARN must be a full VPC connector ARN.</p>"}}}, "DeleteVpcConnectorResponse": {"type": "structure", "required": ["VpcConnector"], "members": {"VpcConnector": {"shape": "VpcConnector", "documentation": "<p>A description of the App Runner VPC connector that this request just deleted.</p>"}}}, "DeleteVpcIngressConnectionRequest": {"type": "structure", "required": ["VpcIngressConnectionArn"], "members": {"VpcIngressConnectionArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner VPC Ingress Connection that you want to delete.</p>"}}}, "DeleteVpcIngressConnectionResponse": {"type": "structure", "required": ["VpcIngressConnection"], "members": {"VpcIngressConnection": {"shape": "VpcIngressConnection", "documentation": "<p>A description of the App Runner VPC Ingress Connection that this request just deleted.</p>"}}}, "DescribeAutoScalingConfigurationRequest": {"type": "structure", "required": ["AutoScalingConfigurationArn"], "members": {"AutoScalingConfigurationArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner auto scaling configuration that you want a description for.</p> <p>The ARN can be a full auto scaling configuration ARN, or a partial ARN ending with either <code>.../<i>name</i> </code> or <code>.../<i>name</i>/<i>revision</i> </code>. If a revision isn't specified, the latest active revision is described.</p>"}}}, "DescribeAutoScalingConfigurationResponse": {"type": "structure", "required": ["AutoScalingConfiguration"], "members": {"AutoScalingConfiguration": {"shape": "AutoScalingConfiguration", "documentation": "<p>A full description of the App Runner auto scaling configuration that you specified in this request.</p>"}}}, "DescribeCustomDomainsMaxResults": {"type": "integer", "max": 5, "min": 1}, "DescribeCustomDomainsRequest": {"type": "structure", "required": ["ServiceArn"], "members": {"ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service that you want associated custom domain names to be described for.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A token from a previous result page. It's used for a paginated request. The request retrieves the next result page. All other parameter values must be identical to the ones that are specified in the initial request.</p> <p>If you don't specify <code>NextToken</code>, the request retrieves the first result page.</p>"}, "MaxResults": {"shape": "DescribeCustomDomainsMaxResults", "documentation": "<p>The maximum number of results that each response (result page) can include. It's used for a paginated request.</p> <p>If you don't specify <code>MaxResults</code>, the request retrieves all available results in a single response.</p>"}}}, "DescribeCustomDomainsResponse": {"type": "structure", "required": ["DNSTarget", "ServiceArn", "CustomDomains", "VpcDNSTargets"], "members": {"DNSTarget": {"shape": "String", "documentation": "<p>The App Runner subdomain of the App Runner service. The associated custom domain names are mapped to this target name.</p>"}, "ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service whose associated custom domain names you want to describe.</p>"}, "CustomDomains": {"shape": "CustomDomainList", "documentation": "<p>A list of descriptions of custom domain names that are associated with the service. In a paginated request, the request returns up to <code>MaxResults</code> records per call.</p>"}, "VpcDNSTargets": {"shape": "VpcDNSTargetList", "documentation": "<p>DNS Target records for the custom domains of this Amazon VPC. </p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token that you can pass in a subsequent request to get the next result page. It's returned in a paginated request.</p>"}}}, "DescribeObservabilityConfigurationRequest": {"type": "structure", "required": ["ObservabilityConfigurationArn"], "members": {"ObservabilityConfigurationArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner observability configuration that you want a description for.</p> <p>The ARN can be a full observability configuration ARN, or a partial ARN ending with either <code>.../<i>name</i> </code> or <code>.../<i>name</i>/<i>revision</i> </code>. If a revision isn't specified, the latest active revision is described.</p>"}}}, "DescribeObservabilityConfigurationResponse": {"type": "structure", "required": ["ObservabilityConfiguration"], "members": {"ObservabilityConfiguration": {"shape": "ObservabilityConfiguration", "documentation": "<p>A full description of the App Runner observability configuration that you specified in this request.</p>"}}}, "DescribeServiceRequest": {"type": "structure", "required": ["ServiceArn"], "members": {"ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service that you want a description for.</p>"}}}, "DescribeServiceResponse": {"type": "structure", "required": ["Service"], "members": {"Service": {"shape": "Service", "documentation": "<p>A full description of the App Runner service that you specified in this request.</p>"}}}, "DescribeVpcConnectorRequest": {"type": "structure", "required": ["VpcConnectorArn"], "members": {"VpcConnectorArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner VPC connector that you want a description for.</p> <p>The ARN must be a full VPC connector ARN.</p>"}}}, "DescribeVpcConnectorResponse": {"type": "structure", "required": ["VpcConnector"], "members": {"VpcConnector": {"shape": "VpcConnector", "documentation": "<p>A description of the App Runner VPC connector that you specified in this request.</p>"}}}, "DescribeVpcIngressConnectionRequest": {"type": "structure", "required": ["VpcIngressConnectionArn"], "members": {"VpcIngressConnectionArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner VPC Ingress Connection that you want a description for.</p>"}}}, "DescribeVpcIngressConnectionResponse": {"type": "structure", "required": ["VpcIngressConnection"], "members": {"VpcIngressConnection": {"shape": "VpcIngressConnection", "documentation": "<p>A description of the App Runner VPC Ingress Connection that you specified in this request.</p>"}}}, "DisassociateCustomDomainRequest": {"type": "structure", "required": ["ServiceArn", "DomainName"], "members": {"ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service that you want to disassociate a custom domain name from.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>The domain name that you want to disassociate from the App Runner service.</p>"}}}, "DisassociateCustomDomainResponse": {"type": "structure", "required": ["DNSTarget", "ServiceArn", "CustomDomain", "VpcDNSTargets"], "members": {"DNSTarget": {"shape": "String", "documentation": "<p>The App Runner subdomain of the App Runner service. The disassociated custom domain name was mapped to this target name.</p>"}, "ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service that a custom domain name is disassociated from.</p>"}, "CustomDomain": {"shape": "CustomDomain", "documentation": "<p>A description of the domain name that's being disassociated.</p>"}, "VpcDNSTargets": {"shape": "VpcDNSTargetList", "documentation": "<p>DNS Target records for the custom domains of this Amazon VPC. </p>"}}}, "DomainName": {"type": "string", "max": 255, "min": 1, "pattern": "[A-Za-z0-9*.-]{1,255}"}, "EgressConfiguration": {"type": "structure", "members": {"EgressType": {"shape": "EgressType", "documentation": "<p>The type of egress configuration.</p> <p>Set to <code>DEFAULT</code> for access to resources hosted on public networks.</p> <p>Set to <code>VPC</code> to associate your service to a custom VPC specified by <code>VpcConnectorArn</code>.</p>"}, "VpcConnectorArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner VPC connector that you want to associate with your App Runner service. Only valid when <code>EgressType = VPC</code>.</p>"}}, "documentation": "<p>Describes configuration settings related to outbound network traffic of an App Runner service.</p>"}, "EgressType": {"type": "string", "enum": ["DEFAULT", "VPC"]}, "EncryptionConfiguration": {"type": "structure", "required": ["KmsKey"], "members": {"KmsKey": {"shape": "KmsKeyArn", "documentation": "<p>The ARN of the KMS key that's used for encryption.</p>"}}, "documentation": "<p>Describes a custom encryption key that App Runner uses to encrypt copies of the source repository and service logs.</p>"}, "ErrorMessage": {"type": "string", "max": 600}, "HealthCheckConfiguration": {"type": "structure", "members": {"Protocol": {"shape": "HealthCheckProtocol", "documentation": "<p>The IP protocol that <PERSON><PERSON> <PERSON> uses to perform health checks for your service.</p> <p>If you set <code>Protocol</code> to <code>HTTP</code>, A<PERSON> <PERSON> sends health check requests to the HTTP path specified by <code>Path</code>.</p> <p>Default: <code>TCP</code> </p>"}, "Path": {"shape": "HealthCheckPath", "documentation": "<p>The URL that health check requests are sent to.</p> <p> <code>Path</code> is only applicable when you set <code>Protocol</code> to <code>HTTP</code>.</p> <p>Default: <code>\"/\"</code> </p>"}, "Interval": {"shape": "HealthCheckInterval", "documentation": "<p>The time interval, in seconds, between health checks.</p> <p>Default: <code>5</code> </p>"}, "Timeout": {"shape": "HealthCheckTimeout", "documentation": "<p>The time, in seconds, to wait for a health check response before deciding it failed.</p> <p>Default: <code>2</code> </p>"}, "HealthyThreshold": {"shape": "HealthCheckHealthyThreshold", "documentation": "<p>The number of consecutive checks that must succeed before App <PERSON> decides that the service is healthy.</p> <p>Default: <code>1</code> </p>"}, "UnhealthyThreshold": {"shape": "HealthCheckUnhealthyThreshold", "documentation": "<p>The number of consecutive checks that must fail before App <PERSON> decides that the service is unhealthy.</p> <p>Default: <code>5</code> </p>"}}, "documentation": "<p>Describes the settings for the health check that App Runner performs to monitor the health of a service.</p>"}, "HealthCheckHealthyThreshold": {"type": "integer", "max": 20, "min": 1}, "HealthCheckInterval": {"type": "integer", "max": 20, "min": 1}, "HealthCheckPath": {"type": "string", "min": 1}, "HealthCheckProtocol": {"type": "string", "enum": ["TCP", "HTTP"]}, "HealthCheckTimeout": {"type": "integer", "max": 20, "min": 1}, "HealthCheckUnhealthyThreshold": {"type": "integer", "max": 20, "min": 1}, "ImageConfiguration": {"type": "structure", "members": {"RuntimeEnvironmentVariables": {"shape": "RuntimeEnvironmentVariables", "documentation": "<p>Environment variables that are available to your running App Runner service. An array of key-value pairs.</p>"}, "StartCommand": {"shape": "StartCommand", "documentation": "<p>An optional command that <PERSON><PERSON> <PERSON> runs to start the application in the source image. If specified, this command overrides the Docker image’s default start command.</p>"}, "Port": {"shape": "String", "documentation": "<p>The port that your application listens to in the container.</p> <p>Default: <code>8080</code> </p>"}, "RuntimeEnvironmentSecrets": {"shape": "RuntimeEnvironmentSecrets", "documentation": "<p>An array of key-value pairs representing the secrets and parameters that get referenced to your service as an environment variable. The supported values are either the full Amazon Resource Name (ARN) of the Secrets Manager secret or the full ARN of the parameter in the Amazon Web Services Systems Manager Parameter Store.</p> <note> <ul> <li> <p> If the Amazon Web Services Systems Manager Parameter Store parameter exists in the same Amazon Web Services Region as the service that you're launching, you can use either the full ARN or name of the secret. If the parameter exists in a different Region, then the full ARN must be specified. </p> </li> <li> <p> Currently, cross account referencing of Amazon Web Services Systems Manager Parameter Store parameter is not supported. </p> </li> </ul> </note>"}}, "documentation": "<p>Describes the configuration that App Runner uses to run an App Runner service using an image pulled from a source image repository.</p>"}, "ImageIdentifier": {"type": "string", "max": 1024, "min": 1, "pattern": "([0-9]{12}.dkr.ecr.[a-z\\-]+-[0-9]{1}.amazonaws.com\\/((?:[a-z0-9]+(?:[._-][a-z0-9]+)*\\/)*[a-z0-9]+(?:[._-][a-z0-9]+)*)(:([\\w\\d+\\-=._:\\/@])+|@([\\w\\d\\:]+))?)|(^public\\.ecr\\.aws\\/.+\\/((?:[a-z0-9]+(?:[._-][a-z0-9]+)*\\/)*[a-z0-9]+(?:[._-][a-z0-9]+)*)(:([\\w\\d+\\-=._:\\/@])+|@([\\w\\d\\:]+))?)"}, "ImageRepository": {"type": "structure", "required": ["ImageIdentifier", "ImageRepositoryType"], "members": {"ImageIdentifier": {"shape": "ImageIdentifier", "documentation": "<p>The identifier of an image.</p> <p>For an image in Amazon Elastic Container Registry (Amazon ECR), this is an image name. For the image name format, see <a href=\"https://docs.aws.amazon.com/AmazonECR/latest/userguide/docker-pull-ecr-image.html\">Pulling an image</a> in the <i>Amazon ECR User Guide</i>.</p>"}, "ImageConfiguration": {"shape": "ImageConfiguration", "documentation": "<p>Configuration for running the identified image.</p>"}, "ImageRepositoryType": {"shape": "ImageRepositoryType", "documentation": "<p>The type of the image repository. This reflects the repository provider and whether the repository is private or public.</p>"}}, "documentation": "<p>Describes a source image repository.</p>"}, "ImageRepositoryType": {"type": "string", "enum": ["ECR", "ECR_PUBLIC"]}, "IngressConfiguration": {"type": "structure", "members": {"IsPubliclyAccessible": {"shape": "Boolean", "documentation": "<p>Specifies whether your App Runner service is publicly accessible. To make the service publicly accessible set it to <code>True</code>. To make the service privately accessible, from only within an Amazon VPC set it to <code>False</code>. </p>"}}, "documentation": "<p>Network configuration settings for inbound network traffic.</p>"}, "IngressVpcConfiguration": {"type": "structure", "members": {"VpcId": {"shape": "String", "documentation": "<p>The ID of the VPC that is used for the VPC endpoint.</p>"}, "VpcEndpointId": {"shape": "String", "documentation": "<p>The ID of the VPC endpoint that your App Runner service connects to. </p>"}}, "documentation": "<p>The configuration of your VPC and the associated VPC endpoint. The VPC endpoint is an Amazon Web Services PrivateLink resource that allows access to your App Runner services from within an Amazon VPC.</p>"}, "InstanceConfiguration": {"type": "structure", "members": {"Cpu": {"shape": "Cpu", "documentation": "<p>The number of CPU units reserved for each instance of your App Runner service.</p> <p>Default: <code>1 vCPU</code> </p>"}, "Memory": {"shape": "Memory", "documentation": "<p>The amount of memory, in MB or GB, reserved for each instance of your App Runner service.</p> <p>Default: <code>2 GB</code> </p>"}, "InstanceRoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role that provides permissions to your App Runner service. These are permissions that your code needs when it calls any Amazon Web Services APIs.</p>"}}, "documentation": "<p>Describes the runtime configuration of an App Runner service instance (scaling unit).</p>"}, "Integer": {"type": "integer"}, "InternalServiceErrorException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>An unexpected service exception occurred.</p>", "exception": true, "fault": true}, "InvalidRequestException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>One or more input parameters aren't valid. Refer to the API action's document page, correct the input parameters, and try the action again.</p>", "exception": true}, "InvalidStateException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>You can't perform this action when the resource is in its current state.</p>", "exception": true}, "KmsKeyArn": {"type": "string", "max": 256, "min": 0, "pattern": "arn:aws(-[\\w]+)*:kms:[a-z\\-]+-[0-9]{1}:[0-9]{12}:key\\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"}, "ListAutoScalingConfigurationsRequest": {"type": "structure", "members": {"AutoScalingConfigurationName": {"shape": "AutoScalingConfigurationName", "documentation": "<p>The name of the App Runner auto scaling configuration that you want to list. If specified, App Runner lists revisions that share this name. If not specified, App Runner returns revisions of all active configurations.</p>"}, "LatestOnly": {"shape": "Boolean", "documentation": "<p>Set to <code>true</code> to list only the latest revision for each requested configuration name.</p> <p>Set to <code>false</code> to list all revisions for each requested configuration name.</p> <p>Default: <code>true</code> </p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to include in each response (result page). It's used for a paginated request.</p> <p>If you don't specify <code>MaxResults</code>, the request retrieves all available results in a single response.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token from a previous result page. It's used for a paginated request. The request retrieves the next result page. All other parameter values must be identical to the ones that are specified in the initial request.</p> <p>If you don't specify <code>NextToken</code>, the request retrieves the first result page.</p>"}}}, "ListAutoScalingConfigurationsResponse": {"type": "structure", "required": ["AutoScalingConfigurationSummaryList"], "members": {"AutoScalingConfigurationSummaryList": {"shape": "AutoScalingConfigurationSummaryList", "documentation": "<p>A list of summary information records for auto scaling configurations. In a paginated request, the request returns up to <code>MaxResults</code> records for each call.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token that you can pass in a subsequent request to get the next result page. It's returned in a paginated request.</p>"}}}, "ListConnectionsRequest": {"type": "structure", "members": {"ConnectionName": {"shape": "ConnectionName", "documentation": "<p>If specified, only this connection is returned. If not specified, the result isn't filtered by name.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to include in each response (result page). Used for a paginated request.</p> <p>If you don't specify <code>MaxResults</code>, the request retrieves all available results in a single response.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token from a previous result page. Used for a paginated request. The request retrieves the next result page. All other parameter values must be identical to the ones specified in the initial request.</p> <p>If you don't specify <code>NextToken</code>, the request retrieves the first result page.</p>"}}}, "ListConnectionsResponse": {"type": "structure", "required": ["ConnectionSummaryList"], "members": {"ConnectionSummaryList": {"shape": "ConnectionSummaryList", "documentation": "<p>A list of summary information records for connections. In a paginated request, the request returns up to <code>MaxResults</code> records for each call.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token that you can pass in a subsequent request to get the next result page. Returned in a paginated request.</p>"}}}, "ListObservabilityConfigurationsRequest": {"type": "structure", "members": {"ObservabilityConfigurationName": {"shape": "ObservabilityConfigurationName", "documentation": "<p>The name of the App Runner observability configuration that you want to list. If specified, App Runner lists revisions that share this name. If not specified, App Runner returns revisions of all active configurations.</p>"}, "LatestOnly": {"shape": "Boolean", "documentation": "<p>Set to <code>true</code> to list only the latest revision for each requested configuration name.</p> <p>Set to <code>false</code> to list all revisions for each requested configuration name.</p> <p>Default: <code>true</code> </p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to include in each response (result page). It's used for a paginated request.</p> <p>If you don't specify <code>MaxResults</code>, the request retrieves all available results in a single response.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token from a previous result page. It's used for a paginated request. The request retrieves the next result page. All other parameter values must be identical to the ones that are specified in the initial request.</p> <p>If you don't specify <code>NextToken</code>, the request retrieves the first result page.</p>"}}}, "ListObservabilityConfigurationsResponse": {"type": "structure", "required": ["ObservabilityConfigurationSummaryList"], "members": {"ObservabilityConfigurationSummaryList": {"shape": "ObservabilityConfigurationSummaryList", "documentation": "<p>A list of summary information records for observability configurations. In a paginated request, the request returns up to <code>MaxResults</code> records for each call.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token that you can pass in a subsequent request to get the next result page. It's returned in a paginated request.</p>"}}}, "ListOperationsMaxResults": {"type": "integer", "max": 20, "min": 1}, "ListOperationsRequest": {"type": "structure", "required": ["ServiceArn"], "members": {"ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service that you want a list of operations for.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>A token from a previous result page. It's used for a paginated request. The request retrieves the next result page. All other parameter values must be identical to the ones specified in the initial request.</p> <p>If you don't specify <code>NextToken</code>, the request retrieves the first result page.</p>"}, "MaxResults": {"shape": "ListOperationsMaxResults", "documentation": "<p>The maximum number of results to include in each response (result page). It's used for a paginated request.</p> <p>If you don't specify <code>MaxResults</code>, the request retrieves all available results in a single response.</p>"}}}, "ListOperationsResponse": {"type": "structure", "members": {"OperationSummaryList": {"shape": "OperationSummaryList", "documentation": "<p>A list of operation summary information records. In a paginated request, the request returns up to <code>MaxResults</code> records for each call.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token that you can pass in a subsequent request to get the next result page. It's returned in a paginated request.</p>"}}}, "ListServicesRequest": {"type": "structure", "members": {"NextToken": {"shape": "String", "documentation": "<p>A token from a previous result page. Used for a paginated request. The request retrieves the next result page. All other parameter values must be identical to the ones specified in the initial request.</p> <p>If you don't specify <code>NextToken</code>, the request retrieves the first result page.</p>"}, "MaxResults": {"shape": "ServiceMaxResults", "documentation": "<p>The maximum number of results to include in each response (result page). It's used for a paginated request.</p> <p>If you don't specify <code>MaxResults</code>, the request retrieves all available results in a single response.</p>"}}}, "ListServicesResponse": {"type": "structure", "required": ["ServiceSummaryList"], "members": {"ServiceSummaryList": {"shape": "ServiceSummaryList", "documentation": "<p>A list of service summary information records. In a paginated request, the request returns up to <code>MaxResults</code> records for each call.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token that you can pass in a subsequent request to get the next result page. It's returned in a paginated request.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that a tag list is requested for.</p> <p>It must be the ARN of an App Runner resource.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>A list of the tag key-value pairs that are associated with the resource.</p>"}}}, "ListVpcConnectorsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to include in each response (result page). It's used for a paginated request.</p> <p>If you don't specify <code>MaxResults</code>, the request retrieves all available results in a single response.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token from a previous result page. It's used for a paginated request. The request retrieves the next result page. All other parameter values must be identical to the ones that are specified in the initial request.</p> <p>If you don't specify <code>NextToken</code>, the request retrieves the first result page.</p>"}}}, "ListVpcConnectorsResponse": {"type": "structure", "required": ["VpcConnectors"], "members": {"VpcConnectors": {"shape": "VpcConnectors", "documentation": "<p>A list of information records for VPC connectors. In a paginated request, the request returns up to <code>MaxResults</code> records for each call.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token that you can pass in a subsequent request to get the next result page. It's returned in a paginated request.</p>"}}}, "ListVpcIngressConnectionsFilter": {"type": "structure", "members": {"ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of a service to filter by. </p>"}, "VpcEndpointId": {"shape": "String", "documentation": "<p>The ID of a VPC Endpoint to filter by. </p>"}}, "documentation": "<p>Returns a list of VPC Ingress Connections based on the filter provided. It can return either <code>ServiceArn</code> or <code>VpcEndpointId</code>, or both.</p>"}, "ListVpcIngressConnectionsRequest": {"type": "structure", "members": {"Filter": {"shape": "ListVpcIngressConnectionsFilter", "documentation": "<p>The VPC Ingress Connections to be listed based on either the Service Arn or Vpc Endpoint Id, or both.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to include in each response (result page). It's used for a paginated request.</p> <p>If you don't specify <code>MaxResults</code>, the request retrieves all available results in a single response.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>A token from a previous result page. It's used for a paginated request. The request retrieves the next result page. All other parameter values must be identical to the ones that are specified in the initial request.</p> <p>If you don't specify <code>NextToken</code>, the request retrieves the first result page.</p>"}}}, "ListVpcIngressConnectionsResponse": {"type": "structure", "required": ["VpcIngressConnectionSummaryList"], "members": {"VpcIngressConnectionSummaryList": {"shape": "VpcIngressConnectionSummaryList", "documentation": "<p>A list of summary information records for VPC Ingress Connections. In a paginated request, the request returns up to <code>MaxResults</code> records for each call.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>The token that you can pass in a subsequent request to get the next result page. It's returned in a paginated request.</p>"}}}, "MaxResults": {"type": "integer", "max": 100, "min": 1}, "Memory": {"type": "string", "max": 4, "min": 4, "pattern": "2048|3072|4096|(2|3|4) GB"}, "NetworkConfiguration": {"type": "structure", "members": {"EgressConfiguration": {"shape": "EgressConfiguration", "documentation": "<p>Network configuration settings for outbound message traffic.</p>"}, "IngressConfiguration": {"shape": "IngressConfiguration", "documentation": "<p>Network configuration settings for inbound message traffic.</p>"}}, "documentation": "<p>Describes configuration settings related to network traffic of an App Runner service. Consists of embedded objects for each configurable network feature.</p>"}, "NextToken": {"type": "string", "max": 1024, "min": 1, "pattern": ".*"}, "NullableBoolean": {"type": "boolean"}, "ObservabilityConfiguration": {"type": "structure", "members": {"ObservabilityConfigurationArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this observability configuration.</p>"}, "ObservabilityConfigurationName": {"shape": "ObservabilityConfigurationName", "documentation": "<p>The customer-provided observability configuration name. It can be used in multiple revisions of a configuration.</p>"}, "TraceConfiguration": {"shape": "TraceConfiguration", "documentation": "<p>The configuration of the tracing feature within this observability configuration. If not specified, tracing isn't enabled.</p>"}, "ObservabilityConfigurationRevision": {"shape": "Integer", "documentation": "<p>The revision of this observability configuration. It's unique among all the active configurations (<code>\"Status\": \"ACTIVE\"</code>) that share the same <code>ObservabilityConfigurationName</code>.</p>"}, "Latest": {"shape": "Boolean", "documentation": "<p>It's set to <code>true</code> for the configuration with the highest <code>Revision</code> among all configurations that share the same <code>ObservabilityConfigurationName</code>. It's set to <code>false</code> otherwise.</p>"}, "Status": {"shape": "ObservabilityConfigurationStatus", "documentation": "<p>The current state of the observability configuration. If the status of a configuration revision is <code>INACTIVE</code>, it was deleted and can't be used. Inactive configuration revisions are permanently removed some time after they are deleted.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The time when the observability configuration was created. It's in Unix time stamp format.</p>"}, "DeletedAt": {"shape": "Timestamp", "documentation": "<p>The time when the observability configuration was deleted. It's in Unix time stamp format.</p>"}}, "documentation": "<p>Describes an App Runner observability configuration resource. Multiple revisions of a configuration have the same <code>ObservabilityConfigurationName</code> and different <code>ObservabilityConfigurationRevision</code> values.</p> <p>The resource is designed to configure multiple features (currently one feature, tracing). This type contains optional members that describe the configuration of these features (currently one member, <code>TraceConfiguration</code>). If a feature member isn't specified, the feature isn't enabled.</p>"}, "ObservabilityConfigurationName": {"type": "string", "max": 32, "min": 4, "pattern": "[A-Za-z0-9][A-Za-z0-9\\-_]{3,31}"}, "ObservabilityConfigurationStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "ObservabilityConfigurationSummary": {"type": "structure", "members": {"ObservabilityConfigurationArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this observability configuration.</p>"}, "ObservabilityConfigurationName": {"shape": "ObservabilityConfigurationName", "documentation": "<p>The customer-provided observability configuration name. It can be used in multiple revisions of a configuration.</p>"}, "ObservabilityConfigurationRevision": {"shape": "Integer", "documentation": "<p>The revision of this observability configuration. It's unique among all the active configurations (<code>\"Status\": \"ACTIVE\"</code>) that share the same <code>ObservabilityConfigurationName</code>.</p>"}}, "documentation": "<p>Provides summary information about an App Runner observability configuration resource.</p> <p>This type contains limited information about an observability configuration. It includes only identification information, without configuration details. It's returned by the <a>ListObservabilityConfigurations</a> action. Complete configuration information is returned by the <a>CreateObservabilityConfiguration</a>, <a>DescribeObservabilityConfiguration</a>, and <a>DeleteObservabilityConfiguration</a> actions using the <a>ObservabilityConfiguration</a> type.</p>"}, "ObservabilityConfigurationSummaryList": {"type": "list", "member": {"shape": "ObservabilityConfigurationSummary"}}, "OperationStatus": {"type": "string", "enum": ["PENDING", "IN_PROGRESS", "FAILED", "SUCCEEDED", "ROLLBACK_IN_PROGRESS", "ROLLBACK_FAILED", "ROLLBACK_SUCCEEDED"]}, "OperationSummary": {"type": "structure", "members": {"Id": {"shape": "UUID", "documentation": "<p>A unique ID of this operation. It's unique in the scope of the App Runner service.</p>"}, "Type": {"shape": "OperationType", "documentation": "<p>The type of operation. It indicates a specific action that occured.</p>"}, "Status": {"shape": "OperationStatus", "documentation": "<p>The current state of the operation.</p>"}, "TargetArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that the operation acted on (for example, an App Runner service).</p>"}, "StartedAt": {"shape": "Timestamp", "documentation": "<p>The time when the operation started. It's in the Unix time stamp format.</p>"}, "EndedAt": {"shape": "Timestamp", "documentation": "<p>The time when the operation ended. It's in the Unix time stamp format.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The time when the operation was last updated. It's in the Unix time stamp format.</p>"}}, "documentation": "<p>Provides summary information for an operation that occurred on an App Runner service.</p>"}, "OperationSummaryList": {"type": "list", "member": {"shape": "OperationSummary"}}, "OperationType": {"type": "string", "enum": ["START_DEPLOYMENT", "CREATE_SERVICE", "PAUSE_SERVICE", "RESUME_SERVICE", "DELETE_SERVICE", "UPDATE_SERVICE"]}, "PauseServiceRequest": {"type": "structure", "required": ["ServiceArn"], "members": {"ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service that you want to pause.</p>"}}}, "PauseServiceResponse": {"type": "structure", "required": ["Service"], "members": {"Service": {"shape": "Service", "documentation": "<p>A description of the App Runner service that this request just paused.</p>"}, "OperationId": {"shape": "UUID", "documentation": "<p>The unique ID of the asynchronous operation that this request started. You can use it combined with the <a>ListOperations</a> call to track the operation's progress.</p>"}}}, "ProviderType": {"type": "string", "enum": ["GITHUB"]}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>A resource doesn't exist for the specified Amazon Resource Name (ARN) in your Amazon Web Services account.</p>", "exception": true}, "ResumeServiceRequest": {"type": "structure", "required": ["ServiceArn"], "members": {"ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service that you want to resume.</p>"}}}, "ResumeServiceResponse": {"type": "structure", "required": ["Service"], "members": {"Service": {"shape": "Service", "documentation": "<p>A description of the App Runner service that this request just resumed.</p>"}, "OperationId": {"shape": "UUID", "documentation": "<p>The unique ID of the asynchronous operation that this request started. You can use it combined with the <a>ListOperations</a> call to track the operation's progress.</p>"}}}, "RoleArn": {"type": "string", "max": 1024, "min": 29, "pattern": "arn:(aws|aws-us-gov|aws-cn|aws-iso|aws-iso-b):iam::[0-9]{12}:(role|role\\/service-role)\\/[\\w+=,.@\\-/]{1,1000}"}, "Runtime": {"type": "string", "enum": ["PYTHON_3", "NODEJS_12", "NODEJS_14", "CORRETTO_8", "CORRETTO_11", "NODEJS_16", "GO_1", "DOTNET_6", "PHP_81", "RUBY_31"]}, "RuntimeEnvironmentSecrets": {"type": "map", "key": {"shape": "RuntimeEnvironmentSecretsName"}, "value": {"shape": "RuntimeEnvironmentSecretsValue"}}, "RuntimeEnvironmentSecretsName": {"type": "string", "max": 2048, "min": 1, "sensitive": true}, "RuntimeEnvironmentSecretsValue": {"type": "string", "max": 2048, "min": 1, "sensitive": true}, "RuntimeEnvironmentVariables": {"type": "map", "key": {"shape": "RuntimeEnvironmentVariablesKey"}, "value": {"shape": "RuntimeEnvironmentVariablesValue"}}, "RuntimeEnvironmentVariablesKey": {"type": "string", "max": 51200, "min": 1, "pattern": ".*", "sensitive": true}, "RuntimeEnvironmentVariablesValue": {"type": "string", "max": 51200, "min": 0, "pattern": ".*", "sensitive": true}, "Service": {"type": "structure", "required": ["ServiceName", "ServiceId", "ServiceArn", "CreatedAt", "UpdatedAt", "Status", "SourceConfiguration", "InstanceConfiguration", "AutoScalingConfigurationSummary", "NetworkConfiguration"], "members": {"ServiceName": {"shape": "ServiceName", "documentation": "<p>The customer-provided service name.</p>"}, "ServiceId": {"shape": "ServiceId", "documentation": "<p>An ID that App Runner generated for this service. It's unique within the Amazon Web Services Region.</p>"}, "ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this service.</p>"}, "ServiceUrl": {"shape": "String", "documentation": "<p>A subdomain URL that A<PERSON> <PERSON> generated for this service. You can use this URL to access your service web application.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The time when the App Runner service was created. It's in the Unix time stamp format.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The time when the App Runner service was last updated at. It's in the Unix time stamp format.</p>"}, "DeletedAt": {"shape": "Timestamp", "documentation": "<p>The time when the App Runner service was deleted. It's in the Unix time stamp format.</p>"}, "Status": {"shape": "ServiceStatus", "documentation": "<p>The current state of the App Runner service. These particular values mean the following.</p> <ul> <li> <p> <code>CREATE_FAILED</code> – The service failed to create. To troubleshoot this failure, read the failure events and logs, change any parameters that need to be fixed, and retry the call to create the service.</p> <p>The failed service isn't usable, and still counts towards your service quota. When you're done analyzing the failure, delete the service.</p> </li> <li> <p> <code>DELETE_FAILED</code> – The service failed to delete and can't be successfully recovered. Retry the service deletion call to ensure that all related resources are removed.</p> </li> </ul>"}, "SourceConfiguration": {"shape": "SourceConfiguration", "documentation": "<p>The source deployed to the App Runner service. It can be a code or an image repository.</p>"}, "InstanceConfiguration": {"shape": "InstanceConfiguration", "documentation": "<p>The runtime configuration of instances (scaling units) of this service.</p>"}, "EncryptionConfiguration": {"shape": "EncryptionConfiguration", "documentation": "<p>The encryption key that <PERSON><PERSON> <PERSON> uses to encrypt the service logs and the copy of the source repository that App Runner maintains for the service. It can be either a customer-provided encryption key or an Amazon Web Services managed key.</p>"}, "HealthCheckConfiguration": {"shape": "HealthCheckConfiguration", "documentation": "<p>The settings for the health check that <PERSON><PERSON> <PERSON> performs to monitor the health of this service.</p>"}, "AutoScalingConfigurationSummary": {"shape": "AutoScalingConfigurationSummary", "documentation": "<p>Summary information for the App Runner automatic scaling configuration resource that's associated with this service.</p>"}, "NetworkConfiguration": {"shape": "NetworkConfiguration", "documentation": "<p>Configuration settings related to network traffic of the web application that this service runs.</p>"}, "ObservabilityConfiguration": {"shape": "ServiceObservabilityConfiguration", "documentation": "<p>The observability configuration of this service.</p>"}}, "documentation": "<p>Describes an App Runner service. It can describe a service in any state, including deleted services.</p> <p>This type contains the full information about a service, including configuration details. It's returned by the <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_CreateService.html\">CreateService</a>, <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_DescribeService.html\">DescribeService</a>, and <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_DeleteService.html\">DeleteService</a> actions. A subset of this information is returned by the <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_ListServices.html\">ListServices</a> action using the <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_ServiceSummary.html\">ServiceSummary</a> type.</p>"}, "ServiceId": {"type": "string", "max": 32, "min": 32, "pattern": "[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}"}, "ServiceMaxResults": {"type": "integer", "max": 20, "min": 1}, "ServiceName": {"type": "string", "max": 40, "min": 4, "pattern": "[A-Za-z0-9][A-Za-z0-9-_]{3,39}"}, "ServiceObservabilityConfiguration": {"type": "structure", "required": ["ObservabilityEnabled"], "members": {"ObservabilityEnabled": {"shape": "Boolean", "documentation": "<p>When <code>true</code>, an observability configuration resource is associated with the service, and an <code>ObservabilityConfigurationArn</code> is specified.</p>"}, "ObservabilityConfigurationArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the observability configuration that is associated with the service. Specified only when <code>ObservabilityEnabled</code> is <code>true</code>.</p> <p>Specify an ARN with a name and a revision number to associate that revision. For example: <code>arn:aws:apprunner:us-east-1:123456789012:observabilityconfiguration/xray-tracing/3</code> </p> <p>Specify just the name to associate the latest revision. For example: <code>arn:aws:apprunner:us-east-1:123456789012:observabilityconfiguration/xray-tracing</code> </p>"}}, "documentation": "<p>Describes the observability configuration of an App Runner service. These are additional observability features, like tracing, that you choose to enable. They're configured in a separate resource that you associate with your service.</p>"}, "ServiceQuotaExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>App Runner can't create this resource. You've reached your account quota for this resource type.</p> <p>For App Runner per-resource quotas, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/apprunner.html\">App Runner endpoints and quotas</a> in the <i>Amazon Web Services General Reference</i>.</p>", "exception": true}, "ServiceStatus": {"type": "string", "enum": ["CREATE_FAILED", "RUNNING", "DELETED", "DELETE_FAILED", "PAUSED", "OPERATION_IN_PROGRESS"]}, "ServiceSummary": {"type": "structure", "members": {"ServiceName": {"shape": "ServiceName", "documentation": "<p>The customer-provided service name.</p>"}, "ServiceId": {"shape": "ServiceId", "documentation": "<p>An ID that App Runner generated for this service. It's unique within the Amazon Web Services Region.</p>"}, "ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this service.</p>"}, "ServiceUrl": {"shape": "String", "documentation": "<p>A subdomain URL that A<PERSON> <PERSON> generated for this service. You can use this URL to access your service web application.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The time when the App Runner service was created. It's in the Unix time stamp format.</p>"}, "UpdatedAt": {"shape": "Timestamp", "documentation": "<p>The time when the App Runner service was last updated. It's in theUnix time stamp format.</p>"}, "Status": {"shape": "ServiceStatus", "documentation": "<p>The current state of the App Runner service. These particular values mean the following.</p> <ul> <li> <p> <code>CREATE_FAILED</code> – The service failed to create. Read the failure events and logs, change any parameters that need to be fixed, and retry the call to create the service.</p> <p>The failed service isn't usable, and still counts towards your service quota. When you're done analyzing the failure, delete the service.</p> </li> <li> <p> <code>DELETE_FAILED</code> – The service failed to delete and can't be successfully recovered. Retry the service deletion call to ensure that all related resources are removed.</p> </li> </ul>"}}, "documentation": "<p>Provides summary information for an App Runner service.</p> <p>This type contains limited information about a service. It doesn't include configuration details. It's returned by the <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_ListServices.html\">ListServices</a> action. Complete service information is returned by the <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_CreateService.html\">CreateService</a>, <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_DescribeService.html\">DescribeService</a>, and <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_DeleteService.html\">DeleteService</a> actions using the <a href=\"https://docs.aws.amazon.com/apprunner/latest/api/API_Service.html\">Service</a> type.</p>"}, "ServiceSummaryList": {"type": "list", "member": {"shape": "ServiceSummary"}}, "SourceCodeVersion": {"type": "structure", "required": ["Type", "Value"], "members": {"Type": {"shape": "SourceCodeVersionType", "documentation": "<p>The type of version identifier.</p> <p>For a git-based repository, branches represent versions.</p>"}, "Value": {"shape": "String", "documentation": "<p>A source code version.</p> <p>For a git-based repository, a branch name maps to a specific version. App Runner uses the most recent commit to the branch.</p>"}}, "documentation": "<p>Identifies a version of code that <PERSON><PERSON> <PERSON> refers to within a source code repository.</p>"}, "SourceCodeVersionType": {"type": "string", "enum": ["BRANCH"]}, "SourceConfiguration": {"type": "structure", "members": {"CodeRepository": {"shape": "CodeRepository", "documentation": "<p>The description of a source code repository.</p> <p>You must provide either this member or <code>ImageRepository</code> (but not both).</p>"}, "ImageRepository": {"shape": "ImageRepository", "documentation": "<p>The description of a source image repository.</p> <p>You must provide either this member or <code>CodeRepository</code> (but not both).</p>"}, "AutoDeploymentsEnabled": {"shape": "NullableBoolean", "documentation": "<p>If <code>true</code>, continuous integration from the source repository is enabled for the App Runner service. Each repository change (including any source code commit or new image version) starts a deployment.</p> <p>Default: App Runner sets to <code>false</code> for a source image that uses an ECR Public repository or an ECR repository that's in an Amazon Web Services account other than the one that the service is in. App Runner sets to <code>true</code> in all other cases (which currently include a source code repository or a source image using a same-account ECR repository).</p>"}, "AuthenticationConfiguration": {"shape": "AuthenticationConfiguration", "documentation": "<p>Describes the resources that are needed to authenticate access to some source repositories.</p>"}}, "documentation": "<p>Describes the source deployed to an App Runner service. It can be a code or an image repository.</p>"}, "StartCommand": {"type": "string", "pattern": "[^\\x0a\\x0d]+", "sensitive": true}, "StartDeploymentRequest": {"type": "structure", "required": ["ServiceArn"], "members": {"ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service that you want to manually deploy to.</p>"}}}, "StartDeploymentResponse": {"type": "structure", "required": ["OperationId"], "members": {"OperationId": {"shape": "UUID", "documentation": "<p>The unique ID of the asynchronous operation that this request started. You can use it combined with the <a>ListOperations</a> call to track the operation's progress.</p>"}}}, "String": {"type": "string", "max": 51200, "min": 0, "pattern": ".*"}, "StringList": {"type": "list", "member": {"shape": "String"}}, "Tag": {"type": "structure", "members": {"Key": {"shape": "TagKey", "documentation": "<p>The key of the tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The value of the tag.</p>"}}, "documentation": "<p>Describes a tag that is applied to an App Runner resource. A tag is a metadata item consisting of a key-value pair.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!aws:).+"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}}, "TagList": {"type": "list", "member": {"shape": "Tag"}}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you want to update tags for.</p> <p>It must be the ARN of an App Runner resource.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>A list of tag key-value pairs to add or update. If a key is new to the resource, the tag is added with the provided value. If a key is already associated with the resource, the value of the tag is updated.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": ".*"}, "Timestamp": {"type": "timestamp"}, "TraceConfiguration": {"type": "structure", "required": ["<PERSON><PERSON><PERSON>"], "members": {"Vendor": {"shape": "TracingVendor", "documentation": "<p>The implementation provider chosen for tracing App Runner services.</p>"}}, "documentation": "<p>Describes the configuration of the tracing feature within an App Runner observability configuration.</p>"}, "TracingVendor": {"type": "string", "enum": ["AWSXRAY"]}, "UUID": {"type": "string", "max": 36, "min": 36, "pattern": "[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}"}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the resource that you want to remove tags from.</p> <p>It must be the ARN of an App Runner resource.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>A list of tag keys that you want to remove.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateServiceRequest": {"type": "structure", "required": ["ServiceArn"], "members": {"ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the App Runner service that you want to update.</p>"}, "SourceConfiguration": {"shape": "SourceConfiguration", "documentation": "<p>The source configuration to apply to the App Runner service.</p> <p>You can change the configuration of the code or image repository that the service uses. However, you can't switch from code to image or the other way around. This means that you must provide the same structure member of <code>SourceConfiguration</code> that you originally included when you created the service. Specifically, you can include either <code>CodeRepository</code> or <code>ImageRepository</code>. To update the source configuration, set the values to members of the structure that you include.</p>"}, "InstanceConfiguration": {"shape": "InstanceConfiguration", "documentation": "<p>The runtime configuration to apply to instances (scaling units) of your service.</p>"}, "AutoScalingConfigurationArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of an App Runner automatic scaling configuration resource that you want to associate with the App Runner service.</p>"}, "HealthCheckConfiguration": {"shape": "HealthCheckConfiguration", "documentation": "<p>The settings for the health check that App Runner performs to monitor the health of the App Runner service.</p>"}, "NetworkConfiguration": {"shape": "NetworkConfiguration", "documentation": "<p>Configuration settings related to network traffic of the web application that the App Runner service runs.</p>"}, "ObservabilityConfiguration": {"shape": "ServiceObservabilityConfiguration", "documentation": "<p>The observability configuration of your service.</p>"}}}, "UpdateServiceResponse": {"type": "structure", "required": ["Service", "OperationId"], "members": {"Service": {"shape": "Service", "documentation": "<p>A description of the App Runner service updated by this request. All configuration values in the returned <code>Service</code> structure reflect configuration changes that are being applied by this request.</p>"}, "OperationId": {"shape": "UUID", "documentation": "<p>The unique ID of the asynchronous operation that this request started. You can use it combined with the <a>ListOperations</a> call to track the operation's progress.</p>"}}}, "UpdateVpcIngressConnectionRequest": {"type": "structure", "required": ["VpcIngressConnectionArn", "IngressVpcConfiguration"], "members": {"VpcIngressConnectionArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (Arn) for the App Runner VPC Ingress Connection resource that you want to update.</p>"}, "IngressVpcConfiguration": {"shape": "IngressVpcConfiguration", "documentation": "<p>Specifications for the customer’s Amazon VPC and the related Amazon Web Services PrivateLink VPC endpoint that are used to update the VPC Ingress Connection resource.</p>"}}}, "UpdateVpcIngressConnectionResponse": {"type": "structure", "required": ["VpcIngressConnection"], "members": {"VpcIngressConnection": {"shape": "VpcIngressConnection", "documentation": "<p>A description of the App Runner VPC Ingress Connection resource that's updated by this request.</p>"}}}, "VpcConnector": {"type": "structure", "members": {"VpcConnectorName": {"shape": "VpcConnectorName", "documentation": "<p>The customer-provided VPC connector name.</p>"}, "VpcConnectorArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this VPC connector.</p>"}, "VpcConnectorRevision": {"shape": "Integer", "documentation": "<p>The revision of this VPC connector. It's unique among all the active connectors (<code>\"Status\": \"ACTIVE\"</code>) that share the same <code>Name</code>.</p> <note> <p>At this time, App Runner supports only one revision per name.</p> </note>"}, "Subnets": {"shape": "StringList", "documentation": "<p>A list of IDs of subnets that App Runner uses for your service. All IDs are of subnets of a single Amazon VPC.</p>"}, "SecurityGroups": {"shape": "StringList", "documentation": "<p>A list of IDs of security groups that <PERSON><PERSON> <PERSON> uses for access to Amazon Web Services resources under the specified subnets. If not specified, App Runner uses the default security group of the Amazon VPC. The default security group allows all outbound traffic.</p>"}, "Status": {"shape": "VpcConnectorStatus", "documentation": "<p>The current state of the VPC connector. If the status of a connector revision is <code>INACTIVE</code>, it was deleted and can't be used. Inactive connector revisions are permanently removed some time after they are deleted.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The time when the VPC connector was created. It's in Unix time stamp format.</p>"}, "DeletedAt": {"shape": "Timestamp", "documentation": "<p>The time when the VPC connector was deleted. It's in Unix time stamp format.</p>"}}, "documentation": "<p>Describes an App Runner VPC connector resource. A VPC connector describes the Amazon Virtual Private Cloud (Amazon VPC) that an App Runner service is associated with, and the subnets and security group that are used.</p> <p>Multiple revisions of a connector might have the same <code>Name</code> and different <code>Revision</code> values.</p> <note> <p>At this time, App Runner supports only one revision per name.</p> </note>"}, "VpcConnectorName": {"type": "string", "max": 40, "min": 4, "pattern": "[A-Za-z0-9][A-Za-z0-9\\-_]{3,39}"}, "VpcConnectorStatus": {"type": "string", "enum": ["ACTIVE", "INACTIVE"]}, "VpcConnectors": {"type": "list", "member": {"shape": "VpcConnector"}}, "VpcDNSTarget": {"type": "structure", "members": {"VpcIngressConnectionArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the VPC Ingress Connection that is associated with your service.</p>"}, "VpcId": {"shape": "String", "documentation": "<p>The ID of the Amazon VPC that is associated with the custom domain name of the target DNS.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>The domain name of your target DNS that is associated with the Amazon VPC.</p>"}}, "documentation": "<p>DNS Target record for a custom domain of this Amazon VPC.</p>"}, "VpcDNSTargetList": {"type": "list", "member": {"shape": "VpcDNSTarget"}}, "VpcIngressConnection": {"type": "structure", "members": {"VpcIngressConnectionArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the VPC Ingress Connection. </p>"}, "VpcIngressConnectionName": {"shape": "VpcIngressConnectionName", "documentation": "<p>The customer-provided VPC Ingress Connection name.</p>"}, "ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service associated with the VPC Ingress Connection. </p>"}, "Status": {"shape": "VpcIngressConnectionStatus", "documentation": "<p>The current status of the VPC Ingress Connection. The VPC Ingress Connection displays one of the following statuses: <code>AVAILABLE</code>, <code>PENDING_CREATION</code>, <code>PENDING_UPDATE</code>, <code>PENDING_DELETION</code>,<code>FAILED_CREATION</code>, <code>FAILED_UPDATE</code>, <code>FAILED_DELETION</code>, and <code>DELETED</code>.. </p>"}, "AccountId": {"shape": "CustomerAccountId", "documentation": "<p>The Account Id you use to create the VPC Ingress Connection resource.</p>"}, "DomainName": {"shape": "DomainName", "documentation": "<p>The domain name associated with the VPC Ingress Connection resource.</p>"}, "IngressVpcConfiguration": {"shape": "IngressVpcConfiguration", "documentation": "<p>Specifications for the customer’s VPC and related PrivateLink VPC endpoint that are used to associate with the VPC Ingress Connection resource.</p>"}, "CreatedAt": {"shape": "Timestamp", "documentation": "<p>The time when the VPC Ingress Connection was created. It's in the Unix time stamp format.</p> <ul> <li> <p> Type: Timestamp </p> </li> <li> <p> Required: Yes </p> </li> </ul>"}, "DeletedAt": {"shape": "Timestamp", "documentation": "<p>The time when the App Runner service was deleted. It's in the Unix time stamp format.</p> <ul> <li> <p> Type: Timestamp </p> </li> <li> <p> Required: No </p> </li> </ul>"}}, "documentation": "<p>The App Runner resource that specifies an App Runner endpoint for incoming traffic. It establishes a connection between a VPC interface endpoint and a App Runner service, to make your App Runner service accessible from only within an Amazon VPC.</p>"}, "VpcIngressConnectionName": {"type": "string", "max": 40, "min": 4, "pattern": "[A-Za-z0-9][A-Za-z0-9\\-_]{3,39}"}, "VpcIngressConnectionStatus": {"type": "string", "enum": ["AVAILABLE", "PENDING_CREATION", "PENDING_UPDATE", "PENDING_DELETION", "FAILED_CREATION", "FAILED_UPDATE", "FAILED_DELETION", "DELETED"]}, "VpcIngressConnectionSummary": {"type": "structure", "members": {"VpcIngressConnectionArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the VPC Ingress Connection. </p>"}, "ServiceArn": {"shape": "AppRunnerResourceArn", "documentation": "<p>The Amazon Resource Name (ARN) of the service associated with the VPC Ingress Connection. </p>"}}, "documentation": "<p>Provides summary information about an VPC Ingress Connection, which includes its VPC Ingress Connection ARN and its associated Service ARN.</p>"}, "VpcIngressConnectionSummaryList": {"type": "list", "member": {"shape": "VpcIngressConnectionSummary"}}}, "documentation": "<fullname>App Runner</fullname> <p>App Runner is an application service that provides a fast, simple, and cost-effective way to go directly from an existing container image or source code to a running service in the Amazon Web Services Cloud in seconds. You don't need to learn new technologies, decide which compute service to use, or understand how to provision and configure Amazon Web Services resources.</p> <p>App Runner connects directly to your container registry or source code repository. It provides an automatic delivery pipeline with fully managed operations, high performance, scalability, and security.</p> <p>For more information about App Runner, see the <a href=\"https://docs.aws.amazon.com/apprunner/latest/dg/\">App Runner Developer Guide</a>. For release information, see the <a href=\"https://docs.aws.amazon.com/apprunner/latest/relnotes/\">App Runner Release Notes</a>.</p> <p> To install the Software Development Kits (SDKs), Integrated Development Environment (IDE) Toolkits, and command line tools that you can use to access the API, see <a href=\"http://aws.amazon.com/tools/\">Tools for Amazon Web Services</a>.</p> <p> <b>Endpoints</b> </p> <p>For a list of Region-specific endpoints that <PERSON>pp Runner supports, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/apprunner.html\">App Runner endpoints and quotas</a> in the <i>Amazon Web Services General Reference</i>.</p>"}