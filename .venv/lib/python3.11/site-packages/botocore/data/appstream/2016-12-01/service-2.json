{"version": "2.0", "metadata": {"apiVersion": "2016-12-01", "endpointPrefix": "appstream2", "jsonVersion": "1.1", "protocol": "json", "serviceFullName": "Amazon AppStream", "serviceId": "AppStream", "signatureVersion": "v4", "signingName": "appstream", "targetPrefix": "PhotonAdminProxyService", "uid": "appstream-2016-12-01"}, "operations": {"AssociateApplicationFleet": {"name": "AssociateApplicationFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateApplicationFleetRequest"}, "output": {"shape": "AssociateApplicationFleetResult"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Associates the specified application with the specified fleet. This is only supported for Elastic fleets.</p>"}, "AssociateApplicationToEntitlement": {"name": "AssociateApplicationToEntitlement", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateApplicationToEntitlementRequest"}, "output": {"shape": "AssociateApplicationToEntitlementResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "EntitlementNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Associates an application to entitle.</p>"}, "AssociateFleet": {"name": "<PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "AssociateFleetRequest"}, "output": {"shape": "AssociateFleetR<PERSON>ult"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "IncompatibleImageException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Associates the specified fleet with the specified stack.</p>"}, "BatchAssociateUserStack": {"name": "BatchAssociateUserStack", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchAssociateUserStackRequest"}, "output": {"shape": "BatchAssociateUserStackResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "InvalidParameterCombinationException"}], "documentation": "<p>Associates the specified users with the specified stacks. Users in a user pool cannot be assigned to stacks with fleets that are joined to an Active Directory domain.</p>"}, "BatchDisassociateUserStack": {"name": "BatchDisassociateUserStack", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "BatchDisassociateUserStackRequest"}, "output": {"shape": "BatchDisassociateUserStackResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "InvalidParameterCombinationException"}], "documentation": "<p>Disassociates the specified users from the specified stacks.</p>"}, "CopyImage": {"name": "CopyImage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CopyImageRequest"}, "output": {"shape": "CopyImageResponse"}, "errors": [{"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceNotAvailableException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "IncompatibleImageException"}], "documentation": "<p>Copies the image within the same region or to a new region within the same AWS account. Note that any tags you added to the image will not be copied.</p>"}, "CreateAppBlock": {"name": "CreateAppBlock", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateAppBlockRequest"}, "output": {"shape": "CreateAppBlockResult"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "LimitExceededException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ResourceAlreadyExistsException"}], "documentation": "<p>Creates an app block.</p> <p>App blocks are an Amazon AppStream 2.0 resource that stores the details about the virtual hard disk in an S3 bucket. It also stores the setup script with details about how to mount the virtual hard disk. The virtual hard disk includes the application binaries and other files necessary to launch your applications. Multiple applications can be assigned to a single app block.</p> <p>This is only supported for Elastic fleets.</p>"}, "CreateApplication": {"name": "CreateApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateApplicationRequest"}, "output": {"shape": "CreateApplicationResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates an application.</p> <p>Applications are an Amazon AppStream 2.0 resource that stores the details about how to launch applications on Elastic fleet streaming instances. An application consists of the launch details, icon, and display name. Applications are associated with an app block that contains the application binaries and other files. The applications assigned to an Elastic fleet are the applications users can launch. </p> <p>This is only supported for Elastic fleets.</p>"}, "CreateDirectoryConfig": {"name": "CreateDirectoryConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateDirectoryConfigRequest"}, "output": {"shape": "CreateDirectoryConfigResult"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "OperationNotPermittedException"}, {"shape": "InvalidRoleException"}], "documentation": "<p>Creates a Directory Config object in AppStream 2.0. This object includes the configuration information required to join fleets and image builders to Microsoft Active Directory domains.</p>"}, "CreateEntitlement": {"name": "CreateEntitlement", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateEntitlementRequest"}, "output": {"shape": "CreateEntitlementResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "EntitlementAlreadyExistsException"}], "documentation": "<p>Creates a new entitlement. Entitlements control access to specific applications within a stack, based on user attributes. Entitlements apply to SAML 2.0 federated user identities. Amazon AppStream 2.0 user pool and streaming URL users are entitled to all applications in a stack. Entitlements don't apply to the desktop stream view application, or to applications managed by a dynamic app provider using the Dynamic Application Framework.</p>"}, "CreateFleet": {"name": "CreateFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateFleetRequest"}, "output": {"shape": "CreateFleetResult"}, "errors": [{"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotAvailableException"}, {"shape": "ResourceNotFoundException"}, {"shape": "LimitExceededException"}, {"shape": "RequestLimitExceededException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "InvalidRoleException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "IncompatibleImageException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Creates a fleet. A fleet consists of streaming instances that your users access for their applications and desktops.</p>"}, "CreateImageBuilder": {"name": "CreateImageBuilder", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateImageBuilderRequest"}, "output": {"shape": "CreateImageBuilderResult"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "RequestLimitExceededException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotAvailableException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidRoleException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "IncompatibleImageException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Creates an image builder. An image builder is a virtual machine that is used to create an image.</p> <p>The initial state of the builder is <code>PENDING</code>. When it is ready, the state is <code>RUNNING</code>.</p>"}, "CreateImageBuilderStreamingURL": {"name": "CreateImageBuilderStreamingURL", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateImageBuilderStreamingURLRequest"}, "output": {"shape": "CreateImageBuilderStreamingURLResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Creates a URL to start an image builder streaming session.</p>"}, "CreateStack": {"name": "CreateStack", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateStackRequest"}, "output": {"shape": "CreateStackResult"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidRoleException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterCombinationException"}], "documentation": "<p>Creates a stack to start streaming applications to users. A stack consists of an associated fleet, user access policies, and storage configurations. </p>"}, "CreateStreamingURL": {"name": "CreateStreamingURL", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateStreamingURLRequest"}, "output": {"shape": "CreateStreamingURLResult"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceNotAvailableException"}, {"shape": "OperationNotPermittedException"}, {"shape": "InvalidParameterCombinationException"}], "documentation": "<p>Creates a temporary URL to start an AppStream 2.0 streaming session for the specified user. A streaming URL enables application streaming to be tested without user setup. </p>"}, "CreateUpdatedImage": {"name": "CreateUpdatedImage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateUpdatedImageRequest"}, "output": {"shape": "CreateUpdatedImageResult"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ResourceAlreadyExistsException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "IncompatibleImageException"}], "documentation": "<p>Creates a new image with the latest Windows operating system updates, driver updates, and AppStream 2.0 agent software.</p> <p>For more information, see the \"Update an Image by Using Managed AppStream 2.0 Image Updates\" section in <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/administer-images.html\">Administer Your AppStream 2.0 Images</a>, in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "CreateUsageReportSubscription": {"name": "CreateUsageReportSubscription", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateUsageReportSubscriptionRequest"}, "output": {"shape": "CreateUsageReportSubscriptionResult"}, "errors": [{"shape": "InvalidRoleException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a usage report subscription. Usage reports are generated daily.</p>"}, "CreateUser": {"name": "CreateUser", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateUserRequest"}, "output": {"shape": "CreateUserResult"}, "errors": [{"shape": "ResourceAlreadyExistsException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "LimitExceededException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Creates a new user in the user pool.</p>"}, "DeleteAppBlock": {"name": "DeleteAppBlock", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteAppBlockRequest"}, "output": {"shape": "DeleteAppBlockResult"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes an app block.</p>"}, "DeleteApplication": {"name": "DeleteApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteApplicationRequest"}, "output": {"shape": "DeleteApplicationResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes an application.</p>"}, "DeleteDirectoryConfig": {"name": "DeleteDirectoryConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteDirectoryConfigRequest"}, "output": {"shape": "DeleteDirectoryConfigResult"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes the specified Directory Config object from AppStream 2.0. This object includes the information required to join streaming instances to an Active Directory domain.</p>"}, "DeleteEntitlement": {"name": "DeleteEntitlement", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteEntitlementRequest"}, "output": {"shape": "DeleteEntitlementResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "EntitlementNotFoundException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes the specified entitlement.</p>"}, "DeleteFleet": {"name": "DeleteFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteFleetRequest"}, "output": {"shape": "DeleteFleetResult"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes the specified fleet.</p>"}, "DeleteImage": {"name": "DeleteImage", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteImageRequest"}, "output": {"shape": "DeleteImageResult"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes the specified image. You cannot delete an image when it is in use. After you delete an image, you cannot provision new capacity using the image.</p>"}, "DeleteImageBuilder": {"name": "DeleteImageBuilder", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteImageBuilderRequest"}, "output": {"shape": "DeleteImageBuilderResult"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes the specified image builder and releases the capacity.</p>"}, "DeleteImagePermissions": {"name": "DeleteImagePermissions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteImagePermissionsRequest"}, "output": {"shape": "DeleteImagePermissionsResult"}, "errors": [{"shape": "ResourceNotAvailableException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes permissions for the specified private image. After you delete permissions for an image, AWS accounts to which you previously granted these permissions can no longer use the image.</p>"}, "DeleteStack": {"name": "DeleteStack", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteStackRequest"}, "output": {"shape": "DeleteStackResult"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Deletes the specified stack. After the stack is deleted, the application streaming environment provided by the stack is no longer available to users. Also, any reservations made for application streaming sessions for the stack are released.</p>"}, "DeleteUsageReportSubscription": {"name": "DeleteUsageReportSubscription", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteUsageReportSubscriptionRequest"}, "output": {"shape": "DeleteUsageReportSubscriptionResult"}, "errors": [{"shape": "InvalidAccountStatusException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Disables usage report generation.</p>"}, "DeleteUser": {"name": "DeleteUser", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteUserRequest"}, "output": {"shape": "DeleteUserResult"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes a user from the user pool.</p>"}, "DescribeAppBlocks": {"name": "DescribeAppBlocks", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeAppBlocksRequest"}, "output": {"shape": "DescribeAppBlocksResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list that describes one or more app blocks.</p>"}, "DescribeApplicationFleetAssociations": {"name": "DescribeApplicationFleetAssociations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeApplicationFleetAssociationsRequest"}, "output": {"shape": "DescribeApplicationFleetAssociationsResult"}, "errors": [{"shape": "InvalidParameterCombinationException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Retrieves a list that describes one or more application fleet associations. Either ApplicationArn or FleetName must be specified.</p>"}, "DescribeApplications": {"name": "DescribeApplications", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeApplicationsRequest"}, "output": {"shape": "DescribeApplicationsResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list that describes one or more applications.</p>"}, "DescribeDirectoryConfigs": {"name": "DescribeDirectoryConfigs", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeDirectoryConfigsRequest"}, "output": {"shape": "DescribeDirectoryConfigsResult"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list that describes one or more specified Directory Config objects for AppStream 2.0, if the names for these objects are provided. Otherwise, all Directory Config objects in the account are described. These objects include the configuration information required to join fleets and image builders to Microsoft Active Directory domains. </p> <p>Although the response syntax in this topic includes the account password, this password is not returned in the actual response.</p>"}, "DescribeEntitlements": {"name": "DescribeEntitlements", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeEntitlementsRequest"}, "output": {"shape": "DescribeEntitlementsResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "EntitlementNotFoundException"}], "documentation": "<p>Retrieves a list that describes one of more entitlements.</p>"}, "DescribeFleets": {"name": "DescribeFleets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeFleetsRequest"}, "output": {"shape": "DescribeFleetsResult"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list that describes one or more specified fleets, if the fleet names are provided. Otherwise, all fleets in the account are described.</p>"}, "DescribeImageBuilders": {"name": "DescribeImageBuilders", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeImageBuildersRequest"}, "output": {"shape": "DescribeImageBuildersResult"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list that describes one or more specified image builders, if the image builder names are provided. Otherwise, all image builders in the account are described.</p>"}, "DescribeImagePermissions": {"name": "DescribeImagePermissions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeImagePermissionsRequest"}, "output": {"shape": "DescribeImagePermissionsResult"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list that describes the permissions for shared AWS account IDs on a private image that you own. </p>"}, "DescribeImages": {"name": "DescribeImages", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeImagesRequest"}, "output": {"shape": "DescribeImagesResult"}, "errors": [{"shape": "InvalidParameterCombinationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list that describes one or more specified images, if the image names or image ARNs are provided. Otherwise, all images in the account are described.</p>"}, "DescribeSessions": {"name": "DescribeSessions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeSessionsRequest"}, "output": {"shape": "DescribeSessionsResult"}, "errors": [{"shape": "InvalidParameterCombinationException"}], "documentation": "<p>Retrieves a list that describes the streaming sessions for a specified stack and fleet. If a UserId is provided for the stack and fleet, only streaming sessions for that user are described. If an authentication type is not provided, the default is to authenticate users using a streaming URL.</p>"}, "DescribeStacks": {"name": "DescribeStacks", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeStacksRequest"}, "output": {"shape": "DescribeStacksResult"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list that describes one or more specified stacks, if the stack names are provided. Otherwise, all stacks in the account are described.</p>"}, "DescribeUsageReportSubscriptions": {"name": "DescribeUsageReportSubscriptions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeUsageReportSubscriptionsRequest"}, "output": {"shape": "DescribeUsageReportSubscriptionsResult"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidAccountStatusException"}], "documentation": "<p>Retrieves a list that describes one or more usage report subscriptions.</p>"}, "DescribeUserStackAssociations": {"name": "DescribeUserStackAssociations", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeUserStackAssociationsRequest"}, "output": {"shape": "DescribeUserStackAssociationsResult"}, "errors": [{"shape": "InvalidParameterCombinationException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Retrieves a list that describes the UserStackAssociation objects. You must specify either or both of the following:</p> <ul> <li> <p>The stack name</p> </li> <li> <p>The user name (email address of the user associated with the stack) and the authentication type for the user</p> </li> </ul>"}, "DescribeUsers": {"name": "DescribeUsers", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeUsersRequest"}, "output": {"shape": "DescribeUsersResult"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Retrieves a list that describes one or more specified users in the user pool.</p>"}, "DisableUser": {"name": "DisableUser", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisableUserRequest"}, "output": {"shape": "DisableUserResult"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Disables the specified user in the user pool. Users can't sign in to AppStream 2.0 until they are re-enabled. This action does not delete the user. </p>"}, "DisassociateApplicationFleet": {"name": "DisassociateApplicationFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateApplicationFleetRequest"}, "output": {"shape": "DisassociateApplicationFleetResult"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Disassociates the specified application from the fleet.</p>"}, "DisassociateApplicationFromEntitlement": {"name": "DisassociateApplicationFromEntitlement", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateApplicationFromEntitlementRequest"}, "output": {"shape": "DisassociateApplicationFromEntitlementResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "EntitlementNotFoundException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Deletes the specified application from the specified entitlement.</p>"}, "DisassociateFleet": {"name": "DisassociateFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DisassociateFleetRequest"}, "output": {"shape": "DisassociateFleetResult"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Disassociates the specified fleet from the specified stack.</p>"}, "EnableUser": {"name": "EnableUser", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "EnableUserRequest"}, "output": {"shape": "EnableUserResult"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidAccountStatusException"}], "documentation": "<p>Enables a user in the user pool. After being enabled, users can sign in to AppStream 2.0 and open applications from the stacks to which they are assigned.</p>"}, "ExpireSession": {"name": "ExpireSession", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ExpireSessionRequest"}, "output": {"shape": "ExpireSessionResult"}, "documentation": "<p>Immediately stops the specified streaming session.</p>"}, "ListAssociatedFleets": {"name": "ListAssociatedFleets", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAssociatedFleetsRequest"}, "output": {"shape": "ListAssociatedFleetsResult"}, "documentation": "<p>Retrieves the name of the fleet that is associated with the specified stack.</p>"}, "ListAssociatedStacks": {"name": "ListAssociatedStacks", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListAssociatedStacksRequest"}, "output": {"shape": "ListAssociatedStacksResult"}, "documentation": "<p>Retrieves the name of the stack with which the specified fleet is associated.</p>"}, "ListEntitledApplications": {"name": "ListEntitledApplications", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListEntitledApplicationsRequest"}, "output": {"shape": "ListEntitledApplicationsResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "EntitlementNotFoundException"}], "documentation": "<p>Retrieves a list of entitled applications.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves a list of all tags for the specified AppStream 2.0 resource. You can tag AppStream 2.0 image builders, images, fleets, and stacks.</p> <p>For more information about tags, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/tagging-basic.html\">Tagging Your Resources</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "StartFleet": {"name": "StartFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartFleetRequest"}, "output": {"shape": "StartFleetResult"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "OperationNotPermittedException"}, {"shape": "LimitExceededException"}, {"shape": "RequestLimitExceededException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotAvailableException"}, {"shape": "InvalidRoleException"}], "documentation": "<p>Starts the specified fleet.</p>"}, "StartImageBuilder": {"name": "StartImageBuilder", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StartImageBuilderRequest"}, "output": {"shape": "StartImageBuilderResult"}, "errors": [{"shape": "ResourceNotAvailableException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "IncompatibleImageException"}], "documentation": "<p>Starts the specified image builder.</p>"}, "StopFleet": {"name": "StopFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopFleetRequest"}, "output": {"shape": "StopFleetResult"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Stops the specified fleet.</p>"}, "StopImageBuilder": {"name": "StopImageBuilder", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "StopImageBuilderRequest"}, "output": {"shape": "StopImageBuilderResult"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Stops the specified image builder.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Adds or overwrites one or more tags for the specified AppStream 2.0 resource. You can tag AppStream 2.0 image builders, images, fleets, and stacks.</p> <p>Each tag consists of a key and an optional value. If a resource already has a tag with the same key, this operation updates its value.</p> <p>To list the current tags for your resources, use <a>ListTagsForResource</a>. To disassociate tags from your resources, use <a>UntagResource</a>.</p> <p>For more information about tags, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/tagging-basic.html\">Tagging Your Resources</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Disassociates one or more specified tags from the specified AppStream 2.0 resource.</p> <p>To list the current tags for your resources, use <a>ListTagsForResource</a>.</p> <p>For more information about tags, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/tagging-basic.html\">Tagging Your Resources</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "UpdateApplication": {"name": "UpdateApplication", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateApplicationRequest"}, "output": {"shape": "UpdateApplicationResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Updates the specified application.</p>"}, "UpdateDirectoryConfig": {"name": "UpdateDirectoryConfig", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateDirectoryConfigRequest"}, "output": {"shape": "UpdateDirectoryConfigResult"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ConcurrentModificationException"}, {"shape": "OperationNotPermittedException"}, {"shape": "InvalidRoleException"}], "documentation": "<p>Updates the specified Directory Config object in AppStream 2.0. This object includes the configuration information required to join fleets and image builders to Microsoft Active Directory domains.</p>"}, "UpdateEntitlement": {"name": "UpdateEntitlement", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateEntitlementRequest"}, "output": {"shape": "UpdateEntitlementResult"}, "errors": [{"shape": "OperationNotPermittedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "EntitlementNotFoundException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Updates the specified entitlement.</p>"}, "UpdateFleet": {"name": "UpdateFleet", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateFleetRequest"}, "output": {"shape": "UpdateFleetResult"}, "errors": [{"shape": "ResourceInUseException"}, {"shape": "LimitExceededException"}, {"shape": "RequestLimitExceededException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "InvalidRoleException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ResourceNotAvailableException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "ConcurrentModificationException"}, {"shape": "IncompatibleImageException"}, {"shape": "OperationNotPermittedException"}], "documentation": "<p>Updates the specified fleet.</p> <p>If the fleet is in the <code>STOPPED</code> state, you can update any attribute except the fleet name.</p> <p>If the fleet is in the <code>RUNNING</code> state, you can update the following based on the fleet type:</p> <ul> <li> <p>Always-On and On-Demand fleet types</p> <p>You can update the <code>DisplayName</code>, <code>ComputeCapacity</code>, <code>ImageARN</code>, <code>ImageName</code>, <code>IdleDisconnectTimeoutInSeconds</code>, and <code>DisconnectTimeoutInSeconds</code> attributes.</p> </li> <li> <p>Elastic fleet type</p> <p>You can update the <code>DisplayName</code>, <code>IdleDisconnectTimeoutInSeconds</code>, <code>DisconnectTimeoutInSeconds</code>, <code>MaxConcurrentSessions</code>, <code>SessionScriptS3Location</code> and <code>UsbDeviceFilterStrings</code> attributes.</p> </li> </ul> <p>If the fleet is in the <code>STARTING</code> or <code>STOPPED</code> state, you can't update it.</p>"}, "UpdateImagePermissions": {"name": "UpdateImagePermissions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateImagePermissionsRequest"}, "output": {"shape": "UpdateImagePermissionsResult"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceNotAvailableException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Adds or updates permissions for the specified private image. </p>"}, "UpdateStack": {"name": "UpdateStack", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateStackRequest"}, "output": {"shape": "UpdateStackResult"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "ResourceInUseException"}, {"shape": "InvalidRoleException"}, {"shape": "InvalidParameterCombinationException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidAccountStatusException"}, {"shape": "IncompatibleImageException"}, {"shape": "OperationNotPermittedException"}, {"shape": "ConcurrentModificationException"}], "documentation": "<p>Updates the specified fields for the specified stack.</p>"}}, "shapes": {"AccessEndpoint": {"type": "structure", "required": ["EndpointType"], "members": {"EndpointType": {"shape": "AccessEndpointType", "documentation": "<p>The type of interface endpoint.</p>"}, "VpceId": {"shape": "String", "documentation": "<p>The identifier (ID) of the VPC in which the interface endpoint is used.</p>"}}, "documentation": "<p>Describes an interface VPC endpoint (interface endpoint) that lets you create a private connection between the virtual private cloud (VPC) that you specify and AppStream 2.0. When you specify an interface endpoint for a stack, users of the stack can connect to AppStream 2.0 only through that endpoint. When you specify an interface endpoint for an image builder, administrators can connect to the image builder only through that endpoint.</p>"}, "AccessEndpointList": {"type": "list", "member": {"shape": "AccessEndpoint"}, "max": 4, "min": 1}, "AccessEndpointType": {"type": "string", "enum": ["STREAMING"]}, "AccountName": {"type": "string", "min": 1, "sensitive": true}, "AccountPassword": {"type": "string", "max": 127, "min": 1, "sensitive": true}, "Action": {"type": "string", "enum": ["CLIPBOARD_COPY_FROM_LOCAL_DEVICE", "CLIPBOARD_COPY_TO_LOCAL_DEVICE", "FILE_UPLOAD", "FILE_DOWNLOAD", "PRINTING_TO_LOCAL_DEVICE", "DOMAIN_PASSWORD_SIGNIN", "DOMAIN_SMART_CARD_SIGNIN"]}, "AppBlock": {"type": "structure", "required": ["Name", "<PERSON><PERSON>", "SetupScriptDetails"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the app block.</p>"}, "Arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the app block.</p>"}, "Description": {"shape": "String", "documentation": "<p>The description of the app block.</p>"}, "DisplayName": {"shape": "String", "documentation": "<p>The display name of the app block.</p>"}, "SourceS3Location": {"shape": "S3Location", "documentation": "<p>The source S3 location of the app block.</p>"}, "SetupScriptDetails": {"shape": "ScriptDetails", "documentation": "<p>The setup script details of the app block.</p>"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The created time of the app block.</p>"}}, "documentation": "<p>Describes an app block.</p> <p>App blocks are an Amazon AppStream 2.0 resource that stores the details about the virtual hard disk in an S3 bucket. It also stores the setup script with details about how to mount the virtual hard disk. The virtual hard disk includes the application binaries and other files necessary to launch your applications. Multiple applications can be assigned to a single app block.</p> <p>This is only supported for Elastic fleets.</p>"}, "AppBlocks": {"type": "list", "member": {"shape": "AppBlock"}}, "AppVisibility": {"type": "string", "enum": ["ALL", "ASSOCIATED"]}, "Application": {"type": "structure", "members": {"Name": {"shape": "String", "documentation": "<p>The name of the application.</p>"}, "DisplayName": {"shape": "String", "documentation": "<p>The application name to display.</p>"}, "IconURL": {"shape": "String", "documentation": "<p>The URL for the application icon. This URL might be time-limited.</p>"}, "LaunchPath": {"shape": "String", "documentation": "<p>The path to the application executable in the instance.</p>"}, "LaunchParameters": {"shape": "String", "documentation": "<p>The arguments that are passed to the application at launch.</p>"}, "Enabled": {"shape": "Boolean", "documentation": "<p>If there is a problem, the application can be disabled after image creation.</p>"}, "Metadata": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>Additional attributes that describe the application.</p>"}, "WorkingDirectory": {"shape": "String", "documentation": "<p>The working directory for the application.</p>"}, "Description": {"shape": "String", "documentation": "<p>The description of the application.</p>"}, "Arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the application.</p>"}, "AppBlockArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The app block ARN of the application.</p>"}, "IconS3Location": {"shape": "S3Location", "documentation": "<p>The S3 location of the application icon.</p>"}, "Platforms": {"shape": "Platforms", "documentation": "<p>The platforms on which the application can run.</p>"}, "InstanceFamilies": {"shape": "StringList", "documentation": "<p>The instance families for the application.</p>"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The time at which the application was created within the app block.</p>"}}, "documentation": "<p>Describes an application in the application catalog.</p>"}, "ApplicationAttribute": {"type": "string", "enum": ["LAUNCH_PARAMETERS", "WORKING_DIRECTORY"]}, "ApplicationAttributes": {"type": "list", "member": {"shape": "ApplicationAttribute"}, "max": 2}, "ApplicationFleetAssociation": {"type": "structure", "required": ["FleetName", "ApplicationArn"], "members": {"FleetName": {"shape": "String", "documentation": "<p>The name of the fleet associated with the application.</p>"}, "ApplicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the application associated with the fleet.</p>"}}, "documentation": "<p>Describes the application fleet association.</p>"}, "ApplicationFleetAssociationList": {"type": "list", "member": {"shape": "ApplicationFleetAssociation"}, "max": 25, "min": 1}, "ApplicationSettings": {"type": "structure", "required": ["Enabled"], "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>Enables or disables persistent application settings for users during their streaming sessions. </p>"}, "SettingsGroup": {"shape": "SettingsGroup", "documentation": "<p>The path prefix for the S3 bucket where users’ persistent application settings are stored. You can allow the same persistent application settings to be used across multiple stacks by specifying the same settings group for each stack. </p>"}}, "documentation": "<p>The persistent application settings for users of a stack.</p>"}, "ApplicationSettingsResponse": {"type": "structure", "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>Specifies whether persistent application settings are enabled for users during their streaming sessions.</p>"}, "SettingsGroup": {"shape": "SettingsGroup", "documentation": "<p>The path prefix for the S3 bucket where users’ persistent application settings are stored.</p>"}, "S3BucketName": {"shape": "String", "documentation": "<p>The S3 bucket where users’ persistent application settings are stored. When persistent application settings are enabled for the first time for an account in an AWS Region, an S3 bucket is created. The bucket is unique to the AWS account and the Region. </p>"}}, "documentation": "<p>Describes the persistent application settings for users of a stack.</p>"}, "Applications": {"type": "list", "member": {"shape": "Application"}}, "AppstreamAgentVersion": {"type": "string", "max": 100, "min": 1}, "Arn": {"type": "string", "pattern": "^arn:aws(?:\\-cn|\\-iso\\-b|\\-iso|\\-us\\-gov)?:[A-Za-z0-9][A-Za-z0-9_/.-]{0,62}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9_/.-]{0,63}:[A-Za-z0-9][A-Za-z0-9:_/+=,@.\\\\-]{0,1023}$"}, "ArnList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "AssociateApplicationFleetRequest": {"type": "structure", "required": ["FleetName", "ApplicationArn"], "members": {"FleetName": {"shape": "Name", "documentation": "<p>The name of the fleet.</p>"}, "ApplicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the application.</p>"}}}, "AssociateApplicationFleetResult": {"type": "structure", "members": {"ApplicationFleetAssociation": {"shape": "ApplicationFleetAssociation", "documentation": "<p>If fleet name is specified, this returns the list of applications that are associated to it. If application ARN is specified, this returns the list of fleets to which it is associated.</p>"}}}, "AssociateApplicationToEntitlementRequest": {"type": "structure", "required": ["StackName", "EntitlementName", "ApplicationIdentifier"], "members": {"StackName": {"shape": "Name", "documentation": "<p>The name of the stack.</p>"}, "EntitlementName": {"shape": "Name", "documentation": "<p>The name of the entitlement.</p>"}, "ApplicationIdentifier": {"shape": "String", "documentation": "<p>The identifier of the application.</p>"}}}, "AssociateApplicationToEntitlementResult": {"type": "structure", "members": {}}, "AssociateFleetRequest": {"type": "structure", "required": ["FleetName", "StackName"], "members": {"FleetName": {"shape": "String", "documentation": "<p>The name of the fleet. </p>"}, "StackName": {"shape": "String", "documentation": "<p>The name of the stack.</p>"}}}, "AssociateFleetResult": {"type": "structure", "members": {}}, "AuthenticationType": {"type": "string", "enum": ["API", "SAML", "USERPOOL", "AWS_AD"]}, "AwsAccountId": {"type": "string", "pattern": "^\\d+$"}, "AwsAccountIdList": {"type": "list", "member": {"shape": "AwsAccountId"}, "max": 5, "min": 1}, "BatchAssociateUserStackRequest": {"type": "structure", "required": ["UserStackAssociations"], "members": {"UserStackAssociations": {"shape": "UserStackAssociationList", "documentation": "<p>The list of UserStackAssociation objects.</p>"}}}, "BatchAssociateUserStackResult": {"type": "structure", "members": {"errors": {"shape": "UserStackAssociationErrorList", "documentation": "<p>The list of UserStackAssociationError objects.</p>"}}}, "BatchDisassociateUserStackRequest": {"type": "structure", "required": ["UserStackAssociations"], "members": {"UserStackAssociations": {"shape": "UserStackAssociationList", "documentation": "<p>The list of UserStackAssociation objects.</p>"}}}, "BatchDisassociateUserStackResult": {"type": "structure", "members": {"errors": {"shape": "UserStackAssociationErrorList", "documentation": "<p>The list of UserStackAssociationError objects.</p>"}}}, "Boolean": {"type": "boolean"}, "BooleanObject": {"type": "boolean"}, "CertificateBasedAuthProperties": {"type": "structure", "members": {"Status": {"shape": "CertificateBasedAuthStatus", "documentation": "<p>The status of the certificate-based authentication properties.</p>"}, "CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the AWS Certificate Manager Private CA resource.</p>"}}, "documentation": "<p>The certificate-based authentication properties used to authenticate SAML 2.0 Identity Provider (IdP) user identities to Active Directory domain-joined streaming instances. Fallback is turned on by default when certificate-based authentication is <b>Enabled</b> . Fallback allows users to log in using their AD domain password if certificate-based authentication is unsuccessful, or to unlock a desktop lock screen. <b>Enabled_no_directory_login_fallback</b> enables certificate-based authentication, but does not allow users to log in using their AD domain password. Users will be disconnected to re-authenticate using certificates.</p>"}, "CertificateBasedAuthStatus": {"type": "string", "enum": ["DISABLED", "ENABLED", "ENABLED_NO_DIRECTORY_LOGIN_FALLBACK"]}, "ComputeCapacity": {"type": "structure", "required": ["DesiredInstances"], "members": {"DesiredInstances": {"shape": "Integer", "documentation": "<p>The desired number of streaming instances.</p>"}}, "documentation": "<p>Describes the capacity for a fleet.</p>"}, "ComputeCapacityStatus": {"type": "structure", "required": ["Desired"], "members": {"Desired": {"shape": "Integer", "documentation": "<p>The desired number of streaming instances.</p>"}, "Running": {"shape": "Integer", "documentation": "<p>The total number of simultaneous streaming instances that are running.</p>"}, "InUse": {"shape": "Integer", "documentation": "<p>The number of instances in use for streaming.</p>"}, "Available": {"shape": "Integer", "documentation": "<p>The number of currently available instances that can be used to stream sessions.</p>"}}, "documentation": "<p>Describes the capacity status for a fleet.</p>"}, "ConcurrentModificationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>An API error occurred. Wait a few minutes and try again.</p>", "exception": true}, "CopyImageRequest": {"type": "structure", "required": ["SourceImageName", "DestinationImageName", "DestinationRegion"], "members": {"SourceImageName": {"shape": "Name", "documentation": "<p>The name of the image to copy.</p>"}, "DestinationImageName": {"shape": "Name", "documentation": "<p>The name that the image will have when it is copied to the destination.</p>"}, "DestinationRegion": {"shape": "RegionName", "documentation": "<p>The destination region to which the image will be copied. This parameter is required, even if you are copying an image within the same region.</p>"}, "DestinationImageDescription": {"shape": "Description", "documentation": "<p>The description that the image will have when it is copied to the destination.</p>"}}}, "CopyImageResponse": {"type": "structure", "members": {"DestinationImageName": {"shape": "Name", "documentation": "<p>The name of the destination image.</p>"}}}, "CreateAppBlockRequest": {"type": "structure", "required": ["Name", "SourceS3Location", "SetupScriptDetails"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the app block.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the app block.</p>"}, "DisplayName": {"shape": "DisplayName", "documentation": "<p>The display name of the app block. This is not displayed to the user.</p>"}, "SourceS3Location": {"shape": "S3Location", "documentation": "<p>The source S3 location of the app block.</p>"}, "SetupScriptDetails": {"shape": "ScriptDetails", "documentation": "<p>The setup script details of the app block.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags assigned to the app block.</p>"}}}, "CreateAppBlockResult": {"type": "structure", "members": {"AppBlock": {"shape": "AppBlock", "documentation": "<p>The app block.</p>"}}}, "CreateApplicationRequest": {"type": "structure", "required": ["Name", "IconS3Location", "LaunchPath", "Platforms", "InstanceFamilies", "AppBlockArn"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the application. This name is visible to users when display name is not specified.</p>"}, "DisplayName": {"shape": "DisplayName", "documentation": "<p>The display name of the application. This name is visible to users in the application catalog.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the application.</p>"}, "IconS3Location": {"shape": "S3Location", "documentation": "<p>The location in S3 of the application icon.</p>"}, "LaunchPath": {"shape": "String", "documentation": "<p>The launch path of the application.</p>"}, "WorkingDirectory": {"shape": "String", "documentation": "<p>The working directory of the application.</p>"}, "LaunchParameters": {"shape": "String", "documentation": "<p>The launch parameters of the application.</p>"}, "Platforms": {"shape": "Platforms", "documentation": "<p>The platforms the application supports. WINDOWS_SERVER_2019 and AMAZON_LINUX2 are supported for Elastic fleets.</p>"}, "InstanceFamilies": {"shape": "StringList", "documentation": "<p>The instance families the application supports. Valid values are GENERAL_PURPOSE and GRAPHICS_G4.</p>"}, "AppBlockArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The app block ARN to which the application should be associated</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags assigned to the application.</p>"}}}, "CreateApplicationResult": {"type": "structure", "members": {"Application": {"shape": "Application"}}}, "CreateDirectoryConfigRequest": {"type": "structure", "required": ["DirectoryName", "OrganizationalUnitDistinguishedNames"], "members": {"DirectoryName": {"shape": "DirectoryName", "documentation": "<p>The fully qualified name of the directory (for example, corp.example.com).</p>"}, "OrganizationalUnitDistinguishedNames": {"shape": "OrganizationalUnitDistinguishedNamesList", "documentation": "<p>The distinguished names of the organizational units for computer accounts.</p>"}, "ServiceAccountCredentials": {"shape": "ServiceAccountCredentials", "documentation": "<p>The credentials for the service account used by the fleet or image builder to connect to the directory.</p>"}, "CertificateBasedAuthProperties": {"shape": "CertificateBasedAuthProperties", "documentation": "<p>The certificate-based authentication properties used to authenticate SAML 2.0 Identity Provider (IdP) user identities to Active Directory domain-joined streaming instances. Fallback is turned on by default when certificate-based authentication is <b>Enabled</b> . Fallback allows users to log in using their AD domain password if certificate-based authentication is unsuccessful, or to unlock a desktop lock screen. <b>Enabled_no_directory_login_fallback</b> enables certificate-based authentication, but does not allow users to log in using their AD domain password. Users will be disconnected to re-authenticate using certificates.</p>"}}}, "CreateDirectoryConfigResult": {"type": "structure", "members": {"DirectoryConfig": {"shape": "DirectoryConfig", "documentation": "<p>Information about the directory configuration.</p>"}}}, "CreateEntitlementRequest": {"type": "structure", "required": ["Name", "StackName", "AppVisibility", "Attributes"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the entitlement.</p>"}, "StackName": {"shape": "Name", "documentation": "<p>The name of the stack with which the entitlement is associated.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the entitlement.</p>"}, "AppVisibility": {"shape": "AppVisibility", "documentation": "<p>Specifies whether all or selected apps are entitled.</p>"}, "Attributes": {"shape": "EntitlementAttributeList", "documentation": "<p>The attributes of the entitlement.</p>"}}}, "CreateEntitlementResult": {"type": "structure", "members": {"Entitlement": {"shape": "Entitlement", "documentation": "<p>The entitlement.</p>"}}}, "CreateFleetRequest": {"type": "structure", "required": ["Name", "InstanceType"], "members": {"Name": {"shape": "Name", "documentation": "<p>A unique name for the fleet.</p>"}, "ImageName": {"shape": "String", "documentation": "<p>The name of the image used to create the fleet.</p>"}, "ImageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the public, private, or shared image to use.</p>"}, "InstanceType": {"shape": "String", "documentation": "<p>The instance type to use when launching fleet instances. The following instance types are available:</p> <ul> <li> <p>stream.standard.small</p> </li> <li> <p>stream.standard.medium</p> </li> <li> <p>stream.standard.large</p> </li> <li> <p>stream.standard.xlarge</p> </li> <li> <p>stream.standard.2xlarge</p> </li> <li> <p>stream.compute.large</p> </li> <li> <p>stream.compute.xlarge</p> </li> <li> <p>stream.compute.2xlarge</p> </li> <li> <p>stream.compute.4xlarge</p> </li> <li> <p>stream.compute.8xlarge</p> </li> <li> <p>stream.memory.large</p> </li> <li> <p>stream.memory.xlarge</p> </li> <li> <p>stream.memory.2xlarge</p> </li> <li> <p>stream.memory.4xlarge</p> </li> <li> <p>stream.memory.8xlarge</p> </li> <li> <p>stream.memory.z1d.large</p> </li> <li> <p>stream.memory.z1d.xlarge</p> </li> <li> <p>stream.memory.z1d.2xlarge</p> </li> <li> <p>stream.memory.z1d.3xlarge</p> </li> <li> <p>stream.memory.z1d.6xlarge</p> </li> <li> <p>stream.memory.z1d.12xlarge</p> </li> <li> <p>stream.graphics-design.large</p> </li> <li> <p>stream.graphics-design.xlarge</p> </li> <li> <p>stream.graphics-design.2xlarge</p> </li> <li> <p>stream.graphics-design.4xlarge</p> </li> <li> <p>stream.graphics-desktop.2xlarge</p> </li> <li> <p>stream.graphics.g4dn.xlarge</p> </li> <li> <p>stream.graphics.g4dn.2xlarge</p> </li> <li> <p>stream.graphics.g4dn.4xlarge</p> </li> <li> <p>stream.graphics.g4dn.8xlarge</p> </li> <li> <p>stream.graphics.g4dn.12xlarge</p> </li> <li> <p>stream.graphics.g4dn.16xlarge</p> </li> <li> <p>stream.graphics-pro.4xlarge</p> </li> <li> <p>stream.graphics-pro.8xlarge</p> </li> <li> <p>stream.graphics-pro.16xlarge</p> </li> </ul> <p>The following instance types are available for Elastic fleets:</p> <ul> <li> <p>stream.standard.small</p> </li> <li> <p>stream.standard.medium</p> </li> <li> <p>stream.standard.large</p> </li> <li> <p>stream.standard.xlarge</p> </li> <li> <p>stream.standard.2xlarge</p> </li> </ul>"}, "FleetType": {"shape": "FleetType", "documentation": "<p>The fleet type.</p> <dl> <dt>ALWAYS_ON</dt> <dd> <p>Provides users with instant-on access to their apps. You are charged for all running instances in your fleet, even if no users are streaming apps.</p> </dd> <dt>ON_DEMAND</dt> <dd> <p>Provide users with access to applications after they connect, which takes one to two minutes. You are charged for instance streaming when users are connected and a small hourly fee for instances that are not streaming apps.</p> </dd> </dl>"}, "ComputeCapacity": {"shape": "ComputeCapacity", "documentation": "<p>The desired capacity for the fleet. This is not allowed for Elastic fleets. For Elastic fleets, specify MaxConcurrentSessions instead.</p>"}, "VpcConfig": {"shape": "VpcConfig", "documentation": "<p>The VPC configuration for the fleet. This is required for Elastic fleets, but not required for other fleet types. Elastic fleets require that you specify at least two subnets in different availability zones.</p>"}, "MaxUserDurationInSeconds": {"shape": "Integer", "documentation": "<p>The maximum amount of time that a streaming session can remain active, in seconds. If users are still connected to a streaming instance five minutes before this limit is reached, they are prompted to save any open documents before being disconnected. After this time elapses, the instance is terminated and replaced by a new instance.</p> <p>Specify a value between 600 and 360000.</p>"}, "DisconnectTimeoutInSeconds": {"shape": "Integer", "documentation": "<p>The amount of time that a streaming session remains active after users disconnect. If users try to reconnect to the streaming session after a disconnection or network interruption within this time interval, they are connected to their previous session. Otherwise, they are connected to a new session with a new streaming instance. </p> <p>Specify a value between 60 and 360000.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description to display.</p>"}, "DisplayName": {"shape": "DisplayName", "documentation": "<p>The fleet name to display.</p>"}, "EnableDefaultInternetAccess": {"shape": "BooleanObject", "documentation": "<p>Enables or disables default internet access for the fleet.</p>"}, "DomainJoinInfo": {"shape": "DomainJoinInfo", "documentation": "<p>The name of the directory and organizational unit (OU) to use to join the fleet to a Microsoft Active Directory domain. This is not allowed for Elastic fleets. </p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags to associate with the fleet. A tag is a key-value pair, and the value is optional. For example, Environment=Test. If you do not specify a value, Environment=. </p> <p>If you do not specify a value, the value is set to an empty string.</p> <p>Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following special characters: </p> <p>_ . : / = + \\ - @</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/tagging-basic.html\">Tagging Your Resources</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "IdleDisconnectTimeoutInSeconds": {"shape": "Integer", "documentation": "<p>The amount of time that users can be idle (inactive) before they are disconnected from their streaming session and the <code>DisconnectTimeoutInSeconds</code> time interval begins. Users are notified before they are disconnected due to inactivity. If they try to reconnect to the streaming session before the time interval specified in <code>DisconnectTimeoutInSeconds</code> elapses, they are connected to their previous session. Users are considered idle when they stop providing keyboard or mouse input during their streaming session. File uploads and downloads, audio in, audio out, and pixels changing do not qualify as user activity. If users continue to be idle after the time interval in <code>IdleDisconnectTimeoutInSeconds</code> elapses, they are disconnected.</p> <p>To prevent users from being disconnected due to inactivity, specify a value of 0. Otherwise, specify a value between 60 and 3600. The default value is 0.</p> <note> <p>If you enable this feature, we recommend that you specify a value that corresponds exactly to a whole number of minutes (for example, 60, 120, and 180). If you don't do this, the value is rounded to the nearest minute. For example, if you specify a value of 70, users are disconnected after 1 minute of inactivity. If you specify a value that is at the midpoint between two different minutes, the value is rounded up. For example, if you specify a value of 90, users are disconnected after 2 minutes of inactivity. </p> </note>"}, "IamRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role to apply to the fleet. To assume a role, a fleet instance calls the AWS Security Token Service (STS) <code>AssumeRole</code> API operation and passes the ARN of the role to use. The operation creates a new session with temporary credentials. AppStream 2.0 retrieves the temporary credentials and creates the <b>appstream_machine_role</b> credential profile on the instance.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/using-iam-roles-to-grant-permissions-to-applications-scripts-streaming-instances.html\">Using an IAM Role to Grant Permissions to Applications and Scripts Running on AppStream 2.0 Streaming Instances</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "StreamView": {"shape": "StreamView", "documentation": "<p>The AppStream 2.0 view that is displayed to your users when they stream from the fleet. When <code>APP</code> is specified, only the windows of applications opened by users display. When <code>DESKTOP</code> is specified, the standard desktop that is provided by the operating system displays.</p> <p>The default value is <code>APP</code>.</p>"}, "Platform": {"shape": "PlatformType", "documentation": "<p>The fleet platform. WINDOWS_SERVER_2019 and AMAZON_LINUX2 are supported for Elastic fleets. </p>"}, "MaxConcurrentSessions": {"shape": "Integer", "documentation": "<p>The maximum concurrent sessions of the Elastic fleet. This is required for Elastic fleets, and not allowed for other fleet types.</p>"}, "UsbDeviceFilterStrings": {"shape": "UsbDeviceFilterStrings", "documentation": "<p>The USB device filter strings that specify which USB devices a user can redirect to the fleet streaming session, when using the Windows native client. This is allowed but not required for Elastic fleets.</p>"}, "SessionScriptS3Location": {"shape": "S3Location", "documentation": "<p>The S3 location of the session scripts configuration zip file. This only applies to Elastic fleets.</p>"}}}, "CreateFleetResult": {"type": "structure", "members": {"Fleet": {"shape": "Fleet", "documentation": "<p>Information about the fleet.</p>"}}}, "CreateImageBuilderRequest": {"type": "structure", "required": ["Name", "InstanceType"], "members": {"Name": {"shape": "Name", "documentation": "<p>A unique name for the image builder.</p>"}, "ImageName": {"shape": "String", "documentation": "<p>The name of the image used to create the image builder.</p>"}, "ImageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the public, private, or shared image to use.</p>"}, "InstanceType": {"shape": "String", "documentation": "<p>The instance type to use when launching the image builder. The following instance types are available:</p> <ul> <li> <p>stream.standard.small</p> </li> <li> <p>stream.standard.medium</p> </li> <li> <p>stream.standard.large</p> </li> <li> <p>stream.compute.large</p> </li> <li> <p>stream.compute.xlarge</p> </li> <li> <p>stream.compute.2xlarge</p> </li> <li> <p>stream.compute.4xlarge</p> </li> <li> <p>stream.compute.8xlarge</p> </li> <li> <p>stream.memory.large</p> </li> <li> <p>stream.memory.xlarge</p> </li> <li> <p>stream.memory.2xlarge</p> </li> <li> <p>stream.memory.4xlarge</p> </li> <li> <p>stream.memory.8xlarge</p> </li> <li> <p>stream.memory.z1d.large</p> </li> <li> <p>stream.memory.z1d.xlarge</p> </li> <li> <p>stream.memory.z1d.2xlarge</p> </li> <li> <p>stream.memory.z1d.3xlarge</p> </li> <li> <p>stream.memory.z1d.6xlarge</p> </li> <li> <p>stream.memory.z1d.12xlarge</p> </li> <li> <p>stream.graphics-design.large</p> </li> <li> <p>stream.graphics-design.xlarge</p> </li> <li> <p>stream.graphics-design.2xlarge</p> </li> <li> <p>stream.graphics-design.4xlarge</p> </li> <li> <p>stream.graphics-desktop.2xlarge</p> </li> <li> <p>stream.graphics.g4dn.xlarge</p> </li> <li> <p>stream.graphics.g4dn.2xlarge</p> </li> <li> <p>stream.graphics.g4dn.4xlarge</p> </li> <li> <p>stream.graphics.g4dn.8xlarge</p> </li> <li> <p>stream.graphics.g4dn.12xlarge</p> </li> <li> <p>stream.graphics.g4dn.16xlarge</p> </li> <li> <p>stream.graphics-pro.4xlarge</p> </li> <li> <p>stream.graphics-pro.8xlarge</p> </li> <li> <p>stream.graphics-pro.16xlarge</p> </li> </ul>"}, "Description": {"shape": "Description", "documentation": "<p>The description to display.</p>"}, "DisplayName": {"shape": "DisplayName", "documentation": "<p>The image builder name to display.</p>"}, "VpcConfig": {"shape": "VpcConfig", "documentation": "<p>The VPC configuration for the image builder. You can specify only one subnet.</p>"}, "IamRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role to apply to the image builder. To assume a role, the image builder calls the AWS Security Token Service (STS) <code>AssumeRole</code> API operation and passes the ARN of the role to use. The operation creates a new session with temporary credentials. AppStream 2.0 retrieves the temporary credentials and creates the <b>appstream_machine_role</b> credential profile on the instance.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/using-iam-roles-to-grant-permissions-to-applications-scripts-streaming-instances.html\">Using an IAM Role to Grant Permissions to Applications and Scripts Running on AppStream 2.0 Streaming Instances</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "EnableDefaultInternetAccess": {"shape": "BooleanObject", "documentation": "<p>Enables or disables default internet access for the image builder.</p>"}, "DomainJoinInfo": {"shape": "DomainJoinInfo", "documentation": "<p>The name of the directory and organizational unit (OU) to use to join the image builder to a Microsoft Active Directory domain. </p>"}, "AppstreamAgentVersion": {"shape": "AppstreamAgentVersion", "documentation": "<p>The version of the AppStream 2.0 agent to use for this image builder. To use the latest version of the AppStream 2.0 agent, specify [LATEST]. </p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags to associate with the image builder. A tag is a key-value pair, and the value is optional. For example, Environment=Test. If you do not specify a value, Environment=. </p> <p>Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following special characters: </p> <p>_ . : / = + \\ - @</p> <p>If you do not specify a value, the value is set to an empty string.</p> <p>For more information about tags, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/tagging-basic.html\">Tagging Your Resources</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "AccessEndpoints": {"shape": "AccessEndpointList", "documentation": "<p>The list of interface VPC endpoint (interface endpoint) objects. Administrators can connect to the image builder only through the specified endpoints.</p>"}}}, "CreateImageBuilderResult": {"type": "structure", "members": {"ImageBuilder": {"shape": "ImageBuilder", "documentation": "<p>Information about the image builder.</p>"}}}, "CreateImageBuilderStreamingURLRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the image builder.</p>"}, "Validity": {"shape": "<PERSON>", "documentation": "<p>The time that the streaming URL will be valid, in seconds. Specify a value between 1 and 604800 seconds. The default is 3600 seconds.</p>"}}}, "CreateImageBuilderStreamingURLResult": {"type": "structure", "members": {"StreamingURL": {"shape": "String", "documentation": "<p>The URL to start the AppStream 2.0 streaming session.</p>"}, "Expires": {"shape": "Timestamp", "documentation": "<p>The elapsed time, in seconds after the Unix epoch, when this URL expires.</p>"}}}, "CreateStackRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the stack.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description to display.</p>"}, "DisplayName": {"shape": "DisplayName", "documentation": "<p>The stack name to display.</p>"}, "StorageConnectors": {"shape": "StorageConnectorList", "documentation": "<p>The storage connectors to enable.</p>"}, "RedirectURL": {"shape": "RedirectURL", "documentation": "<p>The URL that users are redirected to after their streaming session ends.</p>"}, "FeedbackURL": {"shape": "FeedbackURL", "documentation": "<p>The URL that users are redirected to after they click the Send Feedback link. If no URL is specified, no Send Feedback link is displayed.</p>"}, "UserSettings": {"shape": "UserSettingList", "documentation": "<p>The actions that are enabled or disabled for users during their streaming sessions. By default, these actions are enabled. </p>"}, "ApplicationSettings": {"shape": "ApplicationSettings", "documentation": "<p>The persistent application settings for users of a stack. When these settings are enabled, changes that users make to applications and Windows settings are automatically saved after each session and applied to the next session.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags to associate with the stack. A tag is a key-value pair, and the value is optional. For example, Environment=Test. If you do not specify a value, Environment=. </p> <p>If you do not specify a value, the value is set to an empty string.</p> <p>Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following special characters: </p> <p>_ . : / = + \\ - @</p> <p>For more information about tags, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/tagging-basic.html\">Tagging Your Resources</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "AccessEndpoints": {"shape": "AccessEndpointList", "documentation": "<p>The list of interface VPC endpoint (interface endpoint) objects. Users of the stack can connect to AppStream 2.0 only through the specified endpoints.</p>"}, "EmbedHostDomains": {"shape": "EmbedHostDomains", "documentation": "<p>The domains where AppStream 2.0 streaming sessions can be embedded in an iframe. You must approve the domains that you want to host embedded AppStream 2.0 streaming sessions. </p>"}, "StreamingExperienceSettings": {"shape": "StreamingExperienceSettings", "documentation": "<p>The streaming protocol you want your stack to prefer. This can be UDP or TCP. Currently, UDP is only supported in the Windows native client.</p>"}}}, "CreateStackResult": {"type": "structure", "members": {"Stack": {"shape": "<PERSON><PERSON>", "documentation": "<p>Information about the stack.</p>"}}}, "CreateStreamingURLRequest": {"type": "structure", "required": ["StackName", "FleetName", "UserId"], "members": {"StackName": {"shape": "String", "documentation": "<p>The name of the stack.</p>"}, "FleetName": {"shape": "String", "documentation": "<p>The name of the fleet.</p>"}, "UserId": {"shape": "StreamingUrlUserId", "documentation": "<p>The identifier of the user.</p>"}, "ApplicationId": {"shape": "String", "documentation": "<p>The name of the application to launch after the session starts. This is the name that you specified as <b>Name</b> in the Image Assistant. If your fleet is enabled for the <b>Desktop</b> stream view, you can also choose to launch directly to the operating system desktop. To do so, specify <b>Desktop</b>.</p>"}, "Validity": {"shape": "<PERSON>", "documentation": "<p>The time that the streaming URL will be valid, in seconds. Specify a value between 1 and 604800 seconds. The default is 60 seconds.</p>"}, "SessionContext": {"shape": "String", "documentation": "<p>The session context. For more information, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/managing-stacks-fleets.html#managing-stacks-fleets-parameters\">Session Context</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}}}, "CreateStreamingURLResult": {"type": "structure", "members": {"StreamingURL": {"shape": "String", "documentation": "<p>The URL to start the AppStream 2.0 streaming session.</p>"}, "Expires": {"shape": "Timestamp", "documentation": "<p>The elapsed time, in seconds after the Unix epoch, when this URL expires.</p>"}}}, "CreateUpdatedImageRequest": {"type": "structure", "required": ["existingImageName", "newImageName"], "members": {"existingImageName": {"shape": "Name", "documentation": "<p>The name of the image to update.</p>"}, "newImageName": {"shape": "Name", "documentation": "<p>The name of the new image. The name must be unique within the AWS account and Region.</p>"}, "newImageDescription": {"shape": "Description", "documentation": "<p>The description to display for the new image.</p>"}, "newImageDisplayName": {"shape": "DisplayName", "documentation": "<p>The name to display for the new image.</p>"}, "newImageTags": {"shape": "Tags", "documentation": "<p>The tags to associate with the new image. A tag is a key-value pair, and the value is optional. For example, Environment=Test. If you do not specify a value, Environment=. </p> <p>Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following special characters: </p> <p>_ . : / = + \\ - @</p> <p>If you do not specify a value, the value is set to an empty string.</p> <p>For more information about tags, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/tagging-basic.html\">Tagging Your Resources</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "dryRun": {"shape": "Boolean", "documentation": "<p>Indicates whether to display the status of image update availability before AppStream 2.0 initiates the process of creating a new updated image. If this value is set to <code>true</code>, AppStream 2.0 displays whether image updates are available. If this value is set to <code>false</code>, AppStream 2.0 initiates the process of creating a new updated image without displaying whether image updates are available.</p>"}}}, "CreateUpdatedImageResult": {"type": "structure", "members": {"image": {"shape": "Image"}, "canUpdateImage": {"shape": "Boolean", "documentation": "<p>Indicates whether a new image can be created.</p>"}}}, "CreateUsageReportSubscriptionRequest": {"type": "structure", "members": {}}, "CreateUsageReportSubscriptionResult": {"type": "structure", "members": {"S3BucketName": {"shape": "String", "documentation": "<p>The Amazon S3 bucket where generated reports are stored.</p> <p>If you enabled on-instance session scripts and Amazon S3 logging for your session script configuration, AppStream 2.0 created an S3 bucket to store the script output. The bucket is unique to your account and Region. When you enable usage reporting in this case, AppStream 2.0 uses the same bucket to store your usage reports. If you haven't already enabled on-instance session scripts, when you enable usage reports, AppStream 2.0 creates a new S3 bucket.</p>"}, "Schedule": {"shape": "UsageReportSchedule", "documentation": "<p>The schedule for generating usage reports.</p>"}}}, "CreateUserRequest": {"type": "structure", "required": ["UserName", "AuthenticationType"], "members": {"UserName": {"shape": "Username", "documentation": "<p>The email address of the user.</p> <note> <p>Users' email addresses are case-sensitive. During login, if they specify an email address that doesn't use the same capitalization as the email address specified when their user pool account was created, a \"user does not exist\" error message displays.</p> </note>"}, "MessageAction": {"shape": "MessageAction", "documentation": "<p>The action to take for the welcome email that is sent to a user after the user is created in the user pool. If you specify SUPPRESS, no email is sent. If you specify RESEND, do not specify the first name or last name of the user. If the value is null, the email is sent. </p> <note> <p>The temporary password in the welcome email is valid for only 7 days. If users don’t set their passwords within 7 days, you must send them a new welcome email.</p> </note>"}, "FirstName": {"shape": "UserAttributeValue", "documentation": "<p>The first name, or given name, of the user.</p>"}, "LastName": {"shape": "UserAttributeValue", "documentation": "<p>The last name, or surname, of the user.</p>"}, "AuthenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type for the user. You must specify USERPOOL. </p>"}}}, "CreateUserResult": {"type": "structure", "members": {}}, "DeleteAppBlockRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the app block.</p>"}}}, "DeleteAppBlockResult": {"type": "structure", "members": {}}, "DeleteApplicationRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the application.</p>"}}}, "DeleteApplicationResult": {"type": "structure", "members": {}}, "DeleteDirectoryConfigRequest": {"type": "structure", "required": ["DirectoryName"], "members": {"DirectoryName": {"shape": "DirectoryName", "documentation": "<p>The name of the directory configuration.</p>"}}}, "DeleteDirectoryConfigResult": {"type": "structure", "members": {}}, "DeleteEntitlementRequest": {"type": "structure", "required": ["Name", "StackName"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the entitlement.</p>"}, "StackName": {"shape": "Name", "documentation": "<p>The name of the stack with which the entitlement is associated.</p>"}}}, "DeleteEntitlementResult": {"type": "structure", "members": {}}, "DeleteFleetRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the fleet.</p>"}}}, "DeleteFleetResult": {"type": "structure", "members": {}}, "DeleteImageBuilderRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the image builder.</p>"}}}, "DeleteImageBuilderResult": {"type": "structure", "members": {"ImageBuilder": {"shape": "ImageBuilder", "documentation": "<p>Information about the image builder.</p>"}}}, "DeleteImagePermissionsRequest": {"type": "structure", "required": ["Name", "SharedAccountId"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the private image.</p>"}, "SharedAccountId": {"shape": "AwsAccountId", "documentation": "<p>The 12-digit identifier of the AWS account for which to delete image permissions.</p>"}}}, "DeleteImagePermissionsResult": {"type": "structure", "members": {}}, "DeleteImageRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the image.</p>"}}}, "DeleteImageResult": {"type": "structure", "members": {"Image": {"shape": "Image", "documentation": "<p>Information about the image.</p>"}}}, "DeleteStackRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the stack.</p>"}}}, "DeleteStackResult": {"type": "structure", "members": {}}, "DeleteUsageReportSubscriptionRequest": {"type": "structure", "members": {}}, "DeleteUsageReportSubscriptionResult": {"type": "structure", "members": {}}, "DeleteUserRequest": {"type": "structure", "required": ["UserName", "AuthenticationType"], "members": {"UserName": {"shape": "Username", "documentation": "<p>The email address of the user.</p> <note> <p>Users' email addresses are case-sensitive.</p> </note>"}, "AuthenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type for the user. You must specify USERPOOL.</p>"}}}, "DeleteUserResult": {"type": "structure", "members": {}}, "DescribeAppBlocksRequest": {"type": "structure", "members": {"Arns": {"shape": "ArnList", "documentation": "<p>The ARNs of the app blocks.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}, "MaxResults": {"shape": "Integer", "documentation": "<p>The maximum size of each page of results.</p>"}}}, "DescribeAppBlocksResult": {"type": "structure", "members": {"AppBlocks": {"shape": "AppBlocks", "documentation": "<p>The app blocks in the list.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}}}, "DescribeApplicationFleetAssociationsRequest": {"type": "structure", "members": {"FleetName": {"shape": "Name", "documentation": "<p>The name of the fleet.</p>"}, "ApplicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the application.</p>"}, "MaxResults": {"shape": "Integer", "documentation": "<p>The maximum size of each page of results.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}}}, "DescribeApplicationFleetAssociationsResult": {"type": "structure", "members": {"ApplicationFleetAssociations": {"shape": "ApplicationFleetAssociationList", "documentation": "<p>The application fleet associations in the list.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}}}, "DescribeApplicationsRequest": {"type": "structure", "members": {"Arns": {"shape": "ArnList", "documentation": "<p>The ARNs for the applications.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}, "MaxResults": {"shape": "Integer", "documentation": "<p>The maximum size of each page of results.</p>"}}}, "DescribeApplicationsResult": {"type": "structure", "members": {"Applications": {"shape": "Applications", "documentation": "<p>The applications in the list.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}}}, "DescribeDirectoryConfigsRequest": {"type": "structure", "members": {"DirectoryNames": {"shape": "DirectoryNameList", "documentation": "<p>The directory names.</p>"}, "MaxResults": {"shape": "Integer", "documentation": "<p>The maximum size of each page of results.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If this value is null, it retrieves the first page.</p>"}}}, "DescribeDirectoryConfigsResult": {"type": "structure", "members": {"DirectoryConfigs": {"shape": "DirectoryConfigList", "documentation": "<p>Information about the directory configurations. Note that although the response syntax in this topic includes the account password, this password is not returned in the actual response. </p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If there are no more pages, this value is null.</p>"}}}, "DescribeEntitlementsRequest": {"type": "structure", "required": ["StackName"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the entitlement.</p>"}, "StackName": {"shape": "Name", "documentation": "<p>The name of the stack with which the entitlement is associated.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}, "MaxResults": {"shape": "Integer", "documentation": "<p>The maximum size of each page of results.</p>"}}}, "DescribeEntitlementsResult": {"type": "structure", "members": {"Entitlements": {"shape": "EntitlementList", "documentation": "<p>The entitlements.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}}}, "DescribeFleetsRequest": {"type": "structure", "members": {"Names": {"shape": "StringList", "documentation": "<p>The names of the fleets to describe.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If this value is null, it retrieves the first page.</p>"}}}, "DescribeFleetsResult": {"type": "structure", "members": {"Fleets": {"shape": "FleetList", "documentation": "<p>Information about the fleets.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If there are no more pages, this value is null.</p>"}}}, "DescribeImageBuildersRequest": {"type": "structure", "members": {"Names": {"shape": "StringList", "documentation": "<p>The names of the image builders to describe.</p>"}, "MaxResults": {"shape": "Integer", "documentation": "<p>The maximum size of each page of results.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If this value is null, it retrieves the first page.</p>"}}}, "DescribeImageBuildersResult": {"type": "structure", "members": {"ImageBuilders": {"shape": "ImageBuilderList", "documentation": "<p>Information about the image builders.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If there are no more pages, this value is null.</p>"}}}, "DescribeImagePermissionsRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the private image for which to describe permissions. The image must be one that you own. </p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of each page of results.</p>"}, "SharedAwsAccountIds": {"shape": "AwsAccountIdList", "documentation": "<p>The 12-digit identifier of one or more AWS accounts with which the image is shared.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If this value is null, it retrieves the first page.</p>"}}}, "DescribeImagePermissionsResult": {"type": "structure", "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the private image.</p>"}, "SharedImagePermissionsList": {"shape": "SharedImagePermissionsList", "documentation": "<p>The permissions for a private image that you own. </p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If there are no more pages, this value is null.</p>"}}}, "DescribeImagesMaxResults": {"type": "integer", "box": true, "max": 25, "min": 0}, "DescribeImagesRequest": {"type": "structure", "members": {"Names": {"shape": "StringList", "documentation": "<p>The names of the public or private images to describe.</p>"}, "Arns": {"shape": "ArnList", "documentation": "<p>The ARNs of the public, private, and shared images to describe.</p>"}, "Type": {"shape": "VisibilityType", "documentation": "<p>The type of image (public, private, or shared) to describe. </p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If this value is null, it retrieves the first page.</p>"}, "MaxResults": {"shape": "DescribeImagesMaxResults", "documentation": "<p>The maximum size of each page of results.</p>"}}}, "DescribeImagesResult": {"type": "structure", "members": {"Images": {"shape": "ImageList", "documentation": "<p>Information about the images.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If there are no more pages, this value is null.</p>"}}}, "DescribeSessionsRequest": {"type": "structure", "required": ["StackName", "FleetName"], "members": {"StackName": {"shape": "String", "documentation": "<p>The name of the stack. This value is case-sensitive.</p>"}, "FleetName": {"shape": "String", "documentation": "<p>The name of the fleet. This value is case-sensitive.</p>"}, "UserId": {"shape": "UserId", "documentation": "<p>The user identifier (ID). If you specify a user ID, you must also specify the authentication type.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If this value is null, it retrieves the first page.</p>"}, "Limit": {"shape": "Integer", "documentation": "<p>The size of each page of results. The default value is 20 and the maximum value is 50.</p>"}, "AuthenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication method. Specify <code>API</code> for a user authenticated using a streaming URL or <code>SAML</code> for a SAML federated user. The default is to authenticate users using a streaming URL.</p>"}}}, "DescribeSessionsResult": {"type": "structure", "members": {"Sessions": {"shape": "SessionList", "documentation": "<p>Information about the streaming sessions.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If there are no more pages, this value is null.</p>"}}}, "DescribeStacksRequest": {"type": "structure", "members": {"Names": {"shape": "StringList", "documentation": "<p>The names of the stacks to describe.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If this value is null, it retrieves the first page.</p>"}}}, "DescribeStacksResult": {"type": "structure", "members": {"Stacks": {"shape": "StackList", "documentation": "<p>Information about the stacks.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If there are no more pages, this value is null.</p>"}}}, "DescribeUsageReportSubscriptionsRequest": {"type": "structure", "members": {"MaxResults": {"shape": "Integer", "documentation": "<p>The maximum size of each page of results.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If this value is null, it retrieves the first page.</p>"}}}, "DescribeUsageReportSubscriptionsResult": {"type": "structure", "members": {"UsageReportSubscriptions": {"shape": "UsageReportSubscriptionList", "documentation": "<p>Information about the usage report subscription.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If there are no more pages, this value is null.</p>"}}}, "DescribeUserStackAssociationsRequest": {"type": "structure", "members": {"StackName": {"shape": "String", "documentation": "<p>The name of the stack that is associated with the user.</p>"}, "UserName": {"shape": "Username", "documentation": "<p>The email address of the user who is associated with the stack.</p> <note> <p>Users' email addresses are case-sensitive.</p> </note>"}, "AuthenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type for the user who is associated with the stack. You must specify USERPOOL.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>The maximum size of each page of results.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If this value is null, it retrieves the first page.</p>"}}}, "DescribeUserStackAssociationsResult": {"type": "structure", "members": {"UserStackAssociations": {"shape": "UserStackAssociationList", "documentation": "<p>The UserStackAssociation objects.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If there are no more pages, this value is null.</p>"}}}, "DescribeUsersRequest": {"type": "structure", "required": ["AuthenticationType"], "members": {"AuthenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type for the users in the user pool to describe. You must specify USERPOOL.</p>"}, "MaxResults": {"shape": "Integer", "documentation": "<p>The maximum size of each page of results.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If this value is null, it retrieves the first page.</p>"}}}, "DescribeUsersResult": {"type": "structure", "members": {"Users": {"shape": "UserList", "documentation": "<p>Information about users in the user pool.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If there are no more pages, this value is null.</p>"}}}, "Description": {"type": "string", "max": 256}, "DirectoryConfig": {"type": "structure", "required": ["DirectoryName"], "members": {"DirectoryName": {"shape": "DirectoryName", "documentation": "<p>The fully qualified name of the directory (for example, corp.example.com).</p>"}, "OrganizationalUnitDistinguishedNames": {"shape": "OrganizationalUnitDistinguishedNamesList", "documentation": "<p>The distinguished names of the organizational units for computer accounts.</p>"}, "ServiceAccountCredentials": {"shape": "ServiceAccountCredentials", "documentation": "<p>The credentials for the service account used by the fleet or image builder to connect to the directory.</p>"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The time the directory configuration was created.</p>"}, "CertificateBasedAuthProperties": {"shape": "CertificateBasedAuthProperties", "documentation": "<p>The certificate-based authentication properties used to authenticate SAML 2.0 Identity Provider (IdP) user identities to Active Directory domain-joined streaming instances. Fallback is turned on by default when certificate-based authentication is <b>Enabled</b> . Fallback allows users to log in using their AD domain password if certificate-based authentication is unsuccessful, or to unlock a desktop lock screen. <b>Enabled_no_directory_login_fallback</b> enables certificate-based authentication, but does not allow users to log in using their AD domain password. Users will be disconnected to re-authenticate using certificates.</p>"}}, "documentation": "<p>Describes the configuration information required to join fleets and image builders to Microsoft Active Directory domains.</p>"}, "DirectoryConfigList": {"type": "list", "member": {"shape": "DirectoryConfig"}}, "DirectoryName": {"type": "string"}, "DirectoryNameList": {"type": "list", "member": {"shape": "DirectoryName"}}, "DisableUserRequest": {"type": "structure", "required": ["UserName", "AuthenticationType"], "members": {"UserName": {"shape": "Username", "documentation": "<p>The email address of the user.</p> <note> <p>Users' email addresses are case-sensitive.</p> </note>"}, "AuthenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type for the user. You must specify USERPOOL.</p>"}}}, "DisableUserResult": {"type": "structure", "members": {}}, "DisassociateApplicationFleetRequest": {"type": "structure", "required": ["FleetName", "ApplicationArn"], "members": {"FleetName": {"shape": "Name", "documentation": "<p>The name of the fleet.</p>"}, "ApplicationArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the application.</p>"}}}, "DisassociateApplicationFleetResult": {"type": "structure", "members": {}}, "DisassociateApplicationFromEntitlementRequest": {"type": "structure", "required": ["StackName", "EntitlementName", "ApplicationIdentifier"], "members": {"StackName": {"shape": "Name", "documentation": "<p>The name of the stack with which the entitlement is associated.</p>"}, "EntitlementName": {"shape": "Name", "documentation": "<p>The name of the entitlement.</p>"}, "ApplicationIdentifier": {"shape": "String", "documentation": "<p>The identifier of the application to remove from the entitlement.</p>"}}}, "DisassociateApplicationFromEntitlementResult": {"type": "structure", "members": {}}, "DisassociateFleetRequest": {"type": "structure", "required": ["FleetName", "StackName"], "members": {"FleetName": {"shape": "String", "documentation": "<p>The name of the fleet.</p>"}, "StackName": {"shape": "String", "documentation": "<p>The name of the stack.</p>"}}}, "DisassociateFleetResult": {"type": "structure", "members": {}}, "DisplayName": {"type": "string", "max": 100}, "Domain": {"type": "string", "documentation": "GSuite domain for GDrive integration.", "max": 64, "min": 1}, "DomainJoinInfo": {"type": "structure", "members": {"DirectoryName": {"shape": "DirectoryName", "documentation": "<p>The fully qualified name of the directory (for example, corp.example.com).</p>"}, "OrganizationalUnitDistinguishedName": {"shape": "OrganizationalUnitDistinguishedName", "documentation": "<p>The distinguished name of the organizational unit for computer accounts.</p>"}}, "documentation": "<p>Describes the configuration information required to join fleets and image builders to Microsoft Active Directory domains.</p>"}, "DomainList": {"type": "list", "member": {"shape": "Domain"}, "max": 50}, "EmbedHostDomain": {"type": "string", "documentation": "Specifies a valid domain that can embed AppStream. Valid examples include: [\"testorigin.tt--com\", \"testingorigin.com.us\", \"test.com.us\"] Invalid examples include: [\"test,com\", \".com\", \"h*llo.com\". \"\"]", "max": 128, "pattern": "(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]"}, "EmbedHostDomains": {"type": "list", "member": {"shape": "EmbedHostDomain"}, "max": 20, "min": 1}, "EnableUserRequest": {"type": "structure", "required": ["UserName", "AuthenticationType"], "members": {"UserName": {"shape": "Username", "documentation": "<p>The email address of the user.</p> <note> <p>Users' email addresses are case-sensitive. During login, if they specify an email address that doesn't use the same capitalization as the email address specified when their user pool account was created, a \"user does not exist\" error message displays. </p> </note>"}, "AuthenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type for the user. You must specify USERPOOL.</p>"}}}, "EnableUserResult": {"type": "structure", "members": {}}, "EntitledApplication": {"type": "structure", "required": ["ApplicationIdentifier"], "members": {"ApplicationIdentifier": {"shape": "String", "documentation": "<p>The identifier of the application.</p>"}}, "documentation": "<p>The application associated to an entitlement. Access is controlled based on user attributes.</p>"}, "EntitledApplicationList": {"type": "list", "member": {"shape": "EntitledApplication"}}, "Entitlement": {"type": "structure", "required": ["Name", "StackName", "AppVisibility", "Attributes"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the entitlement.</p>"}, "StackName": {"shape": "Name", "documentation": "<p>The name of the stack with which the entitlement is associated.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the entitlement.</p>"}, "AppVisibility": {"shape": "AppVisibility", "documentation": "<p>Specifies whether all or selected apps are entitled.</p>"}, "Attributes": {"shape": "EntitlementAttributeList", "documentation": "<p>The attributes of the entitlement.</p>"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The time when the entitlement was created.</p>"}, "LastModifiedTime": {"shape": "Timestamp", "documentation": "<p>The time when the entitlement was last modified.</p>"}}, "documentation": "<p>Specifies an entitlement. Entitlements control access to specific applications within a stack, based on user attributes. Entitlements apply to SAML 2.0 federated user identities. Amazon AppStream 2.0 user pool and streaming URL users are entitled to all applications in a stack. Entitlements don't apply to the desktop stream view application, or to applications managed by a dynamic app provider using the Dynamic Application Framework.</p>"}, "EntitlementAlreadyExistsException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The entitlement already exists.</p>", "exception": true}, "EntitlementAttribute": {"type": "structure", "required": ["Name", "Value"], "members": {"Name": {"shape": "String", "documentation": "<p>A supported AWS IAM SAML <code>PrincipalTag</code> attribute that is matched to the associated value when a user identity federates into an Amazon AppStream 2.0 SAML application.</p> <p>The following are valid values:</p> <ul> <li> <p>roles</p> </li> <li> <p>department </p> </li> <li> <p>organization </p> </li> <li> <p>groups </p> </li> <li> <p>title </p> </li> <li> <p>costCenter </p> </li> <li> <p>userType</p> </li> </ul> <p> </p>"}, "Value": {"shape": "String", "documentation": "<p>A value that is matched to a supported SAML attribute name when a user identity federates into an Amazon AppStream 2.0 SAML application. </p>"}}, "documentation": "<p>An attribute associated with an entitlement. Application entitlements work by matching a supported SAML 2.0 attribute name to a value when a user identity federates to an Amazon AppStream 2.0 SAML application.</p>"}, "EntitlementAttributeList": {"type": "list", "member": {"shape": "EntitlementAttribute"}, "min": 1}, "EntitlementList": {"type": "list", "member": {"shape": "Entitlement"}}, "EntitlementNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The entitlement can't be found.</p>", "exception": true}, "ErrorMessage": {"type": "string", "documentation": "<p>The error message in the exception.</p>"}, "ExpireSessionRequest": {"type": "structure", "required": ["SessionId"], "members": {"SessionId": {"shape": "String", "documentation": "<p>The identifier of the streaming session.</p>"}}}, "ExpireSessionResult": {"type": "structure", "members": {}}, "FeedbackURL": {"type": "string", "max": 1000}, "Fleet": {"type": "structure", "required": ["<PERSON><PERSON>", "Name", "InstanceType", "ComputeCapacityStatus", "State"], "members": {"Arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) for the fleet.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the fleet.</p>"}, "DisplayName": {"shape": "String", "documentation": "<p>The fleet name to display.</p>"}, "Description": {"shape": "String", "documentation": "<p>The description to display.</p>"}, "ImageName": {"shape": "String", "documentation": "<p>The name of the image used to create the fleet.</p>"}, "ImageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN for the public, private, or shared image.</p>"}, "InstanceType": {"shape": "String", "documentation": "<p>The instance type to use when launching fleet instances. The following instance types are available:</p> <ul> <li> <p>stream.standard.small</p> </li> <li> <p>stream.standard.medium</p> </li> <li> <p>stream.standard.large</p> </li> <li> <p>stream.compute.large</p> </li> <li> <p>stream.compute.xlarge</p> </li> <li> <p>stream.compute.2xlarge</p> </li> <li> <p>stream.compute.4xlarge</p> </li> <li> <p>stream.compute.8xlarge</p> </li> <li> <p>stream.memory.large</p> </li> <li> <p>stream.memory.xlarge</p> </li> <li> <p>stream.memory.2xlarge</p> </li> <li> <p>stream.memory.4xlarge</p> </li> <li> <p>stream.memory.8xlarge</p> </li> <li> <p>stream.memory.z1d.large</p> </li> <li> <p>stream.memory.z1d.xlarge</p> </li> <li> <p>stream.memory.z1d.2xlarge</p> </li> <li> <p>stream.memory.z1d.3xlarge</p> </li> <li> <p>stream.memory.z1d.6xlarge</p> </li> <li> <p>stream.memory.z1d.12xlarge</p> </li> <li> <p>stream.graphics-design.large</p> </li> <li> <p>stream.graphics-design.xlarge</p> </li> <li> <p>stream.graphics-design.2xlarge</p> </li> <li> <p>stream.graphics-design.4xlarge</p> </li> <li> <p>stream.graphics-desktop.2xlarge</p> </li> <li> <p>stream.graphics.g4dn.xlarge</p> </li> <li> <p>stream.graphics.g4dn.2xlarge</p> </li> <li> <p>stream.graphics.g4dn.4xlarge</p> </li> <li> <p>stream.graphics.g4dn.8xlarge</p> </li> <li> <p>stream.graphics.g4dn.12xlarge</p> </li> <li> <p>stream.graphics.g4dn.16xlarge</p> </li> <li> <p>stream.graphics-pro.4xlarge</p> </li> <li> <p>stream.graphics-pro.8xlarge</p> </li> <li> <p>stream.graphics-pro.16xlarge</p> </li> </ul>"}, "FleetType": {"shape": "FleetType", "documentation": "<p>The fleet type.</p> <dl> <dt>ALWAYS_ON</dt> <dd> <p>Provides users with instant-on access to their apps. You are charged for all running instances in your fleet, even if no users are streaming apps.</p> </dd> <dt>ON_DEMAND</dt> <dd> <p>Provide users with access to applications after they connect, which takes one to two minutes. You are charged for instance streaming when users are connected and a small hourly fee for instances that are not streaming apps.</p> </dd> </dl>"}, "ComputeCapacityStatus": {"shape": "ComputeCapacityStatus", "documentation": "<p>The capacity status for the fleet.</p>"}, "MaxUserDurationInSeconds": {"shape": "Integer", "documentation": "<p>The maximum amount of time that a streaming session can remain active, in seconds. If users are still connected to a streaming instance five minutes before this limit is reached, they are prompted to save any open documents before being disconnected. After this time elapses, the instance is terminated and replaced by a new instance. </p> <p>Specify a value between 600 and 360000.</p>"}, "DisconnectTimeoutInSeconds": {"shape": "Integer", "documentation": "<p>The amount of time that a streaming session remains active after users disconnect. If they try to reconnect to the streaming session after a disconnection or network interruption within this time interval, they are connected to their previous session. Otherwise, they are connected to a new session with a new streaming instance.</p> <p>Specify a value between 60 and 360000.</p>"}, "State": {"shape": "FleetState", "documentation": "<p>The current state for the fleet.</p>"}, "VpcConfig": {"shape": "VpcConfig", "documentation": "<p>The VPC configuration for the fleet.</p>"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The time the fleet was created.</p>"}, "FleetErrors": {"shape": "FleetErrors", "documentation": "<p>The fleet errors.</p>"}, "EnableDefaultInternetAccess": {"shape": "BooleanObject", "documentation": "<p>Indicates whether default internet access is enabled for the fleet.</p>"}, "DomainJoinInfo": {"shape": "DomainJoinInfo", "documentation": "<p>The name of the directory and organizational unit (OU) to use to join the fleet to a Microsoft Active Directory domain. </p>"}, "IdleDisconnectTimeoutInSeconds": {"shape": "Integer", "documentation": "<p>The amount of time that users can be idle (inactive) before they are disconnected from their streaming session and the <code>DisconnectTimeoutInSeconds</code> time interval begins. Users are notified before they are disconnected due to inactivity. If users try to reconnect to the streaming session before the time interval specified in <code>DisconnectTimeoutInSeconds</code> elapses, they are connected to their previous session. Users are considered idle when they stop providing keyboard or mouse input during their streaming session. File uploads and downloads, audio in, audio out, and pixels changing do not qualify as user activity. If users continue to be idle after the time interval in <code>IdleDisconnectTimeoutInSeconds</code> elapses, they are disconnected.</p> <p>To prevent users from being disconnected due to inactivity, specify a value of 0. Otherwise, specify a value between 60 and 3600. The default value is 0.</p> <note> <p>If you enable this feature, we recommend that you specify a value that corresponds exactly to a whole number of minutes (for example, 60, 120, and 180). If you don't do this, the value is rounded to the nearest minute. For example, if you specify a value of 70, users are disconnected after 1 minute of inactivity. If you specify a value that is at the midpoint between two different minutes, the value is rounded up. For example, if you specify a value of 90, users are disconnected after 2 minutes of inactivity. </p> </note>"}, "IamRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the IAM role that is applied to the fleet. To assume a role, the fleet instance calls the AWS Security Token Service (STS) <code>AssumeRole</code> API operation and passes the ARN of the role to use. The operation creates a new session with temporary credentials. AppStream 2.0 retrieves the temporary credentials and creates the <b>appstream_machine_role</b> credential profile on the instance.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/using-iam-roles-to-grant-permissions-to-applications-scripts-streaming-instances.html\">Using an IAM Role to Grant Permissions to Applications and Scripts Running on AppStream 2.0 Streaming Instances</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "StreamView": {"shape": "StreamView", "documentation": "<p>The AppStream 2.0 view that is displayed to your users when they stream from the fleet. When <code>APP</code> is specified, only the windows of applications opened by users display. When <code>DESKTOP</code> is specified, the standard desktop that is provided by the operating system displays.</p> <p>The default value is <code>APP</code>.</p>"}, "Platform": {"shape": "PlatformType", "documentation": "<p>The platform of the fleet.</p>"}, "MaxConcurrentSessions": {"shape": "Integer", "documentation": "<p>The maximum number of concurrent sessions for the fleet.</p>"}, "UsbDeviceFilterStrings": {"shape": "UsbDeviceFilterStrings", "documentation": "<p>The USB device filter strings associated with the fleet.</p>"}, "SessionScriptS3Location": {"shape": "S3Location", "documentation": "<p>The S3 location of the session scripts configuration zip file. This only applies to Elastic fleets.</p>"}}, "documentation": "<p>Describes a fleet.</p>"}, "FleetAttribute": {"type": "string", "documentation": "<p>The fleet attribute.</p>", "enum": ["VPC_CONFIGURATION", "VPC_CONFIGURATION_SECURITY_GROUP_IDS", "DOMAIN_JOIN_INFO", "IAM_ROLE_ARN", "USB_DEVICE_FILTER_STRINGS", "SESSION_SCRIPT_S3_LOCATION"]}, "FleetAttributes": {"type": "list", "member": {"shape": "FleetAttribute"}, "documentation": "<p>The fleet attributes.</p>"}, "FleetError": {"type": "structure", "members": {"ErrorCode": {"shape": "FleetErrorCode", "documentation": "<p>The error code.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>The error message.</p>"}}, "documentation": "<p>Describes a fleet error.</p>"}, "FleetErrorCode": {"type": "string", "enum": ["IAM_SERVICE_ROLE_MISSING_ENI_DESCRIBE_ACTION", "IAM_SERVICE_ROLE_MISSING_ENI_CREATE_ACTION", "IAM_SERVICE_ROLE_MISSING_ENI_DELETE_ACTION", "NETWORK_INTERFACE_LIMIT_EXCEEDED", "INTERNAL_SERVICE_ERROR", "IAM_SERVICE_ROLE_IS_MISSING", "MACHINE_ROLE_IS_MISSING", "STS_DISABLED_IN_REGION", "SUBNET_HAS_INSUFFICIENT_IP_ADDRESSES", "IAM_SERVICE_ROLE_MISSING_DESCRIBE_SUBNET_ACTION", "SUBNET_NOT_FOUND", "IMAGE_NOT_FOUND", "INVALID_SUBNET_CONFIGURATION", "SECURITY_GROUPS_NOT_FOUND", "IGW_NOT_ATTACHED", "IAM_SERVICE_ROLE_MISSING_DESCRIBE_SECURITY_GROUPS_ACTION", "FLEET_STOPPED", "FLEET_INSTANCE_PROVISIONING_FAILURE", "DOMAIN_JOIN_ERROR_FILE_NOT_FOUND", "DOMAIN_JOIN_ERROR_ACCESS_DENIED", "DOMAIN_JOIN_ERROR_LOGON_FAILURE", "DOMAIN_JOIN_ERROR_INVALID_PARAMETER", "DOMAIN_JOIN_ERROR_MORE_DATA", "DOMAIN_JOIN_ERROR_NO_SUCH_DOMAIN", "DOMAIN_JOIN_ERROR_NOT_SUPPORTED", "DOMAIN_JOIN_NERR_INVALID_WORKGROUP_NAME", "DOMAIN_JOIN_NERR_WORKSTATION_NOT_STARTED", "DOMAIN_JOIN_ERROR_DS_MACHINE_ACCOUNT_QUOTA_EXCEEDED", "DOMAIN_JOIN_NERR_PASSWORD_EXPIRED", "DOMAIN_JOIN_INTERNAL_SERVICE_ERROR"]}, "FleetErrors": {"type": "list", "member": {"shape": "FleetError"}}, "FleetList": {"type": "list", "member": {"shape": "Fleet"}, "documentation": "<p>The fleets.</p>"}, "FleetState": {"type": "string", "enum": ["STARTING", "RUNNING", "STOPPING", "STOPPED"]}, "FleetType": {"type": "string", "enum": ["ALWAYS_ON", "ON_DEMAND", "ELASTIC"]}, "Image": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the image.</p>"}, "Arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the image.</p>"}, "BaseImageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the image from which this image was created.</p>"}, "DisplayName": {"shape": "String", "documentation": "<p>The image name to display.</p>"}, "State": {"shape": "ImageState", "documentation": "<p>The image starts in the <code>PENDING</code> state. If image creation succeeds, the state is <code>AVAILABLE</code>. If image creation fails, the state is <code>FAILED</code>.</p>"}, "Visibility": {"shape": "VisibilityType", "documentation": "<p>Indicates whether the image is public or private.</p>"}, "ImageBuilderSupported": {"shape": "Boolean", "documentation": "<p>Indicates whether an image builder can be launched from this image.</p>"}, "ImageBuilderName": {"shape": "String", "documentation": "<p>The name of the image builder that was used to create the private image. If the image is shared, this value is null.</p>"}, "Platform": {"shape": "PlatformType", "documentation": "<p>The operating system platform of the image.</p>"}, "Description": {"shape": "String", "documentation": "<p>The description to display.</p>"}, "StateChangeReason": {"shape": "ImageStateChangeReason", "documentation": "<p>The reason why the last state change occurred.</p>"}, "Applications": {"shape": "Applications", "documentation": "<p>The applications associated with the image.</p>"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The time the image was created.</p>"}, "PublicBaseImageReleasedDate": {"shape": "Timestamp", "documentation": "<p>The release date of the public base image. For private images, this date is the release date of the base image from which the image was created.</p>"}, "AppstreamAgentVersion": {"shape": "AppstreamAgentVersion", "documentation": "<p>The version of the AppStream 2.0 agent to use for instances that are launched from this image. </p>"}, "ImagePermissions": {"shape": "ImagePermissions", "documentation": "<p>The permissions to provide to the destination AWS account for the specified image.</p>"}, "ImageErrors": {"shape": "ResourceErrors", "documentation": "<p>Describes the errors that are returned when a new image can't be created.</p>"}}, "documentation": "<p>Describes an image.</p>"}, "ImageBuilder": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the image builder.</p>"}, "Arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN for the image builder.</p>"}, "ImageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the image from which this builder was created.</p>"}, "Description": {"shape": "String", "documentation": "<p>The description to display.</p>"}, "DisplayName": {"shape": "String", "documentation": "<p>The image builder name to display.</p>"}, "VpcConfig": {"shape": "VpcConfig", "documentation": "<p>The VPC configuration of the image builder.</p>"}, "InstanceType": {"shape": "String", "documentation": "<p>The instance type for the image builder. The following instance types are available:</p> <ul> <li> <p>stream.standard.small</p> </li> <li> <p>stream.standard.medium</p> </li> <li> <p>stream.standard.large</p> </li> <li> <p>stream.compute.large</p> </li> <li> <p>stream.compute.xlarge</p> </li> <li> <p>stream.compute.2xlarge</p> </li> <li> <p>stream.compute.4xlarge</p> </li> <li> <p>stream.compute.8xlarge</p> </li> <li> <p>stream.memory.large</p> </li> <li> <p>stream.memory.xlarge</p> </li> <li> <p>stream.memory.2xlarge</p> </li> <li> <p>stream.memory.4xlarge</p> </li> <li> <p>stream.memory.8xlarge</p> </li> <li> <p>stream.memory.z1d.large</p> </li> <li> <p>stream.memory.z1d.xlarge</p> </li> <li> <p>stream.memory.z1d.2xlarge</p> </li> <li> <p>stream.memory.z1d.3xlarge</p> </li> <li> <p>stream.memory.z1d.6xlarge</p> </li> <li> <p>stream.memory.z1d.12xlarge</p> </li> <li> <p>stream.graphics-design.large</p> </li> <li> <p>stream.graphics-design.xlarge</p> </li> <li> <p>stream.graphics-design.2xlarge</p> </li> <li> <p>stream.graphics-design.4xlarge</p> </li> <li> <p>stream.graphics-desktop.2xlarge</p> </li> <li> <p>stream.graphics.g4dn.xlarge</p> </li> <li> <p>stream.graphics.g4dn.2xlarge</p> </li> <li> <p>stream.graphics.g4dn.4xlarge</p> </li> <li> <p>stream.graphics.g4dn.8xlarge</p> </li> <li> <p>stream.graphics.g4dn.12xlarge</p> </li> <li> <p>stream.graphics.g4dn.16xlarge</p> </li> <li> <p>stream.graphics-pro.4xlarge</p> </li> <li> <p>stream.graphics-pro.8xlarge</p> </li> <li> <p>stream.graphics-pro.16xlarge</p> </li> </ul>"}, "Platform": {"shape": "PlatformType", "documentation": "<p>The operating system platform of the image builder.</p>"}, "IamRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the IAM role that is applied to the image builder. To assume a role, the image builder calls the AWS Security Token Service (STS) <code>AssumeRole</code> API operation and passes the ARN of the role to use. The operation creates a new session with temporary credentials. AppStream 2.0 retrieves the temporary credentials and creates the <b>appstream_machine_role</b> credential profile on the instance.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/using-iam-roles-to-grant-permissions-to-applications-scripts-streaming-instances.html\">Using an IAM Role to Grant Permissions to Applications and Scripts Running on AppStream 2.0 Streaming Instances</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "State": {"shape": "ImageBuilderState", "documentation": "<p>The state of the image builder.</p>"}, "StateChangeReason": {"shape": "ImageBuilderStateChangeReason", "documentation": "<p>The reason why the last state change occurred.</p>"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The time stamp when the image builder was created.</p>"}, "EnableDefaultInternetAccess": {"shape": "BooleanObject", "documentation": "<p>Enables or disables default internet access for the image builder.</p>"}, "DomainJoinInfo": {"shape": "DomainJoinInfo", "documentation": "<p>The name of the directory and organizational unit (OU) to use to join the image builder to a Microsoft Active Directory domain. </p>"}, "NetworkAccessConfiguration": {"shape": "NetworkAccessConfiguration"}, "ImageBuilderErrors": {"shape": "ResourceErrors", "documentation": "<p>The image builder errors.</p>"}, "AppstreamAgentVersion": {"shape": "AppstreamAgentVersion", "documentation": "<p>The version of the AppStream 2.0 agent that is currently being used by the image builder. </p>"}, "AccessEndpoints": {"shape": "AccessEndpointList", "documentation": "<p>The list of virtual private cloud (VPC) interface endpoint objects. Administrators can connect to the image builder only through the specified endpoints.</p>"}}, "documentation": "<p>Describes a virtual machine that is used to create an image. </p>"}, "ImageBuilderList": {"type": "list", "member": {"shape": "ImageBuilder"}}, "ImageBuilderState": {"type": "string", "enum": ["PENDING", "UPDATING_AGENT", "RUNNING", "STOPPING", "STOPPED", "REBOOTING", "SNAPSHOTTING", "DELETING", "FAILED", "UPDATING", "PENDING_QUALIFICATION"]}, "ImageBuilderStateChangeReason": {"type": "structure", "members": {"Code": {"shape": "ImageBuilderStateChangeReasonCode", "documentation": "<p>The state change reason code.</p>"}, "Message": {"shape": "String", "documentation": "<p>The state change reason message.</p>"}}, "documentation": "<p>Describes the reason why the last image builder state change occurred.</p>"}, "ImageBuilderStateChangeReasonCode": {"type": "string", "enum": ["INTERNAL_ERROR", "IMAGE_UNAVAILABLE"]}, "ImageList": {"type": "list", "member": {"shape": "Image"}}, "ImagePermissions": {"type": "structure", "members": {"allowFleet": {"shape": "BooleanObject", "documentation": "<p>Indicates whether the image can be used for a fleet.</p>"}, "allowImageBuilder": {"shape": "BooleanObject", "documentation": "<p>Indicates whether the image can be used for an image builder.</p>"}}, "documentation": "<p>Describes the permissions for an image. </p>"}, "ImageState": {"type": "string", "enum": ["PENDING", "AVAILABLE", "FAILED", "COPYING", "DELETING", "CREATING", "IMPORTING"]}, "ImageStateChangeReason": {"type": "structure", "members": {"Code": {"shape": "ImageStateChangeReasonCode", "documentation": "<p>The state change reason code.</p>"}, "Message": {"shape": "String", "documentation": "<p>The state change reason message.</p>"}}, "documentation": "<p>Describes the reason why the last image state change occurred.</p>"}, "ImageStateChangeReasonCode": {"type": "string", "enum": ["INTERNAL_ERROR", "IMAGE_BUILDER_NOT_AVAILABLE", "IMAGE_COPY_FAILURE"]}, "IncompatibleImageException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The image can't be updated because it's not compatible for updates.</p>", "exception": true}, "Integer": {"type": "integer"}, "InvalidAccountStatusException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The resource cannot be created because your AWS account is suspended. For assistance, contact AWS Support. </p>", "exception": true}, "InvalidParameterCombinationException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>Indicates an incorrect combination of parameters, or a missing parameter.</p>", "exception": true}, "InvalidRoleException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified role is invalid.</p>", "exception": true}, "LastReportGenerationExecutionError": {"type": "structure", "members": {"ErrorCode": {"shape": "UsageReportExecutionErrorCode", "documentation": "<p>The error code for the error that is returned when a usage report can't be generated.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>The error message for the error that is returned when a usage report can't be generated.</p>"}}, "documentation": "<p>Describes the error that is returned when a usage report can't be generated.</p>"}, "LastReportGenerationExecutionErrors": {"type": "list", "member": {"shape": "LastReportGenerationExecutionError"}}, "LimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The requested limit exceeds the permitted limit for an account.</p>", "exception": true}, "ListAssociatedFleetsRequest": {"type": "structure", "required": ["StackName"], "members": {"StackName": {"shape": "String", "documentation": "<p>The name of the stack.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If this value is null, it retrieves the first page.</p>"}}}, "ListAssociatedFleetsResult": {"type": "structure", "members": {"Names": {"shape": "StringList", "documentation": "<p>The name of the fleet.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If there are no more pages, this value is null.</p>"}}}, "ListAssociatedStacksRequest": {"type": "structure", "required": ["FleetName"], "members": {"FleetName": {"shape": "String", "documentation": "<p>The name of the fleet.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If this value is null, it retrieves the first page.</p>"}}}, "ListAssociatedStacksResult": {"type": "structure", "members": {"Names": {"shape": "StringList", "documentation": "<p>The name of the stack.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token to use to retrieve the next page of results for this operation. If there are no more pages, this value is null.</p>"}}}, "ListEntitledApplicationsRequest": {"type": "structure", "required": ["StackName", "EntitlementName"], "members": {"StackName": {"shape": "Name", "documentation": "<p>The name of the stack with which the entitlement is associated.</p>"}, "EntitlementName": {"shape": "Name", "documentation": "<p>The name of the entitlement.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}, "MaxResults": {"shape": "Integer", "documentation": "<p>The maximum size of each page of results.</p>"}}}, "ListEntitledApplicationsResult": {"type": "structure", "members": {"EntitledApplications": {"shape": "EntitledApplicationList", "documentation": "<p>The entitled applications.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The pagination token used to retrieve the next page of results for this operation.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "Tags", "documentation": "<p>The information about the tags.</p>"}}}, "Long": {"type": "long"}, "MaxResults": {"type": "integer", "box": true, "max": 500, "min": 0}, "MessageAction": {"type": "string", "enum": ["SUPPRESS", "RESEND"]}, "Metadata": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "Name": {"type": "string", "pattern": "^[a-zA-Z0-9][a-zA-Z0-9_.-]{0,100}$"}, "NetworkAccessConfiguration": {"type": "structure", "members": {"EniPrivateIpAddress": {"shape": "String", "documentation": "<p>The private IP address of the elastic network interface that is attached to instances in your VPC.</p>"}, "EniId": {"shape": "String", "documentation": "<p>The resource identifier of the elastic network interface that is attached to instances in your VPC. All network interfaces have the eni-xxxxxxxx resource identifier.</p>"}}, "documentation": "<p>Describes the network details of the fleet or image builder instance.</p>"}, "OperationNotPermittedException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The attempted operation is not permitted.</p>", "exception": true}, "OrganizationalUnitDistinguishedName": {"type": "string", "max": 2000}, "OrganizationalUnitDistinguishedNamesList": {"type": "list", "member": {"shape": "OrganizationalUnitDistinguishedName"}}, "Permission": {"type": "string", "enum": ["ENABLED", "DISABLED"]}, "PlatformType": {"type": "string", "enum": ["WINDOWS", "WINDOWS_SERVER_2016", "WINDOWS_SERVER_2019", "AMAZON_LINUX2"]}, "Platforms": {"type": "list", "member": {"shape": "PlatformType"}, "max": 4}, "PreferredProtocol": {"type": "string", "enum": ["TCP", "UDP"]}, "RedirectURL": {"type": "string", "max": 1000}, "RegionName": {"type": "string", "max": 32, "min": 1}, "RequestLimitExceededException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>AppStream 2.0 can’t process the request right now because the Describe calls from your AWS account are being throttled by Amazon EC2. Try again later.</p>", "exception": true}, "ResourceAlreadyExistsException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified resource already exists.</p>", "exception": true}, "ResourceError": {"type": "structure", "members": {"ErrorCode": {"shape": "FleetErrorCode", "documentation": "<p>The error code.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>The error message.</p>"}, "ErrorTimestamp": {"shape": "Timestamp", "documentation": "<p>The time the error occurred.</p>"}}, "documentation": "<p>Describes a resource error.</p>"}, "ResourceErrors": {"type": "list", "member": {"shape": "ResourceError"}}, "ResourceIdentifier": {"type": "string", "documentation": "<p>The ARN of the resource.</p>", "max": 2048, "min": 1}, "ResourceInUseException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified resource is in use.</p>", "exception": true}, "ResourceNotAvailableException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified resource exists and is not in use, but isn't available.</p>", "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "ErrorMessage"}}, "documentation": "<p>The specified resource was not found.</p>", "exception": true}, "S3Bucket": {"type": "string", "max": 63, "min": 3, "pattern": "^[0-9a-z\\.\\-]*(?<!\\.)$"}, "S3Key": {"type": "string", "max": 1024, "min": 1}, "S3Location": {"type": "structure", "required": ["S3Bucket", "S3Key"], "members": {"S3Bucket": {"shape": "S3Bucket", "documentation": "<p>The S3 bucket of the S3 object.</p>"}, "S3Key": {"shape": "S3Key", "documentation": "<p>The S3 key of the S3 object.</p>"}}, "documentation": "<p>Describes the S3 location.</p>"}, "ScriptDetails": {"type": "structure", "required": ["ScriptS3Location", "ExecutablePath", "TimeoutInSeconds"], "members": {"ScriptS3Location": {"shape": "S3Location", "documentation": "<p>The S3 object location for the script.</p>"}, "ExecutablePath": {"shape": "String", "documentation": "<p>The run path for the script.</p>"}, "ExecutableParameters": {"shape": "String", "documentation": "<p>The runtime parameters passed to the run path for the script.</p>"}, "TimeoutInSeconds": {"shape": "Integer", "documentation": "<p>The run timeout, in seconds, for the script.</p>"}}, "documentation": "<p>Describes the details of the script.</p>"}, "SecurityGroupIdList": {"type": "list", "member": {"shape": "String"}, "documentation": "<p>The security group identifiers.</p>", "max": 5}, "ServiceAccountCredentials": {"type": "structure", "required": ["Account<PERSON><PERSON>", "Account<PERSON><PERSON><PERSON>"], "members": {"AccountName": {"shape": "Account<PERSON><PERSON>", "documentation": "<p>The user name of the account. This account must have the following privileges: create computer objects, join computers to the domain, and change/reset the password on descendant computer objects for the organizational units specified.</p>"}, "AccountPassword": {"shape": "Account<PERSON><PERSON><PERSON>", "documentation": "<p>The password for the account.</p>"}}, "documentation": "<p>Describes the credentials for the service account used by the fleet or image builder to connect to the directory.</p>"}, "Session": {"type": "structure", "required": ["Id", "UserId", "StackName", "FleetName", "State"], "members": {"Id": {"shape": "String", "documentation": "<p>The identifier of the streaming session.</p>"}, "UserId": {"shape": "UserId", "documentation": "<p>The identifier of the user for whom the session was created.</p>"}, "StackName": {"shape": "String", "documentation": "<p>The name of the stack for the streaming session.</p>"}, "FleetName": {"shape": "String", "documentation": "<p>The name of the fleet for the streaming session.</p>"}, "State": {"shape": "SessionState", "documentation": "<p>The current state of the streaming session.</p>"}, "ConnectionState": {"shape": "SessionConnectionState", "documentation": "<p>Specifies whether a user is connected to the streaming session.</p>"}, "StartTime": {"shape": "Timestamp", "documentation": "<p>The time when a streaming instance is dedicated for the user.</p>"}, "MaxExpirationTime": {"shape": "Timestamp", "documentation": "<p>The time when the streaming session is set to expire. This time is based on the <code>MaxUserDurationinSeconds</code> value, which determines the maximum length of time that a streaming session can run. A streaming session might end earlier than the time specified in <code>SessionMaxExpirationTime</code>, when the <code>DisconnectTimeOutInSeconds</code> elapses or the user chooses to end his or her session. If the <code>DisconnectTimeOutInSeconds</code> elapses, or the user chooses to end his or her session, the streaming instance is terminated and the streaming session ends.</p>"}, "AuthenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication method. The user is authenticated using a streaming URL (<code>API</code>) or SAML 2.0 federation (<code>SAML</code>).</p>"}, "NetworkAccessConfiguration": {"shape": "NetworkAccessConfiguration", "documentation": "<p>The network details for the streaming session.</p>"}}, "documentation": "<p>Describes a streaming session.</p>"}, "SessionConnectionState": {"type": "string", "enum": ["CONNECTED", "NOT_CONNECTED"]}, "SessionList": {"type": "list", "member": {"shape": "Session"}, "documentation": "<p>List of sessions.</p>"}, "SessionState": {"type": "string", "documentation": "<p>Possible values for the state of a streaming session.</p>", "enum": ["ACTIVE", "PENDING", "EXPIRED"]}, "SettingsGroup": {"type": "string", "max": 100}, "SharedImagePermissions": {"type": "structure", "required": ["sharedAccountId", "imagePermissions"], "members": {"sharedAccountId": {"shape": "AwsAccountId", "documentation": "<p>The 12-digit identifier of the AWS account with which the image is shared.</p>"}, "imagePermissions": {"shape": "ImagePermissions", "documentation": "<p>Describes the permissions for a shared image.</p>"}}, "documentation": "<p>Describes the permissions that are available to the specified AWS account for a shared image.</p>"}, "SharedImagePermissionsList": {"type": "list", "member": {"shape": "SharedImagePermissions"}}, "Stack": {"type": "structure", "required": ["Name"], "members": {"Arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the stack.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the stack.</p>"}, "Description": {"shape": "String", "documentation": "<p>The description to display.</p>"}, "DisplayName": {"shape": "String", "documentation": "<p>The stack name to display.</p>"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The time the stack was created.</p>"}, "StorageConnectors": {"shape": "StorageConnectorList", "documentation": "<p>The storage connectors to enable.</p>"}, "RedirectURL": {"shape": "RedirectURL", "documentation": "<p>The URL that users are redirected to after their streaming session ends.</p>"}, "FeedbackURL": {"shape": "FeedbackURL", "documentation": "<p>The URL that users are redirected to after they click the Send Feedback link. If no URL is specified, no Send Feedback link is displayed.</p>"}, "StackErrors": {"shape": "StackErrors", "documentation": "<p>The errors for the stack.</p>"}, "UserSettings": {"shape": "UserSettingList", "documentation": "<p>The actions that are enabled or disabled for users during their streaming sessions. By default these actions are enabled.</p>"}, "ApplicationSettings": {"shape": "ApplicationSettingsResponse", "documentation": "<p>The persistent application settings for users of the stack.</p>"}, "AccessEndpoints": {"shape": "AccessEndpointList", "documentation": "<p>The list of virtual private cloud (VPC) interface endpoint objects. Users of the stack can connect to AppStream 2.0 only through the specified endpoints. </p>"}, "EmbedHostDomains": {"shape": "EmbedHostDomains", "documentation": "<p>The domains where AppStream 2.0 streaming sessions can be embedded in an iframe. You must approve the domains that you want to host embedded AppStream 2.0 streaming sessions.</p>"}, "StreamingExperienceSettings": {"shape": "StreamingExperienceSettings", "documentation": "<p>The streaming protocol you want your stack to prefer. This can be UDP or TCP. Currently, UDP is only supported in the Windows native client.</p>"}}, "documentation": "<p>Describes a stack.</p>"}, "StackAttribute": {"type": "string", "enum": ["STORAGE_CONNECTORS", "STORAGE_CONNECTOR_HOMEFOLDERS", "STORAGE_CONNECTOR_GOOGLE_DRIVE", "STORAGE_CONNECTOR_ONE_DRIVE", "REDIRECT_URL", "FEEDBACK_URL", "THEME_NAME", "USER_SETTINGS", "EMBED_HOST_DOMAINS", "IAM_ROLE_ARN", "ACCESS_ENDPOINTS", "STREAMING_EXPERIENCE_SETTINGS"]}, "StackAttributes": {"type": "list", "member": {"shape": "StackAttribute"}}, "StackError": {"type": "structure", "members": {"ErrorCode": {"shape": "StackErrorCode", "documentation": "<p>The error code.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>The error message.</p>"}}, "documentation": "<p>Describes a stack error.</p>"}, "StackErrorCode": {"type": "string", "enum": ["STORAGE_CONNECTOR_ERROR", "INTERNAL_SERVICE_ERROR"]}, "StackErrors": {"type": "list", "member": {"shape": "StackError"}, "documentation": "<p>The stack errors.</p>"}, "StackList": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}, "documentation": "<p>The stacks.</p>"}, "StartFleetRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the fleet.</p>"}}}, "StartFleetResult": {"type": "structure", "members": {}}, "StartImageBuilderRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the image builder.</p>"}, "AppstreamAgentVersion": {"shape": "AppstreamAgentVersion", "documentation": "<p>The version of the AppStream 2.0 agent to use for this image builder. To use the latest version of the AppStream 2.0 agent, specify [LATEST]. </p>"}}}, "StartImageBuilderResult": {"type": "structure", "members": {"ImageBuilder": {"shape": "ImageBuilder", "documentation": "<p>Information about the image builder.</p>"}}}, "StopFleetRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the fleet.</p>"}}}, "StopFleetResult": {"type": "structure", "members": {}}, "StopImageBuilderRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "String", "documentation": "<p>The name of the image builder.</p>"}}}, "StopImageBuilderResult": {"type": "structure", "members": {"ImageBuilder": {"shape": "ImageBuilder", "documentation": "<p>Information about the image builder.</p>"}}}, "StorageConnector": {"type": "structure", "required": ["ConnectorType"], "members": {"ConnectorType": {"shape": "StorageConnectorType", "documentation": "<p>The type of storage connector.</p>"}, "ResourceIdentifier": {"shape": "ResourceIdentifier", "documentation": "<p>The ARN of the storage connector.</p>"}, "Domains": {"shape": "DomainList", "documentation": "<p>The names of the domains for the account.</p>"}}, "documentation": "<p>Describes a connector that enables persistent storage for users.</p>"}, "StorageConnectorList": {"type": "list", "member": {"shape": "StorageConnector"}, "documentation": "<p>The storage connectors.</p>"}, "StorageConnectorType": {"type": "string", "documentation": "<p>The type of storage connector.</p>", "enum": ["HOMEFOLDERS", "GOOGLE_DRIVE", "ONE_DRIVE"]}, "StreamView": {"type": "string", "enum": ["APP", "DESKTOP"]}, "StreamingExperienceSettings": {"type": "structure", "members": {"PreferredProtocol": {"shape": "PreferredProtocol", "documentation": "<p>The preferred protocol that you want to use while streaming your application.</p>"}}, "documentation": "<p>The streaming protocol you want your stack to prefer. This can be UDP or TCP. Currently, UDP is only supported in the Windows native client.</p>"}, "StreamingUrlUserId": {"type": "string", "max": 32, "min": 2, "pattern": "[\\w+=,.@-]*"}, "String": {"type": "string", "min": 1}, "StringList": {"type": "list", "member": {"shape": "String"}}, "SubnetIdList": {"type": "list", "member": {"shape": "String"}, "documentation": "<p>The subnet identifiers.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(^(?!aws:).[\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}, "Tags": {"shape": "Tags", "documentation": "<p>The tags to associate. A tag is a key-value pair, and the value is optional. For example, Environment=Test. If you do not specify a value, Environment=. </p> <p>If you do not specify a value, the value is set to an empty string.</p> <p>Generally allowed characters are: letters, numbers, and spaces representable in UTF-8, and the following special characters: </p> <p>_ . : / = + \\ - @</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "Tags": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 1}, "Timestamp": {"type": "timestamp"}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the resource.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag keys for the tags to disassociate.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateApplicationRequest": {"type": "structure", "required": ["Name"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the application. This name is visible to users when display name is not specified.</p>"}, "DisplayName": {"shape": "DisplayName", "documentation": "<p>The display name of the application. This name is visible to users in the application catalog.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the application.</p>"}, "IconS3Location": {"shape": "S3Location", "documentation": "<p>The icon S3 location of the application.</p>"}, "LaunchPath": {"shape": "String", "documentation": "<p>The launch path of the application.</p>"}, "WorkingDirectory": {"shape": "String", "documentation": "<p>The working directory of the application.</p>"}, "LaunchParameters": {"shape": "String", "documentation": "<p>The launch parameters of the application.</p>"}, "AppBlockArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the app block.</p>"}, "AttributesToDelete": {"shape": "ApplicationAttributes", "documentation": "<p>The attributes to delete for an application.</p>"}}}, "UpdateApplicationResult": {"type": "structure", "members": {"Application": {"shape": "Application"}}}, "UpdateDirectoryConfigRequest": {"type": "structure", "required": ["DirectoryName"], "members": {"DirectoryName": {"shape": "DirectoryName", "documentation": "<p>The name of the Directory Config object.</p>"}, "OrganizationalUnitDistinguishedNames": {"shape": "OrganizationalUnitDistinguishedNamesList", "documentation": "<p>The distinguished names of the organizational units for computer accounts.</p>"}, "ServiceAccountCredentials": {"shape": "ServiceAccountCredentials", "documentation": "<p>The credentials for the service account used by the fleet or image builder to connect to the directory.</p>"}, "CertificateBasedAuthProperties": {"shape": "CertificateBasedAuthProperties", "documentation": "<p>The certificate-based authentication properties used to authenticate SAML 2.0 Identity Provider (IdP) user identities to Active Directory domain-joined streaming instances. Fallback is turned on by default when certificate-based authentication is <b>Enabled</b> . Fallback allows users to log in using their AD domain password if certificate-based authentication is unsuccessful, or to unlock a desktop lock screen. <b>Enabled_no_directory_login_fallback</b> enables certificate-based authentication, but does not allow users to log in using their AD domain password. Users will be disconnected to re-authenticate using certificates.</p>"}}}, "UpdateDirectoryConfigResult": {"type": "structure", "members": {"DirectoryConfig": {"shape": "DirectoryConfig", "documentation": "<p>Information about the Directory Config object.</p>"}}}, "UpdateEntitlementRequest": {"type": "structure", "required": ["Name", "StackName"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the entitlement.</p>"}, "StackName": {"shape": "Name", "documentation": "<p>The name of the stack with which the entitlement is associated.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description of the entitlement.</p>"}, "AppVisibility": {"shape": "AppVisibility", "documentation": "<p>Specifies whether all or only selected apps are entitled.</p>"}, "Attributes": {"shape": "EntitlementAttributeList", "documentation": "<p>The attributes of the entitlement.</p>"}}}, "UpdateEntitlementResult": {"type": "structure", "members": {"Entitlement": {"shape": "Entitlement", "documentation": "<p>The entitlement.</p>"}}}, "UpdateFleetRequest": {"type": "structure", "members": {"ImageName": {"shape": "String", "documentation": "<p>The name of the image used to create the fleet.</p>"}, "ImageArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the public, private, or shared image to use.</p>"}, "Name": {"shape": "String", "documentation": "<p>A unique name for the fleet.</p>"}, "InstanceType": {"shape": "String", "documentation": "<p>The instance type to use when launching fleet instances. The following instance types are available:</p> <ul> <li> <p>stream.standard.small</p> </li> <li> <p>stream.standard.medium</p> </li> <li> <p>stream.standard.large</p> </li> <li> <p>stream.standard.xlarge</p> </li> <li> <p>stream.standard.2xlarge</p> </li> <li> <p>stream.compute.large</p> </li> <li> <p>stream.compute.xlarge</p> </li> <li> <p>stream.compute.2xlarge</p> </li> <li> <p>stream.compute.4xlarge</p> </li> <li> <p>stream.compute.8xlarge</p> </li> <li> <p>stream.memory.large</p> </li> <li> <p>stream.memory.xlarge</p> </li> <li> <p>stream.memory.2xlarge</p> </li> <li> <p>stream.memory.4xlarge</p> </li> <li> <p>stream.memory.8xlarge</p> </li> <li> <p>stream.memory.z1d.large</p> </li> <li> <p>stream.memory.z1d.xlarge</p> </li> <li> <p>stream.memory.z1d.2xlarge</p> </li> <li> <p>stream.memory.z1d.3xlarge</p> </li> <li> <p>stream.memory.z1d.6xlarge</p> </li> <li> <p>stream.memory.z1d.12xlarge</p> </li> <li> <p>stream.graphics-design.large</p> </li> <li> <p>stream.graphics-design.xlarge</p> </li> <li> <p>stream.graphics-design.2xlarge</p> </li> <li> <p>stream.graphics-design.4xlarge</p> </li> <li> <p>stream.graphics-desktop.2xlarge</p> </li> <li> <p>stream.graphics.g4dn.xlarge</p> </li> <li> <p>stream.graphics.g4dn.2xlarge</p> </li> <li> <p>stream.graphics.g4dn.4xlarge</p> </li> <li> <p>stream.graphics.g4dn.8xlarge</p> </li> <li> <p>stream.graphics.g4dn.12xlarge</p> </li> <li> <p>stream.graphics.g4dn.16xlarge</p> </li> <li> <p>stream.graphics-pro.4xlarge</p> </li> <li> <p>stream.graphics-pro.8xlarge</p> </li> <li> <p>stream.graphics-pro.16xlarge</p> </li> </ul> <p>The following instance types are available for Elastic fleets:</p> <ul> <li> <p>stream.standard.small</p> </li> <li> <p>stream.standard.medium</p> </li> <li> <p>stream.standard.large</p> </li> <li> <p>stream.standard.xlarge</p> </li> <li> <p>stream.standard.2xlarge</p> </li> </ul>"}, "ComputeCapacity": {"shape": "ComputeCapacity", "documentation": "<p>The desired capacity for the fleet. This is not allowed for Elastic fleets.</p>"}, "VpcConfig": {"shape": "VpcConfig", "documentation": "<p>The VPC configuration for the fleet. This is required for Elastic fleets, but not required for other fleet types. Elastic fleets require that you specify at least two subnets in different availability zones. </p>"}, "MaxUserDurationInSeconds": {"shape": "Integer", "documentation": "<p>The maximum amount of time that a streaming session can remain active, in seconds. If users are still connected to a streaming instance five minutes before this limit is reached, they are prompted to save any open documents before being disconnected. After this time elapses, the instance is terminated and replaced by a new instance.</p> <p>Specify a value between 600 and 360000.</p>"}, "DisconnectTimeoutInSeconds": {"shape": "Integer", "documentation": "<p>The amount of time that a streaming session remains active after users disconnect. If users try to reconnect to the streaming session after a disconnection or network interruption within this time interval, they are connected to their previous session. Otherwise, they are connected to a new session with a new streaming instance. </p> <p>Specify a value between 60 and 360000.</p>"}, "DeleteVpcConfig": {"shape": "Boolean", "documentation": "<p>Deletes the VPC association for the specified fleet.</p>", "deprecated": true}, "Description": {"shape": "Description", "documentation": "<p>The description to display.</p>"}, "DisplayName": {"shape": "DisplayName", "documentation": "<p>The fleet name to display.</p>"}, "EnableDefaultInternetAccess": {"shape": "BooleanObject", "documentation": "<p>Enables or disables default internet access for the fleet.</p>"}, "DomainJoinInfo": {"shape": "DomainJoinInfo", "documentation": "<p>The name of the directory and organizational unit (OU) to use to join the fleet to a Microsoft Active Directory domain. </p>"}, "IdleDisconnectTimeoutInSeconds": {"shape": "Integer", "documentation": "<p>The amount of time that users can be idle (inactive) before they are disconnected from their streaming session and the <code>DisconnectTimeoutInSeconds</code> time interval begins. Users are notified before they are disconnected due to inactivity. If users try to reconnect to the streaming session before the time interval specified in <code>DisconnectTimeoutInSeconds</code> elapses, they are connected to their previous session. Users are considered idle when they stop providing keyboard or mouse input during their streaming session. File uploads and downloads, audio in, audio out, and pixels changing do not qualify as user activity. If users continue to be idle after the time interval in <code>IdleDisconnectTimeoutInSeconds</code> elapses, they are disconnected. </p> <p>To prevent users from being disconnected due to inactivity, specify a value of 0. Otherwise, specify a value between 60 and 3600. The default value is 0.</p> <note> <p>If you enable this feature, we recommend that you specify a value that corresponds exactly to a whole number of minutes (for example, 60, 120, and 180). If you don't do this, the value is rounded to the nearest minute. For example, if you specify a value of 70, users are disconnected after 1 minute of inactivity. If you specify a value that is at the midpoint between two different minutes, the value is rounded up. For example, if you specify a value of 90, users are disconnected after 2 minutes of inactivity. </p> </note>"}, "AttributesToDelete": {"shape": "FleetAttributes", "documentation": "<p>The fleet attributes to delete.</p>"}, "IamRoleArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role to apply to the fleet. To assume a role, a fleet instance calls the AWS Security Token Service (STS) <code>AssumeRole</code> API operation and passes the ARN of the role to use. The operation creates a new session with temporary credentials. AppStream 2.0 retrieves the temporary credentials and creates the <b>appstream_machine_role</b> credential profile on the instance.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/using-iam-roles-to-grant-permissions-to-applications-scripts-streaming-instances.html\">Using an IAM Role to Grant Permissions to Applications and Scripts Running on AppStream 2.0 Streaming Instances</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p>"}, "StreamView": {"shape": "StreamView", "documentation": "<p>The AppStream 2.0 view that is displayed to your users when they stream from the fleet. When <code>APP</code> is specified, only the windows of applications opened by users display. When <code>DESKTOP</code> is specified, the standard desktop that is provided by the operating system displays.</p> <p>The default value is <code>APP</code>.</p>"}, "Platform": {"shape": "PlatformType", "documentation": "<p>The platform of the fleet. WINDOWS_SERVER_2019 and AMAZON_LINUX2 are supported for Elastic fleets. </p>"}, "MaxConcurrentSessions": {"shape": "Integer", "documentation": "<p>The maximum number of concurrent sessions for a fleet.</p>"}, "UsbDeviceFilterStrings": {"shape": "UsbDeviceFilterStrings", "documentation": "<p>The USB device filter strings that specify which USB devices a user can redirect to the fleet streaming session, when using the Windows native client. This is allowed but not required for Elastic fleets.</p>"}, "SessionScriptS3Location": {"shape": "S3Location", "documentation": "<p>The S3 location of the session scripts configuration zip file. This only applies to Elastic fleets. </p>"}}}, "UpdateFleetResult": {"type": "structure", "members": {"Fleet": {"shape": "Fleet", "documentation": "<p>Information about the fleet.</p>"}}}, "UpdateImagePermissionsRequest": {"type": "structure", "required": ["Name", "SharedAccountId", "ImagePermissions"], "members": {"Name": {"shape": "Name", "documentation": "<p>The name of the private image.</p>"}, "SharedAccountId": {"shape": "AwsAccountId", "documentation": "<p>The 12-digit identifier of the AWS account for which you want add or update image permissions.</p>"}, "ImagePermissions": {"shape": "ImagePermissions", "documentation": "<p>The permissions for the image.</p>"}}}, "UpdateImagePermissionsResult": {"type": "structure", "members": {}}, "UpdateStackRequest": {"type": "structure", "required": ["Name"], "members": {"DisplayName": {"shape": "DisplayName", "documentation": "<p>The stack name to display.</p>"}, "Description": {"shape": "Description", "documentation": "<p>The description to display.</p>"}, "Name": {"shape": "String", "documentation": "<p>The name of the stack.</p>"}, "StorageConnectors": {"shape": "StorageConnectorList", "documentation": "<p>The storage connectors to enable.</p>"}, "DeleteStorageConnectors": {"shape": "Boolean", "documentation": "<p>Deletes the storage connectors currently enabled for the stack.</p>", "deprecated": true}, "RedirectURL": {"shape": "RedirectURL", "documentation": "<p>The URL that users are redirected to after their streaming session ends.</p>"}, "FeedbackURL": {"shape": "FeedbackURL", "documentation": "<p>The URL that users are redirected to after they choose the Send Feedback link. If no URL is specified, no Send Feedback link is displayed.</p>"}, "AttributesToDelete": {"shape": "StackAttributes", "documentation": "<p>The stack attributes to delete.</p>"}, "UserSettings": {"shape": "UserSettingList", "documentation": "<p>The actions that are enabled or disabled for users during their streaming sessions. By default, these actions are enabled.</p>"}, "ApplicationSettings": {"shape": "ApplicationSettings", "documentation": "<p>The persistent application settings for users of a stack. When these settings are enabled, changes that users make to applications and Windows settings are automatically saved after each session and applied to the next session.</p>"}, "AccessEndpoints": {"shape": "AccessEndpointList", "documentation": "<p>The list of interface VPC endpoint (interface endpoint) objects. Users of the stack can connect to AppStream 2.0 only through the specified endpoints.</p>"}, "EmbedHostDomains": {"shape": "EmbedHostDomains", "documentation": "<p>The domains where AppStream 2.0 streaming sessions can be embedded in an iframe. You must approve the domains that you want to host embedded AppStream 2.0 streaming sessions. </p>"}, "StreamingExperienceSettings": {"shape": "StreamingExperienceSettings", "documentation": "<p>The streaming protocol you want your stack to prefer. This can be UDP or TCP. Currently, UDP is only supported in the Windows native client.</p>"}}}, "UpdateStackResult": {"type": "structure", "members": {"Stack": {"shape": "<PERSON><PERSON>", "documentation": "<p>Information about the stack.</p>"}}}, "UsageReportExecutionErrorCode": {"type": "string", "enum": ["RESOURCE_NOT_FOUND", "ACCESS_DENIED", "INTERNAL_SERVICE_ERROR"]}, "UsageReportSchedule": {"type": "string", "enum": ["DAILY"]}, "UsageReportSubscription": {"type": "structure", "members": {"S3BucketName": {"shape": "String", "documentation": "<p>The Amazon S3 bucket where generated reports are stored.</p> <p>If you enabled on-instance session scripts and Amazon S3 logging for your session script configuration, AppStream 2.0 created an S3 bucket to store the script output. The bucket is unique to your account and Region. When you enable usage reporting in this case, AppStream 2.0 uses the same bucket to store your usage reports. If you haven't already enabled on-instance session scripts, when you enable usage reports, AppStream 2.0 creates a new S3 bucket.</p>"}, "Schedule": {"shape": "UsageReportSchedule", "documentation": "<p>The schedule for generating usage reports.</p>"}, "LastGeneratedReportDate": {"shape": "Timestamp", "documentation": "<p>The time when the last usage report was generated.</p>"}, "SubscriptionErrors": {"shape": "LastReportGenerationExecutionErrors", "documentation": "<p>The errors that were returned if usage reports couldn't be generated.</p>"}}, "documentation": "<p>Describes information about the usage report subscription.</p>"}, "UsageReportSubscriptionList": {"type": "list", "member": {"shape": "UsageReportSubscription"}}, "UsbDeviceFilterString": {"type": "string", "max": 100, "min": 0, "pattern": "^((\\w*)\\s*(\\w*)\\s*\\,\\s*(\\w*)\\s*\\,\\s*\\*?(\\w*)\\s*\\,\\s*\\*?(\\w*)\\s*\\,\\s*\\*?\\d*\\s*\\,\\s*\\*?\\d*\\s*\\,\\s*[0-1]\\s*\\,\\s*[0-1]\\s*)$"}, "UsbDeviceFilterStrings": {"type": "list", "member": {"shape": "UsbDeviceFilterString"}}, "User": {"type": "structure", "required": ["AuthenticationType"], "members": {"Arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the user.</p>"}, "UserName": {"shape": "Username", "documentation": "<p>The email address of the user.</p> <note> <p>Users' email addresses are case-sensitive.</p> </note>"}, "Enabled": {"shape": "Boolean", "documentation": "<p>Specifies whether the user in the user pool is enabled.</p>"}, "Status": {"shape": "String", "documentation": "<p>The status of the user in the user pool. The status can be one of the following:</p> <ul> <li> <p>UNCONFIRMED – The user is created but not confirmed.</p> </li> <li> <p>CONFIRMED – The user is confirmed.</p> </li> <li> <p>ARCHIVED – The user is no longer active.</p> </li> <li> <p>COMPROMISED – The user is disabled because of a potential security threat.</p> </li> <li> <p>UNKNOWN – The user status is not known.</p> </li> </ul>"}, "FirstName": {"shape": "UserAttributeValue", "documentation": "<p>The first name, or given name, of the user.</p>"}, "LastName": {"shape": "UserAttributeValue", "documentation": "<p>The last name, or surname, of the user.</p>"}, "CreatedTime": {"shape": "Timestamp", "documentation": "<p>The date and time the user was created in the user pool.</p>"}, "AuthenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type for the user.</p>"}}, "documentation": "<p>Describes a user in the user pool.</p>"}, "UserAttributeValue": {"type": "string", "max": 2048, "pattern": "^[A-Za-z0-9_\\-\\s]+$", "sensitive": true}, "UserId": {"type": "string", "max": 128, "min": 2}, "UserList": {"type": "list", "member": {"shape": "User"}}, "UserSetting": {"type": "structure", "required": ["Action", "Permission"], "members": {"Action": {"shape": "Action", "documentation": "<p>The action that is enabled or disabled.</p>"}, "Permission": {"shape": "Permission", "documentation": "<p>Indicates whether the action is enabled or disabled.</p>"}}, "documentation": "<p>Describes an action and whether the action is enabled or disabled for users during their streaming sessions.</p>"}, "UserSettingList": {"type": "list", "member": {"shape": "UserSetting"}, "min": 1}, "UserStackAssociation": {"type": "structure", "required": ["StackName", "UserName", "AuthenticationType"], "members": {"StackName": {"shape": "String", "documentation": "<p>The name of the stack that is associated with the user.</p>"}, "UserName": {"shape": "Username", "documentation": "<p>The email address of the user who is associated with the stack.</p> <note> <p>Users' email addresses are case-sensitive.</p> </note>"}, "AuthenticationType": {"shape": "AuthenticationType", "documentation": "<p>The authentication type for the user.</p>"}, "SendEmailNotification": {"shape": "Boolean", "documentation": "<p>Specifies whether a welcome email is sent to a user after the user is created in the user pool.</p>"}}, "documentation": "<p>Describes a user in the user pool and the associated stack.</p>"}, "UserStackAssociationError": {"type": "structure", "members": {"UserStackAssociation": {"shape": "UserStackAssociation", "documentation": "<p>Information about the user and associated stack.</p>"}, "ErrorCode": {"shape": "UserStackAssociationErrorCode", "documentation": "<p>The error code for the error that is returned when a user can’t be associated with or disassociated from a stack.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>The error message for the error that is returned when a user can’t be associated with or disassociated from a stack.</p>"}}, "documentation": "<p>Describes the error that is returned when a user can’t be associated with or disassociated from a stack. </p>"}, "UserStackAssociationErrorCode": {"type": "string", "enum": ["STACK_NOT_FOUND", "USER_NAME_NOT_FOUND", "DIRECTORY_NOT_FOUND", "INTERNAL_ERROR"]}, "UserStackAssociationErrorList": {"type": "list", "member": {"shape": "UserStackAssociationError"}}, "UserStackAssociationList": {"type": "list", "member": {"shape": "UserStackAssociation"}, "max": 25, "min": 1}, "Username": {"type": "string", "max": 128, "min": 1, "pattern": "[\\p{L}\\p{M}\\p{S}\\p{N}\\p{P}]+", "sensitive": true}, "VisibilityType": {"type": "string", "enum": ["PUBLIC", "PRIVATE", "SHARED"]}, "VpcConfig": {"type": "structure", "members": {"SubnetIds": {"shape": "SubnetIdList", "documentation": "<p>The identifiers of the subnets to which a network interface is attached from the fleet instance or image builder instance. Fleet instances use one or more subnets. Image builder instances use one subnet.</p>"}, "SecurityGroupIds": {"shape": "SecurityGroupIdList", "documentation": "<p>The identifiers of the security groups for the fleet or image builder.</p>"}}, "documentation": "<p>Describes VPC configuration information for fleets and image builders.</p>"}}, "documentation": "<fullname>Amazon AppStream 2.0</fullname> <p>This is the <i>Amazon AppStream 2.0 API Reference</i>. This documentation provides descriptions and syntax for each of the actions and data types in AppStream 2.0. AppStream 2.0 is a fully managed, secure application streaming service that lets you stream desktop applications to users without rewriting applications. AppStream 2.0 manages the AWS resources that are required to host and run your applications, scales automatically, and provides access to your users on demand. </p> <note> <p>You can call the AppStream 2.0 API operations by using an interface VPC endpoint (interface endpoint). For more information, see <a href=\"https://docs.aws.amazon.com/appstream2/latest/developerguide/access-api-cli-through-interface-vpc-endpoint.html\">Access AppStream 2.0 API Operations and CLI Commands Through an Interface VPC Endpoint</a> in the <i>Amazon AppStream 2.0 Administration Guide</i>.</p> </note> <p>To learn more about AppStream 2.0, see the following resources:</p> <ul> <li> <p> <a href=\"http://aws.amazon.com/appstream2\">Amazon AppStream 2.0 product page</a> </p> </li> <li> <p> <a href=\"http://aws.amazon.com/documentation/appstream2\">Amazon AppStream 2.0 documentation</a> </p> </li> </ul>"}