{"version": "2.0", "metadata": {"apiVersion": "2022-10-03", "endpointPrefix": "cases", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "ConnectCases", "serviceFullName": "Amazon Connect Cases", "serviceId": "ConnectCases", "signatureVersion": "v4", "signingName": "cases", "uid": "connectcases-2022-10-03"}, "operations": {"BatchGetField": {"name": "BatchGetField", "http": {"method": "POST", "requestUri": "/domains/{domainId}/fields-batch", "responseCode": 200}, "input": {"shape": "BatchGetFieldRequest"}, "output": {"shape": "BatchGetFieldResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns the description for the list of fields in the request parameters. </p>"}, "BatchPutFieldOptions": {"name": "BatchPutFieldOptions", "http": {"method": "PUT", "requestUri": "/domains/{domainId}/fields/{fieldId}/options", "responseCode": 200}, "input": {"shape": "BatchPutFieldOptionsRequest"}, "output": {"shape": "BatchPutFieldOptionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates and updates a set of field options for a single select field in a Cases domain.</p>", "idempotent": true}, "CreateCase": {"name": "CreateCase", "http": {"method": "POST", "requestUri": "/domains/{domainId}/cases", "responseCode": 200}, "input": {"shape": "CreateCaseRequest"}, "output": {"shape": "CreateCaseResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}], "documentation": "<p>Creates a case in the specified Cases domain. Case system and custom fields are taken as an array id/value pairs with a declared data types.</p> <note> <p> <code>customer_id</code> is a required field when creating a case.</p> </note>", "idempotent": true}, "CreateDomain": {"name": "CreateDomain", "http": {"method": "POST", "requestUri": "/domains", "responseCode": 200}, "input": {"shape": "CreateDomainRequest"}, "output": {"shape": "CreateDomainResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a domain, which is a container for all case data, such as cases, fields, templates and layouts. Each Amazon Connect instance can be associated with only one Cases domain.</p> <important> <p>This will not associate your connect instance to Cases domain. Instead, use the Amazon Connect <a href=\"https://docs.aws.amazon.com/connect/latest/APIReference/API_CreateIntegrationAssociation.html\">CreateIntegrationAssociation</a> API.</p> </important>", "idempotent": true}, "CreateField": {"name": "CreateField", "http": {"method": "POST", "requestUri": "/domains/{domainId}/fields", "responseCode": 200}, "input": {"shape": "CreateFieldRequest"}, "output": {"shape": "CreateFieldResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a field in the Cases domain. This field is used to define the case object model (that is, defines what data can be captured on cases) in a Cases domain. </p>", "idempotent": true}, "CreateLayout": {"name": "CreateLayout", "http": {"method": "POST", "requestUri": "/domains/{domainId}/layouts", "responseCode": 200}, "input": {"shape": "CreateLayoutRequest"}, "output": {"shape": "CreateLayoutResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a layout in the Cases domain. Layouts define the following configuration in the top section and More Info tab of the Cases user interface:</p> <ul> <li> <p>Fields to display to the users</p> </li> <li> <p>Field ordering</p> </li> </ul> <note> <p>Title and Status fields cannot be part of layouts since they are not configurable.</p> </note>"}, "CreateRelatedItem": {"name": "CreateRelatedItem", "http": {"method": "POST", "requestUri": "/domains/{domainId}/cases/{caseId}/related-items/", "responseCode": 200}, "input": {"shape": "CreateRelatedItemRequest"}, "output": {"shape": "CreateRelatedItemResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a related item (comments, tasks, and contacts) and associates it with a case.</p> <note> <p>A Related Item is a resource that is associated with a case. It may or may not have an external identifier linking it to an external resource (for example, a <code>contactArn</code>). All Related Items have their own internal identifier, the <code>relatedItemArn</code>. Examples of related items include <code>comments</code> and <code>contacts</code>.</p> </note>", "idempotent": true}, "CreateTemplate": {"name": "CreateTemplate", "http": {"method": "POST", "requestUri": "/domains/{domainId}/templates", "responseCode": 200}, "input": {"shape": "CreateTemplateRequest"}, "output": {"shape": "CreateTemplateResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a template in the Cases domain. This template is used to define the case object model (that is, to define what data can be captured on cases) in a Cases domain. A template must have a unique name within a domain, and it must reference existing field IDs and layout IDs. Additionally, multiple fields with same IDs are not allowed within the same Template. A template can be either Active or Inactive, as indicated by its status. Inactive templates cannot be used to create cases.</p>", "idempotent": true}, "GetCase": {"name": "GetCase", "http": {"method": "POST", "requestUri": "/domains/{domainId}/cases/{caseId}", "responseCode": 200}, "input": {"shape": "GetCaseRequest"}, "output": {"shape": "GetCaseResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns information about a specific case if it exists. </p>"}, "GetCaseEventConfiguration": {"name": "GetCaseEventConfiguration", "http": {"method": "POST", "requestUri": "/domains/{domainId}/case-event-configuration", "responseCode": 200}, "input": {"shape": "GetCaseEventConfigurationRequest"}, "output": {"shape": "GetCaseEventConfigurationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns the case event publishing configuration.</p>"}, "GetDomain": {"name": "GetDomain", "http": {"method": "POST", "requestUri": "/domains/{domainId}", "responseCode": 200}, "input": {"shape": "GetDomainRequest"}, "output": {"shape": "GetDomainResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns information about a specific domain if it exists. </p>"}, "GetLayout": {"name": "GetLayout", "http": {"method": "POST", "requestUri": "/domains/{domainId}/layouts/{layoutId}", "responseCode": 200}, "input": {"shape": "GetLayoutRequest"}, "output": {"shape": "GetLayoutResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns the details for the requested layout.</p>"}, "GetTemplate": {"name": "GetTemplate", "http": {"method": "POST", "requestUri": "/domains/{domainId}/templates/{templateId}", "responseCode": 200}, "input": {"shape": "GetTemplateRequest"}, "output": {"shape": "GetTemplateResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Returns the details for the requested template. </p>"}, "ListCasesForContact": {"name": "ListCasesForContact", "http": {"method": "POST", "requestUri": "/domains/{domainId}/list-cases-for-contact", "responseCode": 200}, "input": {"shape": "ListCasesForContactRequest"}, "output": {"shape": "ListCasesForContactResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists cases for a given contact.</p>"}, "ListDomains": {"name": "ListDomains", "http": {"method": "POST", "requestUri": "/domains-list", "responseCode": 200}, "input": {"shape": "ListDomainsRequest"}, "output": {"shape": "ListDomainsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all cases domains in the Amazon Web Services account. Each list item is a condensed summary object of the domain.</p>"}, "ListFieldOptions": {"name": "ListFieldOptions", "http": {"method": "POST", "requestUri": "/domains/{domainId}/fields/{fieldId}/options-list", "responseCode": 200}, "input": {"shape": "ListFieldOptionsRequest"}, "output": {"shape": "ListFieldOptionsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all of the field options for a field identifier in the domain. </p>"}, "ListFields": {"name": "ListFields", "http": {"method": "POST", "requestUri": "/domains/{domainId}/fields-list", "responseCode": 200}, "input": {"shape": "ListFieldsRequest"}, "output": {"shape": "ListFieldsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all fields in a Cases domain.</p>"}, "ListLayouts": {"name": "ListLayouts", "http": {"method": "POST", "requestUri": "/domains/{domainId}/layouts-list", "responseCode": 200}, "input": {"shape": "ListLayoutsRequest"}, "output": {"shape": "ListLayoutsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all layouts in the given cases domain. Each list item is a condensed summary object of the layout.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{arn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists tags for a resource.</p>", "idempotent": true}, "ListTemplates": {"name": "ListTemplates", "http": {"method": "POST", "requestUri": "/domains/{domainId}/templates-list", "responseCode": 200}, "input": {"shape": "ListTemplatesRequest"}, "output": {"shape": "ListTemplatesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Lists all of the templates in a Cases domain. Each list item is a condensed summary object of the template. </p>"}, "PutCaseEventConfiguration": {"name": "PutCaseEventConfiguration", "http": {"method": "PUT", "requestUri": "/domains/{domainId}/case-event-configuration", "responseCode": 200}, "input": {"shape": "PutCaseEventConfigurationRequest"}, "output": {"shape": "PutCaseEventConfigurationResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>API for adding case event publishing configuration</p>"}, "SearchCases": {"name": "SearchCases", "http": {"method": "POST", "requestUri": "/domains/{domainId}/cases-search", "responseCode": 200}, "input": {"shape": "SearchCasesRequest"}, "output": {"shape": "SearchCasesResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Searches for cases within their associated Cases domain. Search results are returned as a paginated list of abridged case documents.</p>"}, "SearchRelatedItems": {"name": "SearchRelatedItems", "http": {"method": "POST", "requestUri": "/domains/{domainId}/cases/{caseId}/related-items-search", "responseCode": 200}, "input": {"shape": "SearchRelatedItemsRequest"}, "output": {"shape": "SearchRelatedItemsResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Searches for related items that are associated with a case.</p> <note> <p>If no filters are provided, this returns all related items associated with a case.</p> </note>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{arn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Adds tags to a resource.</p>", "idempotent": true}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{arn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Untags a resource.</p>", "idempotent": true}, "UpdateCase": {"name": "UpdateCase", "http": {"method": "PUT", "requestUri": "/domains/{domainId}/cases/{caseId}", "responseCode": 200}, "input": {"shape": "UpdateCaseRequest"}, "output": {"shape": "UpdateCaseResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}], "documentation": "<p>Updates the values of fields on a case. Fields to be updated are received as an array of id/value pairs identical to the <code>CreateCase</code> input .</p> <p>If the action is successful, the service sends back an HTTP 200 response with an empty HTTP body.</p>"}, "UpdateField": {"name": "UpdateField", "http": {"method": "PUT", "requestUri": "/domains/{domainId}/fields/{fieldId}", "responseCode": 200}, "input": {"shape": "UpdateFieldRequest"}, "output": {"shape": "UpdateFieldResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates the properties of an existing field. </p>", "idempotent": true}, "UpdateLayout": {"name": "UpdateLayout", "http": {"method": "PUT", "requestUri": "/domains/{domainId}/layouts/{layoutId}", "responseCode": 200}, "input": {"shape": "UpdateLayoutRequest"}, "output": {"shape": "UpdateLayoutResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates the attributes of an existing layout.</p> <p>If the action is successful, the service sends back an HTTP 200 response with an empty HTTP body.</p> <p>A <code>ValidationException</code> is returned when you add non-existent <code>fieldIds</code> to a layout.</p> <note> <p>Title and Status fields cannot be part of layouts because they are not configurable.</p> </note>", "idempotent": true}, "UpdateTemplate": {"name": "UpdateTemplate", "http": {"method": "PUT", "requestUri": "/domains/{domainId}/templates/{templateId}", "responseCode": 200}, "input": {"shape": "UpdateTemplateRequest"}, "output": {"shape": "UpdateTemplateResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ValidationException"}, {"shape": "ThrottlingException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}], "documentation": "<p>Updates the attributes of an existing template. The template attributes that can be modified include <code>name</code>, <code>description</code>, <code>layoutConfiguration</code>, <code>requiredFields</code>, and <code>status</code>. At least one of these attributes must not be null. If a null value is provided for a given attribute, that attribute is ignored and its current value is preserved.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "Arn": {"type": "string", "max": 500, "min": 1}, "AssociationTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "BasicLayout": {"type": "structure", "members": {"moreInfo": {"shape": "LayoutSections", "documentation": "<p>This represents sections in a tab of the page layout.</p>"}, "topPanel": {"shape": "LayoutSections", "documentation": "<p>This represents sections in a panel of the page layout.</p>"}}, "documentation": "<p>Content specific to <code>BasicLayout</code> type. It configures fields in the top panel and More Info tab of agent application. </p>"}, "BatchGetFieldIdentifierList": {"type": "list", "member": {"shape": "FieldIdentifier"}, "max": 50, "min": 1}, "BatchGetFieldRequest": {"type": "structure", "required": ["domainId", "fields"], "members": {"domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "fields": {"shape": "BatchGetFieldIdentifierList", "documentation": "<p>A list of unique field identifiers. </p>"}}}, "BatchGetFieldResponse": {"type": "structure", "required": ["errors", "fields"], "members": {"errors": {"shape": "BatchGetFieldResponseErrorsList", "documentation": "<p>A list of field errors. </p>"}, "fields": {"shape": "BatchGetFieldResponseFieldsList", "documentation": "<p>A list of detailed field information. </p>"}}}, "BatchGetFieldResponseErrorsList": {"type": "list", "member": {"shape": "FieldError"}, "max": 50, "min": 0}, "BatchGetFieldResponseFieldsList": {"type": "list", "member": {"shape": "GetFieldResponse"}, "max": 50, "min": 0}, "BatchPutFieldOptionsRequest": {"type": "structure", "required": ["domainId", "fieldId", "options"], "members": {"domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "fieldId": {"shape": "FieldId", "documentation": "<p>The unique identifier of a field.</p>", "location": "uri", "locationName": "fieldId"}, "options": {"shape": "BatchPutFieldOptionsRequestOptionsList", "documentation": "<p>A list of <code>FieldOption</code> objects.</p>"}}}, "BatchPutFieldOptionsRequestOptionsList": {"type": "list", "member": {"shape": "FieldOption"}, "max": 50, "min": 0}, "BatchPutFieldOptionsResponse": {"type": "structure", "members": {"errors": {"shape": "BatchPutFieldOptionsResponseErrorsList", "documentation": "<p>A list of field errors. </p>"}}}, "BatchPutFieldOptionsResponseErrorsList": {"type": "list", "member": {"shape": "FieldOptionError"}, "max": 50, "min": 0}, "Boolean": {"type": "boolean", "box": true}, "CaseArn": {"type": "string", "max": 500, "min": 1}, "CaseEventIncludedData": {"type": "structure", "required": ["fields"], "members": {"fields": {"shape": "CaseEventIncludedDataFieldsList", "documentation": "<p>List of field identifiers.</p>"}}, "documentation": "<p>Details of what case data is published through the case event stream.</p>"}, "CaseEventIncludedDataFieldsList": {"type": "list", "member": {"shape": "FieldIdentifier"}, "max": 50, "min": 0}, "CaseFilter": {"type": "structure", "members": {"andAll": {"shape": "CaseFilterAndAllList", "documentation": "<p>Provides \"and all\" filtering.</p>"}, "field": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>A list of fields to filter on.</p>"}, "not": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}}, "documentation": "<p>A filter for cases. Only one value can be provided.</p>", "union": true}, "CaseFilterAndAllList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON>"}, "max": 10, "min": 0}, "CaseId": {"type": "string", "max": 500, "min": 1}, "CaseSummary": {"type": "structure", "required": ["caseId", "templateId"], "members": {"caseId": {"shape": "CaseId", "documentation": "<p>A unique identifier of the case.</p>"}, "templateId": {"shape": "TemplateId", "documentation": "<p>A unique identifier of a template.</p>"}}, "documentation": "<p>Case summary information.</p>"}, "Channel": {"type": "string", "max": 100, "min": 1}, "CommentBody": {"type": "string", "max": 1000, "min": 1}, "CommentBodyTextType": {"type": "string", "enum": ["Text/Plain"]}, "CommentContent": {"type": "structure", "required": ["body", "contentType"], "members": {"body": {"shape": "CommentBody", "documentation": "<p>Text in the body of a <code>Comment</code> on a case.</p>"}, "contentType": {"shape": "CommentBodyTextType", "documentation": "<p>Type of the text in the box of a <code>Comment</code> on a case.</p>"}}, "documentation": "<p>Represents the content of a <code>Comment</code> to be returned to agents.</p>"}, "CommentFilter": {"type": "structure", "members": {}, "documentation": "<p>A filter for related items of type <code>Comment</code>.</p>"}, "ConflictException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The requested operation would cause a conflict with the current state of a service resource associated with the request. Resolve the conflict before retrying this request. See the accompanying error message for details.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "ConnectedToSystemTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "Contact": {"type": "structure", "required": ["contactArn"], "members": {"contactArn": {"shape": "ContactArn", "documentation": "<p>A unique identifier of a contact in Amazon Connect.</p>"}}, "documentation": "<p>An object that represents an Amazon Connect contact object. </p>"}, "ContactArn": {"type": "string", "max": 500, "min": 1}, "ContactContent": {"type": "structure", "required": ["channel", "connectedToSystemTime", "contactArn"], "members": {"channel": {"shape": "Channel", "documentation": "<p>A list of channels to filter on for related items of type <code>Contact</code>.</p>"}, "connectedToSystemTime": {"shape": "ConnectedToSystemTime", "documentation": "<p>The difference between the <code>InitiationTimestamp</code> and the <code>DisconnectTimestamp</code> of the contact.</p>"}, "contactArn": {"shape": "ContactArn", "documentation": "<p>A unique identifier of a contact in Amazon Connect.</p>"}}, "documentation": "<p>An object that represents a content of an Amazon Connect contact object.</p>"}, "ContactFilter": {"type": "structure", "members": {"channel": {"shape": "ContactFilterChannelList", "documentation": "<p>A list of channels to filter on for related items of type <code>Contact</code>.</p>"}, "contactArn": {"shape": "ContactArn", "documentation": "<p>A unique identifier of a contact in Amazon Connect.</p>"}}, "documentation": "<p>A filter for related items of type <code>Contact</code>.</p>"}, "ContactFilterChannelList": {"type": "list", "member": {"shape": "Channel"}, "max": 3, "min": 0}, "CreateCaseRequest": {"type": "structure", "required": ["domainId", "fields", "templateId"], "members": {"clientToken": {"shape": "CreateCaseRequestClientTokenString", "documentation": "<p>A unique, case-sensitive identifier that you provide to ensure the idempotency of the request. If not provided, the Amazon Web Services SDK populates this field. For more information about idempotency, see <a href=\"https://aws.amazon.com/builders-library/making-retries-safe-with-idempotent-APIs/\">Making retries safe with idempotent APIs</a>.</p>", "idempotencyToken": true}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "fields": {"shape": "CreateCaseRequestFieldsList", "documentation": "<p>An array of objects with field ID (matching ListFields/DescribeField) and value union data.</p>"}, "templateId": {"shape": "TemplateId", "documentation": "<p>A unique identifier of a template.</p>"}}}, "CreateCaseRequestClientTokenString": {"type": "string", "max": 64, "min": 0}, "CreateCaseRequestFieldsList": {"type": "list", "member": {"shape": "FieldValue"}, "max": 100, "min": 0}, "CreateCaseResponse": {"type": "structure", "required": ["caseArn", "caseId"], "members": {"caseArn": {"shape": "CaseArn", "documentation": "<p>The Amazon Resource Name (ARN) of the case.</p>"}, "caseId": {"shape": "CaseId", "documentation": "<p>A unique identifier of the case.</p>"}}}, "CreateDomainRequest": {"type": "structure", "required": ["name"], "members": {"name": {"shape": "DomainName", "documentation": "<p>The name for your Cases domain. It must be unique for your Amazon Web Services account.</p>"}}}, "CreateDomainResponse": {"type": "structure", "required": ["domainArn", "domainId", "domainStatus"], "members": {"domainArn": {"shape": "DomainArn", "documentation": "<p>The Amazon Resource Name (ARN) for the Cases domain.</p>"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>"}, "domainStatus": {"shape": "DomainStatus", "documentation": "<p>The status of the domain.</p>"}}}, "CreateFieldRequest": {"type": "structure", "required": ["domainId", "name", "type"], "members": {"description": {"shape": "FieldDescription", "documentation": "<p>The description of the field.</p>"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "name": {"shape": "FieldName", "documentation": "<p>The name of the field.</p>"}, "type": {"shape": "FieldType", "documentation": "<p>Defines the data type, some system constraints, and default display of the field.</p>"}}}, "CreateFieldResponse": {"type": "structure", "required": ["fieldArn", "fieldId"], "members": {"fieldArn": {"shape": "FieldArn", "documentation": "<p>The Amazon Resource Name (ARN) of the field.</p>"}, "fieldId": {"shape": "FieldId", "documentation": "<p>The unique identifier of a field.</p>"}}}, "CreateLayoutRequest": {"type": "structure", "required": ["content", "domainId", "name"], "members": {"content": {"shape": "LayoutContent", "documentation": "<p>Information about which fields will be present in the layout, and information about the order of the fields.</p>"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "name": {"shape": "LayoutName", "documentation": "<p>The name of the layout. It must be unique for the Cases domain.</p>"}}}, "CreateLayoutResponse": {"type": "structure", "required": ["layoutArn", "layoutId"], "members": {"layoutArn": {"shape": "LayoutArn", "documentation": "<p>The Amazon Resource Name (ARN) of the newly created layout.</p>"}, "layoutId": {"shape": "LayoutId", "documentation": "<p>The unique identifier of the layout.</p>"}}}, "CreateRelatedItemRequest": {"type": "structure", "required": ["caseId", "content", "domainId", "type"], "members": {"caseId": {"shape": "CaseId", "documentation": "<p>A unique identifier of the case.</p>", "location": "uri", "locationName": "caseId"}, "content": {"shape": "RelatedItemInputContent", "documentation": "<p>The content of a related item to be created.</p>"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "type": {"shape": "RelatedItemType", "documentation": "<p>The type of a related item.</p>"}}}, "CreateRelatedItemResponse": {"type": "structure", "required": ["relatedItemArn", "relatedItemId"], "members": {"relatedItemArn": {"shape": "RelatedItemArn", "documentation": "<p>The Amazon Resource Name (ARN) of the related item.</p>"}, "relatedItemId": {"shape": "RelatedItemId", "documentation": "<p>The unique identifier of the related item.</p>"}}}, "CreateTemplateRequest": {"type": "structure", "required": ["domainId", "name"], "members": {"description": {"shape": "TemplateDescription", "documentation": "<p>A brief description of the template.</p>"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "layoutConfiguration": {"shape": "LayoutConfiguration", "documentation": "<p>Configuration of layouts associated to the template.</p>"}, "name": {"shape": "TemplateName", "documentation": "<p>A name for the template. It must be unique per domain.</p>"}, "requiredFields": {"shape": "RequiredFieldList", "documentation": "<p>A list of fields that must contain a value for a case to be successfully created with this template.</p>"}, "status": {"shape": "TemplateStatus", "documentation": "<p>The status of the template.</p>"}}}, "CreateTemplateResponse": {"type": "structure", "required": ["templateArn", "templateId"], "members": {"templateArn": {"shape": "TemplateArn", "documentation": "<p>The Amazon Resource Name (ARN) of the newly created template.</p>"}, "templateId": {"shape": "TemplateId", "documentation": "<p>A unique identifier of a template.</p>"}}}, "CreatedTime": {"type": "timestamp", "timestampFormat": "iso8601"}, "DomainArn": {"type": "string", "max": 500, "min": 1}, "DomainId": {"type": "string", "max": 500, "min": 1}, "DomainName": {"type": "string", "max": 100, "min": 1, "pattern": "^.*[\\S]$"}, "DomainStatus": {"type": "string", "enum": ["Active", "CreationInProgress", "CreationFailed"]}, "DomainSummary": {"type": "structure", "required": ["domainArn", "domainId", "name"], "members": {"domainArn": {"shape": "DomainArn", "documentation": "<p>The Amazon Resource Name (ARN) of the domain.</p>"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the domain.</p>"}, "name": {"shape": "DomainName", "documentation": "<p>The name of the domain.</p>"}}, "documentation": "<p>Object for the summarized details of the domain.</p>"}, "DomainSummaryList": {"type": "list", "member": {"shape": "DomainSummary"}}, "Double": {"type": "double", "box": true}, "EventBridgeConfiguration": {"type": "structure", "required": ["enabled"], "members": {"enabled": {"shape": "Boolean", "documentation": "<p>Indicates whether the to broadcast case event data to the customer.</p>"}, "includedData": {"shape": "EventIncludedData", "documentation": "<p>Details of what case and related item data is published through the case event stream.</p>"}}, "documentation": "<p>Configuration to enable EventBridge case event delivery and determine what data is delivered.</p>"}, "EventIncludedData": {"type": "structure", "members": {"caseData": {"shape": "CaseEventIncludedData", "documentation": "<p>Details of what case data is published through the case event stream.</p>"}, "relatedItemData": {"shape": "RelatedItemEventIncludedData", "documentation": "<p>Details of what related item data is published through the case event stream.</p>"}}, "documentation": "<p>Details of what case and related item data is published through the case event stream.</p>"}, "FieldArn": {"type": "string", "max": 500, "min": 1}, "FieldDescription": {"type": "string", "max": 255, "min": 0}, "FieldError": {"type": "structure", "required": ["errorCode", "id"], "members": {"errorCode": {"shape": "String", "documentation": "<p>The error code from getting a field.</p>"}, "id": {"shape": "FieldId", "documentation": "<p>The field identifier that caused the error.</p>"}, "message": {"shape": "String", "documentation": "<p>The error message from getting a field.</p>"}}, "documentation": "<p>Object for errors on fields.</p>"}, "FieldFilter": {"type": "structure", "members": {"contains": {"shape": "FieldValue", "documentation": "<p>Object containing field identifier and value information.</p>"}, "equalTo": {"shape": "FieldValue", "documentation": "<p>Object containing field identifier and value information.</p>"}, "greaterThan": {"shape": "FieldValue", "documentation": "<p>Object containing field identifier and value information.</p>"}, "greaterThanOrEqualTo": {"shape": "FieldValue", "documentation": "<p>Object containing field identifier and value information.</p>"}, "lessThan": {"shape": "FieldValue", "documentation": "<p>Object containing field identifier and value information.</p>"}, "lessThanOrEqualTo": {"shape": "FieldValue", "documentation": "<p>Object containing field identifier and value information. </p>"}}, "documentation": "<p>A filter for fields. Only one value can be provided.</p>", "union": true}, "FieldGroup": {"type": "structure", "required": ["fields"], "members": {"fields": {"shape": "FieldGroupFieldsList", "documentation": "<p>Represents an ordered list containing field related information.</p>"}, "name": {"shape": "FieldGroupNameString", "documentation": "<p>Name of the field group.</p>"}}, "documentation": "<p>Object for a group of fields and associated properties.</p>"}, "FieldGroupFieldsList": {"type": "list", "member": {"shape": "FieldItem"}, "max": 100, "min": 0}, "FieldGroupNameString": {"type": "string", "max": 100, "min": 0}, "FieldId": {"type": "string", "max": 500, "min": 1}, "FieldIdentifier": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "FieldId", "documentation": "<p>Unique identifier of a field.</p>"}}, "documentation": "<p>Object for unique identifier of a field.</p>"}, "FieldItem": {"type": "structure", "required": ["id"], "members": {"id": {"shape": "FieldId", "documentation": "<p>Unique identifier of a field.</p>"}}, "documentation": "<p>Object for field related information.</p>"}, "FieldName": {"type": "string", "max": 100, "min": 1, "pattern": "^.*[\\S]$"}, "FieldNamespace": {"type": "string", "enum": ["System", "Custom"]}, "FieldOption": {"type": "structure", "required": ["active", "name", "value"], "members": {"active": {"shape": "Boolean", "documentation": "<p>Describes whether the <code>FieldOption</code> is active (displayed) or inactive.</p>"}, "name": {"shape": "FieldOptionName", "documentation": "<p> <code>FieldOptionName</code> has max length 100 and disallows trailing spaces.</p>"}, "value": {"shape": "FieldOptionValue", "documentation": "<p> <code>FieldOptionValue</code> has max length 100 and must be alphanumeric with hyphens and underscores.</p>"}}, "documentation": "<p>Object for field Options information.</p>"}, "FieldOptionError": {"type": "structure", "required": ["errorCode", "message", "value"], "members": {"errorCode": {"shape": "String", "documentation": "<p>Error code from creating or updating field option.</p>"}, "message": {"shape": "String", "documentation": "<p>Error message from creating or updating field option.</p>"}, "value": {"shape": "FieldOptionValue", "documentation": "<p>The field option value that caused the error.</p>"}}, "documentation": "<p>Object for field Options errors.</p>"}, "FieldOptionName": {"type": "string", "max": 100, "min": 1, "pattern": "^.*[\\S]$"}, "FieldOptionValue": {"type": "string", "max": 100, "min": 1, "pattern": "^.*[\\S]$"}, "FieldOptionsList": {"type": "list", "member": {"shape": "FieldOption"}}, "FieldSummary": {"type": "structure", "required": ["fieldArn", "fieldId", "name", "namespace", "type"], "members": {"fieldArn": {"shape": "FieldArn", "documentation": "<p>The Amazon Resource Name (ARN) of the field.</p>"}, "fieldId": {"shape": "FieldId", "documentation": "<p>The unique identifier of a field.</p>"}, "name": {"shape": "FieldName", "documentation": "<p>Name of the field.</p>"}, "namespace": {"shape": "FieldNamespace", "documentation": "<p>The namespace of a field.</p>"}, "type": {"shape": "FieldType", "documentation": "<p>The type of a field.</p>"}}, "documentation": "<p>Object for the summarized details of the field.</p>"}, "FieldType": {"type": "string", "enum": ["Text", "Number", "Boolean", "DateTime", "SingleSelect"]}, "FieldValue": {"type": "structure", "required": ["id", "value"], "members": {"id": {"shape": "FieldId", "documentation": "<p>Unique identifier of a field.</p>"}, "value": {"shape": "FieldValueUnion", "documentation": "<p>Union of potential field value types.</p>"}}, "documentation": "<p>Object for case field values.</p>"}, "FieldValueUnion": {"type": "structure", "members": {"booleanValue": {"shape": "Boolean", "documentation": "<p>Can be either null, or have a Boolean value type. Only one value can be provided.</p>"}, "doubleValue": {"shape": "Double", "documentation": "<p>Can be either null, or have a Double number value type. Only one value can be provided.</p>"}, "stringValue": {"shape": "FieldValueUnionStringValueString", "documentation": "<p>String value type.</p>"}}, "documentation": "<p>Object to store union of Field values.</p>", "union": true}, "FieldValueUnionStringValueString": {"type": "string", "max": 500, "min": 0}, "GetCaseEventConfigurationRequest": {"type": "structure", "required": ["domainId"], "members": {"domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}}}, "GetCaseEventConfigurationResponse": {"type": "structure", "required": ["eventBridge"], "members": {"eventBridge": {"shape": "EventBridgeConfiguration", "documentation": "<p>Configuration to enable EventBridge case event delivery and determine what data is delivered.</p>"}}}, "GetCaseRequest": {"type": "structure", "required": ["caseId", "domainId", "fields"], "members": {"caseId": {"shape": "CaseId", "documentation": "<p>A unique identifier of the case.</p>", "location": "uri", "locationName": "caseId"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "fields": {"shape": "GetCaseRequestFieldsList", "documentation": "<p>A list of unique field identifiers. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>"}}}, "GetCaseRequestFieldsList": {"type": "list", "member": {"shape": "FieldIdentifier"}, "max": 100, "min": 1}, "GetCaseResponse": {"type": "structure", "required": ["fields", "templateId"], "members": {"fields": {"shape": "GetCaseResponseFieldsList", "documentation": "<p>A list of detailed field information. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. This is null if there are no more results to return.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A map of of key-value pairs that represent tags on a resource. Tags are used to organize, track, or control access for this resource.</p>"}, "templateId": {"shape": "TemplateId", "documentation": "<p>A unique identifier of a template.</p>"}}}, "GetCaseResponseFieldsList": {"type": "list", "member": {"shape": "FieldValue"}, "max": 100, "min": 0}, "GetDomainRequest": {"type": "structure", "required": ["domainId"], "members": {"domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}}}, "GetDomainResponse": {"type": "structure", "required": ["createdTime", "domainArn", "domainId", "domainStatus", "name"], "members": {"createdTime": {"shape": "CreatedTime", "documentation": "<p>The timestamp when the Cases domain was created.</p>"}, "domainArn": {"shape": "DomainArn", "documentation": "<p>The Amazon Resource Name (ARN) for the Cases domain.</p>"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>"}, "domainStatus": {"shape": "DomainStatus", "documentation": "<p>The status of the Cases domain.</p>"}, "name": {"shape": "DomainName", "documentation": "<p>The name of the Cases domain.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A map of of key-value pairs that represent tags on a resource. Tags are used to organize, track, or control access for this resource.</p>"}}}, "GetFieldResponse": {"type": "structure", "required": ["fieldArn", "fieldId", "name", "namespace", "type"], "members": {"description": {"shape": "FieldDescription", "documentation": "<p>Description of the field.</p>"}, "fieldArn": {"shape": "FieldArn", "documentation": "<p>The Amazon Resource Name (ARN) of the field.</p>"}, "fieldId": {"shape": "FieldId", "documentation": "<p>Unique identifier of the field.</p>"}, "name": {"shape": "FieldName", "documentation": "<p>Name of the field.</p>"}, "namespace": {"shape": "FieldNamespace", "documentation": "<p>Namespace of the field.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A map of of key-value pairs that represent tags on a resource. Tags are used to organize, track, or control access for this resource.</p>"}, "type": {"shape": "FieldType", "documentation": "<p>Type of the field.</p>"}}, "documentation": "<p>Object to store detailed field information.</p>"}, "GetLayoutRequest": {"type": "structure", "required": ["domainId", "layoutId"], "members": {"domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "layoutId": {"shape": "LayoutId", "documentation": "<p>The unique identifier of the layout.</p>", "location": "uri", "locationName": "layoutId"}}}, "GetLayoutResponse": {"type": "structure", "required": ["content", "layoutArn", "layoutId", "name"], "members": {"content": {"shape": "LayoutContent", "documentation": "<p>Information about which fields will be present in the layout, the order of the fields, and read-only attribute of the field. </p>"}, "layoutArn": {"shape": "LayoutArn", "documentation": "<p>The Amazon Resource Name (ARN) of the newly created layout.</p>"}, "layoutId": {"shape": "LayoutId", "documentation": "<p>The unique identifier of the layout.</p>"}, "name": {"shape": "LayoutName", "documentation": "<p>The name of the layout. It must be unique.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A map of of key-value pairs that represent tags on a resource. Tags are used to organize, track, or control access for this resource.</p>"}}}, "GetTemplateRequest": {"type": "structure", "required": ["domainId", "templateId"], "members": {"domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "templateId": {"shape": "TemplateId", "documentation": "<p>A unique identifier of a template.</p>", "location": "uri", "locationName": "templateId"}}}, "GetTemplateResponse": {"type": "structure", "required": ["name", "status", "templateArn", "templateId"], "members": {"description": {"shape": "TemplateDescription", "documentation": "<p>A brief description of the template.</p>"}, "layoutConfiguration": {"shape": "LayoutConfiguration", "documentation": "<p>Configuration of layouts associated to the template.</p>"}, "name": {"shape": "TemplateName", "documentation": "<p>The name of the template.</p>"}, "requiredFields": {"shape": "RequiredFieldList", "documentation": "<p>A list of fields that must contain a value for a case to be successfully created with this template.</p>"}, "status": {"shape": "TemplateStatus", "documentation": "<p>The status of the template.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A map of of key-value pairs that represent tags on a resource. Tags are used to organize, track, or control access for this resource.</p>"}, "templateArn": {"shape": "TemplateArn", "documentation": "<p>The Amazon Resource Name (ARN) of the template.</p>"}, "templateId": {"shape": "TemplateId", "documentation": "<p>A unique identifier of a template.</p>"}}}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>Advice to clients on when the call can be safely retried.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>We couldn't process your request because of an issue with the server. Try again later.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "LayoutArn": {"type": "string", "max": 500, "min": 1}, "LayoutConfiguration": {"type": "structure", "members": {"defaultLayout": {"shape": "LayoutId", "documentation": "<p> Unique identifier of a layout. </p>"}}, "documentation": "<p>Object to store configuration of layouts associated to the template.</p>"}, "LayoutContent": {"type": "structure", "members": {"basic": {"shape": "BasicLayout", "documentation": "<p>Content specific to <code>BasicLayout</code> type. It configures fields in the top panel and More Info tab of Cases user interface.</p>"}}, "documentation": "<p>Object to store union of different versions of layout content.</p>", "union": true}, "LayoutId": {"type": "string", "max": 500, "min": 1}, "LayoutName": {"type": "string", "max": 100, "min": 1, "pattern": "^.*[\\S]$"}, "LayoutSections": {"type": "structure", "members": {"sections": {"shape": "SectionsList"}}, "documentation": "<p>Ordered list containing different kinds of sections that can be added. A LayoutSections object can only contain one section.</p>"}, "LayoutSummary": {"type": "structure", "required": ["layoutArn", "layoutId", "name"], "members": {"layoutArn": {"shape": "LayoutArn", "documentation": "<p>The Amazon Resource Name (ARN) of the layout.</p>"}, "layoutId": {"shape": "LayoutId", "documentation": "<p>The unique identifier for of the layout.</p>"}, "name": {"shape": "LayoutName", "documentation": "<p>The name of the layout.</p>"}}, "documentation": "<p>Object for the summarized details of the layout.</p>"}, "LayoutSummaryList": {"type": "list", "member": {"shape": "LayoutSummary"}}, "ListCasesForContactRequest": {"type": "structure", "required": ["contactArn", "domainId"], "members": {"contactArn": {"shape": "ContactArn", "documentation": "<p>A unique identifier of a contact in Amazon Connect.</p>"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "maxResults": {"shape": "ListCasesForContactRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return per page.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>"}}}, "ListCasesForContactRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 10, "min": 1}, "ListCasesForContactResponse": {"type": "structure", "required": ["cases"], "members": {"cases": {"shape": "ListCasesForContactResponseCasesList", "documentation": "<p>A list of Case summary information.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. This is null if there are no more results to return.</p>"}}}, "ListCasesForContactResponseCasesList": {"type": "list", "member": {"shape": "CaseSummary"}, "max": 10, "min": 0}, "ListDomainsRequest": {"type": "structure", "members": {"maxResults": {"shape": "ListDomainsRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return per page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListDomainsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 10, "min": 1}, "ListDomainsResponse": {"type": "structure", "required": ["domains"], "members": {"domains": {"shape": "DomainSummaryList", "documentation": "<p>The Cases domain.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. This is null if there are no more results to return.</p>"}}}, "ListFieldOptionsRequest": {"type": "structure", "required": ["domainId", "fieldId"], "members": {"domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "fieldId": {"shape": "FieldId", "documentation": "<p>The unique identifier of a field.</p>", "location": "uri", "locationName": "fieldId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "values": {"shape": "ValuesList", "documentation": "<p>A list of <code>FieldOption</code> values to filter on for <code>ListFieldOptions</code>.</p>", "location": "querystring", "locationName": "values"}}}, "ListFieldOptionsResponse": {"type": "structure", "required": ["options"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. This is null if there are no more results to return.</p>"}, "options": {"shape": "FieldOptionsList", "documentation": "<p>A list of <code>FieldOption</code> objects.</p>"}}}, "ListFieldsRequest": {"type": "structure", "required": ["domainId"], "members": {"domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListFieldsResponse": {"type": "structure", "required": ["fields"], "members": {"fields": {"shape": "ListFieldsResponseFieldsList", "documentation": "<p>List of detailed field information.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. This is null if there are no more results to return.</p>"}}}, "ListFieldsResponseFieldsList": {"type": "list", "member": {"shape": "FieldSummary"}, "max": 100, "min": 0}, "ListLayoutsRequest": {"type": "structure", "required": ["domainId"], "members": {"domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}}}, "ListLayoutsResponse": {"type": "structure", "required": ["layouts"], "members": {"layouts": {"shape": "LayoutSummaryList", "documentation": "<p>The layouts for the domain.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. This is null if there are no more results to return.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["arn"], "members": {"arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN)</p>", "location": "uri", "locationName": "arn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "Tags", "documentation": "<p>A map of of key-value pairs that represent tags on a resource. Tags are used to organize, track, or control access for this resource.</p>"}}}, "ListTemplatesRequest": {"type": "structure", "required": ["domainId"], "members": {"domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "maxResults": {"shape": "MaxResults", "documentation": "<p>The maximum number of results to return per page.</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>", "location": "querystring", "locationName": "nextToken"}, "status": {"shape": "TemplateStatusFilters", "documentation": "<p>A list of status values to filter on.</p>", "location": "querystring", "locationName": "status"}}}, "ListTemplatesResponse": {"type": "structure", "required": ["templates"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. This is null if there are no more results to return.</p>"}, "templates": {"shape": "ListTemplatesResponseTemplatesList", "documentation": "<p>List of template summary objects.</p>"}}}, "ListTemplatesResponseTemplatesList": {"type": "list", "member": {"shape": "TemplateSummary"}, "max": 100, "min": 0}, "MaxResults": {"type": "integer", "box": true, "max": 100, "min": 1}, "NextToken": {"type": "string", "max": 9000, "min": 0}, "Order": {"type": "string", "enum": ["Asc", "Desc"]}, "PutCaseEventConfigurationRequest": {"type": "structure", "required": ["domainId", "eventBridge"], "members": {"domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "eventBridge": {"shape": "EventBridgeConfiguration", "documentation": "<p>Configuration to enable EventBridge case event delivery and determine what data is delivered.</p>"}}}, "PutCaseEventConfigurationResponse": {"type": "structure", "members": {}}, "RelatedItemArn": {"type": "string", "max": 500, "min": 1}, "RelatedItemContent": {"type": "structure", "members": {"comment": {"shape": "CommentC<PERSON>nt", "documentation": "<p>Represents the content of a comment to be returned to agents.</p>"}, "contact": {"shape": "ContactContent", "documentation": "<p>Represents the content of a contact to be returned to agents.</p>"}}, "documentation": "<p>Represents the content of a particular type of related item.</p>", "union": true}, "RelatedItemEventIncludedData": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"includeContent": {"shape": "Boolean", "documentation": "<p>Details of what related item data is published through the case event stream.</p>"}}, "documentation": "<p>Details of what related item data is published through the case event stream.</p>"}, "RelatedItemId": {"type": "string", "max": 500, "min": 1}, "RelatedItemInputContent": {"type": "structure", "members": {"comment": {"shape": "CommentC<PERSON>nt", "documentation": "<p>Represents the content of a comment to be returned to agents.</p>"}, "contact": {"shape": "Contact", "documentation": "<p>Object representing a contact in Amazon Connect as an API request field.</p>"}}, "documentation": "<p>Represents the content of a related item to be created.</p>", "union": true}, "RelatedItemType": {"type": "string", "enum": ["Contact", "Comment"]}, "RelatedItemTypeFilter": {"type": "structure", "members": {"comment": {"shape": "Comment<PERSON>ilter", "documentation": "<p>A filter for related items of type <code>Comment</code>.</p>"}, "contact": {"shape": "Contact<PERSON><PERSON>er", "documentation": "<p>A filter for related items of type <code>Contact</code>.</p>"}}, "documentation": "<p>The list of types of related items and their parameters to use for filtering.</p>", "union": true}, "RequiredField": {"type": "structure", "required": ["fieldId"], "members": {"fieldId": {"shape": "FieldId", "documentation": "<p>Unique identifier of a field.</p>"}}, "documentation": "<p>List of fields that must have a value provided to create a case.</p>"}, "RequiredFieldList": {"type": "list", "member": {"shape": "RequiredField"}, "max": 100, "min": 0}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String"}, "resourceId": {"shape": "String", "documentation": "<p>Unique identifier of the resource affected.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>Type of the resource affected.</p>"}}, "documentation": "<p>We couldn't find the requested resource. Check that your resources exists and were created in the same Amazon Web Services Region as your request, and try your request again.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "SearchCasesRequest": {"type": "structure", "required": ["domainId"], "members": {"domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "fields": {"shape": "SearchCasesRequestFieldsList", "documentation": "<p>The list of field identifiers to be returned as part of the response.</p>"}, "filter": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>A list of filter objects.</p>"}, "maxResults": {"shape": "SearchCasesRequestMaxResultsInteger", "documentation": "<p>The maximum number of cases to return. The current maximum supported value is 25. This is also the default value when no other value is provided.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>"}, "searchTerm": {"shape": "SearchCasesRequestSearchTermString", "documentation": "<p>A word or phrase used to perform a quick search.</p>"}, "sorts": {"shape": "SearchCasesRequestSortsList", "documentation": "<p>A list of sorts where each sort specifies a field and their sort order to be applied to the results. </p>"}}}, "SearchCasesRequestFieldsList": {"type": "list", "member": {"shape": "FieldIdentifier"}, "max": 10, "min": 0}, "SearchCasesRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 25, "min": 1}, "SearchCasesRequestSearchTermString": {"type": "string", "max": 255, "min": 0}, "SearchCasesRequestSortsList": {"type": "list", "member": {"shape": "Sort"}, "documentation": "<p>/@documentation(&quot;The templateId&quot;)</p>", "max": 2, "min": 0}, "SearchCasesResponse": {"type": "structure", "required": ["cases"], "members": {"cases": {"shape": "SearchCasesResponseCasesList", "documentation": "<p>A list of case documents where each case contains the properties <code>CaseId</code> and <code>Fields</code> where each field is a complex union structure. </p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. This is null if there are no more results to return.</p>"}}}, "SearchCasesResponseCasesList": {"type": "list", "member": {"shape": "SearchCasesResponseItem"}, "max": 25, "min": 0}, "SearchCasesResponseItem": {"type": "structure", "required": ["caseId", "fields", "templateId"], "members": {"caseId": {"shape": "CaseId", "documentation": "<p>A unique identifier of the case.</p>"}, "fields": {"shape": "SearchCasesResponseItemFieldsList", "documentation": "<p>List of case field values.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A map of of key-value pairs that represent tags on a resource. Tags are used to organize, track, or control access for this resource.</p>"}, "templateId": {"shape": "TemplateId", "documentation": "<p>A unique identifier of a template.</p>"}}, "documentation": "<p>A list of items that represent cases.</p>"}, "SearchCasesResponseItemFieldsList": {"type": "list", "member": {"shape": "FieldValue"}, "max": 10, "min": 0}, "SearchRelatedItemsRequest": {"type": "structure", "required": ["caseId", "domainId"], "members": {"caseId": {"shape": "CaseId", "documentation": "<p>A unique identifier of the case.</p>", "location": "uri", "locationName": "caseId"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "filters": {"shape": "SearchRelatedItemsRequestFiltersList", "documentation": "<p>The list of types of related items and their parameters to use for filtering.</p>"}, "maxResults": {"shape": "SearchRelatedItemsRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return per page.</p>"}, "nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>"}}}, "SearchRelatedItemsRequestFiltersList": {"type": "list", "member": {"shape": "RelatedItemTypeFilter"}, "max": 10, "min": 0}, "SearchRelatedItemsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 25, "min": 1}, "SearchRelatedItemsResponse": {"type": "structure", "required": ["relatedItems"], "members": {"nextToken": {"shape": "NextToken", "documentation": "<p>The token for the next set of results. This is null if there are no more results to return.</p>"}, "relatedItems": {"shape": "SearchRelatedItemsResponseRelatedItemsList", "documentation": "<p>A list of items related to a case. </p>"}}}, "SearchRelatedItemsResponseItem": {"type": "structure", "required": ["associationTime", "content", "relatedItemId", "type"], "members": {"associationTime": {"shape": "AssociationTime", "documentation": "<p>Time at which a related item was associated with a case.</p>"}, "content": {"shape": "RelatedItemContent", "documentation": "<p>Represents the content of a particular type of related item.</p>"}, "relatedItemId": {"shape": "RelatedItemId", "documentation": "<p>Unique identifier of a related item.</p>"}, "tags": {"shape": "Tags", "documentation": "<p>A map of of key-value pairs that represent tags on a resource. Tags are used to organize, track, or control access for this resource.</p>"}, "type": {"shape": "RelatedItemType", "documentation": "<p>Type of a related item.</p>"}}, "documentation": "<p>A list of items that represent RelatedItems.</p>"}, "SearchRelatedItemsResponseRelatedItemsList": {"type": "list", "member": {"shape": "SearchRelatedItemsResponseItem"}, "max": 25, "min": 0}, "Section": {"type": "structure", "members": {"fieldGroup": {"shape": "FieldGroup", "documentation": "<p>Consists of a group of fields and associated properties.</p>"}}, "documentation": "<p>This represents a sections within a panel or tab of the page layout.</p>", "union": true}, "SectionsList": {"type": "list", "member": {"shape": "Section"}, "documentation": "<p>Ordered list containing different kinds of sections that can be added.</p>", "max": 1, "min": 0}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The service quota has been exceeded. For a list of service quotas, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/amazon-connect-service-limits.html\">Amazon Connect Service Quotas</a> in the <i>Amazon Connect Administrator Guide</i>.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "Sort": {"type": "structure", "required": ["fieldId", "sortOrder"], "members": {"fieldId": {"shape": "FieldId", "documentation": "<p>Unique identifier of a field.</p>"}, "sortOrder": {"shape": "Order", "documentation": "<p>A structured set of sort terms</p>"}}, "documentation": "<p>A structured set of sort terms.</p>"}, "String": {"type": "string"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!aws:)[a-zA-Z+-=._:/]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["arn", "tags"], "members": {"arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN)</p>", "location": "uri", "locationName": "arn"}, "tags": {"shape": "Tags", "documentation": "<p>A map of of key-value pairs that represent tags on a resource. Tags are used to organize, track, or control access for this resource.</p>"}}}, "Tags": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "TemplateArn": {"type": "string", "max": 500, "min": 1}, "TemplateDescription": {"type": "string", "max": 255, "min": 0}, "TemplateId": {"type": "string", "max": 500, "min": 1}, "TemplateName": {"type": "string", "max": 100, "min": 1, "pattern": "^.*[\\S]$"}, "TemplateStatus": {"type": "string", "documentation": "<p>Status of a template</p>", "enum": ["Active", "Inactive"]}, "TemplateStatusFilters": {"type": "list", "member": {"shape": "TemplateStatus"}, "documentation": "<p>List of filters used on the ListTemplates result set</p>", "max": 2, "min": 1}, "TemplateSummary": {"type": "structure", "required": ["name", "status", "templateArn", "templateId"], "members": {"name": {"shape": "TemplateName", "documentation": "<p>The template name.</p>"}, "status": {"shape": "TemplateStatus", "documentation": "<p>The status of the template.</p>"}, "templateArn": {"shape": "TemplateArn", "documentation": "<p>The Amazon Resource Name (ARN) of the template.</p>"}, "templateId": {"shape": "TemplateId", "documentation": "<p>The unique identifier for the template.</p>"}}, "documentation": "<p>Template summary information.</p>"}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The rate has been exceeded for this API. Please try again after a few minutes.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "UntagResourceRequest": {"type": "structure", "required": ["arn", "tagKeys"], "members": {"arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN)</p>", "location": "uri", "locationName": "arn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>List of tag keys.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UpdateCaseRequest": {"type": "structure", "required": ["caseId", "domainId", "fields"], "members": {"caseId": {"shape": "CaseId", "documentation": "<p>A unique identifier of the case.</p>", "location": "uri", "locationName": "caseId"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "fields": {"shape": "UpdateCaseRequestFieldsList", "documentation": "<p>An array of objects with <code>fieldId</code> (matching ListFields/DescribeField) and value union data, structured identical to <code>CreateCase</code>.</p>"}}}, "UpdateCaseRequestFieldsList": {"type": "list", "member": {"shape": "FieldValue"}, "max": 100, "min": 0}, "UpdateCaseResponse": {"type": "structure", "members": {}}, "UpdateFieldRequest": {"type": "structure", "required": ["domainId", "fieldId"], "members": {"description": {"shape": "FieldDescription", "documentation": "<p>The description of a field.</p>"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "fieldId": {"shape": "FieldId", "documentation": "<p>The unique identifier of a field.</p>", "location": "uri", "locationName": "fieldId"}, "name": {"shape": "FieldName", "documentation": "<p>The name of the field.</p>"}}}, "UpdateFieldResponse": {"type": "structure", "members": {}}, "UpdateLayoutRequest": {"type": "structure", "required": ["domainId", "layoutId"], "members": {"content": {"shape": "LayoutContent", "documentation": "<p>Information about which fields will be present in the layout, the order of the fields, and a read-only attribute of the field. </p>"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "layoutId": {"shape": "LayoutId", "documentation": "<p>The unique identifier of the layout.</p>", "location": "uri", "locationName": "layoutId"}, "name": {"shape": "LayoutName", "documentation": "<p>The name of the layout. It must be unique per domain.</p>"}}}, "UpdateLayoutResponse": {"type": "structure", "members": {}}, "UpdateTemplateRequest": {"type": "structure", "required": ["domainId", "templateId"], "members": {"description": {"shape": "TemplateDescription", "documentation": "<p>A brief description of the template.</p>"}, "domainId": {"shape": "DomainId", "documentation": "<p>The unique identifier of the Cases domain. </p>", "location": "uri", "locationName": "domainId"}, "layoutConfiguration": {"shape": "LayoutConfiguration", "documentation": "<p>Configuration of layouts associated to the template.</p>"}, "name": {"shape": "TemplateName", "documentation": "<p>The name of the template. It must be unique per domain.</p>"}, "requiredFields": {"shape": "RequiredFieldList", "documentation": "<p>A list of fields that must contain a value for a case to be successfully created with this template.</p>"}, "status": {"shape": "TemplateStatus", "documentation": "<p>The status of the template.</p>"}, "templateId": {"shape": "TemplateId", "documentation": "<p>A unique identifier for the template.</p>", "location": "uri", "locationName": "templateId"}}}, "UpdateTemplateResponse": {"type": "structure", "members": {}}, "ValidationException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String"}}, "documentation": "<p>The request isn't valid. Check the syntax and try again.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "Value": {"type": "string", "max": 100, "min": 0}, "ValuesList": {"type": "list", "member": {"shape": "Value"}, "max": 1, "min": 0}}, "documentation": "<p>Welcome to the Amazon Connect Cases API Reference. This guide provides information about the Amazon Connect Cases API, which you can use to create, update, get, and list Cases domains, fields, field options, layouts, templates, cases, related items, and tags.</p> <pre><code> &lt;p&gt;For more information about Amazon Connect Cases, see &lt;a href=&quot;https://docs.aws.amazon.com/connect/latest/adminguide/cases.html&quot;&gt;Amazon Connect Cases&lt;/a&gt; in the &lt;i&gt;Amazon Connect Administrator Guide&lt;/i&gt;. &lt;/p&gt; </code></pre>"}