{"version": "1.0", "examples": {"AttachInstances": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "InstanceIds": ["i-93633f9b"]}, "comments": {"input": {}, "output": {}}, "description": "This example attaches the specified instance to the specified Auto Scaling group.", "id": "autoscaling-attach-instances-1", "title": "To attach an instance to an Auto Scaling group"}], "AttachLoadBalancerTargetGroups": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "TargetGroupARNs": ["arn:aws:elasticloadbalancing:us-west-2:************:targetgroup/my-targets/73e2d6bc24d8a067"]}, "comments": {"input": {}, "output": {}}, "description": "This example attaches the specified target group to the specified Auto Scaling group.", "id": "autoscaling-attach-load-balancer-target-groups-1", "title": "To attach a target group to an Auto Scaling group"}], "AttachLoadBalancers": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "LoadBalancerNames": ["my-load-balancer"]}, "comments": {"input": {}, "output": {}}, "description": "This example attaches the specified load balancer to the specified Auto Scaling group.", "id": "autoscaling-attach-load-balancers-1", "title": "To attach a load balancer to an Auto Scaling group"}], "CancelInstanceRefresh": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group"}, "output": {"InstanceRefreshId": "08b91cf7-8fa6-48af-b6a6-d227f40f1b9b"}, "comments": {"input": {}, "output": {}}, "description": "This example cancels an instance refresh operation in progress.", "id": "to-cancel-an-instance-refresh-1592960979817", "title": "To cancel an instance refresh"}], "CompleteLifecycleAction": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "LifecycleActionResult": "CONTINUE", "LifecycleActionToken": "bcd2f1b8-9a78-44d3-8a7a-4dd07d7cf635", "LifecycleHookName": "my-lifecycle-hook"}, "comments": {"input": {}, "output": {}}, "description": "This example notifies Auto Scaling that the specified lifecycle action is complete so that it can finish launching or terminating the instance.", "id": "autoscaling-complete-lifecycle-action-1", "title": "To complete the lifecycle action"}], "CreateAutoScalingGroup": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "LaunchTemplate": {"LaunchTemplateName": "my-template-for-auto-scaling", "Version": "$Latest"}, "MaxInstanceLifetime": 2592000, "MaxSize": 3, "MinSize": 1, "VPCZoneIdentifier": "subnet-057fa0918fEXAMPLE"}, "comments": {"input": {}, "output": {}}, "description": "This example creates an Auto Scaling group.", "id": "autoscaling-create-auto-scaling-group-1", "title": "To create an Auto Scaling group"}, {"input": {"AutoScalingGroupName": "my-auto-scaling-group", "HealthCheckGracePeriod": 300, "HealthCheckType": "ELB", "LaunchTemplate": {"LaunchTemplateName": "my-template-for-auto-scaling", "Version": "$Latest"}, "MaxSize": 3, "MinSize": 1, "TargetGroupARNs": ["arn:aws:elasticloadbalancing:us-west-2:************:targetgroup/my-targets/73e2d6bc24d8a067"], "VPCZoneIdentifier": "subnet-057fa0918fEXAMPLE, subnet-610acd08EXAMPLE"}, "comments": {"input": {}, "output": {}}, "description": "This example creates an Auto Scaling group and attaches the specified target group.", "id": "autoscaling-create-auto-scaling-group-2", "title": "To create an Auto Scaling group with an attached target group"}, {"input": {"AutoScalingGroupName": "my-asg", "DesiredCapacity": 3, "MaxSize": 5, "MinSize": 1, "MixedInstancesPolicy": {"InstancesDistribution": {"OnDemandBaseCapacity": 1, "OnDemandPercentageAboveBaseCapacity": 50, "SpotAllocationStrategy": "capacity-optimized"}, "LaunchTemplate": {"LaunchTemplateSpecification": {"LaunchTemplateName": "my-launch-template-for-x86", "Version": "$Latest"}, "Overrides": [{"InstanceType": "c6g.large", "LaunchTemplateSpecification": {"LaunchTemplateName": "my-launch-template-for-arm", "Version": "$Latest"}}, {"InstanceType": "c5.large"}, {"InstanceType": "c5a.large"}]}}, "VPCZoneIdentifier": "subnet-057fa0918fEXAMPLE, subnet-610acd08EXAMPLE"}, "comments": {"input": {}, "output": {}}, "description": "This example creates an Auto Scaling group with a mixed instances policy. It specifies the c5.large, c5a.large, and c6g.large instance types and defines a different launch template for the c6g.large instance type.", "id": "to-create-an-auto-scaling-group-with-a-mixed-instances-policy-1617815269039", "title": "To create an Auto Scaling group with a mixed instances policy"}], "CreateLaunchConfiguration": [{"input": {"IamInstanceProfile": "my-iam-role", "ImageId": "ami-12345678", "InstanceType": "m3.medium", "LaunchConfigurationName": "my-launch-config", "SecurityGroups": ["sg-eb2af88e"]}, "comments": {"input": {}, "output": {}}, "description": "This example creates a launch configuration.", "id": "autoscaling-create-launch-configuration-1", "title": "To create a launch configuration"}], "CreateOrUpdateTags": [{"input": {"Tags": [{"Key": "Role", "PropagateAtLaunch": true, "ResourceId": "my-auto-scaling-group", "ResourceType": "auto-scaling-group", "Value": "WebServer"}, {"Key": "Dept", "PropagateAtLaunch": true, "ResourceId": "my-auto-scaling-group", "ResourceType": "auto-scaling-group", "Value": "Research"}]}, "comments": {"input": {}, "output": {}}, "description": "This example adds two tags to the specified Auto Scaling group.", "id": "autoscaling-create-or-update-tags-1", "title": "To create or update tags for an Auto Scaling group"}], "DeleteAutoScalingGroup": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified Auto Scaling group.", "id": "autoscaling-delete-auto-scaling-group-1", "title": "To delete an Auto Scaling group"}, {"input": {"AutoScalingGroupName": "my-auto-scaling-group", "ForceDelete": true}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified Auto Scaling group and all its instances.", "id": "autoscaling-delete-auto-scaling-group-2", "title": "To delete an Auto Scaling group and all its instances"}], "DeleteLaunchConfiguration": [{"input": {"LaunchConfigurationName": "my-launch-config"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified launch configuration.", "id": "autoscaling-delete-launch-configuration-1", "title": "To delete a launch configuration"}], "DeleteLifecycleHook": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "LifecycleHookName": "my-lifecycle-hook"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified lifecycle hook.", "id": "autoscaling-delete-lifecycle-hook-1", "title": "To delete a lifecycle hook"}], "DeleteNotificationConfiguration": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "TopicARN": "arn:aws:sns:us-west-2:************:my-sns-topic"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified notification from the specified Auto Scaling group.", "id": "autoscaling-delete-notification-configuration-1", "title": "To delete an Auto Scaling notification"}], "DeletePolicy": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "PolicyName": "my-step-scale-out-policy"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified Auto Scaling policy.", "id": "autoscaling-delete-policy-1", "title": "To delete an Auto Scaling policy"}], "DeleteScheduledAction": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "ScheduledActionName": "my-scheduled-action"}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified scheduled action from the specified Auto Scaling group.", "id": "autoscaling-delete-scheduled-action-1", "title": "To delete a scheduled action from an Auto Scaling group"}], "DeleteTags": [{"input": {"Tags": [{"Key": "Dept", "ResourceId": "my-auto-scaling-group", "ResourceType": "auto-scaling-group", "Value": "Research"}]}, "comments": {"input": {}, "output": {}}, "description": "This example deletes the specified tag from the specified Auto Scaling group.", "id": "autoscaling-delete-tags-1", "title": "To delete a tag from an Auto Scaling group"}], "DescribeAccountLimits": [{"output": {"MaxNumberOfAutoScalingGroups": 20, "MaxNumberOfLaunchConfigurations": 100, "NumberOfAutoScalingGroups": 3, "NumberOfLaunchConfigurations": 5}, "comments": {"input": {}, "output": {}}, "description": "This example describes the Amazon EC2 Auto Scaling service quotas for your account.", "id": "autoscaling-describe-account-limits-1", "title": "To describe your Auto Scaling account limits"}], "DescribeAdjustmentTypes": [{"output": {"AdjustmentTypes": [{"AdjustmentType": "ChangeInCapacity"}, {"AdjustmentType": "ExactCapcity"}, {"AdjustmentType": "PercentChangeInCapacity"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the available adjustment types.", "id": "autoscaling-describe-adjustment-types-1", "title": "To describe the Amazon EC2 Auto Scaling adjustment types"}], "DescribeAutoScalingGroups": [{"input": {"AutoScalingGroupNames": ["my-auto-scaling-group"]}, "output": {"AutoScalingGroups": [{"AutoScalingGroupARN": "arn:aws:autoscaling:us-west-2:************:autoScalingGroup:930d940e-891e-4781-a11a-7b0acd480f03:autoScalingGroupName/my-auto-scaling-group", "AutoScalingGroupName": "my-auto-scaling-group", "AvailabilityZones": ["us-west-2c"], "CreatedTime": "2013-08-19T20:53:25.584Z", "DefaultCooldown": 300, "DesiredCapacity": 1, "EnabledMetrics": [], "HealthCheckGracePeriod": 300, "HealthCheckType": "EC2", "Instances": [{"AvailabilityZone": "us-west-2c", "HealthStatus": "Healthy", "InstanceId": "i-4ba0837f", "LaunchConfigurationName": "my-launch-config", "LifecycleState": "InService", "ProtectedFromScaleIn": false}], "LaunchConfigurationName": "my-launch-config", "LoadBalancerNames": [], "MaxSize": 1, "MinSize": 0, "NewInstancesProtectedFromScaleIn": false, "SuspendedProcesses": [], "Tags": [], "TerminationPolicies": ["<PERSON><PERSON><PERSON>"], "VPCZoneIdentifier": "subnet-12345678"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified Auto Scaling group.", "id": "autoscaling-describe-auto-scaling-groups-1", "title": "To describe an Auto Scaling group"}], "DescribeAutoScalingInstances": [{"input": {"InstanceIds": ["i-4ba0837f"]}, "output": {"AutoScalingInstances": [{"AutoScalingGroupName": "my-auto-scaling-group", "AvailabilityZone": "us-west-2c", "HealthStatus": "HEALTHY", "InstanceId": "i-4ba0837f", "LaunchConfigurationName": "my-launch-config", "LifecycleState": "InService", "ProtectedFromScaleIn": false}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified Auto Scaling instance.", "id": "autoscaling-describe-auto-scaling-instances-1", "title": "To describe one or more Auto Scaling instances"}], "DescribeAutoScalingNotificationTypes": [{"output": {"AutoScalingNotificationTypes": ["autoscaling:EC2_INSTANCE_LAUNCH", "autoscaling:EC2_INSTANCE_LAUNCH_ERROR", "autoscaling:EC2_INSTANCE_TERMINATE", "autoscaling:EC2_INSTANCE_TERMINATE_ERROR", "autoscaling:TEST_NOTIFICATION"]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the available notification types.", "id": "autoscaling-describe-auto-scaling-notification-types-1", "title": "To describe the Auto Scaling notification types"}], "DescribeInstanceRefreshes": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group"}, "output": {"InstanceRefreshes": [{"AutoScalingGroupName": "my-auto-scaling-group", "InstanceRefreshId": "08b91cf7-8fa6-48af-b6a6-d227f40f1b9b", "InstancesToUpdate": 5, "PercentageComplete": 0, "StartTime": "2020-06-02T18:11:27Z", "Status": "InProgress"}, {"AutoScalingGroupName": "my-auto-scaling-group", "EndTime": "2020-06-02T16:53:37Z", "InstanceRefreshId": "dd7728d0-5bc4-4575-96a3-1b2c52bf8bb1", "InstancesToUpdate": 0, "PercentageComplete": 100, "StartTime": "2020-06-02T16:43:19Z", "Status": "Successful"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the instance refreshes for the specified Auto Scaling group.", "id": "to-list-instance-refreshes-1592959593746", "title": "To list instance refreshes"}], "DescribeLaunchConfigurations": [{"input": {"LaunchConfigurationNames": ["my-launch-config"]}, "output": {"LaunchConfigurations": [{"AssociatePublicIpAddress": true, "BlockDeviceMappings": [], "CreatedTime": "2014-05-07T17:39:28.599Z", "EbsOptimized": false, "ImageId": "ami-043a5034", "InstanceMonitoring": {"Enabled": true}, "InstanceType": "t1.micro", "LaunchConfigurationARN": "arn:aws:autoscaling:us-west-2:************:launchConfiguration:98d3b196-4cf9-4e88-8ca1-8547c24ced8b:launchConfigurationName/my-launch-config", "LaunchConfigurationName": "my-launch-config", "SecurityGroups": ["sg-67ef0308"]}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the specified launch configuration.", "id": "autoscaling-describe-launch-configurations-1", "title": "To describe Auto Scaling launch configurations"}], "DescribeLifecycleHookTypes": [{"output": {"LifecycleHookTypes": ["autoscaling:EC2_INSTANCE_LAUNCHING", "autoscaling:EC2_INSTANCE_TERMINATING"]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the available lifecycle hook types.", "id": "autoscaling-describe-lifecycle-hook-types-1", "title": "To describe the available types of lifecycle hooks"}], "DescribeLifecycleHooks": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group"}, "output": {"LifecycleHooks": [{"AutoScalingGroupName": "my-auto-scaling-group", "DefaultResult": "ABANDON", "GlobalTimeout": 172800, "HeartbeatTimeout": 3600, "LifecycleHookName": "my-lifecycle-hook", "LifecycleTransition": "autoscaling:EC2_INSTANCE_LAUNCHING", "NotificationTargetARN": "arn:aws:sns:us-west-2:************:my-sns-topic", "RoleARN": "arn:aws:iam::************:role/my-auto-scaling-role"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the lifecycle hooks for the specified Auto Scaling group.", "id": "autoscaling-describe-lifecycle-hooks-1", "title": "To describe your lifecycle hooks"}], "DescribeLoadBalancerTargetGroups": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group"}, "output": {"LoadBalancerTargetGroups": [{"LoadBalancerTargetGroupARN": "arn:aws:elasticloadbalancing:us-west-2:************:targetgroup/my-targets/73e2d6bc24d8a067", "State": "Added"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the target groups attached to the specified Auto Scaling group.", "id": "autoscaling-describe-load-balancer-target-groups-1", "title": "To describe the target groups for an Auto Scaling group"}], "DescribeLoadBalancers": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group"}, "output": {"LoadBalancers": [{"LoadBalancerName": "my-load-balancer", "State": "Added"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the load balancers attached to the specified Auto Scaling group.", "id": "autoscaling-describe-load-balancers-1", "title": "To describe the load balancers for an Auto Scaling group"}], "DescribeMetricCollectionTypes": [{"output": {"Granularities": [{"Granularity": "1Minute"}], "Metrics": [{"Metric": "GroupMinSize"}, {"Metric": "GroupMaxSize"}, {"Metric": "GroupDesiredCapacity"}, {"Metric": "GroupInServiceInstances"}, {"Metric": "GroupPendingInstances"}, {"Metric": "GroupTerminatingInstances"}, {"Metric": "GroupStandbyInstances"}, {"Metric": "GroupTotalInstances"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the available metric collection types.", "id": "autoscaling-describe-metric-collection-types-1", "title": "To describe the Auto Scaling metric collection types"}], "DescribeNotificationConfigurations": [{"input": {"AutoScalingGroupNames": ["my-auto-scaling-group"]}, "output": {"NotificationConfigurations": [{"AutoScalingGroupName": "my-auto-scaling-group", "NotificationType": "autoscaling:TEST_NOTIFICATION", "TopicARN": "arn:aws:sns:us-west-2:************:my-sns-topic-2"}, {"AutoScalingGroupName": "my-auto-scaling-group", "NotificationType": "autoscaling:TEST_NOTIFICATION", "TopicARN": "arn:aws:sns:us-west-2:************:my-sns-topic"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the notification configurations for the specified Auto Scaling group.", "id": "autoscaling-describe-notification-configurations-1", "title": "To describe Auto Scaling notification configurations"}], "DescribePolicies": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group"}, "output": {"ScalingPolicies": [{"AdjustmentType": "ChangeInCapacity", "Alarms": [], "AutoScalingGroupName": "my-auto-scaling-group", "PolicyARN": "arn:aws:autoscaling:us-west-2:************:scalingPolicy:2233f3d7-6290-403b-b632-93c553560106:autoScalingGroupName/my-auto-scaling-group:policyName/ScaleIn", "PolicyName": "ScaleIn", "ScalingAdjustment": -1}, {"AdjustmentType": "PercentChangeInCapacity", "Alarms": [], "AutoScalingGroupName": "my-auto-scaling-group", "Cooldown": 60, "MinAdjustmentStep": 2, "PolicyARN": "arn:aws:autoscaling:us-west-2:************:scalingPolicy:2b435159-cf77-4e89-8c0e-d63b497baad7:autoScalingGroupName/my-auto-scaling-group:policyName/ScalePercentChange", "PolicyName": "ScalePercentChange", "ScalingAdjustment": 25}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the policies for the specified Auto Scaling group.", "id": "autoscaling-describe-policies-1", "title": "To describe scaling policies"}], "DescribeScalingActivities": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group"}, "output": {"Activities": [{"ActivityId": "f9f2d65b-f1f2-43e7-b46d-d86756459699", "AutoScalingGroupName": "my-auto-scaling-group", "Cause": "At 2013-08-19T20:53:25Z a user request created an AutoScalingGroup changing the desired capacity from 0 to 1.  At 2013-08-19T20:53:29Z an instance was started in response to a difference between desired and actual capacity, increasing the capacity from 0 to 1.", "Description": "Launching a new EC2 instance: i-4ba0837f", "Details": "details", "EndTime": "2013-08-19T20:54:02Z", "Progress": 100, "StartTime": "2013-08-19T20:53:29.930Z", "StatusCode": "Successful"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the scaling activities for the specified Auto Scaling group.", "id": "autoscaling-describe-scaling-activities-1", "title": "To describe the scaling activities for an Auto Scaling group"}], "DescribeScalingProcessTypes": [{"output": {"Processes": [{"ProcessName": "AZRebalance"}, {"ProcessName": "AddToLoadBalancer"}, {"ProcessName": "AlarmNotification"}, {"ProcessName": "HealthCheck"}, {"ProcessName": "Launch"}, {"ProcessName": "ReplaceUnhealthy"}, {"ProcessName": "ScheduledActions"}, {"ProcessName": "Terminate"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the Auto Scaling process types.", "id": "autoscaling-describe-scaling-process-types-1", "title": "To describe the Auto Scaling process types"}], "DescribeScheduledActions": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group"}, "output": {"ScheduledUpdateGroupActions": [{"AutoScalingGroupName": "my-auto-scaling-group", "DesiredCapacity": 4, "MaxSize": 6, "MinSize": 2, "Recurrence": "30 0 1 12 0", "ScheduledActionARN": "arn:aws:autoscaling:us-west-2:************:scheduledUpdateGroupAction:8e86b655-b2e6-4410-8f29-b4f094d6871c:autoScalingGroupName/my-auto-scaling-group:scheduledActionName/my-scheduled-action", "ScheduledActionName": "my-scheduled-action", "StartTime": "2016-12-01T00:30:00Z", "Time": "2016-12-01T00:30:00Z"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the scheduled actions for the specified Auto Scaling group.", "id": "autoscaling-describe-scheduled-actions-1", "title": "To describe scheduled actions"}], "DescribeTags": [{"input": {"Filters": [{"Name": "auto-scaling-group", "Values": ["my-auto-scaling-group"]}]}, "output": {"Tags": [{"Key": "Dept", "PropagateAtLaunch": true, "ResourceId": "my-auto-scaling-group", "ResourceType": "auto-scaling-group", "Value": "Research"}, {"Key": "Role", "PropagateAtLaunch": true, "ResourceId": "my-auto-scaling-group", "ResourceType": "auto-scaling-group", "Value": "WebServer"}]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the tags for the specified Auto Scaling group.", "id": "autoscaling-describe-tags-1", "title": "To describe tags"}], "DescribeTerminationPolicyTypes": [{"output": {"TerminationPolicyTypes": ["ClosestToNextInstanceHour", "<PERSON><PERSON><PERSON>", "NewestInstance", "OldestInstance", "OldestLaunchConfiguration"]}, "comments": {"input": {}, "output": {}}, "description": "This example describes the available termination policy types.", "id": "autoscaling-describe-termination-policy-types-1", "title": "To describe termination policy types"}], "DetachInstances": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "InstanceIds": ["i-93633f9b"], "ShouldDecrementDesiredCapacity": true}, "output": {"Activities": [{"ActivityId": "5091cb52-547a-47ce-a236-c9ccbc2cb2c9", "AutoScalingGroupName": "my-auto-scaling-group", "Cause": "At 2015-04-12T15:02:16Z instance i-93633f9b was detached in response to a user request, shrinking the capacity from 2 to 1.", "Description": "Detaching EC2 instance: i-93633f9b", "Details": "details", "Progress": 50, "StartTime": "2015-04-12T15:02:16.179Z", "StatusCode": "InProgress"}]}, "comments": {"input": {}, "output": {}}, "description": "This example detaches the specified instance from the specified Auto Scaling group.", "id": "autoscaling-detach-instances-1", "title": "To detach an instance from an Auto Scaling group"}], "DetachLoadBalancerTargetGroups": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "TargetGroupARNs": ["arn:aws:elasticloadbalancing:us-west-2:************:targetgroup/my-targets/73e2d6bc24d8a067"]}, "comments": {"input": {}, "output": {}}, "description": "This example detaches the specified target group from the specified Auto Scaling group", "id": "autoscaling-detach-load-balancer-target-groups-1", "title": "To detach a target group from an Auto Scaling group"}], "DetachLoadBalancers": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "LoadBalancerNames": ["my-load-balancer"]}, "comments": {"input": {}, "output": {}}, "description": "This example detaches the specified load balancer from the specified Auto Scaling group.", "id": "autoscaling-detach-load-balancers-1", "title": "To detach a load balancer from an Auto Scaling group"}], "DisableMetricsCollection": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "Metrics": ["GroupDesiredCapacity"]}, "comments": {"input": {}, "output": {}}, "description": "This example disables collecting data for the GroupDesiredCapacity metric for the specified Auto Scaling group.", "id": "autoscaling-disable-metrics-collection-1", "title": "To disable metrics collection for an Auto Scaling group"}], "EnableMetricsCollection": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "Granularity": "1Minute"}, "comments": {"input": {}, "output": {}}, "description": "This example enables data collection for the specified Auto Scaling group.", "id": "autoscaling-enable-metrics-collection-1", "title": "To enable metrics collection for an Auto Scaling group"}], "EnterStandby": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "InstanceIds": ["i-93633f9b"], "ShouldDecrementDesiredCapacity": true}, "output": {"Activities": [{"ActivityId": "ffa056b4-6ed3-41ba-ae7c-249dfae6eba1", "AutoScalingGroupName": "my-auto-scaling-group", "Cause": "At 2015-04-12T15:10:23Z instance i-93633f9b was moved to standby in response to a user request, shrinking the capacity from 2 to 1.", "Description": "Moving EC2 instance to Standby: i-93633f9b", "Details": "details", "Progress": 50, "StartTime": "2015-04-12T15:10:23.640Z", "StatusCode": "InProgress"}]}, "comments": {"input": {}, "output": {}}, "description": "This example puts the specified instance into standby mode.", "id": "autoscaling-enter-standby-1", "title": "To move instances into standby mode"}], "ExecutePolicy": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "BreachThreshold": 50.0, "MetricValue": 59.0, "PolicyName": "my-step-scale-out-policy"}, "comments": {"input": {}, "output": {}}, "description": "This example executes the specified policy.", "id": "autoscaling-execute-policy-1", "title": "To execute a scaling policy"}], "ExitStandby": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "InstanceIds": ["i-93633f9b"]}, "output": {"Activities": [{"ActivityId": "142928e1-a2dc-453a-9b24-b85ad6735928", "AutoScalingGroupName": "my-auto-scaling-group", "Cause": "At 2015-04-12T15:14:29Z instance i-93633f9b was moved out of standby in response to a user request, increasing the capacity from 1 to 2.", "Description": "Moving EC2 instance out of Standby: i-93633f9b", "Details": "details", "Progress": 30, "StartTime": "2015-04-12T15:14:29.886Z", "StatusCode": "PreInService"}]}, "comments": {"input": {}, "output": {}}, "description": "This example moves the specified instance out of standby mode.", "id": "autoscaling-exit-standby-1", "title": "To move instances out of standby mode"}], "PutLifecycleHook": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "DefaultResult": "CONTINUE", "HeartbeatTimeout": 300, "LifecycleHookName": "my-launch-lifecycle-hook", "LifecycleTransition": "autoscaling:EC2_INSTANCE_LAUNCHING"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a lifecycle hook for instance launch.", "id": "autoscaling-put-lifecycle-hook-1", "title": "To create a launch lifecycle hook"}], "PutNotificationConfiguration": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "NotificationTypes": ["autoscaling:TEST_NOTIFICATION"], "TopicARN": "arn:aws:sns:us-west-2:************:my-sns-topic"}, "comments": {"input": {}, "output": {}}, "description": "This example adds the specified notification to the specified Auto Scaling group.", "id": "autoscaling-put-notification-configuration-1", "title": "To add an Auto Scaling notification"}], "PutScalingPolicy": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "PolicyName": "alb1000-target-tracking-scaling-policy", "PolicyType": "TargetTrackingScaling", "TargetTrackingConfiguration": {"PredefinedMetricSpecification": {"PredefinedMetricType": "ALBRequestCountPerTarget", "ResourceLabel": "app/my-alb/778d41231b141a0f/targetgroup/my-alb-target-group/943f017f100becff"}, "TargetValue": 1000.0}}, "output": {"Alarms": [{"AlarmARN": "arn:aws:cloudwatch:us-west-2:************:alarm:TargetTracking-my-asg-AlarmHigh-fc0e4183-23ac-497e-9992-691c9980c38e", "AlarmName": "TargetTracking-my-asg-AlarmHigh-fc0e4183-23ac-497e-9992-691c9980c38e"}, {"AlarmARN": "arn:aws:cloudwatch:us-west-2:************:alarm:TargetTracking-my-asg-AlarmLow-61a39305-ed0c-47af-bd9e-471a352ee1a2", "AlarmName": "TargetTracking-my-asg-AlarmLow-61a39305-ed0c-47af-bd9e-471a352ee1a2"}], "PolicyARN": "arn:aws:autoscaling:us-west-2:************:scalingPolicy:228f02c2-c665-4bfd-aaac-8b04080bea3c:autoScalingGroupName/my-auto-scaling-group:policyName/alb1000-target-tracking-scaling-policy"}, "comments": {"input": {}, "output": {}}, "description": "This example adds the specified policy to the specified Auto Scaling group.", "id": "autoscaling-put-scaling-policy-1", "title": "To add a scaling policy to an Auto Scaling group"}], "PutScheduledUpdateGroupAction": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "DesiredCapacity": 4, "EndTime": "2014-05-12T08:00:00Z", "MaxSize": 6, "MinSize": 2, "ScheduledActionName": "my-scheduled-action", "StartTime": "2014-05-12T08:00:00Z"}, "comments": {"input": {}, "output": {}}, "description": "This example adds the specified scheduled action to the specified Auto Scaling group.", "id": "autoscaling-put-scheduled-update-group-action-1", "title": "To add a scheduled action to an Auto Scaling group"}], "PutWarmPool": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "InstanceReusePolicy": {"ReuseOnScaleIn": true}, "MinSize": 30, "PoolState": "Hibernated"}, "comments": {"input": {}, "output": {}}, "description": "This example creates a warm pool for the specified Auto Scaling group.", "id": "to-add-a-warm-pool-to-an-auto-scaling-group-1617818810383", "title": "To create a warm pool for an Auto Scaling group"}], "RecordLifecycleActionHeartbeat": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "LifecycleActionToken": "bcd2f1b8-9a78-44d3-8a7a-4dd07d7cf635", "LifecycleHookName": "my-lifecycle-hook"}, "comments": {"input": {}, "output": {}}, "description": "This example records a lifecycle action heartbeat to keep the instance in a pending state.", "id": "autoscaling-record-lifecycle-action-heartbeat-1", "title": "To record a lifecycle action heartbeat"}], "ResumeProcesses": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "ScalingProcesses": ["AlarmNotification"]}, "comments": {"input": {}, "output": {}}, "description": "This example resumes the specified suspended scaling process for the specified Auto Scaling group.", "id": "autoscaling-resume-processes-1", "title": "To resume Auto Scaling processes"}], "SetDesiredCapacity": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "DesiredCapacity": 2, "HonorCooldown": true}, "comments": {"input": {}, "output": {}}, "description": "This example sets the desired capacity for the specified Auto Scaling group.", "id": "autoscaling-set-desired-capacity-1", "title": "To set the desired capacity for an Auto Scaling group"}], "SetInstanceHealth": [{"input": {"HealthStatus": "Unhealthy", "InstanceId": "i-93633f9b"}, "comments": {"input": {}, "output": {}}, "description": "This example sets the health status of the specified instance to Unhealthy.", "id": "autoscaling-set-instance-health-1", "title": "To set the health status of an instance"}], "SetInstanceProtection": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "InstanceIds": ["i-93633f9b"], "ProtectedFromScaleIn": true}, "comments": {"input": {}, "output": {}}, "description": "This example enables instance protection for the specified instance.", "id": "autoscaling-set-instance-protection-1", "title": "To enable instance protection for an instance"}, {"input": {"AutoScalingGroupName": "my-auto-scaling-group", "InstanceIds": ["i-93633f9b"], "ProtectedFromScaleIn": false}, "comments": {"input": {}, "output": {}}, "description": "This example disables instance protection for the specified instance.", "id": "autoscaling-set-instance-protection-2", "title": "To disable instance protection for an instance"}], "StartInstanceRefresh": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "DesiredConfiguration": {"LaunchTemplate": {"LaunchTemplateName": "my-template-for-auto-scaling", "Version": "$Latest"}}, "Preferences": {"InstanceWarmup": 400, "MinHealthyPercentage": 90, "SkipMatching": true}}, "output": {"InstanceRefreshId": "08b91cf7-8fa6-48af-b6a6-d227f40f1b9b"}, "comments": {"input": {}, "output": {}}, "description": "This example starts an instance refresh for the specified Auto Scaling group.", "id": "to-start-an-instance-refresh-1592957271522", "title": "To start an instance refresh"}], "SuspendProcesses": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "ScalingProcesses": ["AlarmNotification"]}, "comments": {"input": {}, "output": {}}, "description": "This example suspends the specified scaling process for the specified Auto Scaling group.", "id": "autoscaling-suspend-processes-1", "title": "To suspend Auto Scaling processes"}], "TerminateInstanceInAutoScalingGroup": [{"input": {"InstanceId": "i-93633f9b", "ShouldDecrementDesiredCapacity": false}, "comments": {"input": {}, "output": {}}, "description": "This example terminates the specified instance from the specified Auto Scaling group without updating the size of the group. Auto Scaling launches a replacement instance after the specified instance terminates.", "id": "autoscaling-terminate-instance-in-auto-scaling-group-1", "title": "To terminate an instance in an Auto Scaling group"}], "UpdateAutoScalingGroup": [{"input": {"AutoScalingGroupName": "my-auto-scaling-group", "LaunchTemplate": {"LaunchTemplateName": "my-template-for-auto-scaling", "Version": "2"}, "MaxSize": 5, "MinSize": 1, "NewInstancesProtectedFromScaleIn": true}, "comments": {"input": {}, "output": {}}, "description": "This example updates multiple properties at the same time.", "id": "autoscaling-update-auto-scaling-group-1", "title": "To update an Auto Scaling group"}]}}