{"version": "2.0", "metadata": {"apiVersion": "2021-07-15", "endpointPrefix": "meetings-chime", "protocol": "rest-json", "serviceFullName": "Amazon Chime SDK Meetings", "serviceId": "Chime SDK Meetings", "signatureVersion": "v4", "signingName": "chime", "uid": "chime-sdk-meetings-2021-07-15"}, "operations": {"BatchCreateAttendee": {"name": "BatchCreateAttendee", "http": {"method": "POST", "requestUri": "/meetings/{MeetingId}/attendees?operation=batch-create"}, "input": {"shape": "BatchCreateAttendeeRequest"}, "output": {"shape": "BatchCreateAttendeeResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "UnprocessableEntityException"}, {"shape": "LimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Creates up to 100 attendees for an active Amazon Chime SDK meeting. For more information about the Amazon Chime SDK, see <a href=\"https://docs.aws.amazon.com/chime/latest/dg/meetings-sdk.html\">Using the Amazon Chime SDK</a> in the <i>Amazon Chime Developer Guide</i>.</p>"}, "BatchUpdateAttendeeCapabilitiesExcept": {"name": "BatchUpdateAttendeeCapabilitiesExcept", "http": {"method": "PUT", "requestUri": "/meetings/{MeetingId}/attendees/capabilities?operation=batch-update-except", "responseCode": 200}, "input": {"shape": "BatchUpdateAttendeeCapabilitiesExceptRequest"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "UnauthorizedException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>Updates <code>AttendeeCapabilities</code> except the capabilities listed in an <code>ExcludedAttendeeIds</code> table.</p> <note> <p>You use the capabilities with a set of values that control what the capabilities can do, such as <code>SendReceive</code> data. For more information about those values, see .</p> </note> <p>When using capabilities, be aware of these corner cases:</p> <ul> <li> <p>You can't set <code>content</code> capabilities to <code>SendReceive</code> or <code>Receive</code> unless you also set <code>video</code> capabilities to <code>SendReceive</code> or <code>Receive</code>. If you don't set the <code>video</code> capability to receive, the response will contain an HTTP 400 Bad Request status code. However, you can set your <code>video</code> capability to receive and you set your <code>content</code> capability to not receive.</p> </li> <li> <p>When you change an <code>audio</code> capability from <code>None</code> or <code>Receive</code> to <code>Send</code> or <code>SendReceive</code> , and if the attendee left their microphone unmuted, audio will flow from the attendee to the other meeting participants.</p> </li> <li> <p>When you change a <code>video</code> or <code>content</code> capability from <code>None</code> or <code>Receive</code> to <code>Send</code> or <code>SendReceive</code> , and if the attendee turned on their video or content streams, remote attendess can receive those streams, but only after media renegotiation between the client and the Amazon Chime back-end server.</p> </li> </ul>"}, "CreateAttendee": {"name": "CreateAttendee", "http": {"method": "POST", "requestUri": "/meetings/{MeetingId}/attendees"}, "input": {"shape": "CreateAttendeeRequest"}, "output": {"shape": "CreateAttendeeResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "UnprocessableEntityException"}, {"shape": "LimitExceededException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p> Creates a new attendee for an active Amazon Chime SDK meeting. For more information about the Amazon Chime SDK, see <a href=\"https://docs.aws.amazon.com/chime/latest/dg/meetings-sdk.html\">Using the Amazon Chime SDK</a> in the <i>Amazon Chime Developer Guide</i>. </p>"}, "CreateMeeting": {"name": "CreateMeeting", "http": {"method": "POST", "requestUri": "/meetings"}, "input": {"shape": "CreateMeetingRequest"}, "output": {"shape": "CreateMeetingResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "UnauthorizedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a new Amazon Chime SDK meeting in the specified media Region with no initial attendees. For more information about specifying media Regions, see <a href=\"https://docs.aws.amazon.com/chime/latest/dg/chime-sdk-meetings-regions.html\">Amazon Chime SDK Media Regions</a> in the <i>Amazon Chime Developer Guide</i>. For more information about the Amazon Chime SDK, see <a href=\"https://docs.aws.amazon.com/chime/latest/dg/meetings-sdk.html\">Using the Amazon Chime SDK</a> in the <i>Amazon Chime Developer Guide</i>. </p>"}, "CreateMeetingWithAttendees": {"name": "CreateMeetingWithAttendees", "http": {"method": "POST", "requestUri": "/meetings?operation=create-attendees"}, "input": {"shape": "CreateMeetingWithAttendeesRequest"}, "output": {"shape": "CreateMeetingWithAttendeesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "UnauthorizedException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceFailureException"}, {"shape": "ServiceUnavailableException"}, {"shape": "LimitExceededException"}], "documentation": "<p> Creates a new Amazon Chime SDK meeting in the specified media Region, with attendees. For more information about specifying media Regions, see <a href=\"https://docs.aws.amazon.com/chime/latest/dg/chime-sdk-meetings-regions.html\">Amazon Chime SDK Media Regions</a> in the <i>Amazon Chime Developer Guide</i>. For more information about the Amazon Chime SDK, see <a href=\"https://docs.aws.amazon.com/chime/latest/dg/meetings-sdk.html\">Using the Amazon Chime SDK</a> in the <i>Amazon Chime Developer Guide</i>. </p>"}, "DeleteAttendee": {"name": "DeleteAttendee", "http": {"method": "DELETE", "requestUri": "/meetings/{MeetingId}/attendees/{AttendeeId}", "responseCode": 204}, "input": {"shape": "DeleteAttendeeRequest"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes an attendee from the specified Amazon Chime SDK meeting and deletes their <code>JoinToken</code>. Attendees are automatically deleted when a Amazon Chime SDK meeting is deleted. For more information about the Amazon Chime SDK, see <a href=\"https://docs.aws.amazon.com/chime/latest/dg/meetings-sdk.html\">Using the Amazon Chime SDK</a> in the <i>Amazon Chime Developer Guide</i>.</p>"}, "DeleteMeeting": {"name": "DeleteMeeting", "http": {"method": "DELETE", "requestUri": "/meetings/{MeetingId}", "responseCode": 204}, "input": {"shape": "DeleteMeetingRequest"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "UnauthorizedException"}, {"shape": "NotFoundException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Deletes the specified Amazon Chime SDK meeting. The operation deletes all attendees, disconnects all clients, and prevents new clients from joining the meeting. For more information about the Amazon Chime SDK, see <a href=\"https://docs.aws.amazon.com/chime/latest/dg/meetings-sdk.html\">Using the Amazon Chime SDK</a> in the <i>Amazon Chime Developer Guide</i>.</p>"}, "GetAttendee": {"name": "GetAttendee", "http": {"method": "GET", "requestUri": "/meetings/{MeetingId}/attendees/{AttendeeId}"}, "input": {"shape": "GetAttendeeRequest"}, "output": {"shape": "GetAttendeeResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p> Gets the Amazon Chime SDK attendee details for a specified meeting ID and attendee ID. For more information about the Amazon Chime SDK, see <a href=\"https://docs.aws.amazon.com/chime/latest/dg/meetings-sdk.html\">Using the Amazon Chime SDK</a> in the <i>Amazon Chime Developer Guide</i>. </p>"}, "GetMeeting": {"name": "GetMeeting", "http": {"method": "GET", "requestUri": "/meetings/{MeetingId}"}, "input": {"shape": "GetMeetingRequest"}, "output": {"shape": "GetMeetingResponse"}, "errors": [{"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "UnauthorizedException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p>Gets the Amazon Chime SDK meeting details for the specified meeting ID. For more information about the Amazon Chime SDK, see <a href=\"https://docs.aws.amazon.com/chime/latest/dg/meetings-sdk.html\">Using the Amazon Chime SDK</a> in the <i>Amazon Chime Developer Guide</i>.</p>"}, "ListAttendees": {"name": "ListAttendees", "http": {"method": "GET", "requestUri": "/meetings/{MeetingId}/attendees", "responseCode": 200}, "input": {"shape": "ListAttendeesRequest"}, "output": {"shape": "ListAttendeesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ForbiddenException"}, {"shape": "NotFoundException"}, {"shape": "UnauthorizedException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}, {"shape": "ThrottlingException"}], "documentation": "<p> Lists the attendees for the specified Amazon Chime SDK meeting. For more information about the Amazon Chime SDK, see <a href=\"https://docs.aws.amazon.com/chime/latest/dg/meetings-sdk.html\">Using the Amazon Chime SDK</a> in the <i>Amazon Chime Developer Guide</i>. </p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}], "documentation": "<p>Returns a list of the tags available for the specified resource.</p>"}, "StartMeetingTranscription": {"name": "StartMeetingTranscription", "http": {"method": "POST", "requestUri": "/meetings/{MeetingId}/transcription?operation=start", "responseCode": 200}, "input": {"shape": "StartMeetingTranscriptionRequest"}, "errors": [{"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "BadRequestException"}, {"shape": "UnauthorizedException"}, {"shape": "LimitExceededException"}, {"shape": "UnprocessableEntityException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Starts transcription for the specified <code>meetingId</code>. For more information, refer to <a href=\"https://docs.aws.amazon.com/chime-sdk/latest/dg/meeting-transcription.html\"> Using Amazon Chime SDK live transcription </a> in the <i>Amazon Chime SDK Developer Guide</i>.</p> <important> <p>Amazon Chime SDK live transcription is powered by Amazon Transcribe. Use of Amazon Transcribe is subject to the <a href=\"https://aws.amazon.com/service-terms/\">AWS Service Terms</a>, including the terms specific to the AWS Machine Learning and Artificial Intelligence Services.</p> </important>"}, "StopMeetingTranscription": {"name": "StopMeetingTranscription", "http": {"method": "POST", "requestUri": "/meetings/{MeetingId}/transcription?operation=stop", "responseCode": 200}, "input": {"shape": "StopMeetingTranscriptionRequest"}, "errors": [{"shape": "ForbiddenException"}, {"shape": "NotFoundException"}, {"shape": "BadRequestException"}, {"shape": "UnauthorizedException"}, {"shape": "UnprocessableEntityException"}, {"shape": "ThrottlingException"}, {"shape": "ServiceUnavailableException"}, {"shape": "ServiceFailureException"}], "documentation": "<p>Stops transcription for the specified <code>meetingId</code>. For more information, refer to <a href=\"https://docs.aws.amazon.com/chime-sdk/latest/dg/meeting-transcription.html\"> Using Amazon Chime SDK live transcription </a> in the <i>Amazon Chime SDK Developer Guide</i>.</p> <important> <p>Amazon Chime SDK live transcription is powered by Amazon Transcribe. Use of Amazon Transcribe is subject to the <a href=\"https://aws.amazon.com/service-terms/\">AWS Service Terms</a>, including the terms specific to the AWS Machine Learning and Artificial Intelligence Services.</p> </important>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags?operation=tag-resource", "responseCode": 204}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>The resource that supports tags.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "POST", "requestUri": "/tags?operation=untag-resource", "responseCode": 204}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes the specified tags from the specified resources. When you specify a tag key, the action removes both that key and its associated value. The operation succeeds even if you attempt to remove tags from a resource that were already removed. Note the following:</p> <ul> <li> <p>To remove tags from a resource, you need the necessary permissions for the service that the resource belongs to as well as permissions for removing tags. For more information, see the documentation for the service whose resource you want to untag.</p> </li> <li> <p>You can only tag resources that are located in the specified AWS Region for the calling AWS account.</p> </li> </ul> <p> <b>Minimum permissions</b> </p> <p>In addition to the <code>tag:UntagResources</code> permission required by this operation, you must also have the remove tags permission defined by the service that created the resource. For example, to remove the tags from an Amazon EC2 instance using the <code>UntagResources</code> operation, you must have both of the following permissions:</p> <p> <code>tag:UntagResource</code> </p> <p> <code>ChimeSDKMeetings:DeleteTags</code> </p>"}, "UpdateAttendeeCapabilities": {"name": "UpdateAttendeeCapabilities", "http": {"method": "PUT", "requestUri": "/meetings/{MeetingId}/attendees/{AttendeeId}/capabilities"}, "input": {"shape": "UpdateAttendeeCapabilitiesRequest"}, "output": {"shape": "UpdateAttendeeCapabilitiesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ConflictException"}, {"shape": "UnauthorizedException"}, {"shape": "NotFoundException"}, {"shape": "ForbiddenException"}, {"shape": "ServiceUnavailableException"}], "documentation": "<p>The capabilties that you want to update.</p> <note> <p>You use the capabilities with a set of values that control what the capabilities can do, such as <code>SendReceive</code> data. For more information about those values, see .</p> </note> <p>When using capabilities, be aware of these corner cases:</p> <ul> <li> <p>You can't set <code>content</code> capabilities to <code>SendReceive</code> or <code>Receive</code> unless you also set <code>video</code> capabilities to <code>SendReceive</code> or <code>Receive</code>. If you don't set the <code>video</code> capability to receive, the response will contain an HTTP 400 Bad Request status code. However, you can set your <code>video</code> capability to receive and you set your <code>content</code> capability to not receive.</p> </li> <li> <p>When you change an <code>audio</code> capability from <code>None</code> or <code>Receive</code> to <code>Send</code> or <code>SendReceive</code> , and if the attendee left their microphone unmuted, audio will flow from the attendee to the other meeting participants.</p> </li> <li> <p>When you change a <code>video</code> or <code>content</code> capability from <code>None</code> or <code>Receive</code> to <code>Send</code> or <code>SendReceive</code> , and if the attendee turned on their video or content streams, remote attendess can receive those streams, but only after media renegotiation between the client and the Amazon Chime back-end server.</p> </li> </ul>"}}, "shapes": {"AmazonResourceName": {"type": "string", "max": 1011, "min": 1, "pattern": "^arn:.*"}, "Arn": {"type": "string", "max": 1024, "min": 1, "pattern": "^arn[\\/\\:\\-\\_\\.a-zA-Z0-9]+$", "sensitive": true}, "Attendee": {"type": "structure", "members": {"ExternalUserId": {"shape": "ExternalUserId", "documentation": "<p>The Amazon Chime SDK external user ID. An idempotency token. Links the attendee to an identity managed by a builder application.</p> <p>Pattern: <code>[-_&amp;@+=,(){}\\[\\]\\/«».:|'\"#a-zA-Z0-9À-ÿ\\s]*</code> </p> <p>Values that begin with <code>aws:</code> are reserved. You can't configure a value that uses this prefix. Case insensitive.</p>"}, "AttendeeId": {"shape": "GuidString", "documentation": "<p>The Amazon Chime SDK attendee ID.</p>"}, "JoinToken": {"shape": "JoinTokenString", "documentation": "<p>The join token used by the Amazon Chime SDK attendee.</p>"}, "Capabilities": {"shape": "AttendeeCapabilities", "documentation": "<p>The capabilities assigned to an attendee: audio, video, or content.</p> <note> <p>You use the capabilities with a set of values that control what the capabilities can do, such as <code>SendReceive</code> data. For more information about those values, see .</p> </note> <p>When using capabilities, be aware of these corner cases:</p> <ul> <li> <p>You can't set <code>content</code> capabilities to <code>SendReceive</code> or <code>Receive</code> unless you also set <code>video</code> capabilities to <code>SendReceive</code> or <code>Receive</code>. If you don't set the <code>video</code> capability to receive, the response will contain an HTTP 400 Bad Request status code. However, you can set your <code>video</code> capability to receive and you set your <code>content</code> capability to not receive.</p> </li> <li> <p>When you change an <code>audio</code> capability from <code>None</code> or <code>Receive</code> to <code>Send</code> or <code>SendReceive</code> , and if the attendee left their microphone unmuted, audio will flow from the attendee to the other meeting participants.</p> </li> <li> <p>When you change a <code>video</code> or <code>content</code> capability from <code>None</code> or <code>Receive</code> to <code>Send</code> or <code>SendReceive</code> , and if the attendee turned on their video or content streams, remote attendess can receive those streams, but only after media renegotiation between the client and the Amazon Chime back-end server.</p> </li> </ul>"}}, "documentation": "<p>An Amazon Chime SDK meeting attendee. Includes a unique <code>AttendeeId</code> and <code>JoinToken</code>. The <code>JoinToken</code> allows a client to authenticate and join as the specified attendee. The <code>JoinToken</code> expires when the meeting ends, or when <a>Delete<PERSON>tten<PERSON>e</a> is called. After that, the attendee is unable to join the meeting. </p> <p>We recommend securely transferring each <code>JoinToken</code> from your server application to the client so that no other client has access to the token except for the one authorized to represent the attendee.</p>"}, "AttendeeCapabilities": {"type": "structure", "required": ["Audio", "Video", "Content"], "members": {"Audio": {"shape": "MediaCapabilities", "documentation": "<p>The audio capability assigned to an attendee.</p>"}, "Video": {"shape": "MediaCapabilities", "documentation": "<p>The video capability assigned to an attendee.</p>"}, "Content": {"shape": "MediaCapabilities", "documentation": "<p>The content capability assigned to an attendee.</p>"}}, "documentation": "<p>The media capabilities of an attendee: audio, video, or content. </p> <note> <p>You use the capabilities with a set of values that control what the capabilities can do, such as <code>SendReceive</code> data. For more information about those values, see .</p> </note> <p>When using capabilities, be aware of these corner cases:</p> <ul> <li> <p>You can't set <code>content</code> capabilities to <code>SendReceive</code> or <code>Receive</code> unless you also set <code>video</code> capabilities to <code>SendReceive</code> or <code>Receive</code>. If you don't set the <code>video</code> capability to receive, the response will contain an HTTP 400 Bad Request status code. However, you can set your <code>video</code> capability to receive and you set your <code>content</code> capability to not receive.</p> </li> <li> <p>When you change an <code>audio</code> capability from <code>None</code> or <code>Receive</code> to <code>Send</code> or <code>SendReceive</code> , and if the attendee left their microphone unmuted, audio will flow from the attendee to the other meeting participants.</p> </li> <li> <p>When you change a <code>video</code> or <code>content</code> capability from <code>None</code> or <code>Receive</code> to <code>Send</code> or <code>SendReceive</code> , and if the attendee turned on their video or content streams, remote attendess can receive those streams, but only after media renegotiation between the client and the Amazon Chime back-end server.</p> </li> </ul>"}, "AttendeeIdItem": {"type": "structure", "required": ["At<PERSON>eeId"], "members": {"AttendeeId": {"shape": "GuidString", "documentation": "<p>A list of one or more attendee IDs.</p>"}}, "documentation": "<p>A structure that contains one or more attendee IDs.</p>"}, "AttendeeIdsList": {"type": "list", "member": {"shape": "AttendeeIdItem"}, "max": 250, "min": 1}, "AttendeeList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON>"}}, "AudioFeatures": {"type": "structure", "members": {"EchoReduction": {"shape": "MeetingFeatureStatus", "documentation": "<p>Makes echo reduction available to clients who connect to the meeting.</p>"}}, "documentation": "<p>An optional category of meeting features that contains audio-specific configurations, such as operating parameters for Amazon Voice Focus. </p>"}, "BadRequestException": {"type": "structure", "members": {"Code": {"shape": "String"}, "Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The request id associated with the call responsible for the exception.</p>"}}, "documentation": "<p>The input parameters don't match the service's restrictions.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "BatchCreateAttendeeErrorList": {"type": "list", "member": {"shape": "CreateAttendee<PERSON><PERSON>r"}}, "BatchCreateAttendeeRequest": {"type": "structure", "required": ["MeetingId", "Attendees"], "members": {"MeetingId": {"shape": "GuidString", "documentation": "<p>The Amazon Chime SDK ID of the meeting to which you're adding attendees.</p>", "location": "uri", "locationName": "MeetingId"}, "Attendees": {"shape": "CreateAttendeeRequestItemList", "documentation": "<p>The attendee information, including attendees' IDs and join tokens.</p>"}}}, "BatchCreateAttendeeResponse": {"type": "structure", "members": {"Attendees": {"shape": "AttendeeList", "documentation": "<p>The attendee information, including attendees' IDs and join tokens.</p>"}, "Errors": {"shape": "BatchCreateAttendeeErrorList", "documentation": "<p>If the action fails for one or more of the attendees in the request, a list of the attendees is returned, along with error codes and error messages.</p>"}}}, "BatchUpdateAttendeeCapabilitiesExceptRequest": {"type": "structure", "required": ["MeetingId", "ExcludedAttendeeIds", "Capabilities"], "members": {"MeetingId": {"shape": "GuidString", "documentation": "<p>The ID of the meeting associated with the update request.</p>", "location": "uri", "locationName": "MeetingId"}, "ExcludedAttendeeIds": {"shape": "AttendeeIdsList", "documentation": "<p>The <code>AttendeeIDs</code> that you want to exclude from one or more capabilities.</p>"}, "Capabilities": {"shape": "AttendeeCapabilities", "documentation": "<p>The capabilities (<code>audio</code>, <code>video</code>, or <code>content</code>) that you want to update.</p>"}}}, "Boolean": {"type": "boolean"}, "ClientRequestToken": {"type": "string", "max": 64, "min": 2, "pattern": "[-_a-zA-Z0-9]*", "sensitive": true}, "ConflictException": {"type": "structure", "members": {"Code": {"shape": "String"}, "Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The ID of the request involved in the conflict.</p>"}}, "documentation": "<p>Multiple instances of the same request have been made simultaneously.</p>", "error": {"httpStatusCode": 409}, "exception": true}, "CreateAttendeeError": {"type": "structure", "members": {"ExternalUserId": {"shape": "ExternalUserId", "documentation": "<p>The Amazon Chime SDK external user ID. An idempotency token. Links the attendee to an identity managed by a builder application.</p> <p>Pattern: <code>[-_&amp;@+=,(){}\\[\\]\\/«».:|'\"#a-zA-Z0-9À-ÿ\\s]*</code> </p> <p>Values that begin with <code>aws:</code> are reserved. You can't configure a value that uses this prefix. Case insensitive.</p>"}, "ErrorCode": {"shape": "String", "documentation": "<p>The error code.</p>"}, "ErrorMessage": {"shape": "String", "documentation": "<p>The error message.</p>"}}, "documentation": "<p>The list of errors returned when errors are encountered during the BatchCreateAttendee and CreateAttendee actions. This includes external user IDs, error codes, and error messages.</p>"}, "CreateAttendeeRequest": {"type": "structure", "required": ["MeetingId", "ExternalUserId"], "members": {"MeetingId": {"shape": "GuidString", "documentation": "<p>The unique ID of the meeting.</p>", "location": "uri", "locationName": "MeetingId"}, "ExternalUserId": {"shape": "ExternalUserId", "documentation": "<p>The Amazon Chime SDK external user ID. An idempotency token. Links the attendee to an identity managed by a builder application.</p> <p>Pattern: <code>[-_&amp;@+=,(){}\\[\\]\\/«».:|'\"#a-zA-Z0-9À-ÿ\\s]*</code> </p> <p>Values that begin with <code>aws:</code> are reserved. You can't configure a value that uses this prefix.</p>"}, "Capabilities": {"shape": "AttendeeCapabilities", "documentation": "<p>The capabilities (<code>audio</code>, <code>video</code>, or <code>content</code>) that you want to grant an attendee. If you don't specify capabilities, all users have send and receive capabilities on all media channels by default.</p> <note> <p>You use the capabilities with a set of values that control what the capabilities can do, such as <code>SendReceive</code> data. For more information about those values, see .</p> </note> <p>When using capabilities, be aware of these corner cases:</p> <ul> <li> <p>You can't set <code>content</code> capabilities to <code>SendReceive</code> or <code>Receive</code> unless you also set <code>video</code> capabilities to <code>SendReceive</code> or <code>Receive</code>. If you don't set the <code>video</code> capability to receive, the response will contain an HTTP 400 Bad Request status code. However, you can set your <code>video</code> capability to receive and you set your <code>content</code> capability to not receive.</p> </li> <li> <p>When you change an <code>audio</code> capability from <code>None</code> or <code>Receive</code> to <code>Send</code> or <code>SendReceive</code> , and if the attendee left their microphone unmuted, audio will flow from the attendee to the other meeting participants.</p> </li> <li> <p>When you change a <code>video</code> or <code>content</code> capability from <code>None</code> or <code>Receive</code> to <code>Send</code> or <code>SendReceive</code> , and if the attendee turned on their video or content streams, remote attendess can receive those streams, but only after media renegotiation between the client and the Amazon Chime back-end server.</p> </li> </ul>"}}}, "CreateAttendeeRequestItem": {"type": "structure", "required": ["ExternalUserId"], "members": {"ExternalUserId": {"shape": "ExternalUserId", "documentation": "<p>The Amazon Chime SDK external user ID. An idempotency token. Links the attendee to an identity managed by a builder application.</p> <p>Pattern: <code>[-_&amp;@+=,(){}\\[\\]\\/«».:|'\"#a-zA-Z0-9À-ÿ\\s]*</code> </p> <p>Values that begin with <code>aws:</code> are reserved. You can't configure a value that uses this prefix. Case insensitive.</p>"}, "Capabilities": {"shape": "AttendeeCapabilities", "documentation": "<p>A list of one or more capabilities.</p>"}}, "documentation": "<p>The Amazon Chime SDK attendee fields to create, used with the BatchCreateAttendee action.</p>"}, "CreateAttendeeRequestItemList": {"type": "list", "member": {"shape": "CreateAttendeeRequestItem"}, "max": 100, "min": 1}, "CreateAttendeeResponse": {"type": "structure", "members": {"Attendee": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The attendee information, including attendee ID and join token.</p>"}}}, "CreateMeetingRequest": {"type": "structure", "required": ["ClientRequestToken", "MediaRegion", "ExternalMeetingId"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>The unique identifier for the client request. Use a different token for different meetings.</p>", "idempotencyToken": true}, "MediaRegion": {"shape": "MediaRegion", "documentation": "<p>The Region in which to create the meeting.</p> <p> Available values: <code>af-south-1</code>, <code>ap-northeast-1</code>, <code>ap-northeast-2</code>, <code>ap-south-1</code>, <code>ap-southeast-1</code>, <code>ap-southeast-2</code>, <code>ca-central-1</code>, <code>eu-central-1</code>, <code>eu-north-1</code>, <code>eu-south-1</code>, <code>eu-west-1</code>, <code>eu-west-2</code>, <code>eu-west-3</code>, <code>sa-east-1</code>, <code>us-east-1</code>, <code>us-east-2</code>, <code>us-west-1</code>, <code>us-west-2</code>. </p> <p>Available values in AWS GovCloud (US) Regions: <code>us-gov-east-1</code>, <code>us-gov-west-1</code>.</p>"}, "MeetingHostId": {"shape": "ExternalUserId", "documentation": "<p>Reserved.</p>"}, "ExternalMeetingId": {"shape": "ExternalMeetingId", "documentation": "<p>The external meeting ID.</p> <p>Pattern: <code>[-_&amp;@+=,(){}\\[\\]\\/«».:|'\"#a-zA-Z0-9À-ÿ\\s]*</code> </p> <p>Values that begin with <code>aws:</code> are reserved. You can't configure a value that uses this prefix. Case insensitive.</p>"}, "NotificationsConfiguration": {"shape": "NotificationsConfiguration", "documentation": "<p>The configuration for resource targets to receive notifications when meeting and attendee events occur.</p>"}, "MeetingFeatures": {"shape": "MeetingFeaturesConfiguration", "documentation": "<p>Lists the audio and video features enabled for a meeting, such as echo reduction.</p>"}, "PrimaryMeetingId": {"shape": "PrimaryMeetingId", "documentation": "<p>When specified, replicates the media from the primary meeting to the new meeting.</p>"}, "TenantIds": {"shape": "TenantIdList", "documentation": "<p>A consistent and opaque identifier, created and maintained by the builder to represent a segment of their users.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>Applies one or more tags to an Amazon Chime SDK meeting. Note the following:</p> <ul> <li> <p>Not all resources have tags. For a list of services with resources that support tagging using this operation, see <a href=\"https://docs.aws.amazon.com/resourcegroupstagging/latest/APIReference/supported-services.html\">Services that support the Resource Groups Tagging API</a>. If the resource doesn't yet support this operation, the resource's service might support tagging using its own API operations. For more information, refer to the documentation for that service.</p> </li> <li> <p>Each resource can have up to 50 tags. For other limits, see <a href=\"https://docs.aws.amazon.com/general/latest/gr/aws_tagging.html#tag-conventions\">Tag Naming and Usage Conventions</a> in the <i>AWS General Reference</i>.</p> </li> <li> <p>You can only tag resources that are located in the specified AWS Region for the AWS account.</p> </li> <li> <p>To add tags to a resource, you need the necessary permissions for the service that the resource belongs to as well as permissions for adding tags. For more information, see the documentation for each service.</p> </li> </ul> <important> <p>Do not store personally identifiable information (PII) or other confidential or sensitive information in tags. We use tags to provide you with billing and administration services. Tags are not intended to be used for private or sensitive data.</p> </important> <p> <b>Minimum permissions</b> </p> <p>In addition to the <code>tag:TagResources</code> permission required by this operation, you must also have the tagging permission defined by the service that created the resource. For example, to tag a <code>ChimeSDKMeetings</code> instance using the <code>TagResources</code> operation, you must have both of the following permissions:</p> <p> <code>tag:TagResources</code> </p> <p> <code>ChimeSDKMeetings:CreateTags</code> </p> <note> <p>Some services might have specific requirements for tagging some resources. For example, to tag an Amazon S3 bucket, you must also have the <code>s3:GetBucketTagging</code> permission. If the expected minimum permissions don't work, check the documentation for that service's tagging APIs for more information.</p> </note>"}}}, "CreateMeetingResponse": {"type": "structure", "members": {"Meeting": {"shape": "Meeting", "documentation": "<p>The meeting information, including the meeting ID and <code>MediaPlacement</code>.</p>"}}}, "CreateMeetingWithAttendeesRequest": {"type": "structure", "required": ["ClientRequestToken", "MediaRegion", "ExternalMeetingId", "Attendees"], "members": {"ClientRequestToken": {"shape": "ClientRequestToken", "documentation": "<p>The unique identifier for the client request. Use a different token for different meetings.</p>", "idempotencyToken": true}, "MediaRegion": {"shape": "MediaRegion", "documentation": "<p>The Region in which to create the meeting.</p> <p> Available values: <code>af-south-1</code>, <code>ap-northeast-1</code>, <code>ap-northeast-2</code>, <code>ap-south-1</code>, <code>ap-southeast-1</code>, <code>ap-southeast-2</code>, <code>ca-central-1</code>, <code>eu-central-1</code>, <code>eu-north-1</code>, <code>eu-south-1</code>, <code>eu-west-1</code>, <code>eu-west-2</code>, <code>eu-west-3</code>, <code>sa-east-1</code>, <code>us-east-1</code>, <code>us-east-2</code>, <code>us-west-1</code>, <code>us-west-2</code>. </p> <p>Available values in AWS GovCloud (US) Regions: <code>us-gov-east-1</code>, <code>us-gov-west-1</code>.</p>"}, "MeetingHostId": {"shape": "ExternalUserId", "documentation": "<p>Reserved.</p>"}, "ExternalMeetingId": {"shape": "ExternalMeetingId", "documentation": "<p>The external meeting ID.</p> <p>Pattern: <code>[-_&amp;@+=,(){}\\[\\]\\/«».:|'\"#a-zA-Z0-9À-ÿ\\s]*</code> </p> <p>Values that begin with <code>aws:</code> are reserved. You can't configure a value that uses this prefix. Case insensitive.</p>"}, "MeetingFeatures": {"shape": "MeetingFeaturesConfiguration", "documentation": "<p>Lists the audio and video features enabled for a meeting, such as echo reduction.</p>"}, "NotificationsConfiguration": {"shape": "NotificationsConfiguration", "documentation": "<p>The configuration for resource targets to receive notifications when meeting and attendee events occur.</p>"}, "Attendees": {"shape": "CreateMeetingWithAttendeesRequestItemList", "documentation": "<p>The attendee information, including attendees' IDs and join tokens.</p>"}, "PrimaryMeetingId": {"shape": "PrimaryMeetingId", "documentation": "<p>When specified, replicates the media from the primary meeting to the new meeting.</p>"}, "TenantIds": {"shape": "TenantIdList", "documentation": "<p>A consistent and opaque identifier, created and maintained by the builder to represent a segment of their users.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>The tags in the request.</p>"}}}, "CreateMeetingWithAttendeesRequestItemList": {"type": "list", "member": {"shape": "CreateAttendeeRequestItem"}, "max": 20, "min": 1}, "CreateMeetingWithAttendeesResponse": {"type": "structure", "members": {"Meeting": {"shape": "Meeting", "documentation": "<p>The meeting information, including the meeting ID and <code>MediaPlacement</code>.</p>"}, "Attendees": {"shape": "AttendeeList", "documentation": "<p>The attendee information, including attendees' IDs and join tokens.</p>"}, "Errors": {"shape": "BatchCreateAttendeeErrorList", "documentation": "<p>If the action fails for one or more of the attendees in the request, a list of the attendees is returned, along with error codes and error messages.</p>"}}}, "DeleteAttendeeRequest": {"type": "structure", "required": ["MeetingId", "At<PERSON>eeId"], "members": {"MeetingId": {"shape": "GuidString", "documentation": "<p>The Amazon Chime SDK meeting ID.</p>", "location": "uri", "locationName": "MeetingId"}, "AttendeeId": {"shape": "GuidString", "documentation": "<p>The Amazon Chime SDK attendee ID.</p>", "location": "uri", "locationName": "At<PERSON>eeId"}}}, "DeleteMeetingRequest": {"type": "structure", "required": ["MeetingId"], "members": {"MeetingId": {"shape": "GuidString", "documentation": "<p>The Amazon Chime SDK meeting ID.</p>", "location": "uri", "locationName": "MeetingId"}}}, "EngineTranscribeMedicalSettings": {"type": "structure", "required": ["LanguageCode", "Specialty", "Type"], "members": {"LanguageCode": {"shape": "TranscribeMedicalLanguageCode", "documentation": "<p>The language code specified for the Amazon Transcribe Medical engine.</p>"}, "Specialty": {"shape": "TranscribeMedicalSpecialty", "documentation": "<p>The specialty specified for the Amazon Transcribe Medical engine.</p>"}, "Type": {"shape": "TranscribeMedicalType", "documentation": "<p>The type of transcription.</p>"}, "VocabularyName": {"shape": "String", "documentation": "<p>The name of the vocabulary passed to Amazon Transcribe Medical.</p>"}, "Region": {"shape": "TranscribeMedicalRegion", "documentation": "<p>The AWS Region passed to Amazon Transcribe Medical. If you don't specify a Region, Amazon Chime uses the meeting's Region. </p>"}, "ContentIdentificationType": {"shape": "TranscribeMedicalContentIdentificationType", "documentation": "<p>Set this field to <code>PHI</code> to identify personal health information in the transcription output.</p>"}}, "documentation": "<p>Settings specific to the Amazon Transcribe Medical engine.</p>"}, "EngineTranscribeSettings": {"type": "structure", "members": {"LanguageCode": {"shape": "TranscribeLanguageCode", "documentation": "<p>The language code specified for the Amazon Transcribe engine.</p>"}, "VocabularyFilterMethod": {"shape": "TranscribeVocabularyFilterMethod", "documentation": "<p>The filtering method passed to Amazon Transcribe.</p>"}, "VocabularyFilterName": {"shape": "String", "documentation": "<p>The name of the vocabulary filter passed to Amazon Transcribe.</p>"}, "VocabularyName": {"shape": "String", "documentation": "<p>The name of the vocabulary passed to Amazon Transcribe.</p>"}, "Region": {"shape": "TranscribeRegion", "documentation": "<p>The AWS Region passed to Amazon Transcribe. If you don't specify a Region, Amazon Chime uses the meeting's Region.</p>"}, "EnablePartialResultsStabilization": {"shape": "Boolean", "documentation": "<p>Generates partial transcription results that are less likely to change as meeting attendees speak. It does so by only allowing the last few words from the partial results to change.</p>"}, "PartialResultsStability": {"shape": "TranscribePartialResultsStability", "documentation": "<p>The stabity level of a partial results transcription. Determines how stable you want the transcription results to be. A higher level means the transcription results are less likely to change.</p>"}, "ContentIdentificationType": {"shape": "TranscribeContentIdentificationType", "documentation": "<p>Set this field to <code>PII</code> to identify personally identifiable information in the transcription output.</p>"}, "ContentRedactionType": {"shape": "TranscribeContentRedactionType", "documentation": "<p>Set this field to <code>PII</code> to redact personally identifiable information in the transcription output. Content redaction is performed only upon complete transcription of the audio segments.</p> <p>You can’t set <code>ContentRedactionType</code> and <code>ContentIdentificationType</code> in the same request. If you set both, your request returns a <code>BadRequestException</code>.</p>"}, "PiiEntityTypes": {"shape": "TranscribePiiEntityTypes", "documentation": "<p>Lists the PII entity types you want to identify or redact. To specify entity types, you must enable <code>ContentIdentificationType</code> or <code>ContentRedactionType</code>.</p> <p> <code>PIIEntityTypes</code> must be comma-separated. The available values are: <code>BANK_ACCOUNT_NUMBER</code>, <code>BANK_ROUTING, CREDIT_DEBIT_NUMBER</code>, <code>CREDIT_DEBIT_CVV</code>, <code>CREDIT_DEBIT_EXPIRY</code>, <code>PIN</code>, <code>EMAIL</code>, <code>ADDRESS</code>, <code>NAME</code>, <code>PHONE</code>, <code>SSN</code>, and <code>ALL</code>.</p> <p> <code>PiiEntityTypes</code> is an optional parameter with a default value of <code>ALL</code>.</p>"}, "LanguageModelName": {"shape": "TranscribeLanguageModelName", "documentation": "<p>The name of the language model used during transcription.</p>"}, "IdentifyLanguage": {"shape": "Boolean", "documentation": "<p>Automatically identifies the language spoken in media files.</p>"}, "LanguageOptions": {"shape": "TranscribeLanguageOptions", "documentation": "<p>Language codes for the languages that you want to identify. You must provide at least 2 codes.</p>"}, "PreferredLanguage": {"shape": "TranscribeLanguageCode", "documentation": "<p>Language code for the preferred language.</p>"}}, "documentation": "<p>Settings specific to the Amazon Transcribe engine.</p>"}, "ExternalMeetingId": {"type": "string", "max": 64, "min": 2, "sensitive": true}, "ExternalUserId": {"type": "string", "max": 64, "min": 2, "sensitive": true}, "ForbiddenException": {"type": "structure", "members": {"Code": {"shape": "String"}, "Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The request id associated with the call responsible for the exception.</p>"}}, "documentation": "<p>The client is permanently forbidden from making the request.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "GetAttendeeRequest": {"type": "structure", "required": ["MeetingId", "At<PERSON>eeId"], "members": {"MeetingId": {"shape": "GuidString", "documentation": "<p>The Amazon Chime SDK meeting ID.</p>", "location": "uri", "locationName": "MeetingId"}, "AttendeeId": {"shape": "GuidString", "documentation": "<p>The Amazon Chime SDK attendee ID.</p>", "location": "uri", "locationName": "At<PERSON>eeId"}}}, "GetAttendeeResponse": {"type": "structure", "members": {"Attendee": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The Amazon Chime SDK attendee information.</p>"}}}, "GetMeetingRequest": {"type": "structure", "required": ["MeetingId"], "members": {"MeetingId": {"shape": "GuidString", "documentation": "<p>The Amazon Chime SDK meeting ID.</p>", "location": "uri", "locationName": "MeetingId"}}}, "GetMeetingResponse": {"type": "structure", "members": {"Meeting": {"shape": "Meeting", "documentation": "<p>The Amazon Chime SDK meeting information.</p>"}}}, "GuidString": {"type": "string", "pattern": "[a-fA-F0-9]{8}(?:-[a-fA-F0-9]{4}){3}-[a-fA-F0-9]{12}"}, "JoinTokenString": {"type": "string", "max": 2048, "min": 2, "sensitive": true}, "LimitExceededException": {"type": "structure", "members": {"Code": {"shape": "String"}, "Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The request id associated with the call responsible for the exception.</p>"}}, "documentation": "<p>The request exceeds the resource limit.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "ListAttendeesRequest": {"type": "structure", "required": ["MeetingId"], "members": {"MeetingId": {"shape": "GuidString", "documentation": "<p>The Amazon Chime SDK meeting ID.</p>", "location": "uri", "locationName": "MeetingId"}, "NextToken": {"shape": "String", "documentation": "<p>The token to use to retrieve the next page of results.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "ResultMax", "documentation": "<p>The maximum number of results to return in a single call.</p>", "location": "querystring", "locationName": "max-results"}}}, "ListAttendeesResponse": {"type": "structure", "members": {"Attendees": {"shape": "AttendeeList", "documentation": "<p>The Amazon Chime SDK attendee information.</p>"}, "NextToken": {"shape": "String", "documentation": "<p>The token to use to retrieve the next page of results.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["ResourceARN"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource.</p>", "location": "querystring", "locationName": "arn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>The tags requested for the specified resource.</p>"}}}, "MediaCapabilities": {"type": "string", "enum": ["SendReceive", "Send", "Receive", "None"]}, "MediaPlacement": {"type": "structure", "members": {"AudioHostUrl": {"shape": "String", "documentation": "<p>The audio host URL.</p>"}, "AudioFallbackUrl": {"shape": "String", "documentation": "<p>The audio fallback URL.</p>"}, "SignalingUrl": {"shape": "String", "documentation": "<p>The signaling URL.</p>"}, "TurnControlUrl": {"shape": "String", "documentation": "<p>The turn control URL.</p>"}, "ScreenDataUrl": {"shape": "String", "documentation": "<p>The screen data URL.</p>"}, "ScreenViewingUrl": {"shape": "String", "documentation": "<p>The screen viewing URL.</p>"}, "ScreenSharingUrl": {"shape": "String", "documentation": "<p>The screen sharing URL.</p>"}, "EventIngestionUrl": {"shape": "String", "documentation": "<p>The event ingestion URL.</p>"}}, "documentation": "<p>A set of endpoints used by clients to connect to the media service group for an Amazon Chime SDK meeting.</p>"}, "MediaRegion": {"type": "string", "max": 64, "min": 2}, "Meeting": {"type": "structure", "members": {"MeetingId": {"shape": "GuidString", "documentation": "<p>The Amazon Chime SDK meeting ID.</p>"}, "MeetingHostId": {"shape": "ExternalUserId", "documentation": "<p>Reserved.</p>"}, "ExternalMeetingId": {"shape": "ExternalMeetingId", "documentation": "<p>The external meeting ID.</p> <p>Pattern: <code>[-_&amp;@+=,(){}\\[\\]\\/«».:|'\"#a-zA-Z0-9À-ÿ\\s]*</code> </p> <p>Values that begin with <code>aws:</code> are reserved. You can't configure a value that uses this prefix. Case insensitive.</p>"}, "MediaRegion": {"shape": "MediaRegion", "documentation": "<p>The Region in which you create the meeting. Available values: <code>af-south-1</code>, <code>ap-northeast-1</code>, <code>ap-northeast-2</code>, <code>ap-south-1</code>, <code>ap-southeast-1</code>, <code>ap-southeast-2</code>, <code>ca-central-1</code>, <code>eu-central-1</code>, <code>eu-north-1</code>, <code>eu-south-1</code>, <code>eu-west-1</code>, <code>eu-west-2</code>, <code>eu-west-3</code>, <code>sa-east-1</code>, <code>us-east-1</code>, <code>us-east-2</code>, <code>us-west-1</code>, <code>us-west-2</code>.</p> <p>Available values in AWS GovCloud (US) Regions: <code>us-gov-east-1</code>, <code>us-gov-west-1</code>.</p>"}, "MediaPlacement": {"shape": "MediaPlacement", "documentation": "<p>The media placement for the meeting.</p>"}, "MeetingFeatures": {"shape": "MeetingFeaturesConfiguration", "documentation": "<p>The features available to a meeting, such as echo reduction.</p>"}, "PrimaryMeetingId": {"shape": "PrimaryMeetingId", "documentation": "<p>When specified, replicates the media from the primary meeting to this meeting.</p>"}, "TenantIds": {"shape": "TenantIdList", "documentation": "<p>Array of strings.</p>"}, "MeetingArn": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the meeting.</p>"}}, "documentation": "<p>A meeting created using the Amazon Chime SDK.</p>"}, "MeetingFeatureStatus": {"type": "string", "enum": ["AVAILABLE", "UNAVAILABLE"]}, "MeetingFeaturesConfiguration": {"type": "structure", "members": {"Audio": {"shape": "AudioFeatures", "documentation": "<p>The configuration settings for the audio features available to a meeting.</p>"}}, "documentation": "<p>The configuration settings of the features available to a meeting.</p>"}, "NotFoundException": {"type": "structure", "members": {"Code": {"shape": "String"}, "Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The request ID associated with the call responsible for the exception.</p>"}}, "documentation": "<p>One or more of the resources in the request does not exist in the system.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "NotificationsConfiguration": {"type": "structure", "members": {"LambdaFunctionArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the AWS Lambda function in the notifications configuration.</p>"}, "SnsTopicArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the SNS topic.</p>"}, "SqsQueueArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the SQS queue.</p>"}}, "documentation": "<p>The configuration for resource targets to receive notifications when meeting and attendee events occur.</p>"}, "PrimaryMeetingId": {"type": "string", "max": 64, "min": 2}, "ResourceNotFoundException": {"type": "structure", "members": {"Code": {"shape": "String"}, "Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The ID of the resource that couldn't be found.</p>"}, "ResourceName": {"shape": "AmazonResourceName", "documentation": "<p>The name of the resource that couldn't be found.</p>"}}, "documentation": "<p>The resource that you want to tag couldn't be found.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "ResultMax": {"type": "integer", "max": 100, "min": 1}, "RetryAfterSeconds": {"type": "string"}, "ServiceFailureException": {"type": "structure", "members": {"Code": {"shape": "String"}, "Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The ID of the failed request.</p>"}}, "documentation": "<p>The service encountered an unexpected error.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "ServiceUnavailableException": {"type": "structure", "members": {"Code": {"shape": "String"}, "Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The request id associated with the call responsible for the exception.</p>"}, "RetryAfterSeconds": {"shape": "RetryAfterSeconds", "documentation": "<p>The number of seconds the caller should wait before retrying.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>The service is currently unavailable.</p>", "error": {"httpStatusCode": 503}, "exception": true, "fault": true}, "StartMeetingTranscriptionRequest": {"type": "structure", "required": ["MeetingId", "TranscriptionConfiguration"], "members": {"MeetingId": {"shape": "GuidString", "documentation": "<p>The unique ID of the meeting being transcribed.</p>", "location": "uri", "locationName": "MeetingId"}, "TranscriptionConfiguration": {"shape": "TranscriptionConfiguration", "documentation": "<p>The configuration for the current transcription operation. Must contain <code>EngineTranscribeSettings</code> or <code>EngineTranscribeMedicalSettings</code>.</p>"}}}, "StopMeetingTranscriptionRequest": {"type": "structure", "required": ["MeetingId"], "members": {"MeetingId": {"shape": "GuidString", "documentation": "<p>The unique ID of the meeting for which you stop transcription.</p>", "location": "uri", "locationName": "MeetingId"}}}, "String": {"type": "string", "max": 4096}, "Tag": {"type": "structure", "required": ["Key", "Value"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>The tag's key.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>The tag's value.</p>"}}, "documentation": "<p>A key-value pair that you define.</p>"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^[a-zA-Z+-=._:/]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 0}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 50, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["ResourceARN", "Tags"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource.</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>Lists the requested tags.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "[\\s\\w+-=\\.:/@]*"}, "TenantId": {"type": "string", "max": 256, "min": 2, "pattern": "^(?!.*?(.)\\1{3})[-_!@#$a-zA-Z0-9]*$"}, "TenantIdList": {"type": "list", "member": {"shape": "TenantId"}, "max": 5, "min": 1}, "ThrottlingException": {"type": "structure", "members": {"Code": {"shape": "String"}, "Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The ID of the request that exceeded the throttling limit.</p>"}}, "documentation": "<p>The number of customer requests exceeds the request rate limit.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "TooManyTagsException": {"type": "structure", "members": {"Code": {"shape": "String"}, "Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The ID of the request that contains too many tags.</p>"}, "ResourceName": {"shape": "AmazonResourceName", "documentation": "<p>The name of the resource that received too many tags.</p>"}}, "documentation": "<p>Too many tags were added to the specified resource.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "TranscribeContentIdentificationType": {"type": "string", "enum": ["PII"]}, "TranscribeContentRedactionType": {"type": "string", "enum": ["PII"]}, "TranscribeLanguageCode": {"type": "string", "enum": ["en-US", "en-GB", "es-US", "fr-CA", "fr-FR", "en-AU", "it-IT", "de-DE", "pt-BR", "ja-<PERSON>", "ko-KR", "zh-CN"]}, "TranscribeLanguageModelName": {"type": "string", "max": 200, "min": 1, "pattern": "^[0-9a-zA-Z._-]+"}, "TranscribeLanguageOptions": {"type": "string", "max": 200, "min": 1, "pattern": "^[a-zA-Z-,]+"}, "TranscribeMedicalContentIdentificationType": {"type": "string", "enum": ["PHI"]}, "TranscribeMedicalLanguageCode": {"type": "string", "enum": ["en-US"]}, "TranscribeMedicalRegion": {"type": "string", "enum": ["us-east-1", "us-east-2", "us-west-2", "ap-southeast-2", "ca-central-1", "eu-west-1", "auto"]}, "TranscribeMedicalSpecialty": {"type": "string", "enum": ["PRIMARYCARE", "CARDIOLOGY", "NEUROLOGY", "ONCOLOGY", "RADIOLOGY", "UROLOGY"]}, "TranscribeMedicalType": {"type": "string", "enum": ["CONVERSATION", "DICTATION"]}, "TranscribePartialResultsStability": {"type": "string", "enum": ["low", "medium", "high"]}, "TranscribePiiEntityTypes": {"type": "string", "max": 300, "min": 1, "pattern": "^[A-Z_, ]+"}, "TranscribeRegion": {"type": "string", "enum": ["us-east-2", "us-east-1", "us-west-2", "ap-northeast-2", "ap-southeast-2", "ap-northeast-1", "ca-central-1", "eu-central-1", "eu-west-1", "eu-west-2", "sa-east-1", "auto", "us-gov-west-1"]}, "TranscribeVocabularyFilterMethod": {"type": "string", "enum": ["remove", "mask", "tag"]}, "TranscriptionConfiguration": {"type": "structure", "members": {"EngineTranscribeSettings": {"shape": "EngineTranscribeSettings", "documentation": "<p>The transcription configuration settings passed to Amazon Transcribe.</p>"}, "EngineTranscribeMedicalSettings": {"shape": "EngineTranscribeMedicalSettings", "documentation": "<p>The transcription configuration settings passed to Amazon Transcribe Medical.</p>"}}, "documentation": "<p>The configuration for the current transcription operation. Must contain <code>EngineTranscribeSettings</code> or <code>EngineTranscribeMedicalSettings</code>.</p>"}, "UnauthorizedException": {"type": "structure", "members": {"Code": {"shape": "String"}, "Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The request id associated with the call responsible for the exception.</p>"}}, "documentation": "<p>The user isn't authorized to request a resource.</p>", "error": {"httpStatusCode": 401}, "exception": true}, "UnprocessableEntityException": {"type": "structure", "members": {"Code": {"shape": "String"}, "Message": {"shape": "String"}, "RequestId": {"shape": "String", "documentation": "<p>The request id associated with the call responsible for the exception.</p>"}}, "documentation": "<p>The request was well-formed but was unable to be followed due to semantic errors.</p>", "error": {"httpStatusCode": 422}, "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["ResourceARN", "TagKeys"], "members": {"ResourceARN": {"shape": "AmazonResourceName", "documentation": "<p>The ARN of the resource that you're removing tags from.</p>"}, "TagKeys": {"shape": "TagKeyList", "documentation": "<p>The tag keys being removed from the resources.</p>"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAttendeeCapabilitiesRequest": {"type": "structure", "required": ["MeetingId", "At<PERSON>eeId", "Capabilities"], "members": {"MeetingId": {"shape": "GuidString", "documentation": "<p>The ID of the meeting associated with the update request.</p>", "location": "uri", "locationName": "MeetingId"}, "AttendeeId": {"shape": "GuidString", "documentation": "<p>The ID of the attendee associated with the update request.</p>", "location": "uri", "locationName": "At<PERSON>eeId"}, "Capabilities": {"shape": "AttendeeCapabilities", "documentation": "<p>The capabilties that you want to update.</p>"}}}, "UpdateAttendeeCapabilitiesResponse": {"type": "structure", "members": {"Attendee": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The updated attendee data.</p>"}}}}, "documentation": "<p>The Amazon Chime SDK meetings APIs in this section allow software developers to create Amazon Chime SDK meetings, set the AWS Regions for meetings, create and manage users, and send and receive meeting notifications. For more information about the meeting APIs, see <a href=\"https://docs.aws.amazon.com/chime/latest/APIReference/API_Operations_Amazon_Chime_SDK_Meetings.html\">Amazon Chime SDK meetings</a>.</p>"}