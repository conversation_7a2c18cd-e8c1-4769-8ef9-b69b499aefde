{"version": "2.0", "metadata": {"apiVersion": "2019-09-01", "endpointPrefix": "braket", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Braket", "serviceId": "Braket", "signatureVersion": "v4", "signingName": "braket", "uid": "braket-2019-09-01"}, "operations": {"CancelJob": {"name": "<PERSON><PERSON><PERSON><PERSON>", "http": {"method": "PUT", "requestUri": "/job/{jobArn}/cancel", "responseCode": 200}, "input": {"shape": "CancelJobRequest"}, "output": {"shape": "CancelJobResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServiceException"}, {"shape": "ValidationException"}], "documentation": "<p>Cancels an Amazon Braket job.</p>", "idempotent": true}, "CancelQuantumTask": {"name": "CancelQuantumTask", "http": {"method": "PUT", "requestUri": "/quantum-task/{quantumTaskArn}/cancel", "responseCode": 200}, "input": {"shape": "CancelQuantumTaskRequest"}, "output": {"shape": "CancelQuantumTaskResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ConflictException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServiceException"}, {"shape": "ValidationException"}], "documentation": "<p>Cancels the specified task.</p>", "idempotent": true}, "CreateJob": {"name": "<PERSON><PERSON><PERSON><PERSON>", "http": {"method": "POST", "requestUri": "/job", "responseCode": 201}, "input": {"shape": "CreateJobRequest"}, "output": {"shape": "CreateJobResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "DeviceRetiredException"}, {"shape": "InternalServiceException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}], "documentation": "<p>Creates an Amazon Braket job.</p>"}, "CreateQuantumTask": {"name": "CreateQuantumTask", "http": {"method": "POST", "requestUri": "/quantum-task", "responseCode": 201}, "input": {"shape": "CreateQuantumTaskRequest"}, "output": {"shape": "CreateQuantumTaskResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "DeviceOfflineException"}, {"shape": "DeviceRetiredException"}, {"shape": "InternalServiceException"}, {"shape": "ServiceQuotaExceededException"}, {"shape": "ValidationException"}], "documentation": "<p>Creates a quantum task.</p>"}, "GetDevice": {"name": "GetDevice", "http": {"method": "GET", "requestUri": "/device/{deviceArn}", "responseCode": 200}, "input": {"shape": "GetDeviceRequest"}, "output": {"shape": "GetDeviceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServiceException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves the devices available in Amazon Braket.</p> <note> <p>For backwards compatibility with older versions of BraketSchemas, OpenQASM information is omitted from GetDevice API calls. To get this information the user-agent needs to present a recent version of the BraketSchemas (1.8.0 or later). The Braket SDK automatically reports this for you. If you do not see OpenQASM results in the GetDevice response when using a Braket SDK, you may need to set AWS_EXECUTION_ENV environment variable to configure user-agent. See the code examples provided below for how to do this for the AWS CLI, Boto3, and the Go, Java, and JavaScript/TypeScript SDKs.</p> </note>"}, "GetJob": {"name": "Get<PERSON>ob", "http": {"method": "GET", "requestUri": "/job/{jobArn}", "responseCode": 200}, "input": {"shape": "GetJobRequest"}, "output": {"shape": "GetJobResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServiceException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves the specified Amazon Braket job.</p>"}, "GetQuantumTask": {"name": "GetQuantumTask", "http": {"method": "GET", "requestUri": "/quantum-task/{quantumTaskArn}", "responseCode": 200}, "input": {"shape": "GetQuantumTaskRequest"}, "output": {"shape": "GetQuantumTaskResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServiceException"}, {"shape": "ValidationException"}], "documentation": "<p>Retrieves the specified quantum task.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceException"}, {"shape": "ValidationException"}], "documentation": "<p>Shows the tags associated with this resource.</p>"}, "SearchDevices": {"name": "SearchDevices", "http": {"method": "POST", "requestUri": "/devices", "responseCode": 200}, "input": {"shape": "SearchDevicesRequest"}, "output": {"shape": "SearchDevicesResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServiceException"}, {"shape": "ValidationException"}], "documentation": "<p>Searches for devices using the specified filters.</p>"}, "SearchJobs": {"name": "SearchJobs", "http": {"method": "POST", "requestUri": "/jobs", "responseCode": 200}, "input": {"shape": "SearchJobsRequest"}, "output": {"shape": "SearchJobsResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServiceException"}, {"shape": "ValidationException"}], "documentation": "<p>Searches for Amazon Braket jobs that match the specified filter values.</p>"}, "SearchQuantumTasks": {"name": "SearchQuantumTasks", "http": {"method": "POST", "requestUri": "/quantum-tasks", "responseCode": 200}, "input": {"shape": "SearchQuantumTasksRequest"}, "output": {"shape": "SearchQuantumTasksResponse"}, "errors": [{"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServiceException"}, {"shape": "ValidationException"}], "documentation": "<p>Searches for tasks that match the specified filter values.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceException"}, {"shape": "ValidationException"}], "documentation": "<p>Add a tag to the specified resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InternalServiceException"}, {"shape": "ValidationException"}], "documentation": "<p>Remove tags from a resource.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AlgorithmSpecification": {"type": "structure", "members": {"containerImage": {"shape": "ContainerImage", "documentation": "<p>The container image used to create an Amazon Braket job.</p>"}, "scriptModeConfig": {"shape": "ScriptModeConfig", "documentation": "<p>Configures the paths to the Python scripts used for entry and training.</p>"}}, "documentation": "<p>Defines the Amazon Braket job to be created. Specifies the container image the job uses and the paths to the Python scripts used for entry and training.</p>"}, "CancelJobRequest": {"type": "structure", "required": ["jobArn"], "members": {"jobArn": {"shape": "JobArn", "documentation": "<p>The ARN of the Amazon Braket job to cancel.</p>", "location": "uri", "locationName": "jobArn"}}}, "CancelJobResponse": {"type": "structure", "required": ["cancellationStatus", "jobArn"], "members": {"cancellationStatus": {"shape": "CancellationStatus", "documentation": "<p>The status of the job cancellation request.</p>"}, "jobArn": {"shape": "JobArn", "documentation": "<p>The ARN of the Amazon Braket job.</p>"}}}, "CancelQuantumTaskRequest": {"type": "structure", "required": ["clientToken", "quantumTaskArn"], "members": {"clientToken": {"shape": "String64", "documentation": "<p>The client token associated with the request.</p>", "idempotencyToken": true}, "quantumTaskArn": {"shape": "QuantumTaskArn", "documentation": "<p>The ARN of the task to cancel.</p>", "location": "uri", "locationName": "quantumTaskArn"}}}, "CancelQuantumTaskResponse": {"type": "structure", "required": ["cancellationStatus", "quantumTaskArn"], "members": {"cancellationStatus": {"shape": "CancellationStatus", "documentation": "<p>The status of the cancellation request.</p>"}, "quantumTaskArn": {"shape": "QuantumTaskArn", "documentation": "<p>The ARN of the task.</p>"}}}, "CancellationStatus": {"type": "string", "enum": ["CANCELLING", "CANCELLED"]}, "CompressionType": {"type": "string", "enum": ["NONE", "GZIP"]}, "ConflictException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>An error occurred due to a conflict.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "ContainerImage": {"type": "structure", "required": ["uri"], "members": {"uri": {"shape": "<PERSON><PERSON>", "documentation": "<p>The URI locating the container image.</p>"}}, "documentation": "<p>The container image used to create an Amazon Braket job.</p>"}, "CreateJobRequest": {"type": "structure", "required": ["algorithmSpecification", "clientToken", "deviceConfig", "instanceConfig", "job<PERSON>ame", "outputDataConfig", "roleArn"], "members": {"algorithmSpecification": {"shape": "AlgorithmSpecification", "documentation": "<p>Definition of the Amazon Braket job to be created. Specifies the container image the job uses and information about the Python scripts used for entry and training.</p>"}, "checkpointConfig": {"shape": "JobCheckpointConfig", "documentation": "<p>Information about the output locations for job checkpoint data.</p>"}, "clientToken": {"shape": "String64", "documentation": "<p>A unique token that guarantees that the call to this API is idempotent.</p>", "idempotencyToken": true}, "deviceConfig": {"shape": "DeviceConfig", "documentation": "<p>The quantum processing unit (QPU) or simulator used to create an Amazon Braket job.</p>"}, "hyperParameters": {"shape": "HyperParameters", "documentation": "<p>Algorithm-specific parameters used by an Amazon Braket job that influence the quality of the training job. The values are set with a string of JSON key:value pairs, where the key is the name of the hyperparameter and the value is the value of th hyperparameter.</p>"}, "inputDataConfig": {"shape": "CreateJobRequestInputDataConfigList", "documentation": "<p>A list of parameters that specify the name and type of input data and where it is located.</p>"}, "instanceConfig": {"shape": "InstanceConfig", "documentation": "<p>Configuration of the resource instances to use while running the hybrid job on Amazon Braket.</p>"}, "jobName": {"shape": "CreateJobRequestJobNameString", "documentation": "<p>The name of the Amazon Braket job.</p>"}, "outputDataConfig": {"shape": "JobOutputDataConfig", "documentation": "<p>The path to the S3 location where you want to store job artifacts and the encryption key used to store them.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role that Amazon Braket can assume to perform tasks on behalf of a user. It can access user resources, run an Amazon Braket job container on behalf of user, and output resources to the users' s3 buckets.</p>"}, "stoppingCondition": {"shape": "JobStoppingCondition", "documentation": "<p> The user-defined criteria that specifies when a job stops running.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>A tag object that consists of a key and an optional value, used to manage metadata for Amazon Braket resources.</p>"}}}, "CreateJobRequestInputDataConfigList": {"type": "list", "member": {"shape": "InputFileConfig"}, "max": 20, "min": 0}, "CreateJobRequestJobNameString": {"type": "string", "max": 50, "min": 1, "pattern": "^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,50}$"}, "CreateJobResponse": {"type": "structure", "required": ["jobArn"], "members": {"jobArn": {"shape": "JobArn", "documentation": "<p>The ARN of the Amazon Braket job created.</p>"}}}, "CreateQuantumTaskRequest": {"type": "structure", "required": ["action", "clientToken", "deviceArn", "outputS3Bucket", "outputS3KeyPrefix", "shots"], "members": {"action": {"shape": "JsonValue", "documentation": "<p>The action associated with the task.</p>", "jsonvalue": true}, "clientToken": {"shape": "String64", "documentation": "<p>The client token associated with the request.</p>", "idempotencyToken": true}, "deviceArn": {"shape": "DeviceArn", "documentation": "<p>The ARN of the device to run the task on.</p>"}, "deviceParameters": {"shape": "CreateQuantumTaskRequestDeviceParametersString", "documentation": "<p>The parameters for the device to run the task on.</p>", "jsonvalue": true}, "jobToken": {"shape": "JobToken", "documentation": "<p>The token for an Amazon Braket job that associates it with the quantum task.</p>"}, "outputS3Bucket": {"shape": "CreateQuantumTaskRequestOutputS3BucketString", "documentation": "<p>The S3 bucket to store task result files in.</p>"}, "outputS3KeyPrefix": {"shape": "CreateQuantumTaskRequestOutputS3KeyPrefixString", "documentation": "<p>The key prefix for the location in the S3 bucket to store task results in.</p>"}, "shots": {"shape": "CreateQuantumTaskRequestShotsLong", "documentation": "<p>The number of shots to use for the task.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Tags to be added to the quantum task you're creating.</p>"}}}, "CreateQuantumTaskRequestDeviceParametersString": {"type": "string", "max": 48000, "min": 1}, "CreateQuantumTaskRequestOutputS3BucketString": {"type": "string", "max": 63, "min": 3}, "CreateQuantumTaskRequestOutputS3KeyPrefixString": {"type": "string", "max": 1024, "min": 1}, "CreateQuantumTaskRequestShotsLong": {"type": "long", "box": true, "min": 0}, "CreateQuantumTaskResponse": {"type": "structure", "required": ["quantumTaskArn"], "members": {"quantumTaskArn": {"shape": "QuantumTaskArn", "documentation": "<p>The ARN of the task created by the request.</p>"}}}, "DataSource": {"type": "structure", "required": ["s3DataSource"], "members": {"s3DataSource": {"shape": "S3DataSource", "documentation": "<p>Information about the data stored in Amazon S3 used by the Amazon Braket job.</p>"}}, "documentation": "<p>Information about the source of the data used by the Amazon Braket job.</p>"}, "DeviceArn": {"type": "string", "max": 256, "min": 1}, "DeviceConfig": {"type": "structure", "required": ["device"], "members": {"device": {"shape": "String256", "documentation": "<p>The primary quantum processing unit (QPU) or simulator used to create and run an Amazon Braket job.</p>"}}, "documentation": "<p>Configures the quantum processing units (QPUs) or simulator used to create and run an Amazon Braket job.</p>"}, "DeviceOfflineException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The specified device is currently offline.</p>", "error": {"httpStatusCode": 424, "senderFault": true}, "exception": true}, "DeviceRetiredException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The specified device has been retired.</p>", "error": {"httpStatusCode": 410, "senderFault": true}, "exception": true}, "DeviceStatus": {"type": "string", "enum": ["ONLINE", "OFFLINE", "RETIRED"]}, "DeviceSummary": {"type": "structure", "required": ["deviceArn", "deviceName", "deviceStatus", "deviceType", "providerName"], "members": {"deviceArn": {"shape": "DeviceArn", "documentation": "<p>The ARN of the device.</p>"}, "deviceName": {"shape": "String", "documentation": "<p>The name of the device.</p>"}, "deviceStatus": {"shape": "DeviceStatus", "documentation": "<p>The status of the device.</p>"}, "deviceType": {"shape": "DeviceType", "documentation": "<p>The type of the device.</p>"}, "providerName": {"shape": "String", "documentation": "<p>The provider of the device.</p>"}}, "documentation": "<p>Includes information about the device.</p>"}, "DeviceSummaryList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "DeviceType": {"type": "string", "enum": ["QPU", "SIMULATOR"]}, "GetDeviceRequest": {"type": "structure", "required": ["deviceArn"], "members": {"deviceArn": {"shape": "DeviceArn", "documentation": "<p>The ARN of the device to retrieve.</p>", "location": "uri", "locationName": "deviceArn"}}}, "GetDeviceResponse": {"type": "structure", "required": ["deviceArn", "deviceCapabilities", "deviceName", "deviceStatus", "deviceType", "providerName"], "members": {"deviceArn": {"shape": "DeviceArn", "documentation": "<p>The ARN of the device.</p>"}, "deviceCapabilities": {"shape": "JsonValue", "documentation": "<p>Details about the capabilities of the device.</p>", "jsonvalue": true}, "deviceName": {"shape": "String", "documentation": "<p>The name of the device.</p>"}, "deviceStatus": {"shape": "DeviceStatus", "documentation": "<p>The status of the device.</p>"}, "deviceType": {"shape": "DeviceType", "documentation": "<p>The type of the device.</p>"}, "providerName": {"shape": "String", "documentation": "<p>The name of the partner company for the device.</p>"}}}, "GetJobRequest": {"type": "structure", "required": ["jobArn"], "members": {"jobArn": {"shape": "JobArn", "documentation": "<p>The ARN of the job to retrieve.</p>", "location": "uri", "locationName": "jobArn"}}}, "GetJobResponse": {"type": "structure", "required": ["algorithmSpecification", "createdAt", "instanceConfig", "jobArn", "job<PERSON>ame", "outputDataConfig", "roleArn", "status"], "members": {"algorithmSpecification": {"shape": "AlgorithmSpecification", "documentation": "<p>Definition of the Amazon Braket job created. Specifies the container image the job uses, information about the Python scripts used for entry and training, and the user-defined metrics used to evaluation the job.</p>"}, "billableDuration": {"shape": "Integer", "documentation": "<p>The billable time the Amazon Braket job used to complete.</p>"}, "checkpointConfig": {"shape": "JobCheckpointConfig", "documentation": "<p>Information about the output locations for job checkpoint data.</p>"}, "createdAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The date and time that the Amazon Braket job was created.</p>"}, "deviceConfig": {"shape": "DeviceConfig", "documentation": "<p>The quantum processing unit (QPU) or simulator used to run the Amazon Braket job.</p>"}, "endedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The date and time that the Amazon Braket job ended.</p>"}, "events": {"shape": "JobEvents", "documentation": "<p>Details about the type and time events occurred related to the Amazon Braket job.</p>"}, "failureReason": {"shape": "String1024", "documentation": "<p>A description of the reason why an Amazon Braket job failed, if it failed.</p>"}, "hyperParameters": {"shape": "HyperParameters", "documentation": "<p>Algorithm-specific parameters used by an Amazon Braket job that influence the quality of the traiing job. The values are set with a string of JSON key:value pairs, where the key is the name of the hyperparameter and the value is the value of th hyperparameter.</p>"}, "inputDataConfig": {"shape": "InputConfigList", "documentation": "<p>A list of parameters that specify the name and type of input data and where it is located.</p>"}, "instanceConfig": {"shape": "InstanceConfig", "documentation": "<p>The resource instances to use while running the hybrid job on Amazon Braket.</p>"}, "jobArn": {"shape": "JobArn", "documentation": "<p>The ARN of the Amazon Braket job.</p>"}, "jobName": {"shape": "GetJobResponseJobNameString", "documentation": "<p>The name of the Amazon Braket job.</p>"}, "outputDataConfig": {"shape": "JobOutputDataConfig", "documentation": "<p>The path to the S3 location where job artifacts are stored and the encryption key used to store them there.</p>"}, "roleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of an IAM role that Amazon Braket can assume to perform tasks on behalf of a user. It can access user resources, run an Amazon Braket job container on behalf of user, and output resources to the s3 buckets of a user.</p>"}, "startedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The date and time that the Amazon Braket job was started.</p>"}, "status": {"shape": "JobPrimaryStatus", "documentation": "<p>The status of the Amazon Braket job.</p>"}, "stoppingCondition": {"shape": "JobStoppingCondition", "documentation": "<p>The user-defined criteria that specifies when to stop a job running.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>A tag object that consists of a key and an optional value, used to manage metadata for Amazon Braket resources.</p>"}}}, "GetJobResponseJobNameString": {"type": "string", "max": 50, "min": 1, "pattern": "^[a-zA-Z0-9](-*[a-zA-Z0-9]){0,50}$"}, "GetQuantumTaskRequest": {"type": "structure", "required": ["quantumTaskArn"], "members": {"quantumTaskArn": {"shape": "QuantumTaskArn", "documentation": "<p>the ARN of the task to retrieve.</p>", "location": "uri", "locationName": "quantumTaskArn"}}}, "GetQuantumTaskResponse": {"type": "structure", "required": ["createdAt", "deviceArn", "deviceParameters", "outputS3Bucket", "outputS3Directory", "quantumTaskArn", "shots", "status"], "members": {"createdAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the task was created.</p>"}, "deviceArn": {"shape": "DeviceArn", "documentation": "<p>The ARN of the device the task was run on.</p>"}, "deviceParameters": {"shape": "JsonValue", "documentation": "<p>The parameters for the device on which the task ran.</p>", "jsonvalue": true}, "endedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the task ended.</p>"}, "failureReason": {"shape": "String", "documentation": "<p>The reason that a task failed.</p>"}, "jobArn": {"shape": "JobArn", "documentation": "<p>The ARN of the Amazon Braket job associated with the quantum task.</p>"}, "outputS3Bucket": {"shape": "String", "documentation": "<p>The S3 bucket where task results are stored.</p>"}, "outputS3Directory": {"shape": "String", "documentation": "<p>The folder in the S3 bucket where task results are stored.</p>"}, "quantumTaskArn": {"shape": "QuantumTaskArn", "documentation": "<p>The ARN of the task.</p>"}, "shots": {"shape": "<PERSON>", "documentation": "<p>The number of shots used in the task.</p>"}, "status": {"shape": "QuantumTaskStatus", "documentation": "<p>The status of the task.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>The tags that belong to this task.</p>"}}}, "HyperParameters": {"type": "map", "key": {"shape": "String256"}, "value": {"shape": "HyperParametersValueString"}, "max": 100, "min": 0}, "HyperParametersValueString": {"type": "string", "max": 2500, "min": 1}, "InputConfigList": {"type": "list", "member": {"shape": "InputFileConfig"}}, "InputFileConfig": {"type": "structure", "required": ["channelName", "dataSource"], "members": {"channelName": {"shape": "InputFileConfigChannelNameString", "documentation": "<p>A named input source that an Amazon Braket job can consume.</p>"}, "contentType": {"shape": "String256", "documentation": "<p>The MIME type of the data.</p>"}, "dataSource": {"shape": "DataSource", "documentation": "<p>The location of the channel data.</p>"}}, "documentation": "<p>A list of parameters that specify the input channels, type of input data, and where it is located.</p>"}, "InputFileConfigChannelNameString": {"type": "string", "max": 64, "min": 1, "pattern": "^[A-Za-z0-9\\.\\-_]+$"}, "InstanceConfig": {"type": "structure", "required": ["instanceType", "volumeSizeInGb"], "members": {"instanceCount": {"shape": "InstanceConfigInstanceCountInteger", "documentation": "<p>Configures the number of resource instances to use while running an Amazon Braket job on Amazon Braket. The default value is 1.</p>"}, "instanceType": {"shape": "InstanceType", "documentation": "<p>Configures the type resource instances to use while running an Amazon Braket hybrid job.</p>"}, "volumeSizeInGb": {"shape": "InstanceConfigVolumeSizeInGbInteger", "documentation": "<p>The size of the storage volume, in GB, that user wants to provision.</p>"}}, "documentation": "<p>Configures the resource instances to use while running the Amazon Braket hybrid job on Amazon Braket.</p>"}, "InstanceConfigInstanceCountInteger": {"type": "integer", "box": true, "min": 1}, "InstanceConfigVolumeSizeInGbInteger": {"type": "integer", "box": true, "min": 1}, "InstanceType": {"type": "string", "enum": ["ml.m4.xlarge", "ml.m4.2xlarge", "ml.m4.4xlarge", "ml.m4.10xlarge", "ml.m4.16xlarge", "ml.g4dn.xlarge", "ml.g4dn.2xlarge", "ml.g4dn.4xlarge", "ml.g4dn.8xlarge", "ml.g4dn.12xlarge", "ml.g4dn.16xlarge", "ml.m5.large", "ml.m5.xlarge", "ml.m5.2xlarge", "ml.m5.4xlarge", "ml.m5.12xlarge", "ml.m5.24xlarge", "ml.c4.xlarge", "ml.c4.2xlarge", "ml.c4.4xlarge", "ml.c4.8xlarge", "ml.p2.xlarge", "ml.p2.8xlarge", "ml.p2.16xlarge", "ml.p3.2xlarge", "ml.p3.8xlarge", "ml.p3.16xlarge", "ml.p3dn.24xlarge", "ml.p4d.24xlarge", "ml.c5.xlarge", "ml.c5.2xlarge", "ml.c5.4xlarge", "ml.c5.9xlarge", "ml.c5.18xlarge", "ml.c5n.xlarge", "ml.c5n.2xlarge", "ml.c5n.4xlarge", "ml.c5n.9xlarge", "ml.c5n.18xlarge"]}, "Integer": {"type": "integer", "box": true}, "InternalServiceException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request processing has failed because of an unknown error, exception, or failure.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "JobArn": {"type": "string", "pattern": "^arn:aws[a-z\\-]*:braket:[a-z0-9\\-]*:[0-9]{12}:job/.*$"}, "JobCheckpointConfig": {"type": "structure", "required": ["s3Uri"], "members": {"localPath": {"shape": "String4096", "documentation": "<p>(Optional) The local directory where checkpoints are written. The default directory is <code>/opt/braket/checkpoints/</code>.</p>"}, "s3Uri": {"shape": "S3Path", "documentation": "<p>Identifies the S3 path where you want Amazon Braket to store checkpoints. For example, <code>s3://bucket-name/key-name-prefix</code>.</p>"}}, "documentation": "<p>Contains information about the output locations for job checkpoint data.</p>"}, "JobEventDetails": {"type": "structure", "members": {"eventType": {"shape": "JobEventType", "documentation": "<p>The type of event that occurred related to the Amazon Braket job.</p>"}, "message": {"shape": "JobEventDetailsMessageString", "documentation": "<p>A message describing the event that occurred related to the Amazon Braket job.</p>"}, "timeOfEvent": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>TThe type of event that occurred related to the Amazon Braket job.</p>"}}, "documentation": "<p>Details about the type and time events occurred related to the Amazon Braket job.</p>"}, "JobEventDetailsMessageString": {"type": "string", "max": 2500, "min": 0}, "JobEventType": {"type": "string", "enum": ["WAITING_FOR_PRIORITY", "QUEUED_FOR_EXECUTION", "STARTING_INSTANCE", "DOWNLOADING_DATA", "RUNNING", "DEPRIORITIZED_DUE_TO_INACTIVITY", "UPLOADING_RESULTS", "COMPLETED", "FAILED", "MAX_RUNTIME_EXCEEDED", "CANCELLED"]}, "JobEvents": {"type": "list", "member": {"shape": "JobEventDetails"}, "max": 20, "min": 0}, "JobOutputDataConfig": {"type": "structure", "required": ["s3Path"], "members": {"kmsKeyId": {"shape": "String2048", "documentation": "<p>The AWS Key Management Service (AWS KMS) key that Amazon Braket uses to encrypt the job training artifacts at rest using Amazon S3 server-side encryption.</p>"}, "s3Path": {"shape": "S3Path", "documentation": "<p>Identifies the S3 path where you want Amazon Braket to store the job training artifacts. For example, <code>s3://bucket-name/key-name-prefix</code>.</p>"}}, "documentation": "<p>Specifies the path to the S3 location where you want to store job artifacts and the encryption key used to store them.</p>"}, "JobPrimaryStatus": {"type": "string", "enum": ["QUEUED", "RUNNING", "COMPLETED", "FAILED", "CANCELLING", "CANCELLED"]}, "JobStoppingCondition": {"type": "structure", "members": {"maxRuntimeInSeconds": {"shape": "JobStoppingConditionMaxRuntimeInSecondsInteger", "documentation": "<p>The maximum length of time, in seconds, that an Amazon Braket job can run.</p>"}}, "documentation": "<p>Specifies limits for how long an Amazon Braket job can run. </p>"}, "JobStoppingConditionMaxRuntimeInSecondsInteger": {"type": "integer", "box": true, "max": 432000, "min": 1}, "JobSummary": {"type": "structure", "required": ["createdAt", "device", "jobArn", "job<PERSON>ame", "status"], "members": {"createdAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The date and time that the Amazon Braket job was created.</p>"}, "device": {"shape": "String256", "documentation": "<p>Provides summary information about the primary device used by an Amazon Braket job.</p>"}, "endedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The date and time that the Amazon Braket job ended.</p>"}, "jobArn": {"shape": "JobArn", "documentation": "<p>The ARN of the Amazon Braket job.</p>"}, "jobName": {"shape": "String", "documentation": "<p>The name of the Amazon Braket job.</p>"}, "startedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The date and time that the Amazon Braket job was started.</p>"}, "status": {"shape": "JobPrimaryStatus", "documentation": "<p>The status of the Amazon Braket job.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>A tag object that consists of a key and an optional value, used to manage metadata for Amazon Braket resources.</p>"}}, "documentation": "<p>Provides summary information about an Amazon Braket job.</p>"}, "JobSummaryList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "JobToken": {"type": "string", "max": 128, "min": 1}, "JsonValue": {"type": "string"}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>Specify the <code>resourceArn</code> for the resource whose tags to display.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagsMap", "documentation": "<p>Displays the key, value pairs of tags associated with this resource.</p>"}}}, "Long": {"type": "long", "box": true}, "QuantumTaskArn": {"type": "string", "max": 256, "min": 1}, "QuantumTaskStatus": {"type": "string", "enum": ["CREATED", "QUEUED", "RUNNING", "COMPLETED", "FAILED", "CANCELLING", "CANCELLED"]}, "QuantumTaskSummary": {"type": "structure", "required": ["createdAt", "deviceArn", "outputS3Bucket", "outputS3Directory", "quantumTaskArn", "shots", "status"], "members": {"createdAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the task was created.</p>"}, "deviceArn": {"shape": "DeviceArn", "documentation": "<p>The ARN of the device the task ran on.</p>"}, "endedAt": {"shape": "SyntheticTimestamp_date_time", "documentation": "<p>The time at which the task finished.</p>"}, "outputS3Bucket": {"shape": "String", "documentation": "<p>The S3 bucket where the task result file is stored..</p>"}, "outputS3Directory": {"shape": "String", "documentation": "<p>The folder in the S3 bucket where the task result file is stored.</p>"}, "quantumTaskArn": {"shape": "QuantumTaskArn", "documentation": "<p>The ARN of the task.</p>"}, "shots": {"shape": "<PERSON>", "documentation": "<p>The shots used for the task.</p>"}, "status": {"shape": "QuantumTaskStatus", "documentation": "<p>The status of the task.</p>"}, "tags": {"shape": "TagsMap", "documentation": "<p>Displays the key, value pairs of tags associated with this quantum task.</p>"}}, "documentation": "<p>Includes information about a quantum task.</p>"}, "QuantumTaskSummaryList": {"type": "list", "member": {"shape": "QuantumTaskSummary"}}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The specified resource was not found.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "RoleArn": {"type": "string", "pattern": "^arn:aws[a-z\\-]*:iam::\\d{12}:role/?[a-zA-Z_0-9+=,.@\\-_/]+$"}, "S3DataSource": {"type": "structure", "required": ["s3Uri"], "members": {"s3Uri": {"shape": "S3Path", "documentation": "<p>Depending on the value specified for the <code>S3DataType</code>, identifies either a key name prefix or a manifest that locates the S3 data source.</p>"}}, "documentation": "<p>Information about the data stored in Amazon S3 used by the Amazon Braket job.</p>"}, "S3Path": {"type": "string", "max": 1024, "min": 0, "pattern": "^(https|s3)://([^/]+)/?(.*)$"}, "ScriptModeConfig": {"type": "structure", "required": ["entryPoint", "s3Uri"], "members": {"compressionType": {"shape": "CompressionType", "documentation": "<p>The type of compression used by the Python scripts for an Amazon Braket job.</p>"}, "entryPoint": {"shape": "String", "documentation": "<p>The path to the Python script that serves as the entry point for an Amazon Braket job.</p>"}, "s3Uri": {"shape": "S3Path", "documentation": "<p>The URI that specifies the S3 path to the Python script module that contains the training script used by an Amazon Braket job.</p>"}}, "documentation": "<p>Contains information about the Python scripts used for entry and by an Amazon Braket job.</p>"}, "SearchDevicesFilter": {"type": "structure", "required": ["name", "values"], "members": {"name": {"shape": "SearchDevicesFilterNameString", "documentation": "<p>The name to use to filter results.</p>"}, "values": {"shape": "SearchDevicesFilterValuesList", "documentation": "<p>The values to use to filter results.</p>"}}, "documentation": "<p>The filter to use for searching devices.</p>"}, "SearchDevicesFilterNameString": {"type": "string", "max": 64, "min": 1}, "SearchDevicesFilterValuesList": {"type": "list", "member": {"shape": "String256"}, "max": 10, "min": 1}, "SearchDevicesRequest": {"type": "structure", "required": ["filters"], "members": {"filters": {"shape": "SearchDevicesRequestFiltersList", "documentation": "<p>The filter values to use to search for a device.</p>"}, "maxResults": {"shape": "SearchDevicesRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return in the response.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>A token used for pagination of results returned in the response. Use the token returned from the previous request continue results where the previous request ended.</p>"}}}, "SearchDevicesRequestFiltersList": {"type": "list", "member": {"shape": "SearchDevicesFilter"}, "max": 10, "min": 0}, "SearchDevicesRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "SearchDevicesResponse": {"type": "structure", "required": ["devices"], "members": {"devices": {"shape": "DeviceSummaryList", "documentation": "<p>An array of <code>DeviceSummary</code> objects for devices that match the specified filter values.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>A token used for pagination of results, or null if there are no additional results. Use the token value in a subsequent request to continue results where the previous request ended.</p>"}}}, "SearchJobsFilter": {"type": "structure", "required": ["name", "operator", "values"], "members": {"name": {"shape": "String64", "documentation": "<p>The name to use for the jobs filter.</p>"}, "operator": {"shape": "SearchJobsFilterOperator", "documentation": "<p>An operator to use for the jobs filter.</p>"}, "values": {"shape": "SearchJobsFilterValuesList", "documentation": "<p>The values to use for the jobs filter.</p>"}}, "documentation": "<p>A filter used to search for Amazon Braket jobs.</p>"}, "SearchJobsFilterOperator": {"type": "string", "enum": ["LT", "LTE", "EQUAL", "GT", "GTE", "BETWEEN", "CONTAINS"]}, "SearchJobsFilterValuesList": {"type": "list", "member": {"shape": "String256"}, "max": 10, "min": 1}, "SearchJobsRequest": {"type": "structure", "required": ["filters"], "members": {"filters": {"shape": "SearchJobsRequestFiltersList", "documentation": "<p>The filter values to use when searching for a job.</p>"}, "maxResults": {"shape": "SearchJobsRequestMaxResultsInteger", "documentation": "<p>The maximum number of results to return in the response.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>A token used for pagination of results returned in the response. Use the token returned from the previous request to continue results where the previous request ended.</p>"}}}, "SearchJobsRequestFiltersList": {"type": "list", "member": {"shape": "SearchJobs<PERSON>ilter"}, "max": 10, "min": 0}, "SearchJobsRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "SearchJobsResponse": {"type": "structure", "required": ["jobs"], "members": {"jobs": {"shape": "JobSummaryList", "documentation": "<p>An array of <code>JobSummary</code> objects for devices that match the specified filter values.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>A token used for pagination of results, or <code>null</code> if there are no additional results. Use the token value in a subsequent request to continue results where the previous request ended.</p>"}}}, "SearchQuantumTasksFilter": {"type": "structure", "required": ["name", "operator", "values"], "members": {"name": {"shape": "String64", "documentation": "<p>The name of the device used for the task.</p>"}, "operator": {"shape": "SearchQuantumTasksFilterOperator", "documentation": "<p>An operator to use in the filter.</p>"}, "values": {"shape": "SearchQuantumTasksFilterValuesList", "documentation": "<p>The values to use for the filter.</p>"}}, "documentation": "<p>A filter to use to search for tasks.</p>"}, "SearchQuantumTasksFilterOperator": {"type": "string", "enum": ["LT", "LTE", "EQUAL", "GT", "GTE", "BETWEEN"]}, "SearchQuantumTasksFilterValuesList": {"type": "list", "member": {"shape": "String256"}, "max": 10, "min": 1}, "SearchQuantumTasksRequest": {"type": "structure", "required": ["filters"], "members": {"filters": {"shape": "SearchQuantumTasksRequestFiltersList", "documentation": "<p>Array of <code>SearchQuantumTasksFilter</code> objects.</p>"}, "maxResults": {"shape": "SearchQuantumTasksRequestMaxResultsInteger", "documentation": "<p>Maximum number of results to return in the response.</p>"}, "nextToken": {"shape": "String", "documentation": "<p>A token used for pagination of results returned in the response. Use the token returned from the previous request continue results where the previous request ended.</p>"}}}, "SearchQuantumTasksRequestFiltersList": {"type": "list", "member": {"shape": "SearchQuantumTasksFilter"}, "max": 10, "min": 0}, "SearchQuantumTasksRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 100, "min": 1}, "SearchQuantumTasksResponse": {"type": "structure", "required": ["quantumTasks"], "members": {"nextToken": {"shape": "String", "documentation": "<p>A token used for pagination of results, or null if there are no additional results. Use the token value in a subsequent request to continue results where the previous request ended.</p>"}, "quantumTasks": {"shape": "QuantumTaskSummaryList", "documentation": "<p>An array of <code>QuantumTaskSummary</code> objects for tasks that match the specified filters.</p>"}}}, "ServiceQuotaExceededException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request failed because a service quota is exceeded.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "String": {"type": "string"}, "String1024": {"type": "string", "max": 1024, "min": 1}, "String2048": {"type": "string", "max": 2048, "min": 1}, "String256": {"type": "string", "max": 256, "min": 1}, "String4096": {"type": "string", "max": 4096, "min": 1}, "String64": {"type": "string", "max": 64, "min": 1}, "SyntheticTimestamp_date_time": {"type": "timestamp", "timestampFormat": "iso8601"}, "TagKeys": {"type": "list", "member": {"shape": "String"}}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>Specify the <code>resourceArn</code> of the resource to which a tag will be added.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagsMap", "documentation": "<p>Specify the tags to add to the resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagsMap": {"type": "map", "key": {"shape": "String"}, "value": {"shape": "String"}}, "ThrottlingException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The throttling rate limit is met.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>Specify the <code>resourceArn</code> for the resource from which to remove the tags.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeys", "documentation": "<p>Specify the keys for the tags to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "Uri": {"type": "string", "max": 255, "min": 1, "pattern": "\\d{10,14}\\.dkr\\.ecr.[a-z0-9-]+\\.amazonaws\\.com\\/.+(@sha256)?:.+"}, "ValidationException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The input fails to satisfy the constraints specified by an AWS service.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}}, "documentation": "<p>The Amazon Braket API Reference provides information about the operations and structures supported in Amazon Braket.</p> <p>Additional Resources:</p> <ul> <li> <p> <a href=\"https://docs.aws.amazon.com/braket/latest/developerguide/what-is-braket.html\">Amazon Braket Developer Guide</a> </p> </li> </ul>"}