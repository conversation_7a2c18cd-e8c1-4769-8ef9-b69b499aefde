{"version": "2.0", "metadata": {"apiVersion": "2017-08-22", "endpointPrefix": "acm-pca", "jsonVersion": "1.1", "protocol": "json", "serviceAbbreviation": "ACM-PCA", "serviceFullName": "AWS Certificate Manager Private Certificate Authority", "serviceId": "ACM PCA", "signatureVersion": "v4", "targetPrefix": "ACMPrivateCA", "uid": "acm-pca-2017-08-22"}, "operations": {"CreateCertificateAuthority": {"name": "CreateCertificateAuthority", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCertificateAuthorityRequest"}, "output": {"shape": "CreateCertificateAuthorityResponse"}, "errors": [{"shape": "InvalidArgsException"}, {"shape": "InvalidPolicyException"}, {"shape": "InvalidTagException"}, {"shape": "LimitExceededException"}], "documentation": "<p>Creates a root or subordinate private certificate authority (CA). You must specify the CA configuration, an optional configuration for Online Certificate Status Protocol (OCSP) and/or a certificate revocation list (CRL), the CA type, and an optional idempotency token to avoid accidental creation of multiple CAs. The CA configuration specifies the name of the algorithm and key size to be used to create the CA private key, the type of signing algorithm that the CA uses, and X.500 subject information. The OCSP configuration can optionally specify a custom URL for the OCSP responder. The CRL configuration specifies the CRL expiration period in days (the validity period of the CRL), the Amazon S3 bucket that will contain the CRL, and a CNAME alias for the S3 bucket that is included in certificates issued by the CA. If successful, this action returns the Amazon Resource Name (ARN) of the CA.</p> <note> <p>Both Amazon Web Services Private CA and the IAM principal must have permission to write to the S3 bucket that you specify. If the IAM principal making the call does not have permission to write to the bucket, then an exception is thrown. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/crl-planning.html#s3-policies\">Access policies for CRLs in Amazon S3</a>.</p> </note> <p>Amazon Web Services Private CA assets that are stored in Amazon S3 can be protected with encryption. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/PcaCreateCa.html#crl-encryption\">Encrypting Your CRLs</a>.</p>", "idempotent": true}, "CreateCertificateAuthorityAuditReport": {"name": "CreateCertificateAuthorityAuditReport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreateCertificateAuthorityAuditReportRequest"}, "output": {"shape": "CreateCertificateAuthorityAuditReportResponse"}, "errors": [{"shape": "RequestInProgressException"}, {"shape": "RequestFailedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidArgsException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Creates an audit report that lists every time that your CA private key is used. The report is saved in the Amazon S3 bucket that you specify on input. The <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_IssueCertificate.html\">IssueCertificate</a> and <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_RevokeCertificate.html\">RevokeCertificate</a> actions use the private key. </p> <note> <p>Both Amazon Web Services Private CA and the IAM principal must have permission to write to the S3 bucket that you specify. If the IAM principal making the call does not have permission to write to the bucket, then an exception is thrown. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/crl-planning.html#s3-policies\">Access policies for CRLs in Amazon S3</a>.</p> </note> <p>Amazon Web Services Private CA assets that are stored in Amazon S3 can be protected with encryption. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/PcaAuditReport.html#audit-report-encryption\">Encrypting Your Audit Reports</a>.</p> <note> <p>You can generate a maximum of one report every 30 minutes.</p> </note>", "idempotent": true}, "CreatePermission": {"name": "CreatePermission", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "CreatePermissionRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}, {"shape": "PermissionAlreadyExistsException"}, {"shape": "LimitExceededException"}, {"shape": "InvalidStateException"}, {"shape": "RequestFailedException"}], "documentation": "<p>Grants one or more permissions on a private CA to the Certificate Manager (ACM) service principal (<code>acm.amazonaws.com</code>). These permissions allow ACM to issue and renew ACM certificates that reside in the same Amazon Web Services account as the CA.</p> <p>You can list current permissions with the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListPermissions.html\">ListPermissions</a> action and revoke them with the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_DeletePermission.html\">DeletePermission</a> action.</p> <p class=\"title\"> <b>About Permissions</b> </p> <ul> <li> <p>If the private CA and the certificates it issues reside in the same account, you can use <code>CreatePermission</code> to grant permissions for ACM to carry out automatic certificate renewals.</p> </li> <li> <p>For automatic certificate renewal to succeed, the ACM service principal needs permissions to create, retrieve, and list certificates.</p> </li> <li> <p>If the private CA and the ACM certificates reside in different accounts, then permissions cannot be used to enable automatic renewals. Instead, the ACM certificate owner must set up a resource-based policy to enable cross-account issuance and renewals. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/pca-rbp.html\">Using a Resource Based Policy with Amazon Web Services Private CA</a>.</p> </li> </ul>"}, "DeleteCertificateAuthority": {"name": "DeleteCertificateAuthority", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeleteCertificateAuthorityRequest"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Deletes a private certificate authority (CA). You must provide the Amazon Resource Name (ARN) of the private CA that you want to delete. You can find the ARN by calling the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListCertificateAuthorities.html\">ListCertificateAuthorities</a> action. </p> <note> <p>Deleting a CA will invalidate other CAs and certificates below it in your CA hierarchy.</p> </note> <p>Before you can delete a CA that you have created and activated, you must disable it. To do this, call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_UpdateCertificateAuthority.html\">UpdateCertificateAuthority</a> action and set the <b>CertificateAuthorityStatus</b> parameter to <code>DISABLED</code>. </p> <p>Additionally, you can delete a CA if you are waiting for it to be created (that is, the status of the CA is <code>CREATING</code>). You can also delete it if the CA has been created but you haven't yet imported the signed certificate into Amazon Web Services Private CA (that is, the status of the CA is <code>PENDING_CERTIFICATE</code>). </p> <p>When you successfully call <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_DeleteCertificateAuthority.html\">DeleteCertificateAuthority</a>, the CA's status changes to <code>DELETED</code>. However, the CA won't be permanently deleted until the restoration period has passed. By default, if you do not set the <code>PermanentDeletionTimeInDays</code> parameter, the CA remains restorable for 30 days. You can set the parameter from 7 to 30 days. The <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_DescribeCertificateAuthority.html\">DescribeCertificateAuthority</a> action returns the time remaining in the restoration window of a private CA in the <code>DELETED</code> state. To restore an eligible CA, call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_RestoreCertificateAuthority.html\">RestoreCertificateAuthority</a> action.</p>"}, "DeletePermission": {"name": "DeletePermission", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePermissionRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidStateException"}, {"shape": "RequestFailedException"}], "documentation": "<p>Revokes permissions on a private CA granted to the Certificate Manager (ACM) service principal (acm.amazonaws.com). </p> <p>These permissions allow ACM to issue and renew ACM certificates that reside in the same Amazon Web Services account as the CA. If you revoke these permissions, ACM will no longer renew the affected certificates automatically.</p> <p>Permissions can be granted with the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreatePermission.html\">CreatePermission</a> action and listed with the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListPermissions.html\">ListPermissions</a> action. </p> <p class=\"title\"> <b>About Permissions</b> </p> <ul> <li> <p>If the private CA and the certificates it issues reside in the same account, you can use <code>CreatePermission</code> to grant permissions for ACM to carry out automatic certificate renewals.</p> </li> <li> <p>For automatic certificate renewal to succeed, the ACM service principal needs permissions to create, retrieve, and list certificates.</p> </li> <li> <p>If the private CA and the ACM certificates reside in different accounts, then permissions cannot be used to enable automatic renewals. Instead, the ACM certificate owner must set up a resource-based policy to enable cross-account issuance and renewals. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/pca-rbp.html\">Using a Resource Based Policy with Amazon Web Services Private CA</a>.</p> </li> </ul>"}, "DeletePolicy": {"name": "DeletePolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DeletePolicyRequest"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidStateException"}, {"shape": "LockoutPreventedException"}, {"shape": "RequestFailedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Deletes the resource-based policy attached to a private CA. Deletion will remove any access that the policy has granted. If there is no policy attached to the private CA, this action will return successful.</p> <p>If you delete a policy that was applied through Amazon Web Services Resource Access Manager (RAM), the CA will be removed from all shares in which it was included. </p> <p>The Certificate Manager Service Linked Role that the policy supports is not affected when you delete the policy. </p> <p>The current policy can be shown with <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_GetPolicy.html\">GetPolicy</a> and updated with <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_PutPolicy.html\">PutPolicy</a>.</p> <p class=\"title\"> <b>About Policies</b> </p> <ul> <li> <p>A policy grants access on a private CA to an Amazon Web Services customer account, to Amazon Web Services Organizations, or to an Amazon Web Services Organizations unit. Policies are under the control of a CA administrator. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/pca-rbp.html\">Using a Resource Based Policy with Amazon Web Services Private CA</a>.</p> </li> <li> <p>A policy permits a user of Certificate Manager (ACM) to issue ACM certificates signed by a CA in another account.</p> </li> <li> <p>For ACM to manage automatic renewal of these certificates, the ACM user must configure a Service Linked Role (SLR). The SLR allows the ACM service to assume the identity of the user, subject to confirmation against the Amazon Web Services Private CA policy. For more information, see <a href=\"https://docs.aws.amazon.com/acm/latest/userguide/acm-slr.html\">Using a Service Linked Role with ACM</a>.</p> </li> <li> <p>Updates made in Amazon Web Services Resource Manager (RAM) are reflected in policies. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/pca-ram.html\">Attach a Policy for Cross-Account Access</a>.</p> </li> </ul>"}, "DescribeCertificateAuthority": {"name": "DescribeCertificateAuthority", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCertificateAuthorityRequest"}, "output": {"shape": "DescribeCertificateAuthorityResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}], "documentation": "<p>Lists information about your private certificate authority (CA) or one that has been shared with you. You specify the private CA on input by its ARN (Amazon Resource Name). The output contains the status of your CA. This can be any of the following: </p> <ul> <li> <p> <code>CREATING</code> - Amazon Web Services Private CA is creating your private certificate authority.</p> </li> <li> <p> <code>PENDING_CERTIFICATE</code> - The certificate is pending. You must use your Amazon Web Services Private CA-hosted or on-premises root or subordinate CA to sign your private CA CSR and then import it into Amazon Web Services Private CA. </p> </li> <li> <p> <code>ACTIVE</code> - Your private CA is active.</p> </li> <li> <p> <code>DISABLED</code> - Your private CA has been disabled.</p> </li> <li> <p> <code>EXPIRED</code> - Your private CA certificate has expired.</p> </li> <li> <p> <code>FAILED</code> - Your private CA has failed. Your CA can fail because of problems such a network outage or back-end Amazon Web Services failure or other errors. A failed CA can never return to the pending state. You must create a new CA. </p> </li> <li> <p> <code>DELETED</code> - Your private CA is within the restoration period, after which it is permanently deleted. The length of time remaining in the CA's restoration period is also included in this action's output.</p> </li> </ul>"}, "DescribeCertificateAuthorityAuditReport": {"name": "DescribeCertificateAuthorityAuditReport", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "DescribeCertificateAuthorityAuditReportRequest"}, "output": {"shape": "DescribeCertificateAuthorityAuditReportResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidArgsException"}], "documentation": "<p>Lists information about a specific audit report created by calling the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthorityAuditReport.html\">CreateCertificateAuthorityAuditReport</a> action. Audit information is created every time the certificate authority (CA) private key is used. The private key is used when you call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_IssueCertificate.html\">IssueCertificate</a> action or the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_RevokeCertificate.html\">RevokeCertificate</a> action. </p>"}, "GetCertificate": {"name": "GetCertificate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCertificateRequest"}, "output": {"shape": "GetCertificateResponse"}, "errors": [{"shape": "RequestInProgressException"}, {"shape": "RequestFailedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Retrieves a certificate from your private CA or one that has been shared with you. The ARN of the certificate is returned when you call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_IssueCertificate.html\">IssueCertificate</a> action. You must specify both the ARN of your private CA and the ARN of the issued certificate when calling the <b>GetCertificate</b> action. You can retrieve the certificate if it is in the <b>ISSUED</b> state. You can call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthorityAuditReport.html\">CreateCertificateAuthorityAuditReport</a> action to create a report that contains information about all of the certificates issued and revoked by your private CA. </p>"}, "GetCertificateAuthorityCertificate": {"name": "GetCertificateAuthorityCertificate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCertificateAuthorityCertificateRequest"}, "output": {"shape": "GetCertificateAuthorityCertificateResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidStateException"}, {"shape": "InvalidArnException"}], "documentation": "<p>Retrieves the certificate and certificate chain for your private certificate authority (CA) or one that has been shared with you. Both the certificate and the chain are base64 PEM-encoded. The chain does not include the CA certificate. Each certificate in the chain signs the one before it. </p>"}, "GetCertificateAuthorityCsr": {"name": "GetCertificateAuthorityCsr", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetCertificateAuthorityCsrRequest"}, "output": {"shape": "GetCertificateAuthorityCsrResponse"}, "errors": [{"shape": "RequestInProgressException"}, {"shape": "RequestFailedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Retrieves the certificate signing request (CSR) for your private certificate authority (CA). The CSR is created when you call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a> action. Sign the CSR with your Amazon Web Services Private CA-hosted or on-premises root or subordinate CA. Then import the signed certificate back into Amazon Web Services Private CA by calling the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ImportCertificateAuthorityCertificate.html\">ImportCertificateAuthorityCertificate</a> action. The CSR is returned as a base64 PEM-encoded string. </p>"}, "GetPolicy": {"name": "GetPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "GetPolicyRequest"}, "output": {"shape": "GetPolicyResponse"}, "errors": [{"shape": "InvalidArnException"}, {"shape": "InvalidStateException"}, {"shape": "RequestFailedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Retrieves the resource-based policy attached to a private CA. If either the private CA resource or the policy cannot be found, this action returns a <code>ResourceNotFoundException</code>. </p> <p>The policy can be attached or updated with <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_PutPolicy.html\">PutPolicy</a> and removed with <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_DeletePolicy.html\">DeletePolicy</a>.</p> <p class=\"title\"> <b>About Policies</b> </p> <ul> <li> <p>A policy grants access on a private CA to an Amazon Web Services customer account, to Amazon Web Services Organizations, or to an Amazon Web Services Organizations unit. Policies are under the control of a CA administrator. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/pca-rbp.html\">Using a Resource Based Policy with Amazon Web Services Private CA</a>.</p> </li> <li> <p>A policy permits a user of Certificate Manager (ACM) to issue ACM certificates signed by a CA in another account.</p> </li> <li> <p>For ACM to manage automatic renewal of these certificates, the ACM user must configure a Service Linked Role (SLR). The SLR allows the ACM service to assume the identity of the user, subject to confirmation against the Amazon Web Services Private CA policy. For more information, see <a href=\"https://docs.aws.amazon.com/acm/latest/userguide/acm-slr.html\">Using a Service Linked Role with ACM</a>.</p> </li> <li> <p>Updates made in Amazon Web Services Resource Manager (RAM) are reflected in policies. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/pca-ram.html\">Attach a Policy for Cross-Account Access</a>.</p> </li> </ul>"}, "ImportCertificateAuthorityCertificate": {"name": "ImportCertificateAuthorityCertificate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ImportCertificateAuthorityCertificateRequest"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "RequestInProgressException"}, {"shape": "RequestFailedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidStateException"}, {"shape": "MalformedCertificateException"}, {"shape": "CertificateMismatchException"}], "documentation": "<p>Imports a signed private CA certificate into Amazon Web Services Private CA. This action is used when you are using a chain of trust whose root is located outside Amazon Web Services Private CA. Before you can call this action, the following preparations must in place:</p> <ol> <li> <p>In Amazon Web Services Private CA, call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a> action to create the private CA that you plan to back with the imported certificate.</p> </li> <li> <p>Call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_GetCertificateAuthorityCsr.html\">GetCertificateAuthorityCsr</a> action to generate a certificate signing request (CSR).</p> </li> <li> <p>Sign the CSR using a root or intermediate CA hosted by either an on-premises PKI hierarchy or by a commercial CA.</p> </li> <li> <p>Create a certificate chain and copy the signed certificate and the certificate chain to your working directory.</p> </li> </ol> <p>Amazon Web Services Private CA supports three scenarios for installing a CA certificate:</p> <ul> <li> <p>Installing a certificate for a root CA hosted by Amazon Web Services Private CA.</p> </li> <li> <p>Installing a subordinate CA certificate whose parent authority is hosted by Amazon Web Services Private CA.</p> </li> <li> <p>Installing a subordinate CA certificate whose parent authority is externally hosted.</p> </li> </ul> <p>The following additional requirements apply when you import a CA certificate.</p> <ul> <li> <p>Only a self-signed certificate can be imported as a root CA.</p> </li> <li> <p>A self-signed certificate cannot be imported as a subordinate CA.</p> </li> <li> <p>Your certificate chain must not include the private CA certificate that you are importing.</p> </li> <li> <p>Your root CA must be the last certificate in your chain. The subordinate certificate, if any, that your root CA signed must be next to last. The subordinate certificate signed by the preceding subordinate CA must come next, and so on until your chain is built. </p> </li> <li> <p>The chain must be PEM-encoded.</p> </li> <li> <p>The maximum allowed size of a certificate is 32 KB.</p> </li> <li> <p>The maximum allowed size of a certificate chain is 2 MB.</p> </li> </ul> <p> <i>Enforcement of Critical Constraints</i> </p> <p>Amazon Web Services Private CA allows the following extensions to be marked critical in the imported CA certificate or chain.</p> <ul> <li> <p>Basic constraints (<i>must</i> be marked critical)</p> </li> <li> <p>Subject alternative names</p> </li> <li> <p>Key usage</p> </li> <li> <p>Extended key usage</p> </li> <li> <p>Authority key identifier</p> </li> <li> <p>Subject key identifier</p> </li> <li> <p>Issuer alternative name</p> </li> <li> <p>Subject directory attributes</p> </li> <li> <p>Subject information access</p> </li> <li> <p>Certificate policies</p> </li> <li> <p>Policy mappings</p> </li> <li> <p>Inhibit anyPolicy</p> </li> </ul> <p>Amazon Web Services Private CA rejects the following extensions when they are marked critical in an imported CA certificate or chain.</p> <ul> <li> <p>Name constraints</p> </li> <li> <p>Policy constraints</p> </li> <li> <p>CRL distribution points</p> </li> <li> <p>Authority information access</p> </li> <li> <p>Freshest CRL</p> </li> <li> <p>Any other extension</p> </li> </ul>"}, "IssueCertificate": {"name": "IssueCertificate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "IssueCertificateRequest"}, "output": {"shape": "IssueCertificateResponse"}, "errors": [{"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidStateException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidArgsException"}, {"shape": "MalformedCSRException"}], "documentation": "<p>Uses your private certificate authority (CA), or one that has been shared with you, to issue a client certificate. This action returns the Amazon Resource Name (ARN) of the certificate. You can retrieve the certificate by calling the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_GetCertificate.html\">GetCertificate</a> action and specifying the ARN. </p> <note> <p>You cannot use the ACM <b>ListCertificateAuthorities</b> action to retrieve the ARNs of the certificates that you issue by using Amazon Web Services Private CA.</p> </note>", "idempotent": true}, "ListCertificateAuthorities": {"name": "ListCertificateAuthorities", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListCertificateAuthoritiesRequest"}, "output": {"shape": "ListCertificateAuthoritiesResponse"}, "errors": [{"shape": "InvalidNextTokenException"}], "documentation": "<p>Lists the private certificate authorities that you created by using the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a> action.</p>"}, "ListPermissions": {"name": "ListPermissions", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListPermissionsRequest"}, "output": {"shape": "ListPermissionsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidNextTokenException"}, {"shape": "InvalidStateException"}, {"shape": "RequestFailedException"}], "documentation": "<p>List all permissions on a private CA, if any, granted to the Certificate Manager (ACM) service principal (acm.amazonaws.com). </p> <p>These permissions allow ACM to issue and renew ACM certificates that reside in the same Amazon Web Services account as the CA. </p> <p>Permissions can be granted with the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreatePermission.html\">CreatePermission</a> action and revoked with the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_DeletePermission.html\">DeletePermission</a> action.</p> <p class=\"title\"> <b>About Permissions</b> </p> <ul> <li> <p>If the private CA and the certificates it issues reside in the same account, you can use <code>CreatePermission</code> to grant permissions for ACM to carry out automatic certificate renewals.</p> </li> <li> <p>For automatic certificate renewal to succeed, the ACM service principal needs permissions to create, retrieve, and list certificates.</p> </li> <li> <p>If the private CA and the ACM certificates reside in different accounts, then permissions cannot be used to enable automatic renewals. Instead, the ACM certificate owner must set up a resource-based policy to enable cross-account issuance and renewals. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/pca-rbp.html\">Using a Resource Based Policy with Amazon Web Services Private CA</a>.</p> </li> </ul>"}, "ListTags": {"name": "ListTags", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "ListTagsRequest"}, "output": {"shape": "ListTagsResponse"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidStateException"}], "documentation": "<p>Lists the tags, if any, that are associated with your private CA or one that has been shared with you. Tags are labels that you can use to identify and organize your CAs. Each tag consists of a key and an optional value. Call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_TagCertificateAuthority.html\">TagCertificateAuthority</a> action to add one or more tags to your CA. Call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_UntagCertificateAuthority.html\">UntagCertificateAuthority</a> action to remove tags. </p>"}, "PutPolicy": {"name": "PutPolicy", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "PutPolicyRequest"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidStateException"}, {"shape": "InvalidPolicyException"}, {"shape": "LockoutPreventedException"}, {"shape": "RequestFailedException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Attaches a resource-based policy to a private CA. </p> <p>A policy can also be applied by sharing a private CA through Amazon Web Services Resource Access Manager (RAM). For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/pca-ram.html\">Attach a Policy for Cross-Account Access</a>.</p> <p>The policy can be displayed with <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_GetPolicy.html\">GetPolicy</a> and removed with <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_DeletePolicy.html\">DeletePolicy</a>.</p> <p class=\"title\"> <b>About Policies</b> </p> <ul> <li> <p>A policy grants access on a private CA to an Amazon Web Services customer account, to Amazon Web Services Organizations, or to an Amazon Web Services Organizations unit. Policies are under the control of a CA administrator. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/pca-rbp.html\">Using a Resource Based Policy with Amazon Web Services Private CA</a>.</p> </li> <li> <p>A policy permits a user of Certificate Manager (ACM) to issue ACM certificates signed by a CA in another account.</p> </li> <li> <p>For ACM to manage automatic renewal of these certificates, the ACM user must configure a Service Linked Role (SLR). The SLR allows the ACM service to assume the identity of the user, subject to confirmation against the Amazon Web Services Private CA policy. For more information, see <a href=\"https://docs.aws.amazon.com/acm/latest/userguide/acm-slr.html\">Using a Service Linked Role with ACM</a>.</p> </li> <li> <p>Updates made in Amazon Web Services Resource Manager (RAM) are reflected in policies. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/pca-ram.html\">Attach a Policy for Cross-Account Access</a>.</p> </li> </ul>"}, "RestoreCertificateAuthority": {"name": "RestoreCertificateAuthority", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RestoreCertificateAuthorityRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidStateException"}, {"shape": "InvalidArnException"}], "documentation": "<p>Restores a certificate authority (CA) that is in the <code>DELETED</code> state. You can restore a CA during the period that you defined in the <b>PermanentDeletionTimeInDays</b> parameter of the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_DeleteCertificateAuthority.html\">DeleteCertificateAuthority</a> action. Currently, you can specify 7 to 30 days. If you did not specify a <b>PermanentDeletionTimeInDays</b> value, by default you can restore the CA at any time in a 30 day period. You can check the time remaining in the restoration period of a private CA in the <code>DELETED</code> state by calling the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_DescribeCertificateAuthority.html\">DescribeCertificateAuthority</a> or <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListCertificateAuthorities.html\">ListCertificateAuthorities</a> actions. The status of a restored CA is set to its pre-deletion status when the <b>RestoreCertificateAuthority</b> action returns. To change its status to <code>ACTIVE</code>, call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_UpdateCertificateAuthority.html\">UpdateCertificateAuthority</a> action. If the private CA was in the <code>PENDING_CERTIFICATE</code> state at deletion, you must use the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ImportCertificateAuthorityCertificate.html\">ImportCertificateAuthorityCertificate</a> action to import a certificate authority into the private CA before it can be activated. You cannot restore a CA after the restoration period has ended.</p>"}, "RevokeCertificate": {"name": "RevokeCertificate", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "RevokeCertificateRequest"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidRequestException"}, {"shape": "InvalidStateException"}, {"shape": "LimitExceededException"}, {"shape": "ResourceNotFoundException"}, {"shape": "RequestAlreadyProcessedException"}, {"shape": "RequestInProgressException"}, {"shape": "RequestFailedException"}], "documentation": "<p>Revokes a certificate that was issued inside Amazon Web Services Private CA. If you enable a certificate revocation list (CRL) when you create or update your private CA, information about the revoked certificates will be included in the CRL. Amazon Web Services Private CA writes the CRL to an S3 bucket that you specify. A CRL is typically updated approximately 30 minutes after a certificate is revoked. If for any reason the CRL update fails, Amazon Web Services Private CA attempts makes further attempts every 15 minutes. With Amazon CloudWatch, you can create alarms for the metrics <code>CRLGenerated</code> and <code>MisconfiguredCRLBucket</code>. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/PcaCloudWatch.html\">Supported CloudWatch Metrics</a>.</p> <note> <p>Both Amazon Web Services Private CA and the IAM principal must have permission to write to the S3 bucket that you specify. If the IAM principal making the call does not have permission to write to the bucket, then an exception is thrown. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/crl-planning.html#s3-policies\">Access policies for CRLs in Amazon S3</a>.</p> </note> <p>Amazon Web Services Private CA also writes revocation information to the audit report. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthorityAuditReport.html\">CreateCertificateAuthorityAuditReport</a>.</p> <note> <p>You cannot revoke a root CA self-signed certificate.</p> </note>"}, "TagCertificateAuthority": {"name": "TagCertificateAuthority", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "TagCertificateAuthorityRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidStateException"}, {"shape": "InvalidTagException"}, {"shape": "TooManyTagsException"}], "documentation": "<p>Adds one or more tags to your private CA. Tags are labels that you can use to identify and organize your Amazon Web Services resources. Each tag consists of a key and an optional value. You specify the private CA on input by its Amazon Resource Name (ARN). You specify the tag by using a key-value pair. You can apply a tag to just one private CA if you want to identify a specific characteristic of that CA, or you can apply the same tag to multiple private CAs if you want to filter for a common relationship among those CAs. To remove one or more tags, use the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_UntagCertificateAuthority.html\">UntagCertificateAuthority</a> action. Call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListTags.html\">ListTags</a> action to see what tags are associated with your CA. </p> <note> <p>To attach tags to a private CA during the creation procedure, a CA administrator must first associate an inline IAM policy with the <code>CreateCertificateAuthority</code> action and explicitly allow tagging. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/auth-InlinePolicies.html#policy-tag-ca\">Attaching tags to a CA at the time of creation</a>.</p> </note>"}, "UntagCertificateAuthority": {"name": "UntagCertificateAuthority", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UntagCertificateAuthorityRequest"}, "errors": [{"shape": "ResourceNotFoundException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidStateException"}, {"shape": "InvalidTagException"}], "documentation": "<p>Remove one or more tags from your private CA. A tag consists of a key-value pair. If you do not specify the value portion of the tag when calling this action, the tag will be removed regardless of value. If you specify a value, the tag is removed only if it is associated with the specified value. To add tags to a private CA, use the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_TagCertificateAuthority.html\">TagCertificateAuthority</a>. Call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListTags.html\">ListTags</a> action to see what tags are associated with your CA. </p>"}, "UpdateCertificateAuthority": {"name": "UpdateCertificateAuthority", "http": {"method": "POST", "requestUri": "/"}, "input": {"shape": "UpdateCertificateAuthorityRequest"}, "errors": [{"shape": "ConcurrentModificationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "InvalidArgsException"}, {"shape": "InvalidArnException"}, {"shape": "InvalidStateException"}, {"shape": "InvalidPolicyException"}], "documentation": "<p>Updates the status or configuration of a private certificate authority (CA). Your private CA must be in the <code>ACTIVE</code> or <code>DISABLED</code> state before you can update it. You can disable a private CA that is in the <code>ACTIVE</code> state or make a CA that is in the <code>DISABLED</code> state active again.</p> <note> <p>Both Amazon Web Services Private CA and the IAM principal must have permission to write to the S3 bucket that you specify. If the IAM principal making the call does not have permission to write to the bucket, then an exception is thrown. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/crl-planning.html#s3-policies\">Access policies for CRLs in Amazon S3</a>.</p> </note>"}}, "shapes": {"ASN1PrintableString64": {"type": "string", "max": 64, "min": 0, "pattern": "[a-zA-Z0-9'()+-.?:/= ]*"}, "ASN1Subject": {"type": "structure", "members": {"Country": {"shape": "CountryCodeString", "documentation": "<p>Two-digit code that specifies the country in which the certificate subject located.</p>"}, "Organization": {"shape": "String64", "documentation": "<p>Legal name of the organization with which the certificate subject is affiliated. </p>"}, "OrganizationalUnit": {"shape": "String64", "documentation": "<p>A subdivision or unit of the organization (such as sales or finance) with which the certificate subject is affiliated.</p>"}, "DistinguishedNameQualifier": {"shape": "ASN1PrintableString64", "documentation": "<p>Disambiguating information for the certificate subject.</p>"}, "State": {"shape": "String128", "documentation": "<p>State in which the subject of the certificate is located.</p>"}, "CommonName": {"shape": "String64", "documentation": "<p>For CA and end-entity certificates in a private PKI, the common name (CN) can be any string within the length limit. </p> <p>Note: In publicly trusted certificates, the common name must be a fully qualified domain name (FQDN) associated with the certificate subject.</p>"}, "SerialNumber": {"shape": "ASN1PrintableString64", "documentation": "<p>The certificate serial number.</p>"}, "Locality": {"shape": "String128", "documentation": "<p>The locality (such as a city or town) in which the certificate subject is located.</p>"}, "Title": {"shape": "String64", "documentation": "<p>A title such as Mr. or Ms., which is pre-pended to the name to refer formally to the certificate subject.</p>"}, "Surname": {"shape": "String40", "documentation": "<p>Family name. In the US and the UK, for example, the surname of an individual is ordered last. In Asian cultures the surname is typically ordered first.</p>"}, "GivenName": {"shape": "String16", "documentation": "<p>First name.</p>"}, "Initials": {"shape": "String5", "documentation": "<p>Concatenation that typically contains the first letter of the <b>GivenName</b>, the first letter of the middle name if one exists, and the first letter of the <b>Surname</b>.</p>"}, "Pseudonym": {"shape": "String128", "documentation": "<p>Typically a shortened version of a longer <b>GivenName</b>. For example, <PERSON> is often shortened to <PERSON>. <PERSON> is often shortened to <PERSON>, <PERSON>, or <PERSON>.</p>"}, "GenerationQualifier": {"shape": "String3", "documentation": "<p>Typically a qualifier appended to the name of an individual. Examples include Jr. for junior, Sr. for senior, and <PERSON> for third.</p>"}, "CustomAttributes": {"shape": "CustomAttributeList", "documentation": "<p/> <p>Contains a sequence of one or more X.500 relative distinguished names (RDNs), each of which consists of an object identifier (OID) and a value. For more information, see NIST’s definition of <a href=\"https://csrc.nist.gov/glossary/term/Object_Identifier\">Object Identifier (OID)</a>.</p> <note> <p>Custom attributes cannot be used in combination with standard attributes.</p> </note>"}}, "documentation": "<p>Contains information about the certificate subject. The <code>Subject</code> field in the certificate identifies the entity that owns or controls the public key in the certificate. The entity can be a user, computer, device, or service. The <code>Subject </code>must contain an X.500 distinguished name (DN). A DN is a sequence of relative distinguished names (RDNs). The RDNs are separated by commas in the certificate.</p>"}, "AWSPolicy": {"type": "string", "max": 20480, "min": 1, "pattern": "[\\u0009\\u000A\\u000D\\u0020-\\u00FF]+"}, "AccessDescription": {"type": "structure", "required": ["AccessMethod", "AccessLocation"], "members": {"AccessMethod": {"shape": "AccessMethod", "documentation": "<p>The type and format of <code>AccessDescription</code> information.</p>"}, "AccessLocation": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The location of <code>AccessDescription</code> information.</p>"}}, "documentation": "<p>Provides access information used by the <code>authorityInfoAccess</code> and <code>subjectInfoAccess</code> extensions described in <a href=\"https://datatracker.ietf.org/doc/html/rfc5280\">RFC 5280</a>.</p>", "box": true}, "AccessDescriptionList": {"type": "list", "member": {"shape": "AccessDescription"}}, "AccessMethod": {"type": "structure", "members": {"CustomObjectIdentifier": {"shape": "CustomObjectIdentifier", "documentation": "<p>An object identifier (OID) specifying the <code>AccessMethod</code>. The OID must satisfy the regular expression shown below. For more information, see NIST's definition of <a href=\"https://csrc.nist.gov/glossary/term/Object_Identifier\">Object Identifier (OID)</a>.</p>"}, "AccessMethodType": {"shape": "AccessMethodType", "documentation": "<p>Specifies the <code>AccessMethod</code>.</p>"}}, "documentation": "<p>Describes the type and format of extension access. Only one of <code>CustomObjectIdentifier</code> or <code>AccessMethodType</code> may be provided. Providing both results in <code>InvalidArgsException</code>.</p>"}, "AccessMethodType": {"type": "string", "enum": ["CA_REPOSITORY", "RESOURCE_PKI_MANIFEST", "RESOURCE_PKI_NOTIFY"]}, "AccountId": {"type": "string", "max": 12, "min": 12, "pattern": "[0-9]+"}, "ActionList": {"type": "list", "member": {"shape": "ActionType"}, "max": 3, "min": 1}, "ActionType": {"type": "string", "enum": ["IssueCertificate", "GetCertificate", "ListPermissions"]}, "ApiPassthrough": {"type": "structure", "members": {"Extensions": {"shape": "Extensions", "documentation": "<p>Specifies X.509 extension information for a certificate.</p>"}, "Subject": {"shape": "ASN1Subject"}}, "documentation": "<p>Contains X.509 certificate information to be placed in an issued certificate. An <code>APIPassthrough</code> or <code>APICSRPassthrough</code> template variant must be selected, or else this parameter is ignored. </p> <p>If conflicting or duplicate certificate information is supplied from other sources, Amazon Web Services Private CA applies <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/UsingTemplates.html#template-order-of-operations\">order of operation rules</a> to determine what information is used.</p>"}, "Arn": {"type": "string", "max": 200, "min": 5, "pattern": "arn:[\\w+=/,.@-]+:[\\w+=/,.@-]+:[\\w+=/,.@-]*:[0-9]*:[\\w+=,.@-]+(/[\\w+=,.@-]+)*"}, "AuditReportId": {"type": "string", "max": 36, "min": 36, "pattern": "[a-z0-9]{8}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{4}-[a-z0-9]{12}"}, "AuditReportResponseFormat": {"type": "string", "enum": ["JSON", "CSV"]}, "AuditReportStatus": {"type": "string", "enum": ["CREATING", "SUCCESS", "FAILED"]}, "Base64String1To4096": {"type": "string", "max": 4096, "min": 1, "pattern": "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$"}, "Boolean": {"type": "boolean"}, "CertificateAuthorities": {"type": "list", "member": {"shape": "CertificateAuthority"}}, "CertificateAuthority": {"type": "structure", "members": {"Arn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) for your private certificate authority (CA). The format is <code> <i>********-1234-1234-1234-************</i> </code>.</p>"}, "OwnerAccount": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account ID that owns the certificate authority.</p>"}, "CreatedAt": {"shape": "TStamp", "documentation": "<p>Date and time at which your private CA was created.</p>"}, "LastStateChangeAt": {"shape": "TStamp", "documentation": "<p>Date and time at which your private CA was last updated.</p>"}, "Type": {"shape": "CertificateAuthorityType", "documentation": "<p>Type of your private CA.</p>"}, "Serial": {"shape": "String", "documentation": "<p>Serial number of your private CA.</p>"}, "Status": {"shape": "CertificateAuthorityStatus", "documentation": "<p>Status of your private CA.</p>"}, "NotBefore": {"shape": "TStamp", "documentation": "<p>Date and time before which your private CA certificate is not valid.</p>"}, "NotAfter": {"shape": "TStamp", "documentation": "<p>Date and time after which your private CA certificate is not valid.</p>"}, "FailureReason": {"shape": "FailureReason", "documentation": "<p>Reason the request to create your private CA failed.</p>"}, "CertificateAuthorityConfiguration": {"shape": "CertificateAuthorityConfiguration", "documentation": "<p>Your private CA configuration.</p>"}, "RevocationConfiguration": {"shape": "RevocationConfiguration", "documentation": "<p>Information about the Online Certificate Status Protocol (OCSP) configuration or certificate revocation list (CRL) created and maintained by your private CA. </p>"}, "RestorableUntil": {"shape": "TStamp", "documentation": "<p>The period during which a deleted CA can be restored. For more information, see the <code>PermanentDeletionTimeInDays</code> parameter of the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_DeleteCertificateAuthorityRequest.html\">DeleteCertificateAuthorityRequest</a> action. </p>"}, "KeyStorageSecurityStandard": {"shape": "KeyStorageSecurityStandard", "documentation": "<p>Defines a cryptographic key management compliance standard used for handling CA keys. </p> <p>Default: FIPS_140_2_LEVEL_3_OR_HIGHER</p> <p>Note: Amazon Web Services Region ap-northeast-3 supports only FIPS_140_2_LEVEL_2_OR_HIGHER. You must explicitly specify this parameter and value when creating a CA in that Region. Specifying a different value (or no value) results in an <code>InvalidArgsException</code> with the message \"A certificate authority cannot be created in this region with the specified security standard.\"</p>"}, "UsageMode": {"shape": "CertificateAuthorityUsageMode", "documentation": "<p>Specifies whether the CA issues general-purpose certificates that typically require a revocation mechanism, or short-lived certificates that may optionally omit revocation because they expire quickly. Short-lived certificate validity is limited to seven days.</p> <p>The default value is GENERAL_PURPOSE.</p>"}}, "documentation": "<p>Contains information about your private certificate authority (CA). Your private CA can issue and revoke X.509 digital certificates. Digital certificates verify that the entity named in the certificate <b>Subject</b> field owns or controls the public key contained in the <b>Subject Public Key Info</b> field. Call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a> action to create your private CA. You must then call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_GetCertificateAuthorityCertificate.html\">GetCertificateAuthorityCertificate</a> action to retrieve a private CA certificate signing request (CSR). Sign the CSR with your Amazon Web Services Private CA-hosted or on-premises root or subordinate CA certificate. Call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ImportCertificateAuthorityCertificate.html\">ImportCertificateAuthorityCertificate</a> action to import the signed certificate into Certificate Manager (ACM). </p>"}, "CertificateAuthorityConfiguration": {"type": "structure", "required": ["KeyAlgorithm", "SigningAlgorithm", "Subject"], "members": {"KeyAlgorithm": {"shape": "KeyAlgorithm", "documentation": "<p>Type of the public key algorithm and size, in bits, of the key pair that your CA creates when it issues a certificate. When you create a subordinate CA, you must use a key algorithm supported by the parent CA.</p>"}, "SigningAlgorithm": {"shape": "SigningAlgorithm", "documentation": "<p>Name of the algorithm your private CA uses to sign certificate requests.</p> <p>This parameter should not be confused with the <code>SigningAlgorithm</code> parameter used to sign certificates when they are issued.</p>"}, "Subject": {"shape": "ASN1Subject", "documentation": "<p>Structure that contains X.500 distinguished name information for your private CA.</p>"}, "CsrExtensions": {"shape": "CsrExtensions", "documentation": "<p>Specifies information to be added to the extension section of the certificate signing request (CSR).</p>"}}, "documentation": "<p>Contains configuration information for your private certificate authority (CA). This includes information about the class of public key algorithm and the key pair that your private CA creates when it issues a certificate. It also includes the signature algorithm that it uses when issuing certificates, and its X.500 distinguished name. You must specify this information when you call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a> action. </p>"}, "CertificateAuthorityStatus": {"type": "string", "enum": ["CREATING", "PENDING_CERTIFICATE", "ACTIVE", "DELETED", "DISABLED", "EXPIRED", "FAILED"]}, "CertificateAuthorityType": {"type": "string", "enum": ["ROOT", "SUBORDINATE"]}, "CertificateAuthorityUsageMode": {"type": "string", "enum": ["GENERAL_PURPOSE", "SHORT_LIVED_CERTIFICATE"]}, "CertificateBody": {"type": "string"}, "CertificateBodyBlob": {"type": "blob", "max": 32768, "min": 1}, "CertificateChain": {"type": "string"}, "CertificateChainBlob": {"type": "blob", "max": 2097152, "min": 0}, "CertificateMismatchException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The certificate authority certificate you are importing does not comply with conditions specified in the certificate that signed it.</p>", "exception": true}, "CertificatePolicyList": {"type": "list", "member": {"shape": "PolicyInformation"}, "max": 20, "min": 1}, "CnameString": {"type": "string", "max": 253, "min": 0, "pattern": "^[-a-zA-Z0-9;/?:@&=+$,%_.!~*()']*$"}, "ConcurrentModificationException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>A previous update to your private CA is still ongoing.</p>", "exception": true}, "CountryCodeString": {"type": "string", "max": 2, "min": 2, "pattern": "[A-Za-z]{2}"}, "CreateCertificateAuthorityAuditReportRequest": {"type": "structure", "required": ["CertificateAuthorityArn", "S3BucketName", "AuditReportResponseFormat"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the CA to be audited. This is of the form:</p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code>.</p>"}, "S3BucketName": {"shape": "S3BucketName", "documentation": "<p>The name of the S3 bucket that will contain the audit report.</p>"}, "AuditReportResponseFormat": {"shape": "AuditReportResponseFormat", "documentation": "<p>The format in which to create the report. This can be either <b>JSON</b> or <b>CSV</b>.</p>"}}}, "CreateCertificateAuthorityAuditReportResponse": {"type": "structure", "members": {"AuditReportId": {"shape": "AuditReportId", "documentation": "<p>An alphanumeric string that contains a report identifier.</p>"}, "S3Key": {"shape": "S3Key", "documentation": "<p>The <b>key</b> that uniquely identifies the report file in your S3 bucket.</p>"}}}, "CreateCertificateAuthorityRequest": {"type": "structure", "required": ["CertificateAuthorityConfiguration", "CertificateAuthorityType"], "members": {"CertificateAuthorityConfiguration": {"shape": "CertificateAuthorityConfiguration", "documentation": "<p>Name and bit size of the private key algorithm, the name of the signing algorithm, and X.500 certificate subject information.</p>"}, "RevocationConfiguration": {"shape": "RevocationConfiguration", "documentation": "<p>Contains information to enable Online Certificate Status Protocol (OCSP) support, to enable a certificate revocation list (CRL), to enable both, or to enable neither. The default is for both certificate validation mechanisms to be disabled. </p> <note> <p>The following requirements apply to revocation configurations.</p> <ul> <li> <p>A configuration disabling CRLs or OCSP must contain only the <code>Enabled=False</code> parameter, and will fail if other parameters such as <code>CustomCname</code> or <code>ExpirationInDays</code> are included.</p> </li> <li> <p>In a CRL configuration, the <code>S3BucketName</code> parameter must conform to <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucketnamingrules.html\">Amazon S3 bucket naming rules</a>.</p> </li> <li> <p>A configuration containing a custom Canonical Name (CNAME) parameter for CRLs or OCSP must conform to <a href=\"https://www.ietf.org/rfc/rfc2396.txt\">RFC2396</a> restrictions on the use of special characters in a CNAME. </p> </li> <li> <p>In a CRL or OCSP configuration, the value of a CNAME parameter must not include a protocol prefix such as \"http://\" or \"https://\".</p> </li> </ul> </note> <p> For more information, see the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_OcspConfiguration.html\">OcspConfiguration</a> and <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CrlConfiguration.html\">CrlConfiguration</a> types.</p>"}, "CertificateAuthorityType": {"shape": "CertificateAuthorityType", "documentation": "<p>The type of the certificate authority.</p>"}, "IdempotencyToken": {"shape": "IdempotencyToken", "documentation": "<p>Custom string that can be used to distinguish between calls to the <b>CreateCertificateAuthority</b> action. Idempotency tokens for <b>CreateCertificateAuthority</b> time out after five minutes. Therefore, if you call <b>CreateCertificateAuthority</b> multiple times with the same idempotency token within five minutes, Amazon Web Services Private CA recognizes that you are requesting only certificate authority and will issue only one. If you change the idempotency token for each call, Amazon Web Services Private CA recognizes that you are requesting multiple certificate authorities.</p>"}, "KeyStorageSecurityStandard": {"shape": "KeyStorageSecurityStandard", "documentation": "<p>Specifies a cryptographic key management compliance standard used for handling CA keys.</p> <p>Default: FIPS_140_2_LEVEL_3_OR_HIGHER</p> <p> <i>Note:</i> <code>FIPS_140_2_LEVEL_3_OR_HIGHER</code> is not supported in the following Regions:</p> <ul> <li> <p>ap-northeast-3</p> </li> <li> <p>ap-southeast-3</p> </li> </ul> <p>When creating a CA in these Regions, you must provide <code>FIPS_140_2_LEVEL_2_OR_HIGHER</code> as the argument for <code>KeyStorageSecurityStandard</code>. Failure to do this results in an <code>InvalidArgsException</code> with the message, \"A certificate authority cannot be created in this region with the specified security standard.\"</p>"}, "Tags": {"shape": "TagList", "documentation": "<p>Key-value pairs that will be attached to the new private CA. You can associate up to 50 tags with a private CA. For information using tags with IAM to manage permissions, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_iam-tags.html\">Controlling Access Using IAM Tags</a>.</p>"}, "UsageMode": {"shape": "CertificateAuthorityUsageMode", "documentation": "<p>Specifies whether the CA issues general-purpose certificates that typically require a revocation mechanism, or short-lived certificates that may optionally omit revocation because they expire quickly. Short-lived certificate validity is limited to seven days.</p> <p>The default value is GENERAL_PURPOSE.</p>"}}}, "CreateCertificateAuthorityResponse": {"type": "structure", "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>If successful, the Amazon Resource Name (ARN) of the certificate authority (CA). This is of the form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code>. </p>"}}}, "CreatePermissionRequest": {"type": "structure", "required": ["CertificateAuthorityArn", "Principal", "Actions"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the CA that grants the permissions. You can find the ARN by calling the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListCertificateAuthorities.html\">ListCertificateAuthorities</a> action. This must have the following form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code>. </p>"}, "Principal": {"shape": "Principal", "documentation": "<p>The Amazon Web Services service or identity that receives the permission. At this time, the only valid principal is <code>acm.amazonaws.com</code>.</p>"}, "SourceAccount": {"shape": "AccountId", "documentation": "<p>The ID of the calling account.</p>"}, "Actions": {"shape": "ActionList", "documentation": "<p>The actions that the specified Amazon Web Services service principal can use. These include <code>IssueCertificate</code>, <code>GetCertificate</code>, and <code>ListPermissions</code>.</p>"}}}, "CrlConfiguration": {"type": "structure", "required": ["Enabled"], "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>Boolean value that specifies whether certificate revocation lists (CRLs) are enabled. You can use this value to enable certificate revocation for a new CA when you call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a> action or for an existing CA when you call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_UpdateCertificateAuthority.html\">UpdateCertificateAuthority</a> action. </p>", "box": true}, "ExpirationInDays": {"shape": "Integer1To5000", "documentation": "<p>Validity period of the CRL in days.</p>", "box": true}, "CustomCname": {"shape": "CnameString", "documentation": "<p>Name inserted into the certificate <b>CRL Distribution Points</b> extension that enables the use of an alias for the CRL distribution point. Use this value if you don't want the name of your S3 bucket to be public.</p> <note> <p>The content of a Canonical Name (CNAME) record must conform to <a href=\"https://www.ietf.org/rfc/rfc2396.txt\">RFC2396</a> restrictions on the use of special characters in URIs. Additionally, the value of the CNAME must not include a protocol prefix such as \"http://\" or \"https://\".</p> </note>"}, "S3BucketName": {"shape": "S3BucketName3To255", "documentation": "<p>Name of the S3 bucket that contains the CRL. If you do not provide a value for the <b>CustomCname</b> argument, the name of your S3 bucket is placed into the <b>CRL Distribution Points</b> extension of the issued certificate. You can change the name of your bucket by calling the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_UpdateCertificateAuthority.html\">UpdateCertificateAuthority</a> operation. You must specify a <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/PcaCreateCa.html#s3-policies\">bucket policy</a> that allows Amazon Web Services Private CA to write the CRL to your bucket.</p> <note> <p>The <code>S3BucketName</code> parameter must conform to the <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucketnamingrules.html\">S3 bucket naming rules</a>.</p> </note>"}, "S3ObjectAcl": {"shape": "S3ObjectAcl", "documentation": "<p>Determines whether the CRL will be publicly readable or privately held in the CRL Amazon S3 bucket. If you choose PU<PERSON>IC_READ, the CRL will be accessible over the public internet. If you choose BUCKET_OWNER_FULL_CONTROL, only the owner of the CRL S3 bucket can access the CRL, and your PKI clients may need an alternative method of access. </p> <p>If no value is specified, the default is <code>PUBLIC_READ</code>.</p> <p> <i>Note:</i> This default can cause CA creation to fail in some circumstances. If you have have enabled the Block Public Access (BPA) feature in your S3 account, then you must specify the value of this parameter as <code>BUCKET_OWNER_FULL_CONTROL</code>, and not doing so results in an error. If you have disabled BPA in S3, then you can specify either <code>BUCKET_OWNER_FULL_CONTROL</code> or <code>PUBLIC_READ</code> as the value.</p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/PcaCreateCa.html#s3-bpa\">Blocking public access to the S3 bucket</a>.</p>"}}, "documentation": "<p>Contains configuration information for a certificate revocation list (CRL). Your private certificate authority (CA) creates base CRLs. Delta CRLs are not supported. You can enable CRLs for your new or an existing private CA by setting the <b>Enabled</b> parameter to <code>true</code>. Your private CA writes CRLs to an S3 bucket that you specify in the <b>S3BucketName</b> parameter. You can hide the name of your bucket by specifying a value for the <b>CustomCname</b> parameter. Your private CA copies the CNAME or the S3 bucket name to the <b>CRL Distribution Points</b> extension of each certificate it issues. Your S3 bucket policy must give write permission to Amazon Web Services Private CA. </p> <p>Amazon Web Services Private CA assets that are stored in Amazon S3 can be protected with encryption. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/PcaCreateCa.html#crl-encryption\">Encrypting Your CRLs</a>.</p> <p>Your private CA uses the value in the <b>ExpirationInDays</b> parameter to calculate the <b>nextUpdate</b> field in the CRL. The CRL is refreshed prior to a certificate's expiration date or when a certificate is revoked. When a certificate is revoked, it appears in the CRL until the certificate expires, and then in one additional CRL after expiration, and it always appears in the audit report.</p> <p>A CRL is typically updated approximately 30 minutes after a certificate is revoked. If for any reason a CRL update fails, Amazon Web Services Private CA makes further attempts every 15 minutes.</p> <p>CRLs contain the following fields:</p> <ul> <li> <p> <b>Version</b>: The current version number defined in RFC 5280 is V2. The integer value is 0x1. </p> </li> <li> <p> <b>Signature Algorithm</b>: The name of the algorithm used to sign the CRL.</p> </li> <li> <p> <b>Issuer</b>: The X.500 distinguished name of your private CA that issued the CRL.</p> </li> <li> <p> <b>Last Update</b>: The issue date and time of this CRL.</p> </li> <li> <p> <b>Next Update</b>: The day and time by which the next CRL will be issued.</p> </li> <li> <p> <b>Revoked Certificates</b>: List of revoked certificates. Each list item contains the following information.</p> <ul> <li> <p> <b>Serial Number</b>: The serial number, in hexadecimal format, of the revoked certificate.</p> </li> <li> <p> <b>Revocation Date</b>: Date and time the certificate was revoked.</p> </li> <li> <p> <b>CRL Entry Extensions</b>: Optional extensions for the CRL entry.</p> <ul> <li> <p> <b>X509v3 CRL Reason Code</b>: Reason the certificate was revoked.</p> </li> </ul> </li> </ul> </li> <li> <p> <b>CRL Extensions</b>: Optional extensions for the CRL.</p> <ul> <li> <p> <b>X509v3 Authority Key Identifier</b>: Identifies the public key associated with the private key used to sign the certificate.</p> </li> <li> <p> <b>X509v3 CRL Number:</b>: Decimal sequence number for the CRL.</p> </li> </ul> </li> <li> <p> <b>Signature Algorithm</b>: Algorithm used by your private CA to sign the CRL.</p> </li> <li> <p> <b>Signature Value</b>: Signature computed over the CRL.</p> </li> </ul> <p>Certificate revocation lists created by Amazon Web Services Private CA are DER-encoded. You can use the following OpenSSL command to list a CRL.</p> <p> <code>openssl crl -inform DER -text -in <i>crl_path</i> -noout</code> </p> <p>For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/crl-planning.html\">Planning a certificate revocation list (CRL)</a> in the <i>Amazon Web Services Private Certificate Authority User Guide</i> </p>"}, "CsrBlob": {"type": "blob", "max": 32768, "min": 1}, "CsrBody": {"type": "string"}, "CsrExtensions": {"type": "structure", "members": {"KeyUsage": {"shape": "KeyUsage", "documentation": "<p>Indicates the purpose of the certificate and of the key contained in the certificate.</p>"}, "SubjectInformationAccess": {"shape": "AccessDescriptionList", "documentation": "<p>For CA certificates, provides a path to additional information pertaining to the CA, such as revocation and policy. For more information, see <a href=\"https://datatracker.ietf.org/doc/html/rfc5280#section-*******\">Subject Information Access</a> in RFC 5280.</p>"}}, "documentation": "<p>Describes the certificate extensions to be added to the certificate signing request (CSR).</p>"}, "CustomAttribute": {"type": "structure", "required": ["ObjectIdentifier", "Value"], "members": {"ObjectIdentifier": {"shape": "CustomObjectIdentifier", "documentation": "<p>Specifies the object identifier (OID) of the attribute type of the relative distinguished name (RDN).</p>"}, "Value": {"shape": "String1To256", "documentation": "<p/> <p>Specifies the attribute value of relative distinguished name (RDN).</p>"}}, "documentation": "<p>Defines the X.500 relative distinguished name (RDN).</p>"}, "CustomAttributeList": {"type": "list", "member": {"shape": "CustomAttribute"}, "max": 30, "min": 1}, "CustomExtension": {"type": "structure", "required": ["ObjectIdentifier", "Value"], "members": {"ObjectIdentifier": {"shape": "CustomObjectIdentifier", "documentation": "<p/> <p>Specifies the object identifier (OID) of the X.509 extension. For more information, see the <a href=\"https://oidref.com/2.5.29\">Global OID reference database.</a> </p>"}, "Value": {"shape": "Base64String1To4096", "documentation": "<p/> <p>Specifies the base64-encoded value of the X.509 extension.</p>"}, "Critical": {"shape": "Boolean", "documentation": "<p/> <p>Specifies the critical flag of the X.509 extension.</p>", "box": true}}, "documentation": "<p/> <p>Specifies the X.509 extension information for a certificate.</p> <p>Extensions present in <code>CustomExtensions</code> follow the <code>ApiPassthrough</code> <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/UsingTemplates.html#template-order-of-operations\">template rules</a>. </p>"}, "CustomExtensionList": {"type": "list", "member": {"shape": "CustomExtension"}, "max": 20, "min": 1}, "CustomObjectIdentifier": {"type": "string", "max": 64, "min": 0, "pattern": "^([0-2])\\.([0-9]|([0-3][0-9]))((\\.([0-9]+)){0,126})$"}, "DeleteCertificateAuthorityRequest": {"type": "structure", "required": ["CertificateAuthorityArn"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that was returned when you called <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a>. This must have the following form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code>. </p>"}, "PermanentDeletionTimeInDays": {"shape": "PermanentDeletionTimeInDays", "documentation": "<p>The number of days to make a CA restorable after it has been deleted. This can be anywhere from 7 to 30 days, with 30 being the default.</p>"}}}, "DeletePermissionRequest": {"type": "structure", "required": ["CertificateAuthorityArn", "Principal"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Number (ARN) of the private CA that issued the permissions. You can find the CA's ARN by calling the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListCertificateAuthorities.html\">ListCertificateAuthorities</a> action. This must have the following form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code>. </p>"}, "Principal": {"shape": "Principal", "documentation": "<p>The Amazon Web Services service or identity that will have its CA permissions revoked. At this time, the only valid service principal is <code>acm.amazonaws.com</code> </p>"}, "SourceAccount": {"shape": "AccountId", "documentation": "<p>The Amazon Web Services account that calls this action.</p>"}}}, "DeletePolicyRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Number (ARN) of the private CA that will have its policy deleted. You can find the CA's ARN by calling the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListCertificateAuthorities.html\">ListCertificateAuthorities</a> action. The ARN value must have the form <code>arn:aws:acm-pca:region:account:certificate-authority/********-89ab-cdef-0123-0********9ab</code>. </p>"}}}, "DescribeCertificateAuthorityAuditReportRequest": {"type": "structure", "required": ["CertificateAuthorityArn", "AuditReportId"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the private CA. This must be of the form:</p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code>. </p>"}, "AuditReportId": {"shape": "AuditReportId", "documentation": "<p>The report ID returned by calling the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthorityAuditReport.html\">CreateCertificateAuthorityAuditReport</a> action.</p>"}}}, "DescribeCertificateAuthorityAuditReportResponse": {"type": "structure", "members": {"AuditReportStatus": {"shape": "AuditReportStatus", "documentation": "<p>Specifies whether report creation is in progress, has succeeded, or has failed.</p>"}, "S3BucketName": {"shape": "S3BucketName", "documentation": "<p>Name of the S3 bucket that contains the report.</p>"}, "S3Key": {"shape": "S3Key", "documentation": "<p>S3 <b>key</b> that uniquely identifies the report file in your S3 bucket.</p>"}, "CreatedAt": {"shape": "TStamp", "documentation": "<p>The date and time at which the report was created.</p>"}}}, "DescribeCertificateAuthorityRequest": {"type": "structure", "required": ["CertificateAuthorityArn"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that was returned when you called <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a>. This must be of the form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code>. </p>"}}}, "DescribeCertificateAuthorityResponse": {"type": "structure", "members": {"CertificateAuthority": {"shape": "CertificateAuthority", "documentation": "<p>A <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CertificateAuthority.html\">CertificateAuthority</a> structure that contains information about your private CA.</p>"}}}, "EdiPartyName": {"type": "structure", "required": ["PartyName"], "members": {"PartyName": {"shape": "String256", "documentation": "<p>Specifies the party name.</p>"}, "NameAssigner": {"shape": "String256", "documentation": "<p>Specifies the name assigner.</p>"}}, "documentation": "<p>Describes an Electronic Data Interchange (EDI) entity as described in as defined in <a href=\"https://datatracker.ietf.org/doc/html/rfc5280\">Subject Alternative Name</a> in RFC 5280.</p>"}, "ExtendedKeyUsage": {"type": "structure", "members": {"ExtendedKeyUsageType": {"shape": "ExtendedKeyUsageType", "documentation": "<p>Specifies a standard <code>ExtendedKeyUsage</code> as defined as in <a href=\"https://datatracker.ietf.org/doc/html/rfc5280#section-********\">RFC 5280</a>.</p>"}, "ExtendedKeyUsageObjectIdentifier": {"shape": "CustomObjectIdentifier", "documentation": "<p>Specifies a custom <code>ExtendedKeyUsage</code> with an object identifier (OID).</p>"}}, "documentation": "<p>Specifies additional purposes for which the certified public key may be used other than basic purposes indicated in the <code>KeyUsage</code> extension.</p>"}, "ExtendedKeyUsageList": {"type": "list", "member": {"shape": "ExtendedKeyUsage"}, "max": 20, "min": 1}, "ExtendedKeyUsageType": {"type": "string", "enum": ["SERVER_AUTH", "CLIENT_AUTH", "CODE_SIGNING", "EMAIL_PROTECTION", "TIME_STAMPING", "OCSP_SIGNING", "SMART_CARD_LOGIN", "DOCUMENT_SIGNING", "CERTIFICATE_TRANSPARENCY"]}, "Extensions": {"type": "structure", "members": {"CertificatePolicies": {"shape": "CertificatePolicyList", "documentation": "<p>Contains a sequence of one or more policy information terms, each of which consists of an object identifier (OID) and optional qualifiers. For more information, see NIST's definition of <a href=\"https://csrc.nist.gov/glossary/term/Object_Identifier\">Object Identifier (OID)</a>.</p> <p>In an end-entity certificate, these terms indicate the policy under which the certificate was issued and the purposes for which it may be used. In a CA certificate, these terms limit the set of policies for certification paths that include this certificate.</p>"}, "ExtendedKeyUsage": {"shape": "ExtendedKeyUsageList", "documentation": "<p>Specifies additional purposes for which the certified public key may be used other than basic purposes indicated in the <code>KeyUsage</code> extension.</p>"}, "KeyUsage": {"shape": "KeyUsage"}, "SubjectAlternativeNames": {"shape": "GeneralNameList", "documentation": "<p>The subject alternative name extension allows identities to be bound to the subject of the certificate. These identities may be included in addition to or in place of the identity in the subject field of the certificate.</p>"}, "CustomExtensions": {"shape": "CustomExtensionList", "documentation": "<p/> <p>Contains a sequence of one or more X.509 extensions, each of which consists of an object identifier (OID), a base64-encoded value, and the critical flag. For more information, see the <a href=\"https://oidref.com/2.5.29\">Global OID reference database.</a> </p>"}}, "documentation": "<p>Contains X.509 extension information for a certificate.</p>"}, "FailureReason": {"type": "string", "enum": ["REQUEST_TIMED_OUT", "UNSUPPORTED_ALGORITHM", "OTHER"]}, "GeneralName": {"type": "structure", "members": {"OtherName": {"shape": "OtherName", "documentation": "<p>Represents <code>GeneralName</code> using an <code>OtherName</code> object.</p>"}, "Rfc822Name": {"shape": "String256", "documentation": "<p>Represents <code>GeneralName</code> as an <a href=\"https://datatracker.ietf.org/doc/html/rfc822\">RFC 822</a> email address.</p>"}, "DnsName": {"shape": "String253", "documentation": "<p>Represents <code>GeneralName</code> as a DNS name.</p>"}, "DirectoryName": {"shape": "ASN1Subject"}, "EdiPartyName": {"shape": "EdiPartyName", "documentation": "<p>Represents <code>GeneralName</code> as an <code>EdiPartyName</code> object.</p>"}, "UniformResourceIdentifier": {"shape": "String253", "documentation": "<p>Represents <code>GeneralName</code> as a URI.</p>"}, "IpAddress": {"shape": "String39", "documentation": "<p>Represents <code>GeneralName</code> as an IPv4 or IPv6 address.</p>"}, "RegisteredId": {"shape": "CustomObjectIdentifier", "documentation": "<p> Represents <code>GeneralName</code> as an object identifier (OID).</p>"}}, "documentation": "<p>Describes an ASN.1 X.400 <code>GeneralName</code> as defined in <a href=\"https://datatracker.ietf.org/doc/html/rfc5280\">RFC 5280</a>. Only one of the following naming options should be provided. Providing more than one option results in an <code>InvalidArgsException</code> error.</p>"}, "GeneralNameList": {"type": "list", "member": {"shape": "<PERSON><PERSON><PERSON>"}, "max": 20, "min": 1}, "GetCertificateAuthorityCertificateRequest": {"type": "structure", "required": ["CertificateAuthorityArn"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of your private CA. This is of the form:</p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code>. </p>"}}}, "GetCertificateAuthorityCertificateResponse": {"type": "structure", "members": {"Certificate": {"shape": "CertificateBody", "documentation": "<p>Base64-encoded certificate authority (CA) certificate.</p>"}, "CertificateChain": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>Base64-encoded certificate chain that includes any intermediate certificates and chains up to root certificate that you used to sign your private CA certificate. The chain does not include your private CA certificate. If this is a root CA, the value will be null.</p>"}}}, "GetCertificateAuthorityCsrRequest": {"type": "structure", "required": ["CertificateAuthorityArn"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that was returned when you called the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a> action. This must be of the form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code> </p>"}}}, "GetCertificateAuthorityCsrResponse": {"type": "structure", "members": {"Csr": {"shape": "CsrBody", "documentation": "<p>The base64 PEM-encoded certificate signing request (CSR) for your private CA certificate.</p>"}}}, "GetCertificateRequest": {"type": "structure", "required": ["CertificateAuthorityArn", "CertificateArn"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that was returned when you called <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a>. This must be of the form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code>. </p>"}, "CertificateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The ARN of the issued certificate. The ARN contains the certificate serial number and must be in the following form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i>/certificate/<i>286535153982981100925020015808220737245</i> </code> </p>"}}}, "GetCertificateResponse": {"type": "structure", "members": {"Certificate": {"shape": "CertificateBody", "documentation": "<p>The base64 PEM-encoded certificate specified by the <code>CertificateArn</code> parameter.</p>"}, "CertificateChain": {"shape": "<PERSON><PERSON><PERSON><PERSON>", "documentation": "<p>The base64 PEM-encoded certificate chain that chains up to the root CA certificate that you used to sign your private CA certificate. </p>"}}}, "GetPolicyRequest": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Number (ARN) of the private CA that will have its policy retrieved. You can find the CA's ARN by calling the ListCertificateAuthorities action. </p>"}}}, "GetPolicyResponse": {"type": "structure", "members": {"Policy": {"shape": "AWSPolicy", "documentation": "<p>The policy attached to the private CA as a JSON document.</p>"}}}, "IdempotencyToken": {"type": "string", "max": 36, "min": 1, "pattern": "[\\u0009\\u000A\\u000D\\u0020-\\u00FF]*"}, "ImportCertificateAuthorityCertificateRequest": {"type": "structure", "required": ["CertificateAuthorityArn", "Certificate"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that was returned when you called <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a>. This must be of the form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code> </p>"}, "Certificate": {"shape": "CertificateBodyBlob", "documentation": "<p>The PEM-encoded certificate for a private CA. This may be a self-signed certificate in the case of a root CA, or it may be signed by another CA that you control.</p>"}, "CertificateChain": {"shape": "CertificateChainBlob", "documentation": "<p>A PEM-encoded file that contains all of your certificates, other than the certificate you're importing, chaining up to your root CA. Your Amazon Web Services Private CA-hosted or on-premises root certificate is the last in the chain, and each certificate in the chain signs the one preceding. </p> <p>This parameter must be supplied when you import a subordinate CA. When you import a root CA, there is no chain.</p>"}}}, "Integer1To5000": {"type": "integer", "max": 5000, "min": 1}, "InvalidArgsException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>One or more of the specified arguments was not valid.</p>", "exception": true}, "InvalidArnException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The requested Amazon Resource Name (ARN) does not refer to an existing resource.</p>", "exception": true}, "InvalidNextTokenException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The token specified in the <code>NextToken</code> argument is not valid. Use the token returned from your previous call to <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListCertificateAuthorities.html\">ListCertificateAuthorities</a>.</p>", "exception": true}, "InvalidPolicyException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The resource policy is invalid or is missing a required statement. For general information about IAM policy and statement structure, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_policies.html#access_policies-json\">Overview of JSON Policies</a>.</p>", "exception": true}, "InvalidRequestException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request action cannot be performed or is prohibited.</p>", "exception": true}, "InvalidStateException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The state of the private CA does not allow this action to occur.</p>", "exception": true}, "InvalidTagException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The tag associated with the CA is not valid. The invalid argument is contained in the message field.</p>", "exception": true}, "IssueCertificateRequest": {"type": "structure", "required": ["CertificateAuthorityArn", "Csr", "SigningAlgorithm", "Validity"], "members": {"ApiPassthrough": {"shape": "ApiPassthrough", "documentation": "<p>Specifies X.509 certificate information to be included in the issued certificate. An <code>APIPassthrough</code> or <code>APICSRPassthrough</code> template variant must be selected, or else this parameter is ignored. For more information about using these templates, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/UsingTemplates.html\">Understanding Certificate Templates</a>.</p> <p>If conflicting or duplicate certificate information is supplied during certificate issuance, Amazon Web Services Private CA applies <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/UsingTemplates.html#template-order-of-operations\">order of operation rules</a> to determine what information is used.</p>"}, "CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that was returned when you called <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a>. This must be of the form:</p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code> </p>"}, "Csr": {"shape": "CsrBlob", "documentation": "<p>The certificate signing request (CSR) for the certificate you want to issue. As an example, you can use the following OpenSSL command to create the CSR and a 2048 bit RSA private key. </p> <p> <code>openssl req -new -newkey rsa:2048 -days 365 -keyout private/test_cert_priv_key.pem -out csr/test_cert_.csr</code> </p> <p>If you have a configuration file, you can then use the following OpenSSL command. The <code>usr_cert</code> block in the configuration file contains your X509 version 3 extensions. </p> <p> <code>openssl req -new -config openssl_rsa.cnf -extensions usr_cert -newkey rsa:2048 -days 365 -keyout private/test_cert_priv_key.pem -out csr/test_cert_.csr</code> </p> <p>Note: A CSR must provide either a <i>subject name</i> or a <i>subject alternative name</i> or the request will be rejected. </p>"}, "SigningAlgorithm": {"shape": "SigningAlgorithm", "documentation": "<p>The name of the algorithm that will be used to sign the certificate to be issued. </p> <p>This parameter should not be confused with the <code>SigningAlgorithm</code> parameter used to sign a CSR in the <code>CreateCertificateAuthority</code> action.</p> <note> <p>The specified signing algorithm family (RSA or ECDSA) much match the algorithm family of the CA's secret key.</p> </note>"}, "TemplateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Specifies a custom configuration template to use when issuing a certificate. If this parameter is not provided, Amazon Web Services Private CA defaults to the <code>EndEntityCertificate/V1</code> template. For CA certificates, you should choose the shortest path length that meets your needs. The path length is indicated by the PathLen<i>N</i> portion of the ARN, where <i>N</i> is the <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/PcaTerms.html#terms-cadepth\">CA depth</a>.</p> <p>Note: The CA depth configured on a subordinate CA certificate must not exceed the limit set by its parents in the CA hierarchy.</p> <p>For a list of <code>TemplateArn</code> values supported by Amazon Web Services Private CA, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/UsingTemplates.html\">Understanding Certificate Templates</a>.</p>"}, "Validity": {"shape": "Validity", "documentation": "<p>Information describing the end of the validity period of the certificate. This parameter sets the “Not After” date for the certificate.</p> <p>Certificate validity is the period of time during which a certificate is valid. Validity can be expressed as an explicit date and time when the certificate expires, or as a span of time after issuance, stated in days, months, or years. For more information, see <a href=\"https://datatracker.ietf.org/doc/html/rfc5280#section-*******\">Validity</a> in RFC 5280. </p> <p>This value is unaffected when <code>ValidityNotBefore</code> is also specified. For example, if <code>Validity</code> is set to 20 days in the future, the certificate will expire 20 days from issuance time regardless of the <code>ValidityNotBefore</code> value.</p> <p>The end of the validity period configured on a certificate must not exceed the limit set on its parents in the CA hierarchy.</p>"}, "ValidityNotBefore": {"shape": "Validity", "documentation": "<p>Information describing the start of the validity period of the certificate. This parameter sets the “Not Before\" date for the certificate.</p> <p>By default, when issuing a certificate, Amazon Web Services Private CA sets the \"Not Before\" date to the issuance time minus 60 minutes. This compensates for clock inconsistencies across computer systems. The <code>ValidityNotBefore</code> parameter can be used to customize the “Not Before” value. </p> <p>Unlike the <code>Validity</code> parameter, the <code>ValidityNotBefore</code> parameter is optional.</p> <p>The <code>ValidityNotBefore</code> value is expressed as an explicit date and time, using the <code>Validity</code> type value <code>ABSOLUTE</code>. For more information, see <a href=\"https://docs.aws.amazon.com/acm-pca/latest/APIReference/API_Validity.html\">Validity</a> in this API reference and <a href=\"https://datatracker.ietf.org/doc/html/rfc5280#section-*******\">Validity</a> in RFC 5280.</p>"}, "IdempotencyToken": {"shape": "IdempotencyToken", "documentation": "<p>Alphanumeric string that can be used to distinguish between calls to the <b>IssueCertificate</b> action. Idempotency tokens for <b>IssueCertificate</b> time out after one minute. Therefore, if you call <b>IssueCertificate</b> multiple times with the same idempotency token within one minute, Amazon Web Services Private CA recognizes that you are requesting only one certificate and will issue only one. If you change the idempotency token for each call, Amazon Web Services Private CA recognizes that you are requesting multiple certificates.</p>"}}}, "IssueCertificateResponse": {"type": "structure", "members": {"CertificateArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) of the issued certificate and the certificate serial number. This is of the form:</p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i>/certificate/<i>286535153982981100925020015808220737245</i> </code> </p>"}}}, "KeyAlgorithm": {"type": "string", "enum": ["RSA_2048", "RSA_4096", "EC_prime256v1", "EC_secp384r1"]}, "KeyStorageSecurityStandard": {"type": "string", "enum": ["FIPS_140_2_LEVEL_2_OR_HIGHER", "FIPS_140_2_LEVEL_3_OR_HIGHER"]}, "KeyUsage": {"type": "structure", "members": {"DigitalSignature": {"shape": "Boolean", "documentation": "<p> Key can be used for digital signing.</p>"}, "NonRepudiation": {"shape": "Boolean", "documentation": "<p>Key can be used for non-repudiation.</p>"}, "KeyEncipherment": {"shape": "Boolean", "documentation": "<p>Key can be used to encipher data.</p>"}, "DataEncipherment": {"shape": "Boolean", "documentation": "<p>Key can be used to decipher data.</p>"}, "KeyAgreement": {"shape": "Boolean", "documentation": "<p>Key can be used in a key-agreement protocol.</p>"}, "KeyCertSign": {"shape": "Boolean", "documentation": "<p>Key can be used to sign certificates.</p>"}, "CRLSign": {"shape": "Boolean", "documentation": "<p>Key can be used to sign CRLs.</p>"}, "EncipherOnly": {"shape": "Boolean", "documentation": "<p>Key can be used only to encipher data.</p>"}, "DecipherOnly": {"shape": "Boolean", "documentation": "<p>Key can be used only to decipher data.</p>"}}, "documentation": "<p>Defines one or more purposes for which the key contained in the certificate can be used. Default value for each option is false.</p>"}, "LimitExceededException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>An Amazon Web Services Private CA quota has been exceeded. See the exception message returned to determine the quota that was exceeded.</p>", "exception": true}, "ListCertificateAuthoritiesRequest": {"type": "structure", "members": {"NextToken": {"shape": "NextToken", "documentation": "<p>Use this parameter when paginating results in a subsequent request after you receive a response with truncated results. Set it to the value of the <code>NextToken</code> parameter from the response you just received.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Use this parameter when paginating results to specify the maximum number of items to return in the response on each page. If additional items exist beyond the number you specify, the <code>NextToken</code> element is sent in the response. Use this <code>NextToken</code> value in a subsequent request to retrieve additional items.</p>"}, "ResourceOwner": {"shape": "ResourceOwner", "documentation": "<p>Use this parameter to filter the returned set of certificate authorities based on their owner. The default is SELF.</p>"}}}, "ListCertificateAuthoritiesResponse": {"type": "structure", "members": {"CertificateAuthorities": {"shape": "CertificateAuthorities", "documentation": "<p>Summary information about each certificate authority you have created.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When the list is truncated, this value is present and should be used for the <code>NextToken</code> parameter in a subsequent pagination request.</p>"}}}, "ListPermissionsRequest": {"type": "structure", "required": ["CertificateAuthorityArn"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Number (ARN) of the private CA to inspect. You can find the ARN by calling the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListCertificateAuthorities.html\">ListCertificateAuthorities</a> action. This must be of the form: <code>arn:aws:acm-pca:region:account:certificate-authority/********-1234-1234-1234-************</code> You can get a private CA's ARN by running the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListCertificateAuthorities.html\">ListCertificateAuthorities</a> action.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When paginating results, use this parameter in a subsequent request after you receive a response with truncated results. Set it to the value of <b>NextToken</b> from the response you just received.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>When paginating results, use this parameter to specify the maximum number of items to return in the response. If additional items exist beyond the number you specify, the <b>NextToken</b> element is sent in the response. Use this <b>NextToken</b> value in a subsequent request to retrieve additional items.</p>"}}}, "ListPermissionsResponse": {"type": "structure", "members": {"Permissions": {"shape": "PermissionList", "documentation": "<p>Summary information about each permission assigned by the specified private CA, including the action enabled, the policy provided, and the time of creation.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When the list is truncated, this value is present and should be used for the <b>NextToken</b> parameter in a subsequent pagination request. </p>"}}}, "ListTagsRequest": {"type": "structure", "required": ["CertificateAuthorityArn"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that was returned when you called the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a> action. This must be of the form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code> </p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>Use this parameter when paginating results in a subsequent request after you receive a response with truncated results. Set it to the value of <b>NextToken</b> from the response you just received.</p>"}, "MaxResults": {"shape": "MaxResults", "documentation": "<p>Use this parameter when paginating results to specify the maximum number of items to return in the response. If additional items exist beyond the number you specify, the <b>NextToken</b> element is sent in the response. Use this <b>NextToken</b> value in a subsequent request to retrieve additional items.</p>"}}}, "ListTagsResponse": {"type": "structure", "members": {"Tags": {"shape": "TagList", "documentation": "<p>The tags associated with your private CA.</p>"}, "NextToken": {"shape": "NextToken", "documentation": "<p>When the list is truncated, this value is present and should be used for the <b>NextToken</b> parameter in a subsequent pagination request. </p>"}}}, "LockoutPreventedException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The current action was prevented because it would lock the caller out from performing subsequent actions. Verify that the specified parameters would not result in the caller being denied access to the resource. </p>", "exception": true}, "MalformedCSRException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The certificate signing request is invalid.</p>", "exception": true}, "MalformedCertificateException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>One or more fields in the certificate are invalid.</p>", "exception": true}, "MaxResults": {"type": "integer", "max": 1000, "min": 1}, "NextToken": {"type": "string", "max": 500, "min": 1}, "OcspConfiguration": {"type": "structure", "required": ["Enabled"], "members": {"Enabled": {"shape": "Boolean", "documentation": "<p>Flag enabling use of the Online Certificate Status Protocol (OCSP) for validating certificate revocation status.</p>", "box": true}, "OcspCustomCname": {"shape": "CnameString", "documentation": "<p>By default, Amazon Web Services Private CA injects an Amazon Web Services domain into certificates being validated by the Online Certificate Status Protocol (OCSP). A customer can alternatively use this object to define a CNAME specifying a customized OCSP domain.</p> <note> <p>The content of a Canonical Name (CNAME) record must conform to <a href=\"https://www.ietf.org/rfc/rfc2396.txt\">RFC2396</a> restrictions on the use of special characters in URIs. Additionally, the value of the CNAME must not include a protocol prefix such as \"http://\" or \"https://\".</p> </note> <p>For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/ocsp-customize.html\">Customizing Online Certificate Status Protocol (OCSP) </a> in the <i>Amazon Web Services Private Certificate Authority User Guide</i>.</p>"}}, "documentation": "<p>Contains information to enable and configure Online Certificate Status Protocol (OCSP) for validating certificate revocation status.</p> <p>When you revoke a certificate, OCSP responses may take up to 60 minutes to reflect the new status.</p>"}, "OtherName": {"type": "structure", "required": ["TypeId", "Value"], "members": {"TypeId": {"shape": "CustomObjectIdentifier", "documentation": "<p>Specifies an OID. </p>"}, "Value": {"shape": "String256", "documentation": "<p>Specifies an OID value.</p>"}}, "documentation": "<p>Defines a custom ASN.1 X.400 <code>GeneralName</code> using an object identifier (OID) and value. The OID must satisfy the regular expression shown below. For more information, see NIST's definition of <a href=\"https://csrc.nist.gov/glossary/term/Object_Identifier\">Object Identifier (OID)</a>.</p>"}, "PermanentDeletionTimeInDays": {"type": "integer", "max": 30, "min": 7}, "Permission": {"type": "structure", "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Number (ARN) of the private CA from which the permission was issued.</p>"}, "CreatedAt": {"shape": "TStamp", "documentation": "<p>The time at which the permission was created.</p>"}, "Principal": {"shape": "Principal", "documentation": "<p>The Amazon Web Services service or entity that holds the permission. At this time, the only valid principal is <code>acm.amazonaws.com</code>.</p>"}, "SourceAccount": {"shape": "AccountId", "documentation": "<p>The ID of the account that assigned the permission.</p>"}, "Actions": {"shape": "ActionList", "documentation": "<p>The private CA actions that can be performed by the designated Amazon Web Services service.</p>"}, "Policy": {"shape": "AWSPolicy", "documentation": "<p>The name of the policy that is associated with the permission.</p>"}}, "documentation": "<p>Permissions designate which private CA actions can be performed by an Amazon Web Services service or entity. In order for ACM to automatically renew private certificates, you must give the ACM service principal all available permissions (<code>IssueCertificate</code>, <code>GetCertificate</code>, and <code>ListPermissions</code>). Permissions can be assigned with the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreatePermission.html\">CreatePermission</a> action, removed with the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_DeletePermission.html\">DeletePermission</a> action, and listed with the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListPermissions.html\">ListPermissions</a> action.</p>"}, "PermissionAlreadyExistsException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The designated permission has already been given to the user.</p>", "exception": true}, "PermissionList": {"type": "list", "member": {"shape": "Permission"}, "min": 0}, "PolicyInformation": {"type": "structure", "required": ["CertPolicyId"], "members": {"CertPolicyId": {"shape": "CustomObjectIdentifier", "documentation": "<p>Specifies the object identifier (OID) of the certificate policy under which the certificate was issued. For more information, see NIST's definition of <a href=\"https://csrc.nist.gov/glossary/term/Object_Identifier\">Object Identifier (OID)</a>.</p>"}, "PolicyQualifiers": {"shape": "PolicyQualifierInfoList", "documentation": "<p>Modifies the given <code>CertPolicyId</code> with a qualifier. Amazon Web Services Private CA supports the certification practice statement (CPS) qualifier.</p>"}}, "documentation": "<p>Defines the X.509 <code>CertificatePolicies</code> extension.</p>"}, "PolicyQualifierId": {"type": "string", "enum": ["CPS"]}, "PolicyQualifierInfo": {"type": "structure", "required": ["PolicyQualifierId", "Qualifier"], "members": {"PolicyQualifierId": {"shape": "PolicyQualifierId", "documentation": "<p>Identifies the qualifier modifying a <code>CertPolicyId</code>.</p>"}, "Qualifier": {"shape": "Qualifier", "documentation": "<p>Defines the qualifier type. Amazon Web Services Private CA supports the use of a URI for a CPS qualifier in this field.</p>"}}, "documentation": "<p>Modifies the <code>CertPolicyId</code> of a <code>PolicyInformation</code> object with a qualifier. Amazon Web Services Private CA supports the certification practice statement (CPS) qualifier.</p>"}, "PolicyQualifierInfoList": {"type": "list", "member": {"shape": "PolicyQualifierInfo"}, "max": 20, "min": 1}, "PositiveLong": {"type": "long", "min": 1}, "Principal": {"type": "string", "max": 128, "min": 0, "pattern": "^[^*]+$"}, "PutPolicyRequest": {"type": "structure", "required": ["ResourceArn", "Policy"], "members": {"ResourceArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Number (ARN) of the private CA to associate with the policy. The ARN of the CA can be found by calling the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_ListCertificateAuthorities.html\">ListCertificateAuthorities</a> action.</p> <p/>"}, "Policy": {"shape": "AWSPolicy", "documentation": "<p>The path and file name of a JSON-formatted IAM policy to attach to the specified private CA resource. If this policy does not contain all required statements or if it includes any statement that is not allowed, the <code>PutPolicy</code> action returns an <code>InvalidPolicyException</code>. For information about IAM policy and statement structure, see <a href=\"https://docs.aws.amazon.com/IAM/latest/UserGuide/access_policies.html#access_policies-json\">Overview of JSON Policies</a>.</p>"}}}, "Qualifier": {"type": "structure", "required": ["CpsUri"], "members": {"CpsUri": {"shape": "String256", "documentation": "<p>Contains a pointer to a certification practice statement (CPS) published by the CA.</p>"}}, "documentation": "<p>Defines a <code>PolicyInformation</code> qualifier. Amazon Web Services Private CA supports the <a href=\"https://datatracker.ietf.org/doc/html/rfc5280#section-4.2.1.4\">certification practice statement (CPS) qualifier</a> defined in RFC 5280. </p>"}, "RequestAlreadyProcessedException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Your request has already been completed.</p>", "exception": true}, "RequestFailedException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>The request has failed for an unspecified reason.</p>", "exception": true}, "RequestInProgressException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>Your request is already in progress.</p>", "exception": true}, "ResourceNotFoundException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>A resource such as a private CA, S3 bucket, certificate, audit report, or policy cannot be found.</p>", "exception": true}, "ResourceOwner": {"type": "string", "enum": ["SELF", "OTHER_ACCOUNTS"]}, "RestoreCertificateAuthorityRequest": {"type": "structure", "required": ["CertificateAuthorityArn"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that was returned when you called the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a> action. This must be of the form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code> </p>"}}}, "RevocationConfiguration": {"type": "structure", "members": {"CrlConfiguration": {"shape": "CrlConfiguration", "documentation": "<p>Configuration of the certificate revocation list (CRL), if any, maintained by your private CA. A CRL is typically updated approximately 30 minutes after a certificate is revoked. If for any reason a CRL update fails, Amazon Web Services Private CA makes further attempts every 15 minutes.</p>"}, "OcspConfiguration": {"shape": "OcspConfiguration", "documentation": "<p>Configuration of Online Certificate Status Protocol (OCSP) support, if any, maintained by your private CA. When you revoke a certificate, OCSP responses may take up to 60 minutes to reflect the new status.</p>"}}, "documentation": "<p>Certificate revocation information used by the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a> and <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_UpdateCertificateAuthority.html\">UpdateCertificateAuthority</a> actions. Your private certificate authority (CA) can configure Online Certificate Status Protocol (OCSP) support and/or maintain a certificate revocation list (CRL). OCSP returns validation information about certificates as requested by clients, and a CRL contains an updated list of certificates revoked by your CA. For more information, see <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_RevokeCertificate.html\">RevokeCertificate</a> and <a href=\"https://docs.aws.amazon.com/privateca/latest/userguide/revocation-setup.html\">Setting up a certificate revocation method</a> in the <i>Amazon Web Services Private Certificate Authority User Guide</i>.</p>"}, "RevocationReason": {"type": "string", "enum": ["UNSPECIFIED", "KEY_COMPROMISE", "CERTIFICATE_AUTHORITY_COMPROMISE", "AFFILIATION_CHANGED", "SUPERSEDED", "CESSATION_OF_OPERATION", "PRIVILEGE_WITHDRAWN", "A_A_COMPROMISE"]}, "RevokeCertificateRequest": {"type": "structure", "required": ["CertificateAuthorityArn", "CertificateSerial", "RevocationReason"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the private CA that issued the certificate to be revoked. This must be of the form:</p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code> </p>"}, "CertificateSerial": {"shape": "String128", "documentation": "<p>Serial number of the certificate to be revoked. This must be in hexadecimal format. You can retrieve the serial number by calling <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_GetCertificate.html\">GetCertificate</a> with the Amazon Resource Name (ARN) of the certificate you want and the ARN of your private CA. The <b>GetCertificate</b> action retrieves the certificate in the PEM format. You can use the following OpenSSL command to list the certificate in text format and copy the hexadecimal serial number. </p> <p> <code>openssl x509 -in <i>file_path</i> -text -noout</code> </p> <p>You can also copy the serial number from the console or use the <a href=\"https://docs.aws.amazon.com/acm/latest/APIReference/API_DescribeCertificate.html\">DescribeCertificate</a> action in the <i>Certificate Manager API Reference</i>. </p>"}, "RevocationReason": {"shape": "RevocationReason", "documentation": "<p>Specifies why you revoked the certificate.</p>"}}}, "S3BucketName": {"type": "string", "max": 63, "min": 3}, "S3BucketName3To255": {"type": "string", "max": 255, "min": 3, "pattern": "^[-a-zA-Z0-9._/]+$"}, "S3Key": {"type": "string", "max": 1024}, "S3ObjectAcl": {"type": "string", "enum": ["PUBLIC_READ", "BUCKET_OWNER_FULL_CONTROL"]}, "SigningAlgorithm": {"type": "string", "enum": ["SHA256WITHECDSA", "SHA384WITHECDSA", "SHA512WITHECDSA", "SHA256WITHRSA", "SHA384WITHRSA", "SHA512WITHRSA"]}, "String": {"type": "string"}, "String128": {"type": "string", "max": 128, "min": 0}, "String16": {"type": "string", "max": 16, "min": 0}, "String1To256": {"type": "string", "max": 256, "min": 1}, "String253": {"type": "string", "max": 253, "min": 0}, "String256": {"type": "string", "max": 256, "min": 0}, "String3": {"type": "string", "max": 3, "min": 0}, "String39": {"type": "string", "max": 39, "min": 0}, "String40": {"type": "string", "max": 40, "min": 0}, "String5": {"type": "string", "max": 5, "min": 0}, "String64": {"type": "string", "max": 64, "min": 0}, "TStamp": {"type": "timestamp"}, "Tag": {"type": "structure", "required": ["Key"], "members": {"Key": {"shape": "TagKey", "documentation": "<p>Key (name) of the tag.</p>"}, "Value": {"shape": "TagValue", "documentation": "<p>Value of the tag.</p>"}}, "documentation": "<p>Tags are labels that you can use to identify and organize your private CAs. Each tag consists of a key and an optional value. You can associate up to 50 tags with a private CA. To add one or more tags to a private CA, call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_TagCertificateAuthority.html\">TagCertificateAuthority</a> action. To remove a tag, call the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_UntagCertificateAuthority.html\">UntagCertificateAuthority</a> action. </p>"}, "TagCertificateAuthorityRequest": {"type": "structure", "required": ["CertificateAuthorityArn", "Tags"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that was returned when you called <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a>. This must be of the form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code> </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>List of tags to be associated with the CA.</p>"}}}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagList": {"type": "list", "member": {"shape": "Tag"}, "max": 50, "min": 1}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TooManyTagsException": {"type": "structure", "members": {"message": {"shape": "String"}}, "documentation": "<p>You can associate up to 50 tags with a private CA. Exception information is contained in the exception message field.</p>", "exception": true}, "UntagCertificateAuthorityRequest": {"type": "structure", "required": ["CertificateAuthorityArn", "Tags"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>The Amazon Resource Name (ARN) that was returned when you called <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CreateCertificateAuthority.html\">CreateCertificateAuthority</a>. This must be of the form: </p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code> </p>"}, "Tags": {"shape": "TagList", "documentation": "<p>List of tags to be removed from the CA.</p>"}}}, "UpdateCertificateAuthorityRequest": {"type": "structure", "required": ["CertificateAuthorityArn"], "members": {"CertificateAuthorityArn": {"shape": "<PERSON><PERSON>", "documentation": "<p>Amazon Resource Name (ARN) of the private CA that issued the certificate to be revoked. This must be of the form:</p> <p> <code>arn:aws:acm-pca:<i>region</i>:<i>account</i>:certificate-authority/<i>********-1234-1234-1234-************</i> </code> </p>"}, "RevocationConfiguration": {"shape": "RevocationConfiguration", "documentation": "<p>Contains information to enable Online Certificate Status Protocol (OCSP) support, to enable a certificate revocation list (CRL), to enable both, or to enable neither. If this parameter is not supplied, existing capibilites remain unchanged. For more information, see the <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_OcspConfiguration.html\">OcspConfiguration</a> and <a href=\"https://docs.aws.amazon.com/privateca/latest/APIReference/API_CrlConfiguration.html\">CrlConfiguration</a> types.</p> <note> <p>The following requirements apply to revocation configurations.</p> <ul> <li> <p>A configuration disabling CRLs or OCSP must contain only the <code>Enabled=False</code> parameter, and will fail if other parameters such as <code>CustomCname</code> or <code>ExpirationInDays</code> are included.</p> </li> <li> <p>In a CRL configuration, the <code>S3BucketName</code> parameter must conform to <a href=\"https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucketnamingrules.html\">Amazon S3 bucket naming rules</a>.</p> </li> <li> <p>A configuration containing a custom Canonical Name (CNAME) parameter for CRLs or OCSP must conform to <a href=\"https://www.ietf.org/rfc/rfc2396.txt\">RFC2396</a> restrictions on the use of special characters in a CNAME. </p> </li> <li> <p>In a CRL or OCSP configuration, the value of a CNAME parameter must not include a protocol prefix such as \"http://\" or \"https://\".</p> </li> </ul> </note>"}, "Status": {"shape": "CertificateAuthorityStatus", "documentation": "<p>Status of your private CA.</p>"}}}, "Validity": {"type": "structure", "required": ["Value", "Type"], "members": {"Value": {"shape": "PositiveLong", "documentation": "<p>A long integer interpreted according to the value of <code>Type</code>, below.</p>", "box": true}, "Type": {"shape": "ValidityPeriodType", "documentation": "<p>Determines how <i>Amazon Web Services Private CA</i> interprets the <code>Value</code> parameter, an integer. Supported validity types include those listed below. Type definitions with values include a sample input value and the resulting output. </p> <p> <code>END_DATE</code>: The specific date and time when the certificate will expire, expressed using UTCTime (YYMMDDHHMMSS) or GeneralizedTime (YYYYMMDDHHMMSS) format. When UTCTime is used, if the year field (YY) is greater than or equal to 50, the year is interpreted as 19YY. If the year field is less than 50, the year is interpreted as 20YY.</p> <ul> <li> <p>Sample input value: 491231235959 (UTCTime format)</p> </li> <li> <p>Output expiration date/time: 12/31/2049 23:59:59</p> </li> </ul> <p> <code>ABSOLUTE</code>: The specific date and time when the validity of a certificate will start or expire, expressed in seconds since the Unix Epoch. </p> <ul> <li> <p>Sample input value: 2524608000</p> </li> <li> <p>Output expiration date/time: 01/01/2050 00:00:00</p> </li> </ul> <p> <code>DAYS</code>, <code>MONTHS</code>, <code>YEARS</code>: The relative time from the moment of issuance until the certificate will expire, expressed in days, months, or years. </p> <p>Example if <code>DAYS</code>, issued on 10/12/2020 at 12:34:54 UTC:</p> <ul> <li> <p>Sample input value: 90</p> </li> <li> <p>Output expiration date: 01/10/2020 12:34:54 UTC</p> </li> </ul> <p>The minimum validity duration for a certificate using relative time (<code>DAYS</code>) is one day. The minimum validity for a certificate using absolute time (<code>ABSOLUTE</code> or <code>END_DATE</code>) is one second.</p>"}}, "documentation": "<p>Validity specifies the period of time during which a certificate is valid. Validity can be expressed as an explicit date and time when the validity of a certificate starts or expires, or as a span of time after issuance, stated in days, months, or years. For more information, see <a href=\"https://tools.ietf.org/html/rfc5280#section-*******\">Validity</a> in RFC 5280.</p> <p>Amazon Web Services Private CA API consumes the <code>Validity</code> data type differently in two distinct parameters of the <code>IssueCertificate</code> action. The required parameter <code>IssueCertificate</code>:<code>Validity</code> specifies the end of a certificate's validity period. The optional parameter <code>IssueCertificate</code>:<code>ValidityNotBefore</code> specifies a customized starting time for the validity period.</p>"}, "ValidityPeriodType": {"type": "string", "enum": ["END_DATE", "ABSOLUTE", "DAYS", "MONTHS", "YEARS"]}}, "documentation": "<p>This is the <i>Amazon Web Services Private Certificate Authority API Reference</i>. It provides descriptions, syntax, and usage examples for each of the actions and data types involved in creating and managing a private certificate authority (CA) for your organization.</p> <p>The documentation for each action shows the API request parameters and the JSON response. Alternatively, you can use one of the Amazon Web Services SDKs to access an API that is tailored to the programming language or platform that you prefer. For more information, see <a href=\"https://aws.amazon.com/tools/#SDKs\">Amazon Web Services SDKs</a>.</p> <p>Each Amazon Web Services Private CA API operation has a quota that determines the number of times the operation can be called per second. Amazon Web Services Private CA throttles API requests at different rates depending on the operation. Throttling means that Amazon Web Services Private CA rejects an otherwise valid request because the request exceeds the operation's quota for the number of requests per second. When a request is throttled, Amazon Web Services Private CA returns a <a href=\"https://docs.aws.amazon.com/acm-pca/latest/APIReference/CommonErrors.html\">ThrottlingException</a> error. Amazon Web Services Private CA does not guarantee a minimum request rate for APIs. </p> <p>To see an up-to-date list of your Amazon Web Services Private CA quotas, or to request a quota increase, log into your Amazon Web Services account and visit the <a href=\"https://console.aws.amazon.com/servicequotas/\">Service Quotas</a> console.</p>"}