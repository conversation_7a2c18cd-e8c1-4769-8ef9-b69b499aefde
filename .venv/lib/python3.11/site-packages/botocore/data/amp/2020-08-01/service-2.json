{"version": "2.0", "metadata": {"apiVersion": "2020-08-01", "endpointPrefix": "aps", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon Prometheus Service", "serviceId": "amp", "signatureVersion": "v4", "signingName": "aps", "uid": "amp-2020-08-01"}, "operations": {"CreateAlertManagerDefinition": {"name": "CreateAlertManagerDefinition", "http": {"method": "POST", "requestUri": "/workspaces/{workspaceId}/alertmanager/definition", "responseCode": 202}, "input": {"shape": "CreateAlertManagerDefinitionRequest"}, "output": {"shape": "CreateAlertManagerDefinitionResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create an alert manager definition.</p>", "idempotent": true}, "CreateLoggingConfiguration": {"name": "CreateLoggingConfiguration", "http": {"method": "POST", "requestUri": "/workspaces/{workspaceId}/logging", "responseCode": 202}, "input": {"shape": "CreateLoggingConfigurationRequest"}, "output": {"shape": "CreateLoggingConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Create logging configuration.</p>", "idempotent": true}, "CreateRuleGroupsNamespace": {"name": "CreateRuleGroupsNamespace", "http": {"method": "POST", "requestUri": "/workspaces/{workspaceId}/rulegroupsnamespaces", "responseCode": 202}, "input": {"shape": "CreateRuleGroupsNamespaceRequest"}, "output": {"shape": "CreateRuleGroupsNamespaceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Create a rule group namespace.</p>", "idempotent": true}, "CreateWorkspace": {"name": "CreateWorkspace", "http": {"method": "POST", "requestUri": "/workspaces", "responseCode": 202}, "input": {"shape": "CreateWorkspaceRequest"}, "output": {"shape": "CreateWorkspaceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Creates a new AMP workspace.</p>", "idempotent": true}, "DeleteAlertManagerDefinition": {"name": "DeleteAlertManagerDefinition", "http": {"method": "DELETE", "requestUri": "/workspaces/{workspaceId}/alertmanager/definition", "responseCode": 202}, "input": {"shape": "DeleteAlertManagerDefinitionRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an alert manager definition.</p>", "idempotent": true}, "DeleteLoggingConfiguration": {"name": "DeleteLoggingConfiguration", "http": {"method": "DELETE", "requestUri": "/workspaces/{workspaceId}/logging", "responseCode": 202}, "input": {"shape": "DeleteLoggingConfigurationRequest"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Delete logging configuration.</p>", "idempotent": true}, "DeleteRuleGroupsNamespace": {"name": "DeleteRuleGroupsNamespace", "http": {"method": "DELETE", "requestUri": "/workspaces/{workspaceId}/rulegroupsnamespaces/{name}", "responseCode": 202}, "input": {"shape": "DeleteRuleGroupsNamespaceRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Delete a rule groups namespace.</p>", "idempotent": true}, "DeleteWorkspace": {"name": "DeleteWorkspace", "http": {"method": "DELETE", "requestUri": "/workspaces/{workspaceId}", "responseCode": 202}, "input": {"shape": "DeleteWorkspaceRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes an AMP workspace.</p>", "idempotent": true}, "DescribeAlertManagerDefinition": {"name": "DescribeAlertManagerDefinition", "http": {"method": "GET", "requestUri": "/workspaces/{workspaceId}/alertmanager/definition", "responseCode": 200}, "input": {"shape": "DescribeAlertManagerDefinitionRequest"}, "output": {"shape": "DescribeAlertManagerDefinitionResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes an alert manager definition.</p>"}, "DescribeLoggingConfiguration": {"name": "DescribeLoggingConfiguration", "http": {"method": "GET", "requestUri": "/workspaces/{workspaceId}/logging", "responseCode": 200}, "input": {"shape": "DescribeLoggingConfigurationRequest"}, "output": {"shape": "DescribeLoggingConfigurationResponse"}, "errors": [{"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes logging configuration.</p>"}, "DescribeRuleGroupsNamespace": {"name": "DescribeRuleGroupsNamespace", "http": {"method": "GET", "requestUri": "/workspaces/{workspaceId}/rulegroupsnamespaces/{name}", "responseCode": 200}, "input": {"shape": "DescribeRuleGroupsNamespaceRequest"}, "output": {"shape": "DescribeRuleGroupsNamespaceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describe a rule groups namespace.</p>"}, "DescribeWorkspace": {"name": "DescribeWorkspace", "http": {"method": "GET", "requestUri": "/workspaces/{workspaceId}", "responseCode": 200}, "input": {"shape": "DescribeWorkspaceRequest"}, "output": {"shape": "DescribeWorkspaceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Describes an existing AMP workspace.</p>"}, "ListRuleGroupsNamespaces": {"name": "ListRuleGroupsNamespaces", "http": {"method": "GET", "requestUri": "/workspaces/{workspaceId}/rulegroupsnamespaces", "responseCode": 200}, "input": {"shape": "ListRuleGroupsNamespacesRequest"}, "output": {"shape": "ListRuleGroupsNamespacesResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists rule groups namespaces.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists the tags you have assigned to the resource.</p>"}, "ListWorkspaces": {"name": "ListWorkspaces", "http": {"method": "GET", "requestUri": "/workspaces", "responseCode": 200}, "input": {"shape": "ListWorkspacesRequest"}, "output": {"shape": "ListWorkspacesResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all AMP workspaces, including workspaces being created or deleted.</p>"}, "PutAlertManagerDefinition": {"name": "PutAlertManagerDefinition", "http": {"method": "PUT", "requestUri": "/workspaces/{workspaceId}/alertmanager/definition", "responseCode": 202}, "input": {"shape": "PutAlertManagerDefinitionRequest"}, "output": {"shape": "PutAlertManagerDefinitionResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Update an alert manager definition.</p>", "idempotent": true}, "PutRuleGroupsNamespace": {"name": "PutRuleGroupsNamespace", "http": {"method": "PUT", "requestUri": "/workspaces/{workspaceId}/rulegroupsnamespaces/{name}", "responseCode": 202}, "input": {"shape": "PutRuleGroupsNamespaceRequest"}, "output": {"shape": "PutRuleGroupsNamespaceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Update a rule groups namespace.</p>", "idempotent": true}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates tags for the specified resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes tags from the specified resource.</p>", "idempotent": true}, "UpdateLoggingConfiguration": {"name": "UpdateLoggingConfiguration", "http": {"method": "PUT", "requestUri": "/workspaces/{workspaceId}/logging", "responseCode": 202}, "input": {"shape": "UpdateLoggingConfigurationRequest"}, "output": {"shape": "UpdateLoggingConfigurationResponse"}, "errors": [{"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}], "documentation": "<p>Update logging configuration.</p>", "idempotent": true}, "UpdateWorkspaceAlias": {"name": "UpdateWorkspaceAlias", "http": {"method": "POST", "requestUri": "/workspaces/{workspaceId}/alias", "responseCode": 204}, "input": {"shape": "UpdateWorkspaceAliasRequest"}, "errors": [{"shape": "ThrottlingException"}, {"shape": "ConflictException"}, {"shape": "ValidationException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "InternalServerException"}, {"shape": "ServiceQuotaExceededException"}], "documentation": "<p>Updates an AMP workspace alias.</p>", "idempotent": true}}, "shapes": {"AccessDeniedException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String", "documentation": "<p>Description of the error.</p>"}}, "documentation": "<p>User does not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403, "senderFault": true}, "exception": true}, "AlertManagerDefinitionData": {"type": "blob", "documentation": "<p>The alert manager definition data.</p>"}, "AlertManagerDefinitionDescription": {"type": "structure", "required": ["createdAt", "data", "modifiedAt", "status"], "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the alert manager definition was created.</p>"}, "data": {"shape": "AlertManagerDefinitionData", "documentation": "<p>The alert manager definition.</p>"}, "modifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the alert manager definition was modified.</p>"}, "status": {"shape": "AlertManagerDefinitionStatus", "documentation": "<p>The status of alert manager definition.</p>"}}, "documentation": "<p>Represents the properties of an alert manager definition.</p>"}, "AlertManagerDefinitionStatus": {"type": "structure", "required": ["statusCode"], "members": {"statusCode": {"shape": "AlertManagerDefinitionStatusCode", "documentation": "<p>Status code of this definition.</p>"}, "statusReason": {"shape": "String", "documentation": "<p>The reason for failure if any.</p>"}}, "documentation": "<p>Represents the status of a definition.</p>"}, "AlertManagerDefinitionStatusCode": {"type": "string", "documentation": "<p>State of an alert manager definition.</p>", "enum": ["CREATING", "ACTIVE", "UPDATING", "DELETING", "CREATION_FAILED", "UPDATE_FAILED"]}, "ConflictException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String", "documentation": "<p>Description of the error.</p>"}, "resourceId": {"shape": "String", "documentation": "<p>Identifier of the resource affected.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>Type of the resource affected.</p>"}}, "documentation": "<p>Updating or deleting a resource can cause an inconsistent state.</p>", "error": {"httpStatusCode": 409, "senderFault": true}, "exception": true}, "CreateAlertManagerDefinitionRequest": {"type": "structure", "required": ["data", "workspaceId"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Optional, unique, case-sensitive, user-provided identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "data": {"shape": "AlertManagerDefinitionData", "documentation": "<p>The alert manager definition data.</p>"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace in which to create the alert manager definition.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a CreateAlertManagerDefinition operation.</p>"}, "CreateAlertManagerDefinitionResponse": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "AlertManagerDefinitionStatus", "documentation": "<p>The status of alert manager definition.</p>"}}, "documentation": "<p>Represents the output of a CreateAlertManagerDefinition operation.</p>"}, "CreateLoggingConfigurationRequest": {"type": "structure", "required": ["logGroupArn", "workspaceId"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Optional, unique, case-sensitive, user-provided identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "logGroupArn": {"shape": "LogGroupArn", "documentation": "<p>The ARN of the CW log group to which the vended log data will be published.</p>"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace to vend logs to.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a CreateLoggingConfiguration operation.</p>"}, "CreateLoggingConfigurationResponse": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "LoggingConfigurationStatus", "documentation": "<p>The status of the logging configuration.</p>"}}, "documentation": "<p>Represents the output of a CreateLoggingConfiguration operation.</p>"}, "CreateRuleGroupsNamespaceRequest": {"type": "structure", "required": ["data", "name", "workspaceId"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Optional, unique, case-sensitive, user-provided identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "data": {"shape": "RuleGroupsNamespaceData", "documentation": "<p>The namespace data that define the rule groups.</p>"}, "name": {"shape": "RuleGroupsNamespaceName", "documentation": "<p>The rule groups namespace name.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>Optional, user-provided tags for this rule groups namespace.</p>"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace in which to create the rule group namespace.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a CreateRuleGroupsNamespace operation.</p>"}, "CreateRuleGroupsNamespaceResponse": {"type": "structure", "required": ["arn", "name", "status"], "members": {"arn": {"shape": "RuleGroupsNamespaceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this rule groups namespace.</p>"}, "name": {"shape": "RuleGroupsNamespaceName", "documentation": "<p>The rule groups namespace name.</p>"}, "status": {"shape": "RuleGroupsNamespaceStatus", "documentation": "<p>The status of rule groups namespace.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of this rule groups namespace.</p>"}}, "documentation": "<p>Represents the output of a CreateRuleGroupsNamespace operation.</p>"}, "CreateWorkspaceRequest": {"type": "structure", "members": {"alias": {"shape": "WorkspaceAlias", "documentation": "<p>An optional user-assigned alias for this workspace. This alias is for user reference and does not need to be unique.</p>"}, "clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Optional, unique, case-sensitive, user-provided identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "tags": {"shape": "TagMap", "documentation": "<p>Optional, user-provided tags for this workspace.</p>"}}, "documentation": "<p>Represents the input of a CreateWorkspace operation.</p>"}, "CreateWorkspaceResponse": {"type": "structure", "required": ["arn", "status", "workspaceId"], "members": {"arn": {"shape": "WorkspaceArn", "documentation": "<p>The ARN of the workspace that was just created.</p>"}, "status": {"shape": "WorkspaceStatus", "documentation": "<p>The status of the workspace that was just created (usually CREATING).</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of this workspace.</p>"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The generated ID of the workspace that was just created.</p>"}}, "documentation": "<p>Represents the output of a CreateWorkspace operation.</p>"}, "DeleteAlertManagerDefinitionRequest": {"type": "structure", "required": ["workspaceId"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Optional, unique, case-sensitive, user-provided identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace in which to delete the alert manager definition.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a DeleteAlertManagerDefinition operation.</p>"}, "DeleteLoggingConfigurationRequest": {"type": "structure", "required": ["workspaceId"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Optional, unique, case-sensitive, user-provided identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace to vend logs to.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a DeleteLoggingConfiguration operation.</p>"}, "DeleteRuleGroupsNamespaceRequest": {"type": "structure", "required": ["name", "workspaceId"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Optional, unique, case-sensitive, user-provided identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "name": {"shape": "RuleGroupsNamespaceName", "documentation": "<p>The rule groups namespace name.</p>", "location": "uri", "locationName": "name"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace to delete rule group definition.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a DeleteRuleGroupsNamespace operation.</p>"}, "DeleteWorkspaceRequest": {"type": "structure", "required": ["workspaceId"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Optional, unique, case-sensitive, user-provided identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace to delete.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a DeleteWorkspace operation.</p>"}, "DescribeAlertManagerDefinitionRequest": {"type": "structure", "required": ["workspaceId"], "members": {"workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace to describe.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a DescribeAlertManagerDefinition operation.</p>"}, "DescribeAlertManagerDefinitionResponse": {"type": "structure", "required": ["alertManagerDefinition"], "members": {"alertManagerDefinition": {"shape": "AlertManagerDefinitionDescription", "documentation": "<p>The properties of the selected workspace's alert manager definition.</p>"}}, "documentation": "<p>Represents the output of a DescribeAlertManagerDefinition operation.</p>"}, "DescribeLoggingConfigurationRequest": {"type": "structure", "required": ["workspaceId"], "members": {"workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace to vend logs to.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a DescribeLoggingConfiguration operation.</p>"}, "DescribeLoggingConfigurationResponse": {"type": "structure", "required": ["loggingConfiguration"], "members": {"loggingConfiguration": {"shape": "LoggingConfigurationMetadata", "documentation": "<p>Metadata object containing information about the logging configuration of a workspace.</p>"}}, "documentation": "<p>Represents the output of a DescribeLoggingConfiguration operation.</p>"}, "DescribeRuleGroupsNamespaceRequest": {"type": "structure", "required": ["name", "workspaceId"], "members": {"name": {"shape": "RuleGroupsNamespaceName", "documentation": "<p>The rule groups namespace.</p>", "location": "uri", "locationName": "name"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace to describe.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a DescribeRuleGroupsNamespace operation.</p>"}, "DescribeRuleGroupsNamespaceResponse": {"type": "structure", "required": ["ruleGroupsNamespace"], "members": {"ruleGroupsNamespace": {"shape": "RuleGroupsNamespaceDescription", "documentation": "<p>The selected rule groups namespace.</p>"}}, "documentation": "<p>Represents the output of a DescribeRuleGroupsNamespace operation.</p>"}, "DescribeWorkspaceRequest": {"type": "structure", "required": ["workspaceId"], "members": {"workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace to describe.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a DescribeWorkspace operation.</p>"}, "DescribeWorkspaceResponse": {"type": "structure", "required": ["workspace"], "members": {"workspace": {"shape": "WorkspaceDescription", "documentation": "<p>The properties of the selected workspace.</p>"}}, "documentation": "<p>Represents the output of a DescribeWorkspace operation.</p>"}, "IdempotencyToken": {"type": "string", "documentation": "<p>An identifier used to ensure the idempotency of a write request.</p>", "max": 64, "min": 1, "pattern": "[!-~]+"}, "Integer": {"type": "integer", "box": true}, "InternalServerException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String", "documentation": "<p>Description of the error.</p>"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>Advice to clients on when the call can be safely retried.</p>", "location": "header", "locationName": "Retry-After"}}, "documentation": "<p>Unexpected error during processing of request.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true, "retryable": {"throttling": false}}, "ListRuleGroupsNamespacesRequest": {"type": "structure", "required": ["workspaceId"], "members": {"maxResults": {"shape": "ListRuleGroupsNamespacesRequestMaxResultsInteger", "documentation": "<p>Maximum results to return in response (default=100, maximum=1000).</p>", "location": "querystring", "locationName": "maxResults"}, "name": {"shape": "RuleGroupsNamespaceName", "documentation": "<p>Optional filter for rule groups namespace name. Only the rule groups namespace that begin with this value will be returned.</p>", "location": "querystring", "locationName": "name"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Pagination token to request the next page in a paginated list. This token is obtained from the output of the previous ListRuleGroupsNamespaces request.</p>", "location": "querystring", "locationName": "nextToken"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a ListRuleGroupsNamespaces operation.</p>"}, "ListRuleGroupsNamespacesRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "ListRuleGroupsNamespacesResponse": {"type": "structure", "required": ["ruleGroupsNamespaces"], "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>Pagination token to use when requesting the next page in this list.</p>"}, "ruleGroupsNamespaces": {"shape": "RuleGroupsNamespaceSummaryList", "documentation": "<p>The list of the selected rule groups namespaces.</p>"}}, "documentation": "<p>Represents the output of a ListRuleGroupsNamespaces operation.</p>"}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The ARN of the resource.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap"}}}, "ListWorkspacesRequest": {"type": "structure", "members": {"alias": {"shape": "WorkspaceAlias", "documentation": "<p>Optional filter for workspace alias. Only the workspaces with aliases that begin with this value will be returned.</p>", "location": "querystring", "locationName": "alias"}, "maxResults": {"shape": "ListWorkspacesRequestMaxResultsInteger", "documentation": "<p>Maximum results to return in response (default=100, maximum=1000).</p>", "location": "querystring", "locationName": "maxResults"}, "nextToken": {"shape": "PaginationToken", "documentation": "<p>Pagination token to request the next page in a paginated list. This token is obtained from the output of the previous ListWorkspaces request.</p>", "location": "querystring", "locationName": "nextToken"}}, "documentation": "<p>Represents the input of a ListWorkspaces operation.</p>"}, "ListWorkspacesRequestMaxResultsInteger": {"type": "integer", "box": true, "max": 1000, "min": 1}, "ListWorkspacesResponse": {"type": "structure", "required": ["workspaces"], "members": {"nextToken": {"shape": "PaginationToken", "documentation": "<p>Pagination token to use when requesting the next page in this list.</p>"}, "workspaces": {"shape": "WorkspaceSummaryList", "documentation": "<p>The list of existing workspaces, including those undergoing creation or deletion.</p>"}}, "documentation": "<p>Represents the output of a ListWorkspaces operation.</p>"}, "LogGroupArn": {"type": "string", "pattern": "^arn:aws[a-z0-9-]*:logs:[a-z0-9-]+:\\d{12}:log-group:[A-Za-z0-9\\.\\-\\_\\#/]{1,512}\\:\\*$"}, "LoggingConfigurationMetadata": {"type": "structure", "required": ["createdAt", "logGroupArn", "modifiedAt", "status", "workspace"], "members": {"createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the logging configuration was created.</p>"}, "logGroupArn": {"shape": "LogGroupArn", "documentation": "<p>The ARN of the CW log group to which the vended log data will be published.</p>"}, "modifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the logging configuration was modified.</p>"}, "status": {"shape": "LoggingConfigurationStatus", "documentation": "<p>The status of the logging configuration.</p>"}, "workspace": {"shape": "WorkspaceId", "documentation": "<p>The workspace where the logging configuration exists.</p>"}}, "documentation": "<p>Represents the properties of a logging configuration metadata.</p>"}, "LoggingConfigurationStatus": {"type": "structure", "required": ["statusCode"], "members": {"statusCode": {"shape": "LoggingConfigurationStatusCode", "documentation": "<p>Status code of the logging configuration.</p>"}, "statusReason": {"shape": "String", "documentation": "<p>The reason for failure if any.</p>"}}, "documentation": "<p>Represents the status of a logging configuration.</p>"}, "LoggingConfigurationStatusCode": {"type": "string", "documentation": "<p>State of a logging configuration.</p>", "enum": ["CREATING", "ACTIVE", "UPDATING", "DELETING", "CREATION_FAILED", "UPDATE_FAILED"]}, "PaginationToken": {"type": "string", "documentation": "<p>A token used to access the next page in a paginated result set.</p>"}, "PutAlertManagerDefinitionRequest": {"type": "structure", "required": ["data", "workspaceId"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Optional, unique, case-sensitive, user-provided identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "data": {"shape": "AlertManagerDefinitionData", "documentation": "<p>The alert manager definition data.</p>"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace in which to update the alert manager definition.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a PutAlertManagerDefinition operation.</p>"}, "PutAlertManagerDefinitionResponse": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "AlertManagerDefinitionStatus", "documentation": "<p>The status of alert manager definition.</p>"}}, "documentation": "<p>Represents the output of a PutAlertManagerDefinition operation.</p>"}, "PutRuleGroupsNamespaceRequest": {"type": "structure", "required": ["data", "name", "workspaceId"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Optional, unique, case-sensitive, user-provided identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "data": {"shape": "RuleGroupsNamespaceData", "documentation": "<p>The namespace data that define the rule groups.</p>"}, "name": {"shape": "RuleGroupsNamespaceName", "documentation": "<p>The rule groups namespace name.</p>", "location": "uri", "locationName": "name"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace in which to update the rule group namespace.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of a PutRuleGroupsNamespace operation.</p>"}, "PutRuleGroupsNamespaceResponse": {"type": "structure", "required": ["arn", "name", "status"], "members": {"arn": {"shape": "RuleGroupsNamespaceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this rule groups namespace.</p>"}, "name": {"shape": "RuleGroupsNamespaceName", "documentation": "<p>The rule groups namespace name.</p>"}, "status": {"shape": "RuleGroupsNamespaceStatus", "documentation": "<p>The status of rule groups namespace.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of this rule groups namespace.</p>"}}, "documentation": "<p>Represents the output of a PutRuleGroupsNamespace operation.</p>"}, "ResourceNotFoundException": {"type": "structure", "required": ["message", "resourceId", "resourceType"], "members": {"message": {"shape": "String", "documentation": "<p>Description of the error.</p>"}, "resourceId": {"shape": "String", "documentation": "<p>Identifier of the resource affected.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>Type of the resource affected.</p>"}}, "documentation": "<p>Request references a resource which does not exist.</p>", "error": {"httpStatusCode": 404, "senderFault": true}, "exception": true}, "RuleGroupsNamespaceArn": {"type": "string", "documentation": "<p>An ARN identifying a rule groups namespace.</p>"}, "RuleGroupsNamespaceData": {"type": "blob", "documentation": "<p>The rule groups namespace data.</p>"}, "RuleGroupsNamespaceDescription": {"type": "structure", "required": ["arn", "createdAt", "data", "modifiedAt", "name", "status"], "members": {"arn": {"shape": "RuleGroupsNamespaceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this rule groups namespace.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the rule groups namespace was created.</p>"}, "data": {"shape": "RuleGroupsNamespaceData", "documentation": "<p>The rule groups namespace data.</p>"}, "modifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the rule groups namespace was modified.</p>"}, "name": {"shape": "RuleGroupsNamespaceName", "documentation": "<p>The rule groups namespace name.</p>"}, "status": {"shape": "RuleGroupsNamespaceStatus", "documentation": "<p>The status of rule groups namespace.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of this rule groups namespace.</p>"}}, "documentation": "<p>Represents a description of the rule groups namespace.</p>"}, "RuleGroupsNamespaceName": {"type": "string", "documentation": "<p>The namespace name that the rule group belong to.</p>", "max": 64, "min": 1, "pattern": "[0-9A-Za-z][-.0-9A-Z_a-z]*"}, "RuleGroupsNamespaceStatus": {"type": "structure", "required": ["statusCode"], "members": {"statusCode": {"shape": "RuleGroupsNamespaceStatusCode", "documentation": "<p>Status code of this namespace.</p>"}, "statusReason": {"shape": "String", "documentation": "<p>The reason for failure if any.</p>"}}, "documentation": "<p>Represents the status of a namespace.</p>"}, "RuleGroupsNamespaceStatusCode": {"type": "string", "documentation": "<p>State of a namespace.</p>", "enum": ["CREATING", "ACTIVE", "UPDATING", "DELETING", "CREATION_FAILED", "UPDATE_FAILED"]}, "RuleGroupsNamespaceSummary": {"type": "structure", "required": ["arn", "createdAt", "modifiedAt", "name", "status"], "members": {"arn": {"shape": "RuleGroupsNamespaceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this rule groups namespace.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the rule groups namespace was created.</p>"}, "modifiedAt": {"shape": "Timestamp", "documentation": "<p>The time when the rule groups namespace was modified.</p>"}, "name": {"shape": "RuleGroupsNamespaceName", "documentation": "<p>The rule groups namespace name.</p>"}, "status": {"shape": "RuleGroupsNamespaceStatus", "documentation": "<p>The status of rule groups namespace.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of this rule groups namespace.</p>"}}, "documentation": "<p>Represents a summary of the rule groups namespace.</p>"}, "RuleGroupsNamespaceSummaryList": {"type": "list", "member": {"shape": "RuleGroupsNamespaceSummary"}, "documentation": "<p>A list of rule groups namespace summary.</p>"}, "ServiceQuotaExceededException": {"type": "structure", "required": ["message", "quotaCode", "resourceId", "resourceType", "serviceCode"], "members": {"message": {"shape": "String", "documentation": "<p>Description of the error.</p>"}, "quotaCode": {"shape": "String", "documentation": "<p>Service Quotas requirement to identify originating quota.</p>"}, "resourceId": {"shape": "String", "documentation": "<p>Identifier of the resource affected.</p>"}, "resourceType": {"shape": "String", "documentation": "<p>Type of the resource affected.</p>"}, "serviceCode": {"shape": "String", "documentation": "<p>Service Quotas requirement to identify originating service.</p>"}}, "documentation": "<p>Request would cause a service quota to be exceeded.</p>", "error": {"httpStatusCode": 402, "senderFault": true}, "exception": true}, "String": {"type": "string"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "TagKeys": {"type": "list", "member": {"shape": "TagKey"}}, "TagMap": {"type": "map", "key": {"shape": "TagKey", "documentation": "<p>The key of the tag.</p> <p>Constraints: Tag keys are case-sensitive and accept a maximum of 128 Unicode characters. Valid characters are Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @ May not begin with <code>aws:</code>.</p>"}, "value": {"shape": "TagValue", "documentation": "<p>The value of the tag.</p> <p>Constraints: Tag values are case-sensitive and accept a maximum of 256 Unicode characters. Valid characters are Unicode letters, digits, white space, and any of the following symbols: _ . : / = + - @</p>"}, "documentation": "<p>The list of tags assigned to the resource.</p>", "max": 50, "min": 0}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The ARN of the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256, "min": 0, "pattern": "^([\\p{L}\\p{Z}\\p{N}_.:/=+\\-@]*)$"}, "ThrottlingException": {"type": "structure", "required": ["message"], "members": {"message": {"shape": "String", "documentation": "<p>Description of the error.</p>"}, "quotaCode": {"shape": "String", "documentation": "<p>Service Quotas requirement to identify originating quota.</p>"}, "retryAfterSeconds": {"shape": "Integer", "documentation": "<p>Advice to clients on when the call can be safely retried.</p>", "location": "header", "locationName": "Retry-After"}, "serviceCode": {"shape": "String", "documentation": "<p>Service Quotas requirement to identify originating service.</p>"}}, "documentation": "<p>Request was denied due to request throttling.</p>", "error": {"httpStatusCode": 429, "senderFault": true}, "exception": true, "retryable": {"throttling": false}}, "Timestamp": {"type": "timestamp"}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "String", "documentation": "<p>The ARN of the resource.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeys", "documentation": "<p>One or more tag keys</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateLoggingConfigurationRequest": {"type": "structure", "required": ["logGroupArn", "workspaceId"], "members": {"clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Optional, unique, case-sensitive, user-provided identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "logGroupArn": {"shape": "LogGroupArn", "documentation": "<p>The ARN of the CW log group to which the vended log data will be published.</p>"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace to vend logs to.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of an UpdateLoggingConfiguration operation.</p>"}, "UpdateLoggingConfigurationResponse": {"type": "structure", "required": ["status"], "members": {"status": {"shape": "LoggingConfigurationStatus", "documentation": "<p>The status of the logging configuration.</p>"}}, "documentation": "<p>Represents the output of an UpdateLoggingConfiguration operation.</p>"}, "UpdateWorkspaceAliasRequest": {"type": "structure", "required": ["workspaceId"], "members": {"alias": {"shape": "WorkspaceAlias", "documentation": "<p>The new alias of the workspace.</p>"}, "clientToken": {"shape": "IdempotencyToken", "documentation": "<p>Optional, unique, case-sensitive, user-provided identifier to ensure the idempotency of the request.</p>", "idempotencyToken": true}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>The ID of the workspace being updated.</p>", "location": "uri", "locationName": "workspaceId"}}, "documentation": "<p>Represents the input of an UpdateWorkspaceAlias operation.</p>"}, "Uri": {"type": "string", "max": 1024, "min": 1}, "ValidationException": {"type": "structure", "required": ["message", "reason"], "members": {"fieldList": {"shape": "ValidationExceptionFieldList", "documentation": "<p>The field that caused the error, if applicable. If more than one field caused the error, pick one and elaborate in the message.</p>"}, "message": {"shape": "String", "documentation": "<p>Description of the error.</p>"}, "reason": {"shape": "ValidationExceptionReason", "documentation": "<p>Reason the request failed validation.</p>"}}, "documentation": "<p>The input fails to satisfy the constraints specified by an AWS service.</p>", "error": {"httpStatusCode": 400, "senderFault": true}, "exception": true}, "ValidationExceptionField": {"type": "structure", "required": ["message", "name"], "members": {"message": {"shape": "String", "documentation": "<p>Message describing why the field failed validation.</p>"}, "name": {"shape": "String", "documentation": "<p>The field name.</p>"}}, "documentation": "<p>Stores information about a field passed inside a request that resulted in an exception.</p>"}, "ValidationExceptionFieldList": {"type": "list", "member": {"shape": "ValidationExceptionField"}, "documentation": "<p>A list of fields.</p>"}, "ValidationExceptionReason": {"type": "string", "documentation": "<p>Possible reasons a request failed validation.</p>", "enum": ["UNKNOWN_OPERATION", "CANNOT_PARSE", "FIELD_VALIDATION_FAILED", "OTHER"]}, "WorkspaceAlias": {"type": "string", "documentation": "<p>A user-assigned workspace alias.</p>", "max": 100, "min": 1}, "WorkspaceArn": {"type": "string", "documentation": "<p>An ARN identifying a Workspace.</p>"}, "WorkspaceDescription": {"type": "structure", "required": ["arn", "createdAt", "status", "workspaceId"], "members": {"alias": {"shape": "WorkspaceAlias", "documentation": "<p><PERSON>as of this workspace.</p>"}, "arn": {"shape": "WorkspaceArn", "documentation": "<p>The Amazon Resource Name (ARN) of this workspace.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the workspace was created.</p>"}, "prometheusEndpoint": {"shape": "<PERSON><PERSON>", "documentation": "<p>Prometheus endpoint URI.</p>"}, "status": {"shape": "WorkspaceStatus", "documentation": "<p>The status of this workspace.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of this workspace.</p>"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>Unique string identifying this workspace.</p>"}}, "documentation": "<p>Represents the properties of a workspace.</p>"}, "WorkspaceId": {"type": "string", "documentation": "<p>A workspace ID.</p>", "max": 64, "min": 1, "pattern": "[0-9A-Za-z][-.0-9A-Z_a-z]*"}, "WorkspaceStatus": {"type": "structure", "required": ["statusCode"], "members": {"statusCode": {"shape": "WorkspaceStatusCode", "documentation": "<p>Status code of this workspace.</p>"}}, "documentation": "<p>Represents the status of a workspace.</p>"}, "WorkspaceStatusCode": {"type": "string", "documentation": "<p>State of a workspace.</p>", "enum": ["CREATING", "ACTIVE", "UPDATING", "DELETING", "CREATION_FAILED"]}, "WorkspaceSummary": {"type": "structure", "required": ["arn", "createdAt", "status", "workspaceId"], "members": {"alias": {"shape": "WorkspaceAlias", "documentation": "<p><PERSON>as of this workspace.</p>"}, "arn": {"shape": "WorkspaceArn", "documentation": "<p>The AmazonResourceName of this workspace.</p>"}, "createdAt": {"shape": "Timestamp", "documentation": "<p>The time when the workspace was created.</p>"}, "status": {"shape": "WorkspaceStatus", "documentation": "<p>The status of this workspace.</p>"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags of this workspace.</p>"}, "workspaceId": {"shape": "WorkspaceId", "documentation": "<p>Unique string identifying this workspace.</p>"}}, "documentation": "<p>Represents a summary of the properties of a workspace.</p>"}, "WorkspaceSummaryList": {"type": "list", "member": {"shape": "WorkspaceSummary"}, "documentation": "<p>A list of workspace summaries.</p>"}}, "documentation": "<p>Amazon Managed Service for Prometheus</p>"}