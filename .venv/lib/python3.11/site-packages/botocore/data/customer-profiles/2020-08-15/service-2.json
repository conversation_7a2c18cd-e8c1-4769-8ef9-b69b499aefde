{"version": "2.0", "metadata": {"apiVersion": "2020-08-15", "endpointPrefix": "profile", "jsonVersion": "1.1", "protocol": "rest-json", "serviceAbbreviation": "Customer Profiles", "serviceFullName": "Amazon Connect Customer Profiles", "serviceId": "Customer Profiles", "signatureVersion": "v4", "signingName": "profile", "uid": "customer-profiles-2020-08-15"}, "operations": {"AddProfileKey": {"name": "AddProfileKey", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/profiles/keys"}, "input": {"shape": "AddProfileKeyRequest"}, "output": {"shape": "AddProfileKeyResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Associates a new key value with a specific profile, such as a Contact Record ContactId.</p> <p>A profile object can have a single unique key and any number of additional keys that can be used to identify the profile that it belongs to.</p>"}, "CreateDomain": {"name": "CreateDomain", "http": {"method": "POST", "requestUri": "/domains/{DomainName}"}, "input": {"shape": "CreateDomainRequest"}, "output": {"shape": "CreateDomainResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a domain, which is a container for all customer data, such as customer profile attributes, object types, profile keys, and encryption keys. You can create multiple domains, and each domain can have multiple third-party integrations.</p> <p>Each Amazon Connect instance can be associated with only one domain. Multiple Amazon Connect instances can be associated with one domain.</p> <p>Use this API or <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_UpdateDomain.html\">UpdateDomain</a> to enable <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_GetMatches.html\">identity resolution</a>: set <code>Matching</code> to true. </p> <p>To prevent cross-service impersonation when you call this API, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/cross-service-confused-deputy-prevention.html\">Cross-service confused deputy prevention</a> for sample policies that you should apply. </p>"}, "CreateIntegrationWorkflow": {"name": "CreateIntegrationWorkflow", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/workflows/integrations"}, "input": {"shape": "CreateIntegrationWorkflowRequest"}, "output": {"shape": "CreateIntegrationWorkflowResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p> Creates an integration workflow. An integration workflow is an async process which ingests historic data and sets up an integration for ongoing updates. The supported Amazon AppFlow sources are Salesforce, ServiceNow, and Marketo. </p>"}, "CreateProfile": {"name": "CreateProfile", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/profiles"}, "input": {"shape": "CreateProfileRequest"}, "output": {"shape": "CreateProfileResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Creates a standard profile.</p> <p>A standard profile represents the following attributes for a customer profile in a domain.</p>"}, "DeleteDomain": {"name": "DeleteDomain", "http": {"method": "DELETE", "requestUri": "/domains/{DomainName}"}, "input": {"shape": "DeleteDomainRequest"}, "output": {"shape": "DeleteDomainResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes a specific domain and all of its customer data, such as customer profile attributes and their related objects.</p>"}, "DeleteIntegration": {"name": "DeleteIntegration", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/integrations/delete"}, "input": {"shape": "DeleteIntegrationRequest"}, "output": {"shape": "DeleteIntegrationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes an integration from a specific domain.</p>"}, "DeleteProfile": {"name": "DeleteProfile", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/profiles/delete"}, "input": {"shape": "DeleteProfileRequest"}, "output": {"shape": "DeleteProfileResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes the standard customer profile and all data pertaining to the profile.</p>"}, "DeleteProfileKey": {"name": "DeleteProfileKey", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/profiles/keys/delete"}, "input": {"shape": "DeleteProfileKeyRequest"}, "output": {"shape": "DeleteProfileKeyResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes a searchable key from a customer profile.</p>"}, "DeleteProfileObject": {"name": "DeleteProfileObject", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/profiles/objects/delete"}, "input": {"shape": "DeleteProfileObjectRequest"}, "output": {"shape": "DeleteProfileObjectResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes an object associated with a profile of a given ProfileObjectType.</p>"}, "DeleteProfileObjectType": {"name": "DeleteProfileObjectType", "http": {"method": "DELETE", "requestUri": "/domains/{DomainName}/object-types/{ObjectTypeName}"}, "input": {"shape": "DeleteProfileObjectTypeRequest"}, "output": {"shape": "DeleteProfileObjectTypeResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Removes a ProfileObjectType from a specific domain as well as removes all the ProfileObjects of that type. It also disables integrations from this specific ProfileObjectType. In addition, it scrubs all of the fields of the standard profile that were populated from this ProfileObjectType.</p>"}, "DeleteWorkflow": {"name": "DeleteWorkflow", "http": {"method": "DELETE", "requestUri": "/domains/{DomainName}/workflows/{WorkflowId}"}, "input": {"shape": "DeleteWorkflowRequest"}, "output": {"shape": "DeleteWorkflowResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Deletes the specified workflow and all its corresponding resources. This is an async process.</p>"}, "GetAutoMergingPreview": {"name": "GetAutoMergingPreview", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/identity-resolution-jobs/auto-merging-preview"}, "input": {"shape": "GetAutoMergingPreviewRequest"}, "output": {"shape": "GetAutoMergingPreviewResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Tests the auto-merging settings of your Identity Resolution Job without merging your data. It randomly selects a sample of matching groups from the existing matching results, and applies the automerging settings that you provided. You can then view the number of profiles in the sample, the number of matches, and the number of profiles identified to be merged. This enables you to evaluate the accuracy of the attributes in your matching list. </p> <p>You can't view which profiles are matched and would be merged.</p> <important> <p>We strongly recommend you use this API to do a dry run of the automerging process before running the Identity Resolution Job. Include <b>at least</b> two matching attributes. If your matching list includes too few attributes (such as only <code>FirstName</code> or only <code>LastName</code>), there may be a large number of matches. This increases the chances of erroneous merges.</p> </important>"}, "GetDomain": {"name": "GetDomain", "http": {"method": "GET", "requestUri": "/domains/{DomainName}"}, "input": {"shape": "GetDomainRequest"}, "output": {"shape": "GetDomainResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about a specific domain.</p>"}, "GetIdentityResolutionJob": {"name": "GetIdentityResolutionJob", "http": {"method": "GET", "requestUri": "/domains/{DomainName}/identity-resolution-jobs/{JobId}"}, "input": {"shape": "GetIdentityResolutionJobRequest"}, "output": {"shape": "GetIdentityResolutionJobResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns information about an Identity Resolution Job in a specific domain. </p> <p>Identity Resolution Jobs are set up using the Amazon Connect admin console. For more information, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/use-identity-resolution.html\">Use Identity Resolution to consolidate similar profiles</a>.</p>"}, "GetIntegration": {"name": "GetIntegration", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/integrations"}, "input": {"shape": "GetIntegrationRequest"}, "output": {"shape": "GetIntegrationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns an integration for a domain.</p>"}, "GetMatches": {"name": "GetMatches", "http": {"method": "GET", "requestUri": "/domains/{DomainName}/matches"}, "input": {"shape": "GetMatchesRequest"}, "output": {"shape": "GetMatchesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Before calling this API, use <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_CreateDomain.html\">CreateDomain</a> or <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_UpdateDomain.html\">UpdateDomain</a> to enable identity resolution: set <code>Matching</code> to true.</p> <p>GetMatches returns potentially matching profiles, based on the results of the latest run of a machine learning process. </p> <important> <p>The process of matching duplicate profiles. If <code>Matching</code> = <code>true</code>, Amazon Connect Customer Profiles starts a weekly batch process called Identity Resolution Job. If you do not specify a date and time for Identity Resolution Job to run, by default it runs every Saturday at 12AM UTC to detect duplicate profiles in your domains. </p> <p>After the Identity Resolution Job completes, use the <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_GetMatches.html\">GetMatches</a> API to return and review the results. Or, if you have configured <code>ExportingConfig</code> in the <code>MatchingRequest</code>, you can download the results from S3.</p> </important> <p>Amazon Connect uses the following profile attributes to identify matches:</p> <ul> <li> <p>PhoneNumber</p> </li> <li> <p>HomePhoneNumber</p> </li> <li> <p>BusinessPhoneNumber</p> </li> <li> <p>MobilePhoneNumber</p> </li> <li> <p>EmailAddress</p> </li> <li> <p>PersonalEmailAddress</p> </li> <li> <p>BusinessEmailAddress</p> </li> <li> <p>FullName</p> </li> </ul> <p>For example, two or more profiles—with spelling mistakes such as <b>John Doe</b> and <b>Jhn Doe</b>, or different casing email addresses such as <b><EMAIL></b> and <b><EMAIL></b>, or different phone number formats such as <b>************</b> and <b>+1-************</b>—can be detected as belonging to the same customer <b>John Doe</b> and merged into a unified profile.</p>"}, "GetProfileObjectType": {"name": "GetProfileObjectType", "http": {"method": "GET", "requestUri": "/domains/{DomainName}/object-types/{ObjectTypeName}"}, "input": {"shape": "GetProfileObjectTypeRequest"}, "output": {"shape": "GetProfileObjectTypeResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns the object types for a specific domain.</p>"}, "GetProfileObjectTypeTemplate": {"name": "GetProfileObjectTypeTemplate", "http": {"method": "GET", "requestUri": "/templates/{TemplateId}"}, "input": {"shape": "GetProfileObjectTypeTemplateRequest"}, "output": {"shape": "GetProfileObjectTypeTemplateResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns the template information for a specific object type.</p> <p>A template is a predefined ProfileObjectType, such as “Salesforce-Account” or “Salesforce-Contact.” When a user sends a ProfileObject, using the PutProfileObject API, with an ObjectTypeName that matches one of the TemplateIds, it uses the mappings from the template.</p>"}, "GetWorkflow": {"name": "GetWorkflow", "http": {"method": "GET", "requestUri": "/domains/{DomainName}/workflows/{WorkflowId}"}, "input": {"shape": "GetWorkflowRequest"}, "output": {"shape": "GetWorkflowResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get details of specified workflow.</p>"}, "GetWorkflowSteps": {"name": "GetWorkflowSteps", "http": {"method": "GET", "requestUri": "/domains/{DomainName}/workflows/{WorkflowId}/steps"}, "input": {"shape": "GetWorkflowStepsRequest"}, "output": {"shape": "GetWorkflowStepsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Get granular list of steps in workflow.</p>"}, "ListAccountIntegrations": {"name": "ListAccountIntegrations", "http": {"method": "POST", "requestUri": "/integrations"}, "input": {"shape": "ListAccountIntegrationsRequest"}, "output": {"shape": "ListAccountIntegrationsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all of the integrations associated to a specific URI in the AWS account.</p>"}, "ListDomains": {"name": "ListDomains", "http": {"method": "GET", "requestUri": "/domains"}, "input": {"shape": "ListDomainsRequest"}, "output": {"shape": "ListDomainsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a list of all the domains for an AWS account that have been created.</p>"}, "ListIdentityResolutionJobs": {"name": "ListIdentityResolutionJobs", "http": {"method": "GET", "requestUri": "/domains/{DomainName}/identity-resolution-jobs"}, "input": {"shape": "ListIdentityResolutionJobsRequest"}, "output": {"shape": "ListIdentityResolutionJobsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all of the Identity Resolution Jobs in your domain. The response sorts the list by <code>JobStartTime</code>.</p>"}, "ListIntegrations": {"name": "ListIntegrations", "http": {"method": "GET", "requestUri": "/domains/{DomainName}/integrations"}, "input": {"shape": "ListIntegrationsRequest"}, "output": {"shape": "ListIntegrationsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all of the integrations in your domain.</p>"}, "ListProfileObjectTypeTemplates": {"name": "ListProfileObjectTypeTemplates", "http": {"method": "GET", "requestUri": "/templates"}, "input": {"shape": "ListProfileObjectTypeTemplatesRequest"}, "output": {"shape": "ListProfileObjectTypeTemplatesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all of the template information for object types.</p>"}, "ListProfileObjectTypes": {"name": "ListProfileObjectTypes", "http": {"method": "GET", "requestUri": "/domains/{DomainName}/object-types"}, "input": {"shape": "ListProfileObjectTypesRequest"}, "output": {"shape": "ListProfileObjectTypesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Lists all of the templates available within the service.</p>"}, "ListProfileObjects": {"name": "ListProfileObjects", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/profiles/objects"}, "input": {"shape": "ListProfileObjectsRequest"}, "output": {"shape": "ListProfileObjectsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Returns a list of objects associated with a profile of a given ProfileObjectType.</p>"}, "ListTagsForResource": {"name": "ListTagsForResource", "http": {"method": "GET", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "ListTagsForResourceRequest"}, "output": {"shape": "ListTagsForResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Displays the tags associated with an Amazon Connect Customer Profiles resource. In Connect Customer Profiles, domains, profile object types, and integrations can be tagged.</p>"}, "ListWorkflows": {"name": "ListWorkflows", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/workflows"}, "input": {"shape": "ListWorkflowsRequest"}, "output": {"shape": "ListWorkflowsResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Query to list all workflows.</p>"}, "MergeProfiles": {"name": "MergeProfiles", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/profiles/objects/merge"}, "input": {"shape": "MergeProfilesRequest"}, "output": {"shape": "MergeProfilesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Runs an AWS Lambda job that does the following:</p> <ol> <li> <p>All the profileKeys in the <code>ProfileToBeMerged</code> will be moved to the main profile.</p> </li> <li> <p>All the objects in the <code>ProfileToBeMerged</code> will be moved to the main profile.</p> </li> <li> <p>All the <code>ProfileToBeMerged</code> will be deleted at the end.</p> </li> <li> <p>All the profileKeys in the <code>ProfileIdsToBeMerged</code> will be moved to the main profile.</p> </li> <li> <p>Standard fields are merged as follows:</p> <ol> <li> <p>Fields are always \"union\"-ed if there are no conflicts in standard fields or attributeKeys.</p> </li> <li> <p>When there are conflicting fields:</p> <ol> <li> <p>If no <code>SourceProfileIds</code> entry is specified, the main Profile value is always taken. </p> </li> <li> <p>If a <code>SourceProfileIds</code> entry is specified, the specified profileId is always taken, even if it is a NULL value.</p> </li> </ol> </li> </ol> </li> </ol> <p>You can use MergeProfiles together with <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_GetMatches.html\">GetMatches</a>, which returns potentially matching profiles, or use it with the results of another matching system. After profiles have been merged, they cannot be separated (unmerged).</p>"}, "PutIntegration": {"name": "PutIntegration", "http": {"method": "PUT", "requestUri": "/domains/{DomainName}/integrations"}, "input": {"shape": "PutIntegrationRequest"}, "output": {"shape": "PutIntegrationResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Adds an integration between the service and a third-party service, which includes Amazon AppFlow and Amazon Connect.</p> <p>An integration can belong to only one domain.</p> <p>To add or remove tags on an existing Integration, see <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_TagResource.html\"> TagResource </a>/<a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_UntagResource.html\"> UntagResource</a>.</p>"}, "PutProfileObject": {"name": "PutProfileObject", "http": {"method": "PUT", "requestUri": "/domains/{DomainName}/profiles/objects"}, "input": {"shape": "PutProfileObjectRequest"}, "output": {"shape": "PutProfileObjectResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Adds additional objects to customer profiles of a given ObjectType.</p> <p>When adding a specific profile object, like a Contact Record, an inferred profile can get created if it is not mapped to an existing profile. The resulting profile will only have a phone number populated in the standard ProfileObject. Any additional Contact Records with the same phone number will be mapped to the same inferred profile.</p> <p>When a ProfileObject is created and if a ProfileObjectType already exists for the ProfileObject, it will provide data to a standard profile depending on the ProfileObjectType definition.</p> <p>PutProfileObject needs an ObjectType, which can be created using PutProfileObjectType.</p>"}, "PutProfileObjectType": {"name": "PutProfileObjectType", "http": {"method": "PUT", "requestUri": "/domains/{DomainName}/object-types/{ObjectTypeName}"}, "input": {"shape": "PutProfileObjectTypeRequest"}, "output": {"shape": "PutProfileObjectTypeResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Defines a ProfileObjectType.</p> <p>To add or remove tags on an existing ObjectType, see <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_TagResource.html\"> TagResource</a>/<a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_UntagResource.html\">UntagResource</a>.</p>"}, "SearchProfiles": {"name": "SearchProfiles", "http": {"method": "POST", "requestUri": "/domains/{DomainName}/profiles/search"}, "input": {"shape": "SearchProfilesRequest"}, "output": {"shape": "SearchProfilesResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Searches for profiles within a specific domain using one or more predefined search keys (e.g., _fullName, _phone, _email, _account, etc.) and/or custom-defined search keys. A search key is a data type pair that consists of a <code>KeyName</code> and <code>Values</code> list.</p> <p>This operation supports searching for profiles with a minimum of 1 key-value(s) pair and up to 5 key-value(s) pairs using either <code>AND</code> or <code>OR</code> logic.</p>"}, "TagResource": {"name": "TagResource", "http": {"method": "POST", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "TagResourceRequest"}, "output": {"shape": "TagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Assigns one or more tags (key-value pairs) to the specified Amazon Connect Customer Profiles resource. Tags can help you organize and categorize your resources. You can also use them to scope user permissions by granting a user permission to access or change only resources with certain tag values. In Connect Customer Profiles, domains, profile object types, and integrations can be tagged.</p> <p>Tags don't have any semantic meaning to AWS and are interpreted strictly as strings of characters.</p> <p>You can use the TagResource action with a resource that already has tags. If you specify a new tag key, this tag is appended to the list of tags associated with the resource. If you specify a tag key that is already associated with the resource, the new tag value that you specify replaces the previous value for that tag.</p> <p>You can associate as many as 50 tags with a resource.</p>"}, "UntagResource": {"name": "UntagResource", "http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}"}, "input": {"shape": "UntagResourceRequest"}, "output": {"shape": "UntagResourceResponse"}, "errors": [{"shape": "InternalServerException"}, {"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}], "documentation": "<p>Removes one or more tags from the specified Amazon Connect Customer Profiles resource. In Connect Customer Profiles, domains, profile object types, and integrations can be tagged.</p>"}, "UpdateDomain": {"name": "UpdateDomain", "http": {"method": "PUT", "requestUri": "/domains/{DomainName}"}, "input": {"shape": "UpdateDomainRequest"}, "output": {"shape": "UpdateDomainResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "ResourceNotFoundException"}, {"shape": "AccessDeniedException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the properties of a domain, including creating or selecting a dead letter queue or an encryption key.</p> <p>After a domain is created, the name can’t be changed.</p> <p>Use this API or <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_CreateDomain.html\">CreateDomain</a> to enable <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_GetMatches.html\">identity resolution</a>: set <code>Matching</code> to true. </p> <p>To prevent cross-service impersonation when you call this API, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/cross-service-confused-deputy-prevention.html\">Cross-service confused deputy prevention</a> for sample policies that you should apply. </p> <p>To add or remove tags on an existing Domain, see <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_TagResource.html\">TagResource</a>/<a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_UntagResource.html\">UntagResource</a>.</p>"}, "UpdateProfile": {"name": "UpdateProfile", "http": {"method": "PUT", "requestUri": "/domains/{DomainName}/profiles"}, "input": {"shape": "UpdateProfileRequest"}, "output": {"shape": "UpdateProfileResponse"}, "errors": [{"shape": "BadRequestException"}, {"shape": "AccessDeniedException"}, {"shape": "ResourceNotFoundException"}, {"shape": "ThrottlingException"}, {"shape": "InternalServerException"}], "documentation": "<p>Updates the properties of a profile. The ProfileId is required for updating a customer profile.</p> <p>When calling the UpdateProfile API, specifying an empty string value means that any existing value will be removed. Not specifying a string value means that any value already there will be kept.</p>"}}, "shapes": {"name": {"type": "string", "max": 64, "min": 1, "pattern": "^[a-zA-Z0-9_-]+$"}, "AccessDeniedException": {"type": "structure", "members": {"Message": {"shape": "message"}}, "documentation": "<p>You do not have sufficient access to perform this action.</p>", "error": {"httpStatusCode": 403}, "exception": true}, "AddProfileKeyRequest": {"type": "structure", "required": ["ProfileId", "KeyName", "Values", "DomainName"], "members": {"ProfileId": {"shape": "uuid", "documentation": "<p>The unique identifier of a customer profile.</p>"}, "KeyName": {"shape": "name", "documentation": "<p>A searchable identifier of a customer profile. The predefined keys you can use include: _account, _profileId, _assetId, _caseId, _orderId, _fullName, _phone, _email, _ctrContactId, _marketoLeadId, _salesforceAccountId, _salesforceContactId, _salesforceAssetId, _zendeskUserId, _zendeskExternalId, _zendeskTicketId, _serviceNowSystemId, _serviceNowIncidentId, _segmentUserId, _shopifyCustomerId, _shopifyOrderId.</p>"}, "Values": {"shape": "requestValueList", "documentation": "<p>A list of key values.</p>"}, "DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}}}, "AddProfileKeyResponse": {"type": "structure", "members": {"KeyName": {"shape": "name", "documentation": "<p>A searchable identifier of a customer profile.</p>"}, "Values": {"shape": "requestValueList", "documentation": "<p>A list of key values.</p>"}}}, "AdditionalSearchKey": {"type": "structure", "required": ["KeyName", "Values"], "members": {"KeyName": {"shape": "name", "documentation": "<p>A searchable identifier of a customer profile.</p>"}, "Values": {"shape": "requestValueList", "documentation": "<p>A list of key values.</p>"}}, "documentation": "<p>A data type pair that consists of a <code>KeyName</code> and <code>Values</code> list that is used in conjunction with the <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_SearchProfiles.html#customerprofiles-SearchProfiles-request-KeyName\">KeyName</a> and <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_SearchProfiles.html#customerprofiles-SearchProfiles-request-Values\">Values</a> parameters to search for profiles using the <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_SearchProfiles.html\">SearchProfiles</a> API.</p>"}, "Address": {"type": "structure", "members": {"Address1": {"shape": "string1To255", "documentation": "<p>The first line of a customer address.</p>"}, "Address2": {"shape": "string1To255", "documentation": "<p>The second line of a customer address.</p>"}, "Address3": {"shape": "string1To255", "documentation": "<p>The third line of a customer address.</p>"}, "Address4": {"shape": "string1To255", "documentation": "<p>The fourth line of a customer address.</p>"}, "City": {"shape": "string1To255", "documentation": "<p>The city in which a customer lives.</p>"}, "County": {"shape": "string1To255", "documentation": "<p>The county in which a customer lives.</p>"}, "State": {"shape": "string1To255", "documentation": "<p>The state in which a customer lives.</p>"}, "Province": {"shape": "string1To255", "documentation": "<p>The province in which a customer lives.</p>"}, "Country": {"shape": "string1To255", "documentation": "<p>The country in which a customer lives.</p>"}, "PostalCode": {"shape": "string1To255", "documentation": "<p>The postal code of a customer address.</p>"}}, "documentation": "<p>A generic address associated with the customer that is not mailing, shipping, or billing.</p>"}, "AppflowIntegration": {"type": "structure", "required": ["FlowDefinition"], "members": {"FlowDefinition": {"shape": "FlowDefinition"}, "Batches": {"shape": "Batches", "documentation": "<p>Batches in workflow of type <code>APPFLOW_INTEGRATION</code>.</p>"}}, "documentation": "<p>Details for workflow of type <code>APPFLOW_INTEGRATION</code>.</p>"}, "AppflowIntegrationWorkflowAttributes": {"type": "structure", "required": ["SourceConnectorType", "ConnectorProfileName"], "members": {"SourceConnectorType": {"shape": "SourceConnectorType", "documentation": "<p>Specifies the source connector type, such as Salesforce, ServiceNow, and Marketo. Indicates source of ingestion.</p>"}, "ConnectorProfileName": {"shape": "ConnectorProfileName", "documentation": "<p>The name of the AppFlow connector profile used for ingestion.</p>"}, "RoleArn": {"shape": "string1To255", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role. Customer Profiles assumes this role to create resources on your behalf as part of workflow execution.</p>"}}, "documentation": "<p>Structure holding all <code>APPFLOW_INTEGRATION</code> specific workflow attributes.</p>"}, "AppflowIntegrationWorkflowMetrics": {"type": "structure", "required": ["RecordsProcessed", "StepsCompleted", "TotalSteps"], "members": {"RecordsProcessed": {"shape": "long", "documentation": "<p>Number of records processed in <code>APPFLOW_INTEGRATION</code> workflow.</p>"}, "StepsCompleted": {"shape": "long", "documentation": "<p>Total steps completed in <code>APPFLOW_INTEGRATION</code> workflow.</p>"}, "TotalSteps": {"shape": "long", "documentation": "<p>Total steps in <code>APPFLOW_INTEGRATION</code> workflow.</p>"}}, "documentation": "<p>Workflow specific execution metrics for <code>APPFLOW_INTEGRATION</code> workflow.</p>"}, "AppflowIntegrationWorkflowStep": {"type": "structure", "required": ["FlowName", "Status", "ExecutionMessage", "RecordsProcessed", "BatchRecordsStartTime", "BatchRecordsEndTime", "CreatedAt", "LastUpdatedAt"], "members": {"FlowName": {"shape": "FlowName", "documentation": "<p>Name of the flow created during execution of workflow step. <code>APPFLOW_INTEGRATION</code> workflow type creates an appflow flow during workflow step execution on the customers behalf.</p>"}, "Status": {"shape": "Status", "documentation": "<p>Workflow step status for <code>APPFLOW_INTEGRATION</code> workflow.</p>"}, "ExecutionMessage": {"shape": "string1To255", "documentation": "<p>Message indicating execution of workflow step for <code>APPFLOW_INTEGRATION</code> workflow.</p>"}, "RecordsProcessed": {"shape": "long", "documentation": "<p>Total number of records processed during execution of workflow step for <code>APPFLOW_INTEGRATION</code> workflow.</p>"}, "BatchRecordsStartTime": {"shape": "string1To255", "documentation": "<p>Start datetime of records pulled in batch during execution of workflow step for <code>APPFLOW_INTEGRATION</code> workflow.</p>"}, "BatchRecordsEndTime": {"shape": "string1To255", "documentation": "<p>End datetime of records pulled in batch during execution of workflow step for <code>APPFLOW_INTEGRATION</code> workflow.</p>"}, "CreatedAt": {"shape": "timestamp", "documentation": "<p>Creation timestamp of workflow step for <code>APPFLOW_INTEGRATION</code> workflow.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>Last updated timestamp for workflow step for <code>APPFLOW_INTEGRATION</code> workflow.</p>"}}, "documentation": "<p>Workflow step details for <code>APPFLOW_INTEGRATION</code> workflow.</p>"}, "AttributeSourceIdMap": {"type": "map", "key": {"shape": "string1To255"}, "value": {"shape": "uuid"}}, "Attributes": {"type": "map", "key": {"shape": "string1To255"}, "value": {"shape": "string1To255"}}, "AutoMerging": {"type": "structure", "required": ["Enabled"], "members": {"Enabled": {"shape": "optionalBoolean", "documentation": "<p>The flag that enables the auto-merging of duplicate profiles.</p>"}, "Consolidation": {"shape": "Consolidation", "documentation": "<p>A list of matching attributes that represent matching criteria. If two profiles meet at least one of the requirements in the matching attributes list, they will be merged.</p>"}, "ConflictResolution": {"shape": "ConflictResolution", "documentation": "<p>How the auto-merging process should resolve conflicts between different profiles. For example, if Profile A and Profile B have the same <code>FirstName</code> and <code>LastName</code> (and that is the matching criteria), which <code>EmailAddress</code> should be used? </p>"}, "MinAllowedConfidenceScoreForMerging": {"shape": "Double0To1", "documentation": "<p>A number between 0 and 1 that represents the minimum confidence score required for profiles within a matching group to be merged during the auto-merge process. A higher score means higher similarity required to merge profiles. </p>"}}, "documentation": "<p>Configuration settings for how to perform the auto-merging of profiles.</p>"}, "BadRequestException": {"type": "structure", "members": {"Message": {"shape": "message"}}, "documentation": "<p>The input you provided is invalid.</p>", "error": {"httpStatusCode": 400}, "exception": true}, "Batch": {"type": "structure", "required": ["StartTime", "EndTime"], "members": {"StartTime": {"shape": "timestamp", "documentation": "<p>Start time of batch to split ingestion.</p>"}, "EndTime": {"shape": "timestamp", "documentation": "<p>End time of batch to split ingestion.</p>"}}, "documentation": "<p><PERSON><PERSON> defines the boundaries for ingestion for each step in <code>APPFLOW_INTEGRATION</code> workflow. <code>APPFLOW_INTEGRATION</code> workflow splits ingestion based on these boundaries.</p>"}, "Batches": {"type": "list", "member": {"shape": "<PERSON><PERSON>"}}, "BucketName": {"type": "string", "max": 63, "min": 3, "pattern": "\\S+"}, "BucketPrefix": {"type": "string", "max": 512, "pattern": ".*"}, "ConflictResolution": {"type": "structure", "required": ["ConflictResolvingModel"], "members": {"ConflictResolvingModel": {"shape": "ConflictResolvingModel", "documentation": "<p>How the auto-merging process should resolve conflicts between different profiles.</p> <ul> <li> <p> <code>RECENCY</code>: Uses the data that was most recently updated.</p> </li> <li> <p> <code>SOURCE</code>: Uses the data from a specific source. For example, if a company has been aquired or two departments have merged, data from the specified source is used. If two duplicate profiles are from the same source, then <code>RECENCY</code> is used again.</p> </li> </ul>"}, "SourceName": {"shape": "string1To255", "documentation": "<p>The <code>ObjectType</code> name that is used to resolve profile merging conflicts when choosing <code>SOURCE</code> as the <code>ConflictResolvingModel</code>.</p>"}}, "documentation": "<p>How the auto-merging process should resolve conflicts between different profiles.</p>"}, "ConflictResolvingModel": {"type": "string", "enum": ["RECENCY", "SOURCE"]}, "ConnectorOperator": {"type": "structure", "members": {"Marketo": {"shape": "MarketoConnectorOperator", "documentation": "<p>The operation to be performed on the provided Marketo source fields.</p>"}, "S3": {"shape": "S3ConnectorOperator", "documentation": "<p>The operation to be performed on the provided Amazon S3 source fields.</p>"}, "Salesforce": {"shape": "SalesforceConnectorOperator", "documentation": "<p>The operation to be performed on the provided Salesforce source fields.</p>"}, "ServiceNow": {"shape": "ServiceNowConnectorOperator", "documentation": "<p>The operation to be performed on the provided ServiceNow source fields.</p>"}, "Zendesk": {"shape": "ZendeskConnectorOperator", "documentation": "<p>The operation to be performed on the provided Zendesk source fields.</p>"}}, "documentation": "<p>The operation to be performed on the provided source fields.</p>"}, "ConnectorProfileName": {"type": "string", "max": 256, "pattern": "[\\w/!@#+=.-]+"}, "Consolidation": {"type": "structure", "required": ["MatchingAttributesList"], "members": {"MatchingAttributesList": {"shape": "MatchingAttributesList", "documentation": "<p>A list of matching criteria.</p>"}}, "documentation": "<p>The matching criteria to be used during the auto-merging process. </p>"}, "CreateDomainRequest": {"type": "structure", "required": ["DomainName", "DefaultExpirationDays"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "DefaultExpirationDays": {"shape": "expirationDaysInteger", "documentation": "<p>The default number of days until the data within the domain expires.</p>"}, "DefaultEncryptionKey": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The default encryption key, which is an AWS managed key, is used when no specific type of encryption key is specified. It is used to encrypt all data before it is placed in permanent or semi-permanent storage.</p>"}, "DeadLetterQueueUrl": {"shape": "sqsQueueUrl", "documentation": "<p>The URL of the SQS dead letter queue, which is used for reporting errors associated with ingesting data from third party applications. You must set up a policy on the DeadLetterQueue for the SendMessage operation to enable Amazon Connect Customer Profiles to send messages to the DeadLetterQueue.</p>"}, "Matching": {"shape": "MatchingRequest", "documentation": "<p>The process of matching duplicate profiles. If <code>Matching</code> = <code>true</code>, Amazon Connect Customer Profiles starts a weekly batch process called Identity Resolution Job. If you do not specify a date and time for Identity Resolution Job to run, by default it runs every Saturday at 12AM UTC to detect duplicate profiles in your domains. </p> <p>After the Identity Resolution Job completes, use the <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_GetMatches.html\">GetMatches</a> API to return and review the results. Or, if you have configured <code>ExportingConfig</code> in the <code>MatchingRequest</code>, you can download the results from S3.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "CreateDomainResponse": {"type": "structure", "required": ["DomainName", "DefaultExpirationDays", "CreatedAt", "LastUpdatedAt"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>"}, "DefaultExpirationDays": {"shape": "expirationDaysInteger", "documentation": "<p>The default number of days until the data within the domain expires.</p>"}, "DefaultEncryptionKey": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The default encryption key, which is an AWS managed key, is used when no specific type of encryption key is specified. It is used to encrypt all data before it is placed in permanent or semi-permanent storage.</p>"}, "DeadLetterQueueUrl": {"shape": "sqsQueueUrl", "documentation": "<p>The URL of the SQS dead letter queue, which is used for reporting errors associated with ingesting data from third party applications.</p>"}, "Matching": {"shape": "MatchingResponse", "documentation": "<p>The process of matching duplicate profiles. If <code>Matching</code> = <code>true</code>, Amazon Connect Customer Profiles starts a weekly batch process called Identity Resolution Job. If you do not specify a date and time for Identity Resolution Job to run, by default it runs every Saturday at 12AM UTC to detect duplicate profiles in your domains. </p> <p>After the Identity Resolution Job completes, use the <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_GetMatches.html\">GetMatches</a> API to return and review the results. Or, if you have configured <code>ExportingConfig</code> in the <code>MatchingRequest</code>, you can download the results from S3.</p>"}, "CreatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was created.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was most recently edited.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "CreateIntegrationWorkflowRequest": {"type": "structure", "required": ["DomainName", "WorkflowType", "IntegrationConfig", "ObjectTypeName", "RoleArn"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "WorkflowType": {"shape": "WorkflowType", "documentation": "<p>The type of workflow. The only supported value is APPFLOW_INTEGRATION.</p>"}, "IntegrationConfig": {"shape": "IntegrationConfig", "documentation": "<p>Configuration data for integration workflow.</p>"}, "ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>"}, "RoleArn": {"shape": "RoleArn", "documentation": "<p>The Amazon Resource Name (ARN) of the IAM role. Customer Profiles assumes this role to create resources on your behalf as part of workflow execution.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "CreateIntegrationWorkflowResponse": {"type": "structure", "required": ["WorkflowId", "Message"], "members": {"WorkflowId": {"shape": "uuid", "documentation": "<p>Unique identifier for the workflow.</p>"}, "Message": {"shape": "string1To255", "documentation": "<p>A message indicating create request was received.</p>"}}}, "CreateProfileRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "AccountNumber": {"shape": "string1To255", "documentation": "<p>A unique account number that you have given to the customer.</p>"}, "AdditionalInformation": {"shape": "string1To1000", "documentation": "<p>Any additional information relevant to the customer’s profile.</p>"}, "PartyType": {"shape": "PartyType", "documentation": "<p>The type of profile used to describe the customer.</p>"}, "BusinessName": {"shape": "string1To255", "documentation": "<p>The name of the customer’s business.</p>"}, "FirstName": {"shape": "string1To255", "documentation": "<p>The customer’s first name.</p>"}, "MiddleName": {"shape": "string1To255", "documentation": "<p>The customer’s middle name.</p>"}, "LastName": {"shape": "string1To255", "documentation": "<p>The customer’s last name.</p>"}, "BirthDate": {"shape": "string1To255", "documentation": "<p>The customer’s birth date. </p>"}, "Gender": {"shape": "Gender", "documentation": "<p>The gender with which the customer identifies. </p>"}, "PhoneNumber": {"shape": "string1To255", "documentation": "<p>The customer’s phone number, which has not been specified as a mobile, home, or business number. </p>"}, "MobilePhoneNumber": {"shape": "string1To255", "documentation": "<p>The customer’s mobile phone number.</p>"}, "HomePhoneNumber": {"shape": "string1To255", "documentation": "<p>The customer’s home phone number.</p>"}, "BusinessPhoneNumber": {"shape": "string1To255", "documentation": "<p>The customer’s business phone number.</p>"}, "EmailAddress": {"shape": "string1To255", "documentation": "<p>The customer’s email address, which has not been specified as a personal or business address. </p>"}, "PersonalEmailAddress": {"shape": "string1To255", "documentation": "<p>The customer’s personal email address.</p>"}, "BusinessEmailAddress": {"shape": "string1To255", "documentation": "<p>The customer’s business email address.</p>"}, "Address": {"shape": "Address", "documentation": "<p>A generic address associated with the customer that is not mailing, shipping, or billing.</p>"}, "ShippingAddress": {"shape": "Address", "documentation": "<p>The customer’s shipping address.</p>"}, "MailingAddress": {"shape": "Address", "documentation": "<p>The customer’s mailing address.</p>"}, "BillingAddress": {"shape": "Address", "documentation": "<p>The customer’s billing address.</p>"}, "Attributes": {"shape": "Attributes", "documentation": "<p>A key value pair of attributes of a customer profile.</p>"}, "PartyTypeString": {"shape": "string1To255", "documentation": "<p>An alternative to <code>PartyType</code> which accepts any string as input.</p>"}, "GenderString": {"shape": "string1To255", "documentation": "<p>An alternative to <code>Gender</code> which accepts any string as input.</p>"}}}, "CreateProfileResponse": {"type": "structure", "required": ["ProfileId"], "members": {"ProfileId": {"shape": "uuid", "documentation": "<p>The unique identifier of a customer profile.</p>"}}}, "DataPullMode": {"type": "string", "enum": ["Incremental", "Complete"]}, "Date": {"type": "timestamp"}, "DatetimeTypeFieldName": {"type": "string", "max": 256, "pattern": ".*"}, "DeleteDomainRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}}}, "DeleteDomainResponse": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "message", "documentation": "<p>A message that indicates the delete request is done.</p>"}}}, "DeleteIntegrationRequest": {"type": "structure", "required": ["DomainName", "<PERSON><PERSON>"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "Uri": {"shape": "string1To255", "documentation": "<p>The URI of the S3 bucket or any other type of data source.</p>"}}}, "DeleteIntegrationResponse": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "message", "documentation": "<p>A message that indicates the delete request is done.</p>"}}}, "DeleteProfileKeyRequest": {"type": "structure", "required": ["ProfileId", "KeyName", "Values", "DomainName"], "members": {"ProfileId": {"shape": "uuid", "documentation": "<p>The unique identifier of a customer profile.</p>"}, "KeyName": {"shape": "name", "documentation": "<p>A searchable identifier of a customer profile.</p>"}, "Values": {"shape": "requestValueList", "documentation": "<p>A list of key values.</p>"}, "DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}}}, "DeleteProfileKeyResponse": {"type": "structure", "members": {"Message": {"shape": "message", "documentation": "<p>A message that indicates the delete request is done.</p>"}}}, "DeleteProfileObjectRequest": {"type": "structure", "required": ["ProfileId", "ProfileObjectUniqueKey", "ObjectTypeName", "DomainName"], "members": {"ProfileId": {"shape": "uuid", "documentation": "<p>The unique identifier of a customer profile.</p>"}, "ProfileObjectUniqueKey": {"shape": "string1To255", "documentation": "<p>The unique identifier of the profile object generated by the service.</p>"}, "ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>"}, "DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}}}, "DeleteProfileObjectResponse": {"type": "structure", "members": {"Message": {"shape": "message", "documentation": "<p>A message that indicates the delete request is done.</p>"}}}, "DeleteProfileObjectTypeRequest": {"type": "structure", "required": ["DomainName", "ObjectTypeName"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>", "location": "uri", "locationName": "ObjectTypeName"}}}, "DeleteProfileObjectTypeResponse": {"type": "structure", "required": ["Message"], "members": {"Message": {"shape": "message", "documentation": "<p>A message that indicates the delete request is done.</p>"}}}, "DeleteProfileRequest": {"type": "structure", "required": ["ProfileId", "DomainName"], "members": {"ProfileId": {"shape": "uuid", "documentation": "<p>The unique identifier of a customer profile.</p>"}, "DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}}}, "DeleteProfileResponse": {"type": "structure", "members": {"Message": {"shape": "message", "documentation": "<p>A message that indicates the delete request is done.</p>"}}}, "DeleteWorkflowRequest": {"type": "structure", "required": ["DomainName", "WorkflowId"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "WorkflowId": {"shape": "string1To255", "documentation": "<p>Unique identifier for the workflow.</p>", "location": "uri", "locationName": "WorkflowId"}}}, "DeleteWorkflowResponse": {"type": "structure", "members": {}}, "DestinationField": {"type": "string", "max": 256, "pattern": ".*"}, "DomainList": {"type": "list", "member": {"shape": "ListDomainItem"}}, "DomainStats": {"type": "structure", "members": {"ProfileCount": {"shape": "long", "documentation": "<p>The total number of profiles currently in the domain.</p>"}, "MeteringProfileCount": {"shape": "long", "documentation": "<p>The number of profiles that you are currently paying for in the domain. If you have more than 100 objects associated with a single profile, that profile counts as two profiles. If you have more than 200 objects, that profile counts as three, and so on.</p>"}, "ObjectCount": {"shape": "long", "documentation": "<p>The total number of objects in domain.</p>"}, "TotalSize": {"shape": "long", "documentation": "<p>The total size, in bytes, of all objects in the domain.</p>"}}, "documentation": "<p>Usage-specific statistics about the domain.</p>"}, "Double": {"type": "double"}, "Double0To1": {"type": "double", "max": 1.0, "min": 0.0}, "ExportingConfig": {"type": "structure", "members": {"S3Exporting": {"shape": "S3ExportingConfig", "documentation": "<p>The S3 location where Identity Resolution Jobs write result files.</p>"}}, "documentation": "<p>Configuration information about the S3 bucket where Identity Resolution Jobs writes result files. </p> <note> <p>You need to give Customer Profiles service principal write permission to your S3 bucket. Otherwise, you'll get an exception in the API response. For an example policy, see <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/cross-service-confused-deputy-prevention.html#customer-profiles-cross-service\">Amazon Connect Customer Profiles cross-service confused deputy prevention</a>. </p> </note>"}, "ExportingLocation": {"type": "structure", "members": {"S3Exporting": {"shape": "S3ExportingLocation", "documentation": "<p>Information about the S3 location where Identity Resolution Jobs write result files.</p>"}}, "documentation": "<p>The S3 location where Identity Resolution Jobs write result files.</p>"}, "FieldContentType": {"type": "string", "enum": ["STRING", "NUMBER", "PHONE_NUMBER", "EMAIL_ADDRESS", "NAME"]}, "FieldMap": {"type": "map", "key": {"shape": "name"}, "value": {"shape": "ObjectTypeField"}}, "FieldNameList": {"type": "list", "member": {"shape": "name"}}, "FieldSourceProfileIds": {"type": "structure", "members": {"AccountNumber": {"shape": "uuid", "documentation": "<p>A unique identifier for the account number field to be merged. </p>"}, "AdditionalInformation": {"shape": "uuid", "documentation": "<p>A unique identifier for the additional information field to be merged.</p>"}, "PartyType": {"shape": "uuid", "documentation": "<p>A unique identifier for the party type field to be merged.</p>"}, "BusinessName": {"shape": "uuid", "documentation": "<p>A unique identifier for the business name field to be merged.</p>"}, "FirstName": {"shape": "uuid", "documentation": "<p>A unique identifier for the first name field to be merged.</p>"}, "MiddleName": {"shape": "uuid", "documentation": "<p>A unique identifier for the middle name field to be merged.</p>"}, "LastName": {"shape": "uuid", "documentation": "<p>A unique identifier for the last name field to be merged.</p>"}, "BirthDate": {"shape": "uuid", "documentation": "<p>A unique identifier for the birthdate field to be merged.</p>"}, "Gender": {"shape": "uuid", "documentation": "<p>A unique identifier for the gender field to be merged.</p>"}, "PhoneNumber": {"shape": "uuid", "documentation": "<p>A unique identifier for the phone number field to be merged.</p>"}, "MobilePhoneNumber": {"shape": "uuid", "documentation": "<p>A unique identifier for the mobile phone number field to be merged.</p>"}, "HomePhoneNumber": {"shape": "uuid", "documentation": "<p>A unique identifier for the home phone number field to be merged.</p>"}, "BusinessPhoneNumber": {"shape": "uuid", "documentation": "<p>A unique identifier for the business phone number field to be merged.</p>"}, "EmailAddress": {"shape": "uuid", "documentation": "<p>A unique identifier for the email address field to be merged.</p>"}, "PersonalEmailAddress": {"shape": "uuid", "documentation": "<p>A unique identifier for the personal email address field to be merged.</p>"}, "BusinessEmailAddress": {"shape": "uuid", "documentation": "<p>A unique identifier for the party type field to be merged.</p>"}, "Address": {"shape": "uuid", "documentation": "<p>A unique identifier for the party type field to be merged.</p>"}, "ShippingAddress": {"shape": "uuid", "documentation": "<p>A unique identifier for the shipping address field to be merged.</p>"}, "MailingAddress": {"shape": "uuid", "documentation": "<p>A unique identifier for the mailing address field to be merged.</p>"}, "BillingAddress": {"shape": "uuid", "documentation": "<p>A unique identifier for the billing type field to be merged.</p>"}, "Attributes": {"shape": "AttributeSourceIdMap", "documentation": "<p>A unique identifier for the attributes field to be merged.</p>"}}, "documentation": "<p>A duplicate customer profile that is to be merged into a main profile. </p>"}, "FlowDefinition": {"type": "structure", "required": ["FlowName", "KmsArn", "SourceFlowConfig", "Tasks", "TriggerConfig"], "members": {"Description": {"shape": "FlowDescription", "documentation": "<p>A description of the flow you want to create.</p>"}, "FlowName": {"shape": "FlowName", "documentation": "<p>The specified name of the flow. Use underscores (_) or hyphens (-) only. Spaces are not allowed.</p>"}, "KmsArn": {"shape": "KmsArn", "documentation": "<p>The Amazon Resource Name of the AWS Key Management Service (KMS) key you provide for encryption.</p>"}, "SourceFlowConfig": {"shape": "SourceFlowConfig", "documentation": "<p>The configuration that controls how Customer Profiles retrieves data from the source.</p>"}, "Tasks": {"shape": "Tasks", "documentation": "<p>A list of tasks that Customer Profiles performs while transferring the data in the flow run.</p>"}, "TriggerConfig": {"shape": "TriggerConfig", "documentation": "<p>The trigger settings that determine how and when the flow runs.</p>"}}, "documentation": "<p>The configurations that control how Customer Profiles retrieves data from the source, Amazon AppFlow. Customer Profiles uses this information to create an AppFlow flow on behalf of customers.</p>"}, "FlowDescription": {"type": "string", "max": 2048, "pattern": "[\\w!@#\\-.?,\\s]*"}, "FlowName": {"type": "string", "max": 256, "pattern": "[a-zA-Z0-9][\\w!@#.-]+"}, "FoundByKeyValue": {"type": "structure", "members": {"KeyName": {"shape": "name", "documentation": "<p>A searchable identifier of a customer profile.</p>"}, "Values": {"shape": "requestValueList", "documentation": "<p>A list of key values.</p>"}}, "documentation": "<p>A data type pair that consists of a <code>KeyName</code> and <code>Values</code> list that were used to find a profile returned in response to a <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_SearchProfiles.html\">SearchProfiles</a> request. </p>"}, "Gender": {"type": "string", "deprecated": true, "enum": ["MALE", "FEMALE", "UNSPECIFIED"]}, "GetAutoMergingPreviewRequest": {"type": "structure", "required": ["DomainName", "Consolidation", "ConflictResolution"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "Consolidation": {"shape": "Consolidation", "documentation": "<p>A list of matching attributes that represent matching criteria.</p>"}, "ConflictResolution": {"shape": "ConflictResolution", "documentation": "<p>How the auto-merging process should resolve conflicts between different profiles.</p>"}, "MinAllowedConfidenceScoreForMerging": {"shape": "Double0To1", "documentation": "<p>Minimum confidence score required for profiles within a matching group to be merged during the auto-merge process.</p>"}}}, "GetAutoMergingPreviewResponse": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>"}, "NumberOfMatchesInSample": {"shape": "long", "documentation": "<p>The number of match groups in the domain that have been reviewed in this preview dry run.</p>"}, "NumberOfProfilesInSample": {"shape": "long", "documentation": "<p>The number of profiles found in this preview dry run.</p>"}, "NumberOfProfilesWillBeMerged": {"shape": "long", "documentation": "<p>The number of profiles that would be merged if this wasn't a preview dry run.</p>"}}}, "GetDomainRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}}}, "GetDomainResponse": {"type": "structure", "required": ["DomainName", "CreatedAt", "LastUpdatedAt"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>"}, "DefaultExpirationDays": {"shape": "expirationDaysInteger", "documentation": "<p>The default number of days until the data within the domain expires.</p>"}, "DefaultEncryptionKey": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The default encryption key, which is an AWS managed key, is used when no specific type of encryption key is specified. It is used to encrypt all data before it is placed in permanent or semi-permanent storage.</p>"}, "DeadLetterQueueUrl": {"shape": "sqsQueueUrl", "documentation": "<p>The URL of the SQS dead letter queue, which is used for reporting errors associated with ingesting data from third party applications.</p>"}, "Stats": {"shape": "DomainStats", "documentation": "<p>Usage-specific statistics about the domain.</p>"}, "Matching": {"shape": "MatchingResponse", "documentation": "<p>The process of matching duplicate profiles. If <code>Matching</code> = <code>true</code>, Amazon Connect Customer Profiles starts a weekly batch process called Identity Resolution Job. If you do not specify a date and time for Identity Resolution Job to run, by default it runs every Saturday at 12AM UTC to detect duplicate profiles in your domains. </p> <p>After the Identity Resolution Job completes, use the <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_GetMatches.html\">GetMatches</a> API to return and review the results. Or, if you have configured <code>ExportingConfig</code> in the <code>MatchingRequest</code>, you can download the results from S3.</p>"}, "CreatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was created.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was most recently edited.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "GetIdentityResolutionJobRequest": {"type": "structure", "required": ["DomainName", "JobId"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "JobId": {"shape": "uuid", "documentation": "<p>The unique identifier of the Identity Resolution Job.</p>", "location": "uri", "locationName": "JobId"}}}, "GetIdentityResolutionJobResponse": {"type": "structure", "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>"}, "JobId": {"shape": "uuid", "documentation": "<p>The unique identifier of the Identity Resolution Job.</p>"}, "Status": {"shape": "IdentityResolutionJobStatus", "documentation": "<p>The status of the Identity Resolution Job.</p> <ul> <li> <p> <code>PENDING</code>: The Identity Resolution Job is scheduled but has not started yet. If you turn off the Identity Resolution feature in your domain, jobs in the <code>PENDING</code> state are deleted.</p> </li> <li> <p> <code>PREPROCESSING</code>: The Identity Resolution Job is loading your data.</p> </li> <li> <p> <code>FIND_MATCHING</code>: The Identity Resolution Job is using the machine learning model to identify profiles that belong to the same matching group.</p> </li> <li> <p> <code>MERGING</code>: The Identity Resolution Job is merging duplicate profiles.</p> </li> <li> <p> <code>COMPLETED</code>: The Identity Resolution Job completed successfully.</p> </li> <li> <p> <code>PARTIAL_SUCCESS</code>: There's a system error and not all of the data is merged. The Identity Resolution Job writes a message indicating the source of the problem.</p> </li> <li> <p> <code>FAILED</code>: The Identity Resolution Job did not merge any data. It writes a message indicating the source of the problem.</p> </li> </ul>"}, "Message": {"shape": "stringTo2048", "documentation": "<p>The error messages that are generated when the Identity Resolution Job runs.</p>"}, "JobStartTime": {"shape": "timestamp", "documentation": "<p>The timestamp of when the Identity Resolution Job was started or will be started.</p>"}, "JobEndTime": {"shape": "timestamp", "documentation": "<p>The timestamp of when the Identity Resolution Job was completed.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the Identity Resolution Job was most recently edited.</p>"}, "JobExpirationTime": {"shape": "timestamp", "documentation": "<p>The timestamp of when the Identity Resolution Job will expire.</p>"}, "AutoMerging": {"shape": "AutoMerging", "documentation": "<p>Configuration settings for how to perform the auto-merging of profiles.</p>"}, "ExportingLocation": {"shape": "ExportingLocation", "documentation": "<p>The S3 location where the Identity Resolution Job writes result files.</p>"}, "JobStats": {"shape": "JobStats", "documentation": "<p>Statistics about the Identity Resolution Job.</p>"}}}, "GetIntegrationRequest": {"type": "structure", "required": ["DomainName", "<PERSON><PERSON>"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "Uri": {"shape": "string1To255", "documentation": "<p>The URI of the S3 bucket or any other type of data source.</p>"}}}, "GetIntegrationResponse": {"type": "structure", "required": ["DomainName", "<PERSON><PERSON>", "CreatedAt", "LastUpdatedAt"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>"}, "Uri": {"shape": "string1To255", "documentation": "<p>The URI of the S3 bucket or any other type of data source.</p>"}, "ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>"}, "CreatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was created.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was most recently edited.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}, "ObjectTypeNames": {"shape": "ObjectTypeNames", "documentation": "<p>A map in which each key is an event type from an external application such as Segment or Shopify, and each value is an <code>ObjectTypeName</code> (template) used to ingest the event. It supports the following event types: <code>SegmentIdentify</code>, <code>ShopifyCreateCustomers</code>, <code>ShopifyUpdateCustomers</code>, <code>ShopifyCreateDraftOrders</code>, <code>ShopifyUpdateDraftOrders</code>, <code>ShopifyCreateOrders</code>, and <code>ShopifyUpdatedOrders</code>.</p>"}, "WorkflowId": {"shape": "string1To255", "documentation": "<p>Unique identifier for the workflow.</p>"}, "IsUnstructured": {"shape": "optionalBoolean", "documentation": "<p>Boolean that shows if the Flow that's associated with the Integration is created in Amazon Appflow, or with ObjectTypeName equals _unstructured via API/CLI in flowDefinition.</p>"}}}, "GetMatchesRequest": {"type": "structure", "required": ["DomainName"], "members": {"NextToken": {"shape": "token", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "maxSize100", "documentation": "<p>The maximum number of results to return per page.</p>", "location": "querystring", "locationName": "max-results"}, "DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}}}, "GetMatchesResponse": {"type": "structure", "members": {"NextToken": {"shape": "token", "documentation": "<p>If there are additional results, this is the token for the next set of results.</p>"}, "MatchGenerationDate": {"shape": "timestamp", "documentation": "<p>The timestamp this version of Match Result generated.</p>"}, "PotentialMatches": {"shape": "matchesNumber", "documentation": "<p>The number of potential matches found.</p>"}, "Matches": {"shape": "MatchesList", "documentation": "<p>The list of matched profiles for this instance.</p>"}}}, "GetProfileObjectTypeRequest": {"type": "structure", "required": ["DomainName", "ObjectTypeName"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>", "location": "uri", "locationName": "ObjectTypeName"}}}, "GetProfileObjectTypeResponse": {"type": "structure", "required": ["ObjectTypeName", "Description"], "members": {"ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>"}, "Description": {"shape": "text", "documentation": "<p>The description of the profile object type.</p>"}, "TemplateId": {"shape": "name", "documentation": "<p>A unique identifier for the object template.</p>"}, "ExpirationDays": {"shape": "expirationDaysInteger", "documentation": "<p>The number of days until the data in the object expires.</p>"}, "EncryptionKey": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The customer-provided key to encrypt the profile object that will be created in this profile object type.</p>"}, "AllowProfileCreation": {"shape": "boolean", "documentation": "<p>Indicates whether a profile should be created when data is received if one doesn’t exist for an object of this type. The default is <code>FALSE</code>. If the AllowProfileCreation flag is set to <code>FALSE</code>, then the service tries to fetch a standard profile and associate this object with the profile. If it is set to <code>TRUE</code>, and if no match is found, then the service creates a new standard profile.</p>"}, "SourceLastUpdatedTimestampFormat": {"shape": "string1To255", "documentation": "<p>The format of your <code>sourceLastUpdatedTimestamp</code> that was previously set up.</p>"}, "Fields": {"shape": "FieldMap", "documentation": "<p>A map of the name and ObjectType field.</p>"}, "Keys": {"shape": "KeyMap", "documentation": "<p>A list of unique keys that can be used to map data to the profile.</p>"}, "CreatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was created.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was most recently edited.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "GetProfileObjectTypeTemplateRequest": {"type": "structure", "required": ["TemplateId"], "members": {"TemplateId": {"shape": "name", "documentation": "<p>A unique identifier for the object template.</p>", "location": "uri", "locationName": "TemplateId"}}}, "GetProfileObjectTypeTemplateResponse": {"type": "structure", "members": {"TemplateId": {"shape": "name", "documentation": "<p>A unique identifier for the object template.</p>"}, "SourceName": {"shape": "name", "documentation": "<p>The name of the source of the object template.</p>"}, "SourceObject": {"shape": "name", "documentation": "<p>The source of the object template.</p>"}, "AllowProfileCreation": {"shape": "boolean", "documentation": "<p>Indicates whether a profile should be created when data is received if one doesn’t exist for an object of this type. The default is <code>FALSE</code>. If the AllowProfileCreation flag is set to <code>FALSE</code>, then the service tries to fetch a standard profile and associate this object with the profile. If it is set to <code>TRUE</code>, and if no match is found, then the service creates a new standard profile.</p>"}, "SourceLastUpdatedTimestampFormat": {"shape": "string1To255", "documentation": "<p>The format of your <code>sourceLastUpdatedTimestamp</code> that was previously set up.</p>"}, "Fields": {"shape": "FieldMap", "documentation": "<p>A map of the name and ObjectType field.</p>"}, "Keys": {"shape": "KeyMap", "documentation": "<p>A list of unique keys that can be used to map data to the profile.</p>"}}}, "GetWorkflowRequest": {"type": "structure", "required": ["DomainName", "WorkflowId"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "WorkflowId": {"shape": "uuid", "documentation": "<p>Unique identifier for the workflow.</p>", "location": "uri", "locationName": "WorkflowId"}}}, "GetWorkflowResponse": {"type": "structure", "members": {"WorkflowId": {"shape": "uuid", "documentation": "<p>Unique identifier for the workflow.</p>"}, "WorkflowType": {"shape": "WorkflowType", "documentation": "<p>The type of workflow. The only supported value is APPFLOW_INTEGRATION.</p>"}, "Status": {"shape": "Status", "documentation": "<p>Status of workflow execution.</p>"}, "ErrorDescription": {"shape": "string1To255", "documentation": "<p>Workflow error messages during execution (if any).</p>"}, "StartDate": {"shape": "timestamp", "documentation": "<p>The timestamp that represents when workflow execution started.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp that represents when workflow execution last updated.</p>"}, "Attributes": {"shape": "WorkflowAttributes", "documentation": "<p>Attributes provided for workflow execution.</p>"}, "Metrics": {"shape": "WorkflowMetrics", "documentation": "<p>Workflow specific execution metrics.</p>"}}}, "GetWorkflowStepsRequest": {"type": "structure", "required": ["DomainName", "WorkflowId"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "WorkflowId": {"shape": "uuid", "documentation": "<p>Unique identifier for the workflow.</p>", "location": "uri", "locationName": "WorkflowId"}, "NextToken": {"shape": "token", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "maxSize100", "documentation": "<p>The maximum number of results to return per page.</p>", "location": "querystring", "locationName": "max-results"}}}, "GetWorkflowStepsResponse": {"type": "structure", "members": {"WorkflowId": {"shape": "uuid", "documentation": "<p>Unique identifier for the workflow.</p>"}, "WorkflowType": {"shape": "WorkflowType", "documentation": "<p>The type of workflow. The only supported value is APPFLOW_INTEGRATION.</p>"}, "Items": {"shape": "WorkflowStepsList", "documentation": "<p>List containing workflow step details.</p>"}, "NextToken": {"shape": "token", "documentation": "<p>If there are additional results, this is the token for the next set of results.</p>"}}}, "IdentityResolutionJob": {"type": "structure", "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>"}, "JobId": {"shape": "uuid", "documentation": "<p>The unique identifier of the Identity Resolution Job.</p>"}, "Status": {"shape": "IdentityResolutionJobStatus", "documentation": "<p>The status of the Identity Resolution Job.</p> <ul> <li> <p> <code>PENDING</code>: The Identity Resolution Job is scheduled but has not started yet. If you turn off the Identity Resolution feature in your domain, jobs in the <code>PENDING</code> state are deleted.</p> </li> <li> <p> <code>PREPROCESSING</code>: The Identity Resolution Job is loading your data.</p> </li> <li> <p> <code>FIND_MATCHING</code>: The Identity Resolution Job is using the machine learning model to identify profiles that belong to the same matching group.</p> </li> <li> <p> <code>MERGING</code>: The Identity Resolution Job is merging duplicate profiles.</p> </li> <li> <p> <code>COMPLETED</code>: The Identity Resolution Job completed successfully.</p> </li> <li> <p> <code>PARTIAL_SUCCESS</code>: There's a system error and not all of the data is merged. The Identity Resolution Job writes a message indicating the source of the problem.</p> </li> <li> <p> <code>FAILED</code>: The Identity Resolution Job did not merge any data. It writes a message indicating the source of the problem.</p> </li> </ul>"}, "JobStartTime": {"shape": "timestamp", "documentation": "<p>The timestamp of when the job was started or will be started.</p>"}, "JobEndTime": {"shape": "timestamp", "documentation": "<p>The timestamp of when the job was completed.</p>"}, "JobStats": {"shape": "JobStats", "documentation": "<p>Statistics about an Identity Resolution Job.</p>"}, "ExportingLocation": {"shape": "ExportingLocation", "documentation": "<p>The S3 location where the Identity Resolution Job writes result files.</p>"}, "Message": {"shape": "stringTo2048", "documentation": "<p>The error messages that are generated when the Identity Resolution Job runs.</p>"}}, "documentation": "<p>Information about the Identity Resolution Job.</p>"}, "IdentityResolutionJobStatus": {"type": "string", "enum": ["PENDING", "PREPROCESSING", "FIND_MATCHING", "MERGING", "COMPLETED", "PARTIAL_SUCCESS", "FAILED"]}, "IdentityResolutionJobsList": {"type": "list", "member": {"shape": "IdentityResolutionJob"}}, "IncrementalPullConfig": {"type": "structure", "members": {"DatetimeTypeFieldName": {"shape": "DatetimeTypeFieldName", "documentation": "<p>A field that specifies the date time or timestamp field as the criteria to use when importing incremental records from the source.</p>"}}, "documentation": "<p>Specifies the configuration used when importing incremental records from the source.</p>"}, "IntegrationConfig": {"type": "structure", "members": {"AppflowIntegration": {"shape": "AppflowIntegration", "documentation": "<p>Configuration data for <code>APPFLOW_INTEGRATION</code> workflow type.</p>"}}, "documentation": "<p>Configuration data for integration workflow.</p>"}, "IntegrationList": {"type": "list", "member": {"shape": "ListIntegrationItem"}}, "InternalServerException": {"type": "structure", "members": {"Message": {"shape": "message"}}, "documentation": "<p>An internal service error occurred.</p>", "error": {"httpStatusCode": 500}, "exception": true, "fault": true}, "JobSchedule": {"type": "structure", "required": ["DayOfTheWeek", "Time"], "members": {"DayOfTheWeek": {"shape": "JobScheduleDayOfTheWeek", "documentation": "<p>The day when the Identity Resolution Job should run every week.</p>"}, "Time": {"shape": "JobScheduleTime", "documentation": "<p>The time when the Identity Resolution Job should run every week.</p>"}}, "documentation": "<p>The day and time when do you want to start the Identity Resolution Job every week.</p>"}, "JobScheduleDayOfTheWeek": {"type": "string", "enum": ["SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"]}, "JobScheduleTime": {"type": "string", "max": 5, "min": 3, "pattern": "^([0-9]|0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$"}, "JobStats": {"type": "structure", "members": {"NumberOfProfilesReviewed": {"shape": "long", "documentation": "<p>The number of profiles reviewed.</p>"}, "NumberOfMatchesFound": {"shape": "long", "documentation": "<p>The number of matches found.</p>"}, "NumberOfMergesDone": {"shape": "long", "documentation": "<p>The number of merges completed.</p>"}}, "documentation": "<p>Statistics about the Identity Resolution Job.</p>"}, "KeyMap": {"type": "map", "key": {"shape": "name"}, "value": {"shape": "ObjectTypeKeyList"}}, "KmsArn": {"type": "string", "max": 2048, "min": 20, "pattern": "arn:aws:kms:.*:[0-9]+:.*"}, "ListAccountIntegrationsRequest": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Uri": {"shape": "string1To255", "documentation": "<p>The URI of the S3 bucket or any other type of data source.</p>"}, "NextToken": {"shape": "token", "documentation": "<p>The pagination token from the previous ListAccountIntegrations API call.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "maxSize100", "documentation": "<p>The maximum number of objects returned per page.</p>", "location": "querystring", "locationName": "max-results"}, "IncludeHidden": {"shape": "optionalBoolean", "documentation": "<p><PERSON><PERSON><PERSON> to indicate if hidden integration should be returned. Defaults to <code>False</code>.</p>", "location": "querystring", "locationName": "include-hidden"}}}, "ListAccountIntegrationsResponse": {"type": "structure", "members": {"Items": {"shape": "IntegrationList", "documentation": "<p>The list of ListAccountIntegration instances.</p>"}, "NextToken": {"shape": "token", "documentation": "<p>The pagination token from the previous ListAccountIntegrations API call.</p>"}}}, "ListDomainItem": {"type": "structure", "required": ["DomainName", "CreatedAt", "LastUpdatedAt"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>"}, "CreatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was created.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was most recently edited.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}, "documentation": "<p>An object in a list that represents a domain.</p>"}, "ListDomainsRequest": {"type": "structure", "members": {"NextToken": {"shape": "token", "documentation": "<p>The pagination token from the previous ListDomain API call.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "maxSize100", "documentation": "<p>The maximum number of objects returned per page.</p>", "location": "querystring", "locationName": "max-results"}}}, "ListDomainsResponse": {"type": "structure", "members": {"Items": {"shape": "DomainList", "documentation": "<p>The list of ListDomains instances.</p>"}, "NextToken": {"shape": "token", "documentation": "<p>The pagination token from the previous ListDomains API call.</p>"}}}, "ListIdentityResolutionJobsRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "NextToken": {"shape": "token", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "maxSize100", "documentation": "<p>The maximum number of results to return per page.</p>", "location": "querystring", "locationName": "max-results"}}}, "ListIdentityResolutionJobsResponse": {"type": "structure", "members": {"IdentityResolutionJobsList": {"shape": "IdentityResolutionJobsList", "documentation": "<p>A list of Identity Resolution Jobs.</p>"}, "NextToken": {"shape": "token", "documentation": "<p>If there are additional results, this is the token for the next set of results.</p>"}}}, "ListIntegrationItem": {"type": "structure", "required": ["DomainName", "<PERSON><PERSON>", "CreatedAt", "LastUpdatedAt"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>"}, "Uri": {"shape": "string1To255", "documentation": "<p>The URI of the S3 bucket or any other type of data source.</p>"}, "ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>"}, "CreatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was created.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was most recently edited.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}, "ObjectTypeNames": {"shape": "ObjectTypeNames", "documentation": "<p>A map in which each key is an event type from an external application such as Segment or Shopify, and each value is an <code>ObjectTypeName</code> (template) used to ingest the event. It supports the following event types: <code>SegmentIdentify</code>, <code>ShopifyCreateCustomers</code>, <code>ShopifyUpdateCustomers</code>, <code>ShopifyCreateDraftOrders</code>, <code>ShopifyUpdateDraftOrders</code>, <code>ShopifyCreateOrders</code>, and <code>ShopifyUpdatedOrders</code>.</p>"}, "WorkflowId": {"shape": "string1To255", "documentation": "<p>Unique identifier for the workflow.</p>"}, "IsUnstructured": {"shape": "optionalBoolean", "documentation": "<p>Boolean that shows if the Flow that's associated with the Integration is created in Amazon Appflow, or with ObjectTypeName equals _unstructured via API/CLI in flowDefinition.</p>"}}, "documentation": "<p>An integration in list of integrations.</p>"}, "ListIntegrationsRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "NextToken": {"shape": "token", "documentation": "<p>The pagination token from the previous ListIntegrations API call.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "maxSize100", "documentation": "<p>The maximum number of objects returned per page.</p>", "location": "querystring", "locationName": "max-results"}, "IncludeHidden": {"shape": "optionalBoolean", "documentation": "<p><PERSON><PERSON><PERSON> to indicate if hidden integration should be returned. Defaults to <code>False</code>.</p>", "location": "querystring", "locationName": "include-hidden"}}}, "ListIntegrationsResponse": {"type": "structure", "members": {"Items": {"shape": "IntegrationList", "documentation": "<p>The list of ListIntegrations instances.</p>"}, "NextToken": {"shape": "token", "documentation": "<p>The pagination token from the previous ListIntegrations API call.</p>"}}}, "ListProfileObjectTypeItem": {"type": "structure", "required": ["ObjectTypeName", "Description"], "members": {"ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>"}, "Description": {"shape": "text", "documentation": "<p>Description of the profile object type.</p>"}, "CreatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was created.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was most recently edited.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}, "documentation": "<p>A ProfileObjectType instance.</p>"}, "ListProfileObjectTypeTemplateItem": {"type": "structure", "members": {"TemplateId": {"shape": "name", "documentation": "<p>A unique identifier for the object template.</p>"}, "SourceName": {"shape": "name", "documentation": "<p>The name of the source of the object template.</p>"}, "SourceObject": {"shape": "name", "documentation": "<p>The source of the object template.</p>"}}, "documentation": "<p>A ProfileObjectTypeTemplate in a list of ProfileObjectTypeTemplates.</p>"}, "ListProfileObjectTypeTemplatesRequest": {"type": "structure", "members": {"NextToken": {"shape": "token", "documentation": "<p>The pagination token from the previous ListObjectTypeTemplates API call.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "maxSize100", "documentation": "<p>The maximum number of objects returned per page.</p>", "location": "querystring", "locationName": "max-results"}}}, "ListProfileObjectTypeTemplatesResponse": {"type": "structure", "members": {"Items": {"shape": "ProfileObjectTypeTemplateList", "documentation": "<p>The list of ListProfileObjectType template instances.</p>"}, "NextToken": {"shape": "token", "documentation": "<p>The pagination token from the previous ListObjectTypeTemplates API call. </p>"}}}, "ListProfileObjectTypesRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "NextToken": {"shape": "token", "documentation": "<p>Identifies the next page of results to return.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "maxSize100", "documentation": "<p>The maximum number of objects returned per page.</p>", "location": "querystring", "locationName": "max-results"}}}, "ListProfileObjectTypesResponse": {"type": "structure", "members": {"Items": {"shape": "ProfileObjectTypeList", "documentation": "<p>The list of ListProfileObjectTypes instances.</p>"}, "NextToken": {"shape": "token", "documentation": "<p>Identifies the next page of results to return.</p>"}}}, "ListProfileObjectsItem": {"type": "structure", "members": {"ObjectTypeName": {"shape": "typeName", "documentation": "<p>Specifies the kind of object being added to a profile, such as \"Salesforce-Account.\"</p>"}, "ProfileObjectUniqueKey": {"shape": "string1To255", "documentation": "<p>The unique identifier of the ProfileObject generated by the service.</p>"}, "Object": {"shape": "string<PERSON><PERSON><PERSON>", "documentation": "<p>A JSON representation of a ProfileObject that belongs to a profile.</p>"}}, "documentation": "<p>A ProfileObject in a list of ProfileObjects.</p>"}, "ListProfileObjectsRequest": {"type": "structure", "required": ["DomainName", "ObjectTypeName", "ProfileId"], "members": {"NextToken": {"shape": "token", "documentation": "<p>The pagination token from the previous call to ListProfileObjects.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "maxSize100", "documentation": "<p>The maximum number of objects returned per page.</p>", "location": "querystring", "locationName": "max-results"}, "DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>"}, "ProfileId": {"shape": "uuid", "documentation": "<p>The unique identifier of a customer profile.</p>"}, "ObjectFilter": {"shape": "ObjectFilter", "documentation": "<p>Applies a filter to the response to include profile objects with the specified index values. This filter is only supported for ObjectTypeName _asset, _case and _order.</p>"}}}, "ListProfileObjectsResponse": {"type": "structure", "members": {"Items": {"shape": "ProfileObjectList", "documentation": "<p>The list of ListProfileObject instances.</p>"}, "NextToken": {"shape": "token", "documentation": "<p>The pagination token from the previous call to ListProfileObjects.</p>"}}}, "ListTagsForResourceRequest": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"shape": "TagArn", "documentation": "<p>The ARN of the resource for which you want to view tags.</p>", "location": "uri", "locationName": "resourceArn"}}}, "ListTagsForResourceResponse": {"type": "structure", "members": {"tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "ListWorkflowsItem": {"type": "structure", "required": ["WorkflowType", "WorkflowId", "Status", "StatusDescription", "CreatedAt", "LastUpdatedAt"], "members": {"WorkflowType": {"shape": "WorkflowType", "documentation": "<p>The type of workflow. The only supported value is APPFLOW_INTEGRATION.</p>"}, "WorkflowId": {"shape": "string1To255", "documentation": "<p>Unique identifier for the workflow.</p>"}, "Status": {"shape": "Status", "documentation": "<p>Status of workflow execution.</p>"}, "StatusDescription": {"shape": "string1To255", "documentation": "<p>Description for workflow execution status.</p>"}, "CreatedAt": {"shape": "timestamp", "documentation": "<p>Creation timestamp for workflow.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>Last updated timestamp for workflow.</p>"}}, "documentation": "<p>A workflow in list of workflows.</p>"}, "ListWorkflowsRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "WorkflowType": {"shape": "WorkflowType", "documentation": "<p>The type of workflow. The only supported value is APPFLOW_INTEGRATION.</p>"}, "Status": {"shape": "Status", "documentation": "<p>Status of workflow execution.</p>"}, "QueryStartDate": {"shape": "timestamp", "documentation": "<p>Retrieve workflows started after timestamp.</p>"}, "QueryEndDate": {"shape": "timestamp", "documentation": "<p>Retrieve workflows ended after timestamp.</p>"}, "NextToken": {"shape": "token", "documentation": "<p>The token for the next set of results. Use the value returned in the previous response in the next request to retrieve the next set of results.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "maxSize100", "documentation": "<p>The maximum number of results to return per page.</p>", "location": "querystring", "locationName": "max-results"}}}, "ListWorkflowsResponse": {"type": "structure", "members": {"Items": {"shape": "WorkflowList", "documentation": "<p>List containing workflow details.</p>"}, "NextToken": {"shape": "token", "documentation": "<p>If there are additional results, this is the token for the next set of results.</p>"}}}, "MarketoConnectorOperator": {"type": "string", "enum": ["PROJECTION", "LESS_THAN", "GREATER_THAN", "BETWEEN", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "MarketoSourceProperties": {"type": "structure", "required": ["Object"], "members": {"Object": {"shape": "Object", "documentation": "<p>The object specified in the Marketo flow source.</p>"}}, "documentation": "<p>The properties that are applied when Marketo is being used as a source.</p>"}, "MatchItem": {"type": "structure", "members": {"MatchId": {"shape": "string1To255", "documentation": "<p>The unique identifiers for this group of profiles that match.</p>"}, "ProfileIds": {"shape": "ProfileIdList", "documentation": "<p>A list of identifiers for profiles that match.</p>"}, "ConfidenceScore": {"shape": "Double", "documentation": "<p>A number between 0 and 1, where a higher score means higher similarity. Examining match confidence scores lets you distinguish between groups of similar records in which the system is highly confident (which you may decide to merge), groups of similar records about which the system is uncertain (which you may decide to have reviewed by a human), and groups of similar records that the system deems to be unlikely (which you may decide to reject). Given confidence scores vary as per the data input, it should not be used an absolute measure of matching quality.</p>"}}, "documentation": "<p>The Match group object.</p>"}, "MatchesList": {"type": "list", "member": {"shape": "MatchItem"}}, "MatchingAttributes": {"type": "list", "member": {"shape": "string1To255"}, "max": 20, "min": 1}, "MatchingAttributesList": {"type": "list", "member": {"shape": "MatchingAttributes"}, "max": 10, "min": 1}, "MatchingRequest": {"type": "structure", "required": ["Enabled"], "members": {"Enabled": {"shape": "optionalBoolean", "documentation": "<p>The flag that enables the matching process of duplicate profiles.</p>"}, "JobSchedule": {"shape": "JobSchedule", "documentation": "<p>The day and time when do you want to start the Identity Resolution Job every week.</p>"}, "AutoMerging": {"shape": "AutoMerging", "documentation": "<p>Configuration information about the auto-merging process.</p>"}, "ExportingConfig": {"shape": "ExportingConfig", "documentation": "<p>Configuration information for exporting Identity Resolution results, for example, to an S3 bucket.</p>"}}, "documentation": "<p>The flag that enables the matching process of duplicate profiles.</p>"}, "MatchingResponse": {"type": "structure", "members": {"Enabled": {"shape": "optionalBoolean", "documentation": "<p>The flag that enables the matching process of duplicate profiles.</p>"}, "JobSchedule": {"shape": "JobSchedule", "documentation": "<p>The day and time when do you want to start the Identity Resolution Job every week.</p>"}, "AutoMerging": {"shape": "AutoMerging", "documentation": "<p>Configuration information about the auto-merging process.</p>"}, "ExportingConfig": {"shape": "ExportingConfig", "documentation": "<p>Configuration information for exporting Identity Resolution results, for example, to an S3 bucket.</p>"}}, "documentation": "<p>The flag that enables the matching process of duplicate profiles.</p>"}, "MergeProfilesRequest": {"type": "structure", "required": ["DomainName", "MainProfileId", "ProfileIdsToBeMerged"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "MainProfileId": {"shape": "uuid", "documentation": "<p>The identifier of the profile to be taken.</p>"}, "ProfileIdsToBeMerged": {"shape": "ProfileIdToBeMergedList", "documentation": "<p>The identifier of the profile to be merged into MainProfileId.</p>"}, "FieldSourceProfileIds": {"shape": "FieldSourceProfileIds", "documentation": "<p>The identifiers of the fields in the profile that has the information you want to apply to the merge. For example, say you want to merge EmailAddress from Profile1 into MainProfile. This would be the identifier of the EmailAddress field in Profile1. </p>"}}}, "MergeProfilesResponse": {"type": "structure", "members": {"Message": {"shape": "message", "documentation": "<p>A message that indicates the merge request is complete.</p>"}}}, "Object": {"type": "string", "max": 512, "pattern": "\\S+"}, "ObjectFilter": {"type": "structure", "required": ["KeyName", "Values"], "members": {"KeyName": {"shape": "name", "documentation": "<p>A searchable identifier of a standard profile object. The predefined keys you can use to search for _asset include: _assetId, _assetName, _serialNumber. The predefined keys you can use to search for _case include: _caseId. The predefined keys you can use to search for _order include: _orderId.</p>"}, "Values": {"shape": "requestValueList", "documentation": "<p>A list of key values.</p>"}}, "documentation": "<p>The filter applied to ListProfileObjects response to include profile objects with the specified index values. This filter is only supported for ObjectTypeName _asset, _case and _order.</p>"}, "ObjectTypeField": {"type": "structure", "members": {"Source": {"shape": "text", "documentation": "<p>A field of a ProfileObject. For example: _source.FirstName, where “_source” is a ProfileObjectType of a Zendesk user and “FirstName” is a field in that ObjectType.</p>"}, "Target": {"shape": "text", "documentation": "<p>The location of the data in the standard ProfileObject model. For example: _profile.Address.PostalCode.</p>"}, "ContentType": {"shape": "FieldContentType", "documentation": "<p>The content type of the field. Used for determining equality when searching.</p>"}}, "documentation": "<p>Represents a field in a ProfileObjectType.</p>"}, "ObjectTypeKey": {"type": "structure", "members": {"StandardIdentifiers": {"shape": "StandardIdentifierList", "documentation": "<p>The types of keys that a ProfileObject can have. Each ProfileObject can have only 1 UNIQUE key but multiple PROFILE keys. PROFILE, ASSET, CASE, or ORDER means that this key can be used to tie an object to a PROFILE, ASSET, CASE, or ORDER respectively. UNIQUE means that it can be used to uniquely identify an object. If a key a is marked as SEC<PERSON><PERSON><PERSON><PERSON>, it will be used to search for profiles after all other PROFILE keys have been searched. A LOOKUP_ONLY key is only used to match a profile but is not persisted to be used for searching of the profile. A NEW_ONLY key is only used if the profile does not already exist before the object is ingested, otherwise it is only used for matching objects to profiles.</p>"}, "FieldNames": {"shape": "FieldNameList", "documentation": "<p>The reference for the key name of the fields map.</p>"}}, "documentation": "<p>An object that defines the Key element of a ProfileObject. A Key is a special element that can be used to search for a customer profile.</p>"}, "ObjectTypeKeyList": {"type": "list", "member": {"shape": "ObjectTypeKey"}}, "ObjectTypeNames": {"type": "map", "key": {"shape": "string1To255"}, "value": {"shape": "typeName"}}, "OperatorPropertiesKeys": {"type": "string", "enum": ["VALUE", "VALUES", "DATA_TYPE", "UPPER_BOUND", "LOWER_BOUND", "SOURCE_DATA_TYPE", "DESTINATION_DATA_TYPE", "VALIDATION_ACTION", "MASK_VALUE", "MASK_LENGTH", "TRUNCATE_LENGTH", "MATH_OPERATION_FIELDS_ORDER", "CONCAT_FORMAT", "SUBFIELD_CATEGORY_MAP"]}, "PartyType": {"type": "string", "deprecated": true, "enum": ["INDIVIDUAL", "BUSINESS", "OTHER"]}, "Profile": {"type": "structure", "members": {"ProfileId": {"shape": "uuid", "documentation": "<p>The unique identifier of a customer profile.</p>"}, "AccountNumber": {"shape": "string1To255", "documentation": "<p>A unique account number that you have given to the customer.</p>"}, "AdditionalInformation": {"shape": "string1To1000", "documentation": "<p>Any additional information relevant to the customer’s profile.</p>"}, "PartyType": {"shape": "PartyType", "documentation": "<p>The type of profile used to describe the customer.</p>"}, "BusinessName": {"shape": "string1To255", "documentation": "<p>The name of the customer’s business.</p>"}, "FirstName": {"shape": "string1To255", "documentation": "<p>The customer’s first name.</p>"}, "MiddleName": {"shape": "string1To255", "documentation": "<p>The customer’s middle name.</p>"}, "LastName": {"shape": "string1To255", "documentation": "<p>The customer’s last name.</p>"}, "BirthDate": {"shape": "string1To255", "documentation": "<p>The customer’s birth date. </p>"}, "Gender": {"shape": "Gender", "documentation": "<p>The gender with which the customer identifies. </p>"}, "PhoneNumber": {"shape": "string1To255", "documentation": "<p>The customer's phone number, which has not been specified as a mobile, home, or business number.</p>"}, "MobilePhoneNumber": {"shape": "string1To255", "documentation": "<p>The customer’s mobile phone number.</p>"}, "HomePhoneNumber": {"shape": "string1To255", "documentation": "<p>The customer’s home phone number.</p>"}, "BusinessPhoneNumber": {"shape": "string1To255", "documentation": "<p>The customer’s home phone number.</p>"}, "EmailAddress": {"shape": "string1To255", "documentation": "<p>The customer’s email address, which has not been specified as a personal or business address. </p>"}, "PersonalEmailAddress": {"shape": "string1To255", "documentation": "<p>The customer’s personal email address.</p>"}, "BusinessEmailAddress": {"shape": "string1To255", "documentation": "<p>The customer’s business email address.</p>"}, "Address": {"shape": "Address", "documentation": "<p>A generic address associated with the customer that is not mailing, shipping, or billing.</p>"}, "ShippingAddress": {"shape": "Address", "documentation": "<p>The customer’s shipping address.</p>"}, "MailingAddress": {"shape": "Address", "documentation": "<p>The customer’s mailing address.</p>"}, "BillingAddress": {"shape": "Address", "documentation": "<p>The customer’s billing address.</p>"}, "Attributes": {"shape": "Attributes", "documentation": "<p>A key value pair of attributes of a customer profile.</p>"}, "FoundByItems": {"shape": "foundByList", "documentation": "<p>A list of items used to find a profile returned in a <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_SearchProfiles.html\">SearchProfiles</a> response. An item is a key-value(s) pair that matches an attribute in the profile.</p> <p>If the optional <code>AdditionalSearchKeys</code> parameter was included in the <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_SearchProfiles.html\">SearchProfiles</a> request, the <code>FoundByItems</code> list should be interpreted based on the <code>LogicalOperator</code> used in the request:</p> <ul> <li> <p> <code>AND</code> - The profile included in the response matched all of the search keys specified in the request. The <code>FoundByItems</code> will include all of the key-value(s) pairs that were specified in the request (as this is a requirement of <code>AND</code> search logic).</p> </li> <li> <p> <code>OR</code> - The profile included in the response matched at least one of the search keys specified in the request. The <code>FoundByItems</code> will include each of the key-value(s) pairs that the profile was found by.</p> </li> </ul> <p>The <code>OR</code> relationship is the default behavior if the <code>LogicalOperator</code> parameter is not included in the <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_SearchProfiles.html\">SearchProfiles</a> request.</p>"}, "PartyTypeString": {"shape": "string1To255", "documentation": "<p>An alternative to PartyType which accepts any string as input.</p>"}, "GenderString": {"shape": "string1To255", "documentation": "<p>An alternative to Gender which accepts any string as input.</p>"}}, "documentation": "<p>The standard profile of a customer.</p>"}, "ProfileIdList": {"type": "list", "member": {"shape": "uuid"}}, "ProfileIdToBeMergedList": {"type": "list", "member": {"shape": "uuid"}, "max": 20, "min": 1}, "ProfileList": {"type": "list", "member": {"shape": "Profile"}}, "ProfileObjectList": {"type": "list", "member": {"shape": "ListProfileObjectsItem"}}, "ProfileObjectTypeList": {"type": "list", "member": {"shape": "ListProfileObjectTypeItem"}}, "ProfileObjectTypeTemplateList": {"type": "list", "member": {"shape": "ListProfileObjectTypeTemplateItem"}}, "Property": {"type": "string", "max": 2048, "pattern": ".+"}, "PutIntegrationRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "Uri": {"shape": "string1To255", "documentation": "<p>The URI of the S3 bucket or any other type of data source.</p>"}, "ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}, "FlowDefinition": {"shape": "FlowDefinition", "documentation": "<p>The configuration that controls how Customer Profiles retrieves data from the source.</p>"}, "ObjectTypeNames": {"shape": "ObjectTypeNames", "documentation": "<p>A map in which each key is an event type from an external application such as Segment or Shopify, and each value is an <code>ObjectTypeName</code> (template) used to ingest the event. It supports the following event types: <code>SegmentIdentify</code>, <code>ShopifyCreateCustomers</code>, <code>ShopifyUpdateCustomers</code>, <code>ShopifyCreateDraftOrders</code>, <code>ShopifyUpdateDraftOrders</code>, <code>ShopifyCreateOrders</code>, and <code>ShopifyUpdatedOrders</code>.</p>"}}}, "PutIntegrationResponse": {"type": "structure", "required": ["DomainName", "<PERSON><PERSON>", "CreatedAt", "LastUpdatedAt"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>"}, "Uri": {"shape": "string1To255", "documentation": "<p>The URI of the S3 bucket or any other type of data source.</p>"}, "ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>"}, "CreatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was created.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was most recently edited.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}, "ObjectTypeNames": {"shape": "ObjectTypeNames", "documentation": "<p>A map in which each key is an event type from an external application such as Segment or Shopify, and each value is an <code>ObjectTypeName</code> (template) used to ingest the event. It supports the following event types: <code>SegmentIdentify</code>, <code>ShopifyCreateCustomers</code>, <code>ShopifyUpdateCustomers</code>, <code>ShopifyCreateDraftOrders</code>, <code>ShopifyUpdateDraftOrders</code>, <code>ShopifyCreateOrders</code>, and <code>ShopifyUpdatedOrders</code>.</p>"}, "WorkflowId": {"shape": "string1To255", "documentation": "<p>Unique identifier for the workflow.</p>"}, "IsUnstructured": {"shape": "optionalBoolean", "documentation": "<p>Boolean that shows if the Flow that's associated with the Integration is created in Amazon Appflow, or with ObjectTypeName equals _unstructured via API/CLI in flowDefinition.</p>"}}}, "PutProfileObjectRequest": {"type": "structure", "required": ["ObjectTypeName", "Object", "DomainName"], "members": {"ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>"}, "Object": {"shape": "string<PERSON><PERSON><PERSON>", "documentation": "<p>A string that is serialized from a JSON object.</p>"}, "DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}}}, "PutProfileObjectResponse": {"type": "structure", "members": {"ProfileObjectUniqueKey": {"shape": "string1To255", "documentation": "<p>The unique identifier of the profile object generated by the service.</p>"}}}, "PutProfileObjectTypeRequest": {"type": "structure", "required": ["DomainName", "ObjectTypeName", "Description"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>", "location": "uri", "locationName": "ObjectTypeName"}, "Description": {"shape": "text", "documentation": "<p>Description of the profile object type.</p>"}, "TemplateId": {"shape": "name", "documentation": "<p>A unique identifier for the object template. For some attributes in the request, the service will use the default value from the object template when TemplateId is present. If these attributes are present in the request, the service may return a <code>BadRequestException</code>. These attributes include: AllowProfileCreation, SourceLastUpdatedTimestampFormat, Fields, and Keys. For example, if AllowProfileCreation is set to true when TemplateId is set, the service may return a <code>BadRequestException</code>.</p>"}, "ExpirationDays": {"shape": "expirationDaysInteger", "documentation": "<p>The number of days until the data in the object expires.</p>"}, "EncryptionKey": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The customer-provided key to encrypt the profile object that will be created in this profile object type.</p>"}, "AllowProfileCreation": {"shape": "boolean", "documentation": "<p>Indicates whether a profile should be created when data is received if one doesn’t exist for an object of this type. The default is <code>FALSE</code>. If the AllowProfileCreation flag is set to <code>FALSE</code>, then the service tries to fetch a standard profile and associate this object with the profile. If it is set to <code>TRUE</code>, and if no match is found, then the service creates a new standard profile.</p>"}, "SourceLastUpdatedTimestampFormat": {"shape": "string1To255", "documentation": "<p>The format of your <code>sourceLastUpdatedTimestamp</code> that was previously set up. </p>"}, "Fields": {"shape": "FieldMap", "documentation": "<p>A map of the name and ObjectType field.</p>"}, "Keys": {"shape": "KeyMap", "documentation": "<p>A list of unique keys that can be used to map data to the profile.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "PutProfileObjectTypeResponse": {"type": "structure", "required": ["ObjectTypeName", "Description"], "members": {"ObjectTypeName": {"shape": "typeName", "documentation": "<p>The name of the profile object type.</p>"}, "Description": {"shape": "text", "documentation": "<p>Description of the profile object type.</p>"}, "TemplateId": {"shape": "name", "documentation": "<p>A unique identifier for the object template.</p>"}, "ExpirationDays": {"shape": "expirationDaysInteger", "documentation": "<p>The number of days until the data in the object expires.</p>"}, "EncryptionKey": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The customer-provided key to encrypt the profile object that will be created in this profile object type.</p>"}, "AllowProfileCreation": {"shape": "boolean", "documentation": "<p>Indicates whether a profile should be created when data is received if one doesn’t exist for an object of this type. The default is <code>FALSE</code>. If the AllowProfileCreation flag is set to <code>FALSE</code>, then the service tries to fetch a standard profile and associate this object with the profile. If it is set to <code>TRUE</code>, and if no match is found, then the service creates a new standard profile.</p>"}, "SourceLastUpdatedTimestampFormat": {"shape": "string1To255", "documentation": "<p>The format of your <code>sourceLastUpdatedTimestamp</code> that was previously set up in fields that were parsed using <a href=\"https://docs.oracle.com/javase/10/docs/api/java/text/SimpleDateFormat.html\">SimpleDateFormat</a>. If you have <code>sourceLastUpdatedTimestamp</code> in your field, you must set up <code>sourceLastUpdatedTimestampFormat</code>.</p>"}, "Fields": {"shape": "FieldMap", "documentation": "<p>A map of the name and ObjectType field.</p>"}, "Keys": {"shape": "KeyMap", "documentation": "<p>A list of unique keys that can be used to map data to the profile.</p>"}, "CreatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was created.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was most recently edited.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "ResourceNotFoundException": {"type": "structure", "members": {"Message": {"shape": "message"}}, "documentation": "<p>The requested resource does not exist, or access was denied.</p>", "error": {"httpStatusCode": 404}, "exception": true}, "RoleArn": {"type": "string", "max": 512, "pattern": "arn:aws:iam:.*:[0-9]+:.*"}, "S3ConnectorOperator": {"type": "string", "enum": ["PROJECTION", "LESS_THAN", "GREATER_THAN", "BETWEEN", "LESS_THAN_OR_EQUAL_TO", "GREATER_THAN_OR_EQUAL_TO", "EQUAL_TO", "NOT_EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "S3ExportingConfig": {"type": "structure", "required": ["S3BucketName"], "members": {"S3BucketName": {"shape": "s3BucketName", "documentation": "<p>The name of the S3 bucket where Identity Resolution Jobs write result files.</p>"}, "S3KeyName": {"shape": "s3KeyNameCustomerOutputConfig", "documentation": "<p>The S3 key name of the location where Identity Resolution Jobs write result files.</p>"}}, "documentation": "<p>Configuration information about the S3 bucket where Identity Resolution Jobs write result files.</p>"}, "S3ExportingLocation": {"type": "structure", "members": {"S3BucketName": {"shape": "s3BucketName", "documentation": "<p>The name of the S3 bucket name where Identity Resolution Jobs write result files.</p>"}, "S3KeyName": {"shape": "s3KeyName", "documentation": "<p>The S3 key name of the location where Identity Resolution Jobs write result files.</p>"}}, "documentation": "<p>The S3 location where Identity Resolution Jobs write result files.</p>"}, "S3SourceProperties": {"type": "structure", "required": ["BucketName"], "members": {"BucketName": {"shape": "BucketName", "documentation": "<p>The Amazon S3 bucket name where the source files are stored.</p>"}, "BucketPrefix": {"shape": "BucketPrefix", "documentation": "<p>The object key for the Amazon S3 bucket in which the source files are stored.</p>"}}, "documentation": "<p>The properties that are applied when Amazon S3 is being used as the flow source.</p>"}, "SalesforceConnectorOperator": {"type": "string", "enum": ["PROJECTION", "LESS_THAN", "CONTAINS", "GREATER_THAN", "BETWEEN", "LESS_THAN_OR_EQUAL_TO", "GREATER_THAN_OR_EQUAL_TO", "EQUAL_TO", "NOT_EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "SalesforceSourceProperties": {"type": "structure", "required": ["Object"], "members": {"Object": {"shape": "Object", "documentation": "<p>The object specified in the Salesforce flow source.</p>"}, "EnableDynamicFieldUpdate": {"shape": "boolean", "documentation": "<p>The flag that enables dynamic fetching of new (recently added) fields in the Salesforce objects while running a flow.</p>"}, "IncludeDeletedRecords": {"shape": "boolean", "documentation": "<p>Indicates whether Amazon AppFlow includes deleted files in the flow run.</p>"}}, "documentation": "<p>The properties that are applied when Salesforce is being used as a source.</p>"}, "ScheduleExpression": {"type": "string", "max": 256, "pattern": ".*"}, "ScheduleOffset": {"type": "long", "max": 36000, "min": 0}, "ScheduledTriggerProperties": {"type": "structure", "required": ["ScheduleExpression"], "members": {"ScheduleExpression": {"shape": "ScheduleExpression", "documentation": "<p>The scheduling expression that determines the rate at which the schedule will run, for example rate (5 minutes).</p>"}, "DataPullMode": {"shape": "DataPullMode", "documentation": "<p>Specifies whether a scheduled flow has an incremental data transfer or a complete data transfer for each flow run.</p>"}, "ScheduleStartTime": {"shape": "Date", "documentation": "<p>Specifies the scheduled start time for a scheduled-trigger flow.</p>"}, "ScheduleEndTime": {"shape": "Date", "documentation": "<p>Specifies the scheduled end time for a scheduled-trigger flow.</p>"}, "Timezone": {"shape": "Timezone", "documentation": "<p>Specifies the time zone used when referring to the date and time of a scheduled-triggered flow, such as America/New_York.</p>"}, "ScheduleOffset": {"shape": "ScheduleOffset", "documentation": "<p>Specifies the optional offset that is added to the time interval for a schedule-triggered flow.</p>", "box": true}, "FirstExecutionFrom": {"shape": "Date", "documentation": "<p>Specifies the date range for the records to import from the connector in the first flow run.</p>"}}, "documentation": "<p>Specifies the configuration details of a scheduled-trigger flow that you define. Currently, these settings only apply to the scheduled-trigger type.</p>"}, "SearchProfilesRequest": {"type": "structure", "required": ["DomainName", "KeyName", "Values"], "members": {"NextToken": {"shape": "token", "documentation": "<p>The pagination token from the previous SearchProfiles API call.</p>", "location": "querystring", "locationName": "next-token"}, "MaxResults": {"shape": "maxSize100", "documentation": "<p>The maximum number of objects returned per page.</p> <p>The default is 20 if this parameter is not included in the request.</p>", "location": "querystring", "locationName": "max-results"}, "DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "KeyName": {"shape": "name", "documentation": "<p>A searchable identifier of a customer profile. The predefined keys you can use to search include: _account, _profileId, _assetId, _caseId, _orderId, _fullName, _phone, _email, _ctrContactId, _marketoLeadId, _salesforceAccountId, _salesforceContactId, _salesforceAssetId, _zendeskUserId, _zendeskExternalId, _zendeskTicketId, _serviceNowSystemId, _serviceNowIncidentId, _segmentUserId, _shopifyCustomerId, _shopifyOrderId.</p>"}, "Values": {"shape": "requestValueList", "documentation": "<p>A list of key values.</p>"}, "AdditionalSearchKeys": {"shape": "additionalSearchKeysList", "documentation": "<p>A list of <code>AdditionalSearchKey</code> objects that are each searchable identifiers of a profile. Each <code>AdditionalSearchKey</code> object contains a <code>KeyName</code> and a list of <code>Values</code> associated with that specific key (i.e., a key-value(s) pair). These additional search keys will be used in conjunction with the <code>LogicalOperator</code> and the required <code>KeyName</code> and <code>Values</code> parameters to search for profiles that satisfy the search criteria. </p>"}, "LogicalOperator": {"shape": "logicalOperator", "documentation": "<p>Relationship between all specified search keys that will be used to search for profiles. This includes the required <code>KeyName</code> and <code>Values</code> parameters as well as any key-value(s) pairs specified in the <code>AdditionalSearchKeys</code> list.</p> <p>This parameter influences which profiles will be returned in the response in the following manner:</p> <ul> <li> <p> <code>AND</code> - The response only includes profiles that match all of the search keys.</p> </li> <li> <p> <code>OR</code> - The response includes profiles that match at least one of the search keys.</p> </li> </ul> <p>The <code>OR</code> relationship is the default behavior if this parameter is not included in the request.</p>"}}}, "SearchProfilesResponse": {"type": "structure", "members": {"Items": {"shape": "ProfileList", "documentation": "<p>The list of Profiles matching the search criteria.</p>"}, "NextToken": {"shape": "token", "documentation": "<p>The pagination token from the previous SearchProfiles API call.</p>"}}}, "ServiceNowConnectorOperator": {"type": "string", "enum": ["PROJECTION", "CONTAINS", "LESS_THAN", "GREATER_THAN", "BETWEEN", "LESS_THAN_OR_EQUAL_TO", "GREATER_THAN_OR_EQUAL_TO", "EQUAL_TO", "NOT_EQUAL_TO", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "ServiceNowSourceProperties": {"type": "structure", "required": ["Object"], "members": {"Object": {"shape": "Object", "documentation": "<p>The object specified in the ServiceNow flow source.</p>"}}, "documentation": "<p>The properties that are applied when ServiceNow is being used as a source.</p>"}, "SourceConnectorProperties": {"type": "structure", "members": {"Marketo": {"shape": "MarketoSourceProperties", "documentation": "<p>The properties that are applied when Marketo is being used as a source.</p>"}, "S3": {"shape": "S3SourceProperties", "documentation": "<p>The properties that are applied when Amazon S3 is being used as the flow source.</p>"}, "Salesforce": {"shape": "SalesforceSourceProperties", "documentation": "<p>The properties that are applied when Salesforce is being used as a source.</p>"}, "ServiceNow": {"shape": "ServiceNowSourceProperties", "documentation": "<p>The properties that are applied when ServiceNow is being used as a source.</p>"}, "Zendesk": {"shape": "ZendeskSourceProperties", "documentation": "<p>The properties that are applied when using Zendesk as a flow source.</p>"}}, "documentation": "<p>Specifies the information that is required to query a particular Amazon AppFlow connector. Customer Profiles supports Salesforce, Zendesk, Marketo, ServiceNow and Amazon S3.</p>"}, "SourceConnectorType": {"type": "string", "enum": ["Salesforce", "Marketo", "Zendesk", "Servicenow", "S3"]}, "SourceFields": {"type": "list", "member": {"shape": "stringTo2048"}}, "SourceFlowConfig": {"type": "structure", "required": ["ConnectorType", "SourceConnectorProperties"], "members": {"ConnectorProfileName": {"shape": "ConnectorProfileName", "documentation": "<p>The name of the AppFlow connector profile. This name must be unique for each connector profile in the AWS account.</p>"}, "ConnectorType": {"shape": "SourceConnectorType", "documentation": "<p>The type of connector, such as Salesforce, Marketo, and so on.</p>"}, "IncrementalPullConfig": {"shape": "IncrementalPullConfig", "documentation": "<p>Defines the configuration for a scheduled incremental data pull. If a valid configuration is provided, the fields specified in the configuration are used when querying for the incremental data pull.</p>"}, "SourceConnectorProperties": {"shape": "SourceConnectorProperties", "documentation": "<p>Specifies the information that is required to query a particular source connector.</p>"}}, "documentation": "<p>Contains information about the configuration of the source connector used in the flow.</p>"}, "StandardIdentifier": {"type": "string", "enum": ["PROFILE", "ASSET", "CASE", "UNIQUE", "SECONDARY", "LOOKUP_ONLY", "NEW_ONLY", "ORDER"]}, "StandardIdentifierList": {"type": "list", "member": {"shape": "StandardIdentifier"}}, "Status": {"type": "string", "enum": ["NOT_STARTED", "IN_PROGRESS", "COMPLETE", "FAILED", "SPLIT", "RETRY", "CANCELLED"]}, "TagArn": {"type": "string", "max": 256, "pattern": "^arn:[a-z0-9]{1,10}:profile"}, "TagKey": {"type": "string", "max": 128, "min": 1, "pattern": "^(?!aws:)[a-zA-Z+-=._:/]+$"}, "TagKeyList": {"type": "list", "member": {"shape": "TagKey"}, "max": 50, "min": 1}, "TagMap": {"type": "map", "key": {"shape": "TagKey"}, "value": {"shape": "TagValue"}, "max": 50, "min": 1}, "TagResourceRequest": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"shape": "TagArn", "documentation": "<p>The ARN of the resource that you're adding tags to.</p>", "location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "TagResourceResponse": {"type": "structure", "members": {}}, "TagValue": {"type": "string", "max": 256}, "Task": {"type": "structure", "required": ["SourceFields", "TaskType"], "members": {"ConnectorOperator": {"shape": "ConnectorOperator", "documentation": "<p>The operation to be performed on the provided source fields.</p>"}, "DestinationField": {"shape": "DestinationField", "documentation": "<p>A field in a destination connector, or a field value against which Amazon AppFlow validates a source field.</p>"}, "SourceFields": {"shape": "SourceFields", "documentation": "<p>The source fields to which a particular task is applied.</p>"}, "TaskProperties": {"shape": "TaskPropertiesMap", "documentation": "<p>A map used to store task-related information. The service looks for particular information based on the TaskType.</p>"}, "TaskType": {"shape": "TaskType", "documentation": "<p>Specifies the particular task implementation that Amazon AppFlow performs.</p>"}}, "documentation": "<p>A class for modeling different type of tasks. Task implementation varies based on the TaskType.</p>"}, "TaskPropertiesMap": {"type": "map", "key": {"shape": "OperatorPropertiesKeys"}, "value": {"shape": "Property"}}, "TaskType": {"type": "string", "enum": ["Arithmetic", "Filter", "Map", "Mask", "<PERSON><PERSON>", "Truncate", "Validate"]}, "Tasks": {"type": "list", "member": {"shape": "Task"}}, "ThrottlingException": {"type": "structure", "members": {"Message": {"shape": "message"}}, "documentation": "<p>You exceeded the maximum number of requests.</p>", "error": {"httpStatusCode": 429}, "exception": true}, "Timezone": {"type": "string", "max": 256, "pattern": ".*"}, "TriggerConfig": {"type": "structure", "required": ["TriggerType"], "members": {"TriggerType": {"shape": "TriggerType", "documentation": "<p>Specifies the type of flow trigger. It can be OnDemand, Scheduled, or Event.</p>"}, "TriggerProperties": {"shape": "TriggerProperties", "documentation": "<p>Specifies the configuration details of a schedule-triggered flow that you define. Currently, these settings only apply to the Scheduled trigger type.</p>"}}, "documentation": "<p>The trigger settings that determine how and when Amazon AppFlow runs the specified flow.</p>"}, "TriggerProperties": {"type": "structure", "members": {"Scheduled": {"shape": "ScheduledTriggerProperties", "documentation": "<p>Specifies the configuration details of a schedule-triggered flow that you define.</p>"}}, "documentation": "<p>Specifies the configuration details that control the trigger for a flow. Currently, these settings only apply to the Scheduled trigger type.</p>"}, "TriggerType": {"type": "string", "enum": ["Scheduled", "Event", "OnDemand"]}, "UntagResourceRequest": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"shape": "TagArn", "documentation": "<p>The ARN of the resource from which you are removing tags.</p>", "location": "uri", "locationName": "resourceArn"}, "tagKeys": {"shape": "TagKeyList", "documentation": "<p>The list of tag keys to remove from the resource.</p>", "location": "querystring", "locationName": "tagKeys"}}}, "UntagResourceResponse": {"type": "structure", "members": {}}, "UpdateAddress": {"type": "structure", "members": {"Address1": {"shape": "string0To255", "documentation": "<p>The first line of a customer address.</p>"}, "Address2": {"shape": "string0To255", "documentation": "<p>The second line of a customer address.</p>"}, "Address3": {"shape": "string0To255", "documentation": "<p>The third line of a customer address.</p>"}, "Address4": {"shape": "string0To255", "documentation": "<p>The fourth line of a customer address.</p>"}, "City": {"shape": "string0To255", "documentation": "<p>The city in which a customer lives.</p>"}, "County": {"shape": "string0To255", "documentation": "<p>The county in which a customer lives.</p>"}, "State": {"shape": "string0To255", "documentation": "<p>The state in which a customer lives.</p>"}, "Province": {"shape": "string0To255", "documentation": "<p>The province in which a customer lives.</p>"}, "Country": {"shape": "string0To255", "documentation": "<p>The country in which a customer lives.</p>"}, "PostalCode": {"shape": "string0To255", "documentation": "<p>The postal code of a customer address.</p>"}}, "documentation": "<p>Updates associated with the address properties of a customer profile.</p>"}, "UpdateAttributes": {"type": "map", "key": {"shape": "string1To255"}, "value": {"shape": "string0To255"}}, "UpdateDomainRequest": {"type": "structure", "required": ["DomainName"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "DefaultExpirationDays": {"shape": "expirationDaysInteger", "documentation": "<p>The default number of days until the data within the domain expires.</p>"}, "DefaultEncryptionKey": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The default encryption key, which is an AWS managed key, is used when no specific type of encryption key is specified. It is used to encrypt all data before it is placed in permanent or semi-permanent storage. If specified as an empty string, it will clear any existing value.</p>"}, "DeadLetterQueueUrl": {"shape": "sqsQueueUrl", "documentation": "<p>The URL of the SQS dead letter queue, which is used for reporting errors associated with ingesting data from third party applications. If specified as an empty string, it will clear any existing value. You must set up a policy on the DeadLetterQueue for the SendMessage operation to enable Amazon Connect Customer Profiles to send messages to the DeadLetterQueue.</p>"}, "Matching": {"shape": "MatchingRequest", "documentation": "<p>The process of matching duplicate profiles. If <code>Matching</code> = <code>true</code>, Amazon Connect Customer Profiles starts a weekly batch process called Identity Resolution Job. If you do not specify a date and time for Identity Resolution Job to run, by default it runs every Saturday at 12AM UTC to detect duplicate profiles in your domains. </p> <p>After the Identity Resolution Job completes, use the <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_GetMatches.html\">GetMatches</a> API to return and review the results. Or, if you have configured <code>ExportingConfig</code> in the <code>MatchingRequest</code>, you can download the results from S3.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "UpdateDomainResponse": {"type": "structure", "required": ["DomainName", "CreatedAt", "LastUpdatedAt"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>"}, "DefaultExpirationDays": {"shape": "expirationDaysInteger", "documentation": "<p>The default number of days until the data within the domain expires.</p>"}, "DefaultEncryptionKey": {"shape": "<PERSON><PERSON><PERSON>", "documentation": "<p>The default encryption key, which is an AWS managed key, is used when no specific type of encryption key is specified. It is used to encrypt all data before it is placed in permanent or semi-permanent storage.</p>"}, "DeadLetterQueueUrl": {"shape": "sqsQueueUrl", "documentation": "<p>The URL of the SQS dead letter queue, which is used for reporting errors associated with ingesting data from third party applications.</p>"}, "Matching": {"shape": "MatchingResponse", "documentation": "<p>The process of matching duplicate profiles. If <code>Matching</code> = <code>true</code>, Amazon Connect Customer Profiles starts a weekly batch process called Identity Resolution Job. If you do not specify a date and time for Identity Resolution Job to run, by default it runs every Saturday at 12AM UTC to detect duplicate profiles in your domains. </p> <p>After the Identity Resolution Job completes, use the <a href=\"https://docs.aws.amazon.com/customerprofiles/latest/APIReference/API_GetMatches.html\">GetMatches</a> API to return and review the results. Or, if you have configured <code>ExportingConfig</code> in the <code>MatchingRequest</code>, you can download the results from S3.</p>"}, "CreatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was created.</p>"}, "LastUpdatedAt": {"shape": "timestamp", "documentation": "<p>The timestamp of when the domain was most recently edited.</p>"}, "Tags": {"shape": "TagMap", "documentation": "<p>The tags used to organize, track, or control access for this resource.</p>"}}}, "UpdateProfileRequest": {"type": "structure", "required": ["DomainName", "ProfileId"], "members": {"DomainName": {"shape": "name", "documentation": "<p>The unique name of the domain.</p>", "location": "uri", "locationName": "DomainName"}, "ProfileId": {"shape": "uuid", "documentation": "<p>The unique identifier of a customer profile.</p>"}, "AdditionalInformation": {"shape": "string0To1000", "documentation": "<p>Any additional information relevant to the customer’s profile.</p>"}, "AccountNumber": {"shape": "string0To255", "documentation": "<p>A unique account number that you have given to the customer.</p>"}, "PartyType": {"shape": "PartyType", "documentation": "<p>The type of profile used to describe the customer.</p>"}, "BusinessName": {"shape": "string0To255", "documentation": "<p>The name of the customer’s business.</p>"}, "FirstName": {"shape": "string0To255", "documentation": "<p>The customer’s first name.</p>"}, "MiddleName": {"shape": "string0To255", "documentation": "<p>The customer’s middle name.</p>"}, "LastName": {"shape": "string0To255", "documentation": "<p>The customer’s last name.</p>"}, "BirthDate": {"shape": "string0To255", "documentation": "<p>The customer’s birth date. </p>"}, "Gender": {"shape": "Gender", "documentation": "<p>The gender with which the customer identifies. </p>"}, "PhoneNumber": {"shape": "string0To255", "documentation": "<p>The customer’s phone number, which has not been specified as a mobile, home, or business number. </p>"}, "MobilePhoneNumber": {"shape": "string0To255", "documentation": "<p>The customer’s mobile phone number.</p>"}, "HomePhoneNumber": {"shape": "string0To255", "documentation": "<p>The customer’s home phone number.</p>"}, "BusinessPhoneNumber": {"shape": "string0To255", "documentation": "<p>The customer’s business phone number.</p>"}, "EmailAddress": {"shape": "string0To255", "documentation": "<p>The customer’s email address, which has not been specified as a personal or business address. </p>"}, "PersonalEmailAddress": {"shape": "string0To255", "documentation": "<p>The customer’s personal email address.</p>"}, "BusinessEmailAddress": {"shape": "string0To255", "documentation": "<p>The customer’s business email address.</p>"}, "Address": {"shape": "Update<PERSON><PERSON><PERSON>", "documentation": "<p>A generic address associated with the customer that is not mailing, shipping, or billing.</p>"}, "ShippingAddress": {"shape": "Update<PERSON><PERSON><PERSON>", "documentation": "<p>The customer’s shipping address.</p>"}, "MailingAddress": {"shape": "Update<PERSON><PERSON><PERSON>", "documentation": "<p>The customer’s mailing address.</p>"}, "BillingAddress": {"shape": "Update<PERSON><PERSON><PERSON>", "documentation": "<p>The customer’s billing address.</p>"}, "Attributes": {"shape": "UpdateAttributes", "documentation": "<p>A key value pair of attributes of a customer profile.</p>"}, "PartyTypeString": {"shape": "string0To255", "documentation": "<p>An alternative to <code>PartyType</code> which accepts any string as input.</p>"}, "GenderString": {"shape": "string0To255", "documentation": "<p>An alternative to <code>Gender</code> which accepts any string as input.</p>"}}}, "UpdateProfileResponse": {"type": "structure", "required": ["ProfileId"], "members": {"ProfileId": {"shape": "uuid", "documentation": "<p>The unique identifier of a customer profile.</p>"}}}, "WorkflowAttributes": {"type": "structure", "members": {"AppflowIntegration": {"shape": "AppflowIntegrationWorkflowAttributes", "documentation": "<p>Workflow attributes specific to <code>APPFLOW_INTEGRATION</code> workflow.</p>"}}, "documentation": "<p>Structure to hold workflow attributes.</p>"}, "WorkflowList": {"type": "list", "member": {"shape": "ListWorkflowsItem"}}, "WorkflowMetrics": {"type": "structure", "members": {"AppflowIntegration": {"shape": "AppflowIntegrationWorkflowMetrics", "documentation": "<p>Workflow execution metrics for <code>APPFLOW_INTEGRATION</code> workflow.</p>"}}, "documentation": "<p>Generic object containing workflow execution metrics.</p>"}, "WorkflowStepItem": {"type": "structure", "members": {"AppflowIntegration": {"shape": "AppflowIntegrationWorkflowStep", "documentation": "<p>Workflow step information specific to <code>APPFLOW_INTEGRATION</code> workflow.</p>"}}, "documentation": "<p>List containing steps in workflow.</p>"}, "WorkflowStepsList": {"type": "list", "member": {"shape": "WorkflowStepItem"}}, "WorkflowType": {"type": "string", "enum": ["APPFLOW_INTEGRATION"]}, "ZendeskConnectorOperator": {"type": "string", "enum": ["PROJECTION", "GREATER_THAN", "ADDITION", "MULTIPLICATION", "DIVISION", "SUBTRACTION", "MASK_ALL", "MASK_FIRST_N", "MASK_LAST_N", "VALIDATE_NON_NULL", "VALIDATE_NON_ZERO", "VALIDATE_NON_NEGATIVE", "VALIDATE_NUMERIC", "NO_OP"]}, "ZendeskSourceProperties": {"type": "structure", "required": ["Object"], "members": {"Object": {"shape": "Object", "documentation": "<p>The object specified in the Zendesk flow source.</p>"}}, "documentation": "<p>The properties that are applied when using Zendesk as a flow source.</p>"}, "additionalSearchKeysList": {"type": "list", "member": {"shape": "Additional<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "max": 4, "min": 1}, "boolean": {"type": "boolean"}, "encryptionKey": {"type": "string", "max": 255, "min": 0}, "expirationDaysInteger": {"type": "integer", "max": 1098, "min": 1}, "foundByList": {"type": "list", "member": {"shape": "FoundByKeyValue"}, "max": 5, "min": 1}, "logicalOperator": {"type": "string", "enum": ["AND", "OR"]}, "long": {"type": "long"}, "matchesNumber": {"type": "integer", "min": 0}, "maxSize100": {"type": "integer", "max": 100, "min": 1}, "message": {"type": "string"}, "optionalBoolean": {"type": "boolean"}, "requestValueList": {"type": "list", "member": {"shape": "string1To255"}}, "s3BucketName": {"type": "string", "max": 63, "min": 3, "pattern": "^[a-z0-9.-]+$"}, "s3KeyName": {"type": "string", "max": 1024, "min": 1, "pattern": ".*"}, "s3KeyNameCustomerOutputConfig": {"type": "string", "max": 800, "min": 1, "pattern": ".*"}, "sqsQueueUrl": {"type": "string", "max": 255, "min": 0}, "string0To1000": {"type": "string", "max": 1000, "min": 0}, "string0To255": {"type": "string", "max": 255, "min": 0}, "string1To1000": {"type": "string", "max": 1000, "min": 1}, "string1To255": {"type": "string", "max": 255, "min": 1}, "stringTo2048": {"type": "string", "max": 2048, "pattern": ".*"}, "stringifiedJson": {"type": "string", "max": 256000, "min": 1}, "text": {"type": "string", "max": 1000, "min": 1}, "timestamp": {"type": "timestamp"}, "token": {"type": "string", "max": 1024, "min": 1}, "typeName": {"type": "string", "max": 255, "min": 1, "pattern": "^[a-zA-Z_][a-zA-Z_0-9-]*$"}, "uuid": {"type": "string", "pattern": "[a-f0-9]{32}"}}, "documentation": "<fullname>Amazon Connect Customer Profiles</fullname> <p>Amazon Connect Customer Profiles is a unified customer profile for your contact center that has pre-built connectors powered by AppFlow that make it easy to combine customer information from third party applications, such as Salesforce (CRM), ServiceNow (ITSM), and your enterprise resource planning (ERP), with contact history from your Amazon Connect contact center. If you're new to Amazon Connect, you might find it helpful to review the <a href=\"https://docs.aws.amazon.com/connect/latest/adminguide/\">Amazon Connect Administrator Guide</a>.</p>"}