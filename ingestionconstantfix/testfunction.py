def bound_score(scr: float, min_score: float = 0.0, max_score: float = 1.0) -> float:
    """
    Bound the score within the specified minimum and maximum values.

    Parameters:
    - scr (float): The score to be bounded.
    - min_score (float): The minimum score allowed (default is 0.0).
    - max_score (float): The maximum score allowed (default is 1.0).

    Returns:
    - float: The bounded score.
    """
    return max(min(scr, max_score), min_score)

def delivery_fn(destination_delay_minutes: int) -> float:
    """
    Calculate a score based on the destination delay in minutes.
    For this example, we will return a fixed score of 0.5.

    Parameters:
    - destination_delay_minutes (int): The delay in minutes.

    Returns:
    - float: A fixed score of 0.5.
    """
    return 0.5  # Fixed score

# Example shipment dictionary with a large delay
shipment = {
    "destinationDelayMinutes": 6518  # Example delay in minutes
}

# Calculate the score using delivery_fn and bound it using bound_score
otd_score = bound_score(delivery_fn(shipment["destinationDelayMinutes"]))

# Print the bounded score
print(f"The bounded on-time delivery score is: {otd_score}")