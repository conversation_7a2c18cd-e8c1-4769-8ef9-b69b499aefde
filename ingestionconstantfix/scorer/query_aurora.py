import sys
from os import path
from os.path import dirname as pdir

import boto3

# Ensure imports work from root level directory.

sys.path.append(pdir(pdir(pdir(pdir(path.abspath(__file__))))))
from common import credentials

CREDENTIALS = credentials.Credentials()
secret_arn = CREDENTIALS.rds_secret_arn
cluster_arn = "arn:aws:rds:us-east-1:903881895532:cluster:mvp-db-cluster"


def query_chunked(query: str, database_name: str, chunk_size: int = 1000) -> dict:
    """
    Query RDS, using stored credentials.
    TODO: Refactor to use aurora data API for speed?
    TODO: Evaluate chunk size

    :param wheres: Where causes for sql query.
    :return: Dictionary of boto3 client response containing SQL query's results.
    """
    client = boto3.client("rds-data")

    start_idx = 0
    response_size = chunk_size
    response_all = {}
    while response_size >= chunk_size:
        query_text = query + f" LIMIT {start_idx},{chunk_size};"
        include_metadata = start_idx == 0
        response = client.execute_statement(
            resourceArn=cluster_arn,
            database=database_name,
            secretArn=secret_arn,
            sql=query_text,
            includeResultMetadata=include_metadata,
        )

        if start_idx == 0:
            response_all = response
        else:
            response_all["records"] += response["records"]

        response_size = len(response["records"])
        start_idx += chunk_size

    return response_all
