import datetime
import typing
import unittest

import pandas as pd
import scorer.util as util
from parameterized import parameterized


class TestUtil(unittest.TestCase):
    @parameterized.expand(
        [
            [0.5, 0.5],  # case when min_score < score < max_score
            [0, 0],  # case when score = min_score
            [1, 1],  # case when score = max_score
            [-1, 0],  # edge case when score < min_score
            [1.1, 1],  # edge case when score > max_score
            [2, 2, 2, 1],  # edge case when min_score > max_score and score = min_score
        ]
    )
    def test_bound_score(
        self,
        scr: float,
        bound_score_output: float,
        min_score: float = 0.0,
        max_score: float = 1.0,
    ):
        """Tests bound_score function."""
        self.assertEqual(
            util.bound_score(scr, min_score, max_score), bound_score_output
        )

    def test_edge_case_bound_score(self):
        self.assertTrue(pd.isna(util.bound_score(None)))  # edge case when score is None

    @parameterized.expand(
        [
            [(0, 0), 0],  # case when scores are 0 and weights are 1
            [(0, 1), 0.5],  # case when single score is 1 and weights are 1
            [(1, 3, 5), 3],  # case when three scores and weights are 1
            [
                (1, pd.NA, 5),
                3,
            ],  # case when one of the scores is pd.NA and weights are 1
            [
                (1, pd.NA, 5),
                11 / 3,
                (1, 3, 2),
            ],  # case when one of the scores is pd.NA with specific weights
        ]
    )
    def test_weighted_score(
        self, scores: tuple, weighted_score_output: float, weights: tuple = None
    ):
        """Tests weighted_score function."""
        self.assertEqual(util.weighted_score(scores, weights), weighted_score_output)

    def test_edge_case_weighted_score(self):
        self.assertTrue(
            pd.isna(util.weighted_score((1, 3), (0, 0)))
        )  # edge case when weights are all 0
        self.assertTrue(
            pd.isna(util.weighted_score((pd.NA, pd.NA, pd.NA)))
        )  # edge case when scores are all pd.NA

    @parameterized.expand(
        [
            [
                "2019-03-19T13:48:00",
                datetime.datetime(2019, 3, 19, 13, 48, 0, 0),
            ],  # case with normal time string
            [
                "2019-03-19T13:48:00.0",
                datetime.datetime(2019, 3, 19, 13, 48, 0, 0),
            ],  # case with normal time string including microsceond
            [
                "2019-03-19T13:48:00.5",
                datetime.datetime(2019, 3, 19, 13, 48, 0, 500000),
            ],  # case with normal time string including specific microseconds
        ]
    )
    def test_parse_time_string(self, time_str: str, time_str_output: datetime.datetime):
        """Ensure parsed string returns the correct datetime output"""
        self.assertEqual(util.parse_time_string(time_str), time_str_output)

    def test_edge_case_parse_time_string(self):
        self.assertTrue(
            pd.isna(util.parse_time_string(None))
        )  # edge case when input is None
        self.assertTrue(
            pd.isna(util.parse_time_string(32))
        )  # edge case where input is not a string

    @parameterized.expand(
        [
            [
                datetime.datetime(2020, 1, 1, 0, 0, 0),
                datetime.datetime(2019, 12, 31, 18, 0, 0),
            ],  # case with normal timestamp
            [
                datetime.datetime(2022, 3, 13, 7, 59, 59),
                datetime.datetime(2022, 3, 13, 1, 59, 59),
            ],  # case with normal timestamp (just before it's about to switch from cst to cdt)
            [
                datetime.datetime(2022, 3, 13, 8, 0, 0),
                datetime.datetime(2022, 3, 13, 3, 0, 0),
            ],  # edge case when cdt (just switched from cst to cdt)
        ]
    )
    def test_localize_utc_to_central(
        self, utc_time: datetime.datetime, central_time: datetime.datetime
    ):
        """Ensure utc_time correctly converts to central time"""
        self.assertEqual(util.localize_utc_to_central(utc_time), central_time)

    @parameterized.expand(
        [
            [
                datetime.datetime(2019, 12, 31, 16, 0, 0),
                7,
                17,
                datetime.datetime(2019, 12, 31, 16, 0, 0),
            ],  # case when day is after working_day_start_hour and before working_day_end_hour
            [
                datetime.datetime(2019, 12, 31, 23, 0, 0),
                7,
                17,
                datetime.datetime(2019, 12, 31, 17, 0, 0),
            ],  # case when day is after working_day_end
            [
                datetime.datetime(2019, 12, 31, 23, 59, 59),
                7,
                17,
                datetime.datetime(2019, 12, 31, 17, 0, 0),
            ],  # case when day is just before the next day and clips correctly
            [
                datetime.datetime(2020, 1, 1, 0, 0, 0),
                7,
                17,
                datetime.datetime(2020, 1, 1, 7, 0, 0),
            ],  # case when day just turned next day and clips correctly
        ]
    )
    def test_clip(
        self,
        day: datetime.datetime,
        working_day_start_hour: int,
        working_day_end_hour: int,
        day_output: datetime.datetime,
    ):
        """Ensure clip function returns the correct time"""
        self.assertEqual(
            util.clip(day, working_day_start_hour, working_day_end_hour), day_output
        )

    @parameterized.expand(
        [
            [
                datetime.datetime(2019, 12, 31, 7, 0, 0),
                datetime.datetime(2019, 12, 31, 17, 0, 0),
                7,
                17,
                10.0,
            ],  # case with working hours exactly between working_day_start_hour and working_day_end_hour
            [
                datetime.datetime(2019, 12, 30, 7, 0, 0),
                datetime.datetime(2019, 12, 31, 7, 0, 0),
                7,
                17,
                10.0,
            ],  # case with working hours of 24 hours
            [
                datetime.datetime(2019, 12, 30, 7, 0, 0),
                datetime.datetime(2019, 12, 31, 17, 0, 0),
                7,
                17,
                20.0,
            ],  # case with working hours of 48 hours
            [
                datetime.datetime(2019, 12, 31, 2, 0, 0),
                datetime.datetime(2019, 12, 31, 9, 0, 0),
                7,
                17,
                2.0,
            ],  # case when start < working_day_start_hour < end
            [
                datetime.datetime(2022, 3, 12, 8, 0, 0),
                datetime.datetime(2022, 3, 13, 8, 0, 0),
                7,
                17,
                0.0,
            ],  # case when start and end is in the weekend
            [
                datetime.datetime(2022, 3, 11, 18, 0, 0),
                datetime.datetime(2022, 3, 14, 7, 0, 0),
                9,
                19,
                1.0,
            ],  # case when there is a weekend between start and end
        ]
    )
    def test_working_hours_between(
        self,
        start: datetime.datetime,
        end: datetime.datetime,
        working_day_start_hour: int,
        working_day_end_hour: int,
        hours: float,
    ):
        """Ensure working_hours_between returns the corrects hours worked"""
        self.assertEqual(
            util.working_hours_between(
                start, end, working_day_start_hour, working_day_end_hour
            ),
            hours,
        )

    @parameterized.expand(
        [
            [
                {
                    "carrierAssignedTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                    "originCloseTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                },
                0.0,
            ],  # case when carrierAssignedTime = originCloseTime
            [
                {
                    "carrierAssignedTime": datetime.datetime(2023, 2, 14, 13, 0, 0),
                    "originCloseTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                },
                0.0,
            ],  # edge case when carrierAssignedTime is after originCloseTime
            [
                {
                    "carrierAssignedTime": datetime.datetime(2023, 2, 13, 14, 0, 0),
                    "originCloseTime": datetime.datetime(2023, 2, 14, 19, 0, 0),
                },
                15.0,
            ],  # case when timestamps in CST
            [
                {
                    "carrierAssignedTime": datetime.datetime(2023, 3, 10, 14, 0, 0),
                    "originCloseTime": datetime.datetime(2023, 3, 13, 19, 0, 0),
                },
                16.0,
            ],  # edge case when timestamp switches from CST to CDT
        ]
    )
    def test_calculate_prebook(self, shipment: dict, hours: float):
        """Ensure prebook is calculated correctly, given carrier assigned and origin close."""
        self.assertEqual(util.calculate_prebook(shipment), hours)

    def test_edge_case_calculate_prebook(self):
        self.assertTrue(
            pd.isna(util.calculate_prebook({}))
        )  # edge case when shipment is None
        self.assertTrue(
            pd.isna(util.calculate_prebook({"carrierAssignedTime": pd.NA}))
        )  # edge case when carrier assign is pd.NA

    def test_normalize_datetime(self):
        """Tests normalize_datetime function"""
        self.assertTrue(pd.isna(util.normalize_datetime(pd.NA)))

    @parameterized.expand(
        [
            [
                {
                    "originArrivalTime": pd.NaT,
                    "originCloseTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                    "originDelayMinutes": pd.NaT,
                },
                {},
                "originArrivalTime",
                "originCloseTime",
                "originDelayMinutes",
                (pd.NaT, {}),
            ],  # case when arrival time is pd.NA/pd.NaT
            [
                {
                    "originArrivalTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                    "originCloseTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                    "originDelayMinutes": pd.NaT,
                },
                {"originDelayMinutes": 5},
                "originArrivalTime",
                "originCloseTime",
                "originDelayMinutes",
                (0, {"originDelayMinutes": 5}),
            ],  # case when shipment[delay_col] is pd.NA/pd.NaT
            [
                {
                    "originArrivalTime": datetime.datetime(2019, 12, 31, 17, 57, 0),
                    "originCloseTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                    "originDelayMinutes": 0,
                },
                {"originDelayMinutes": 0},
                "originArrivalTime",
                "originCloseTime",
                "originDelayMinutes",
                (0, {"originDelayMinutes": -3}),
            ],  # case when arrival time is earlier than close time
            [
                {
                    "originArrivalTime": datetime.datetime(2019, 12, 31, 18, 3, 0),
                    "originCloseTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                    "originDelayMinutes": 4,
                },
                {"originDelayMinutes": 4},
                "originArrivalTime",
                "originCloseTime",
                "originDelayMinutes",
                (4, {"originDelayMinutes": 3}),
            ],  # case when shipment delay was already calculated and proposed delay is less
        ]
    )
    def test_calculate_delay(
        self,
        shipment: dict,
        pending_update: dict,
        arrival_col: str,
        close_col: str,
        delay_col: str,
        delay_output: typing.Tuple[int, dict],
    ):
        """Ensure delay is calculated correctly."""
        self.assertEqual(
            util.calculate_delay(
                shipment, pending_update, arrival_col, close_col, delay_col
            ),
            delay_output,
        )


if __name__ == "__main__":
    unittest.main()
