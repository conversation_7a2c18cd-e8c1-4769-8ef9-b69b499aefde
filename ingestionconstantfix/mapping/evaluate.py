import pandas as pd
from models import edit_distance
from models import tfidf_cos
from tqdm import tqdm


def evaluate(model, zipped_data, top_k=1):
    X, y = zip(*zipped_data)
    y_categories = sorted(set(y))
    num_true = 0
    for i, data in tqdm(enumerate(zipped_data), total=len(zipped_data)):
        # Tuples of distances and categories.
        distances = ((model(data[0], y_i), y_i) for y_i in y_categories)
        for dist in sorted(distances)[:top_k]:
            if dist[1] == data[1]:
                num_true += 1
    return num_true / (i + 1)


if __name__ == "__main__":
    df = pd.read_csv("evaluation_data.csv", header=None)
    zipped_data = list(df[[0, 1]].value_counts().keys())

    model = edit_distance.EditDistance()
    acc = evaluate(model.get_model(), zipped_data)
    print(f"Edit Distance has {round(acc * 100)}% accuracy")
    model = tfidf_cos.TfIdfCos()
    acc = evaluate(model.get_model(), zipped_data)
    print(f"TfIdf Cos has {round(acc * 100)}% accuracy")
