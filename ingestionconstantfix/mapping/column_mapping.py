import enum

from mapping.models import edit_distance


class GoldenColumns(enum.Enum):
    broker_name = "Broker Name"
    customer_name = "Customer Name"
    business_unit = "Business Unit"
    broker_primary_reference = "Broker Primary Reference"
    shipper_primary_reference = "Shipper Primary Reference"
    shipment_mode = "Shipment Mode"
    equipment_type = "Equipment Type"
    origin_name = "Origin Name"
    destination_name = "Destination Name"
    origin_city = "Origin City"
    destination_city = "Destination City"
    origin_state = "Origin State"
    destination_state = "Destination State"
    origin_zip = "Origin Zip"
    destination_zip = "Destination Zip"
    origin_country = "Origin Country"
    destination_country = "Destination Country"
    shipment_status = "Shipment Status"
    shipment_rank = "Shipment Rank"
    weight = "Weight"
    origin_open = "Origin Open"
    destination_open = "Destination Open"
    origin_close = "Origin Close"
    destination_close = "Destination Close"
    origin_arrival = "Origin Arrival"
    destination_arrival = "Destination Arrival"
    origin_departure = "Origin Departure"
    destination_departure = "Destination Departure"
    stop_count = "Stop Count"
    distance_miles = "Distance Miles"
    cogs_line_haul = "COGS Line Haul"
    revenue_line_haul = "Revenue Line Haul"
    cogs_fuel = "COGS Fuel"
    revenue_fuel = "Revenue Fuel"
    cogs_accessorial = "COGS Accessorial"
    revenue_accessorial = "Revenue Accessorial"
    cogs_total = "COGS Total"
    revenue_total = "Revenue Total"
    load_creation = "Load Creation"
    load_activation = "Load Activation"
    carrier_assigned = "Carrier Assigned"


# Lists of all possible column values in input file.
GOLDEN_COLUMN_MAPPING = {
    GoldenColumns.broker_name: ["Broker Name"],
    GoldenColumns.customer_name: [
        "Customer Name",
        "Parent Customer Name",
        "Shipper Name",
    ],
    GoldenColumns.business_unit: ["Business Unit", "BusinessUnitID"],
    GoldenColumns.broker_primary_reference: [
        "Broker Primary Reference",
        "Supplier Primary Reference",
        "Order_ID",
    ],
    GoldenColumns.shipper_primary_reference: [
        "Shipper Primary Reference",
        "Customer Primary Reference",
    ],
    GoldenColumns.shipment_mode: ["Shipment Mode", "Mode"],
    GoldenColumns.equipment_type: ["Equipment Type", "Shipment Truck Type"],
    GoldenColumns.origin_name: ["Origin Name", "Origin Facility Name"],
    GoldenColumns.destination_name: [
        "Destination Facility Name",
        "Destination Name",
        "Destination Company Name",
    ],
    GoldenColumns.origin_city: ["Origin City"],
    GoldenColumns.destination_city: ["Destination City", "Delivery City"],
    GoldenColumns.origin_state: [
        "Origin State",
        "Origin State Province",
        "Origin Province",
    ],
    GoldenColumns.destination_state: [
        "Destination State",
        "Destination State Province",
        "Destination Province",
        "Delivery State",
    ],
    GoldenColumns.origin_zip: ["Origin Zip", "Origin Postal Code", "Origin Zipcode"],
    GoldenColumns.destination_zip: [
        "Destination Zip",
        "Destination Postal Code",
        "Delivery Zip",
        "Destination Zipcode",
    ],
    GoldenColumns.origin_country: ["Origin Country"],
    GoldenColumns.destination_country: ["Destination Country", "Delivery Country"],
    GoldenColumns.shipment_status: ["Shipment Status"],
    GoldenColumns.shipment_rank: ["Shipment Rank", "Shipment Ranking"],
    GoldenColumns.weight: ["Weight"],
    GoldenColumns.origin_open: [
        "Origin Open",
        "Origin Open D/T",
        "Earliest Pickup",
        "Origin Appt Open",
        "Origin Appointment Open",
    ],
    GoldenColumns.destination_open: [
        "Destination Open",
        "Destination Open D/T",
        "Earliest Delivery",
        "Destination Appt Open",
        "Delivery Appt Open",
        "Destination Appointment Open",
        "Delivery Appointment Open",
    ],
    GoldenColumns.origin_close: [
        "Origin Close",
        "Origin Close D/T",
        "Latest Pickup",
        "Origin Appt Close",
        "Origin Appointment Close",
    ],
    GoldenColumns.destination_close: [
        "Destination Close",
        "Destination Close D/T",
        "Latest Delivery",
        "Destination Appt Close",
        "Delivery Appt Close",
        "Destination Appointment Close",
        "Delivery Appointment Close",
    ],
    GoldenColumns.origin_arrival: [
        "Origin Arrival",
        "Origin Arrival D/T",
        "Origin Tracking Earliest Arrival",
        "Origin Carrier Arrival",
    ],
    GoldenColumns.destination_arrival: [
        "Destination Arrival",
        "Destination Arrival D/T",
        "Destination Tracking Earliest Arrival",
        "Destination Carrier Arrival",
    ],
    GoldenColumns.origin_departure: [
        "Origin Departure",
        "Origin Departure D/T",
        "Actual Pickup",
        "Origin Carrier Departure",
    ],
    GoldenColumns.destination_departure: [
        "Destination Departure",
        "Destination Departure D/T",
        "Actual Delivery",
        "Destination Carrier Departure",
    ],
    GoldenColumns.stop_count: ["Stop Count"],
    GoldenColumns.distance_miles: ["Distance", "Miles", "Distance Miles"],
    GoldenColumns.cogs_line_haul: ["COGS Line Haul", "Carrier Linehaul"],
    GoldenColumns.revenue_line_haul: ["Revenue Line Haul", "Customer Linehaul"],
    GoldenColumns.cogs_fuel: ["COGS Fuel", "Carrier Fuel"],
    GoldenColumns.revenue_fuel: ["Revenue Fuel", "Customer Fuel"],
    GoldenColumns.cogs_accessorial: [
        "COGS Accessorial",
        "Carrier Accessorial",
        "COGS Total Accessorial",
        "COGS Total Accessorials",
        "COGS Total Acessorial",
    ],
    GoldenColumns.revenue_accessorial: [
        "Revenue Accessorial",
        "Customer Accessorial",
        "Revenue Total Accessorial",
        "Revenue Total Accessorials",
        "Revenue Total Accesorial",
    ],
    GoldenColumns.cogs_total: ["COGS Total", "Carrier Charge Total", "Total COGS"],
    GoldenColumns.revenue_total: ["Revenue Total", "Customer Cost Total"],
    GoldenColumns.load_creation: [
        "Load Creation",
        "Load Create",
        "Load Creation D/T UTC",
        "Load Create D/T UTC",
        "Date Time Created UTC",
        "Load Creation Date/Time",
        "Load Creation Date Time",
        "Load_Creation_Date",
    ],
    GoldenColumns.load_activation: [
        "Load Activation",
        "Load Activate",
        "Load Activation D/T UTC",
        "Load Activate D/T UTC",
        "Off Hold Time Stamp UTC",
        "Load Activation Date/Time",
        "Load Activation Date Time",
        "Load_Activation_Date",
    ],
    GoldenColumns.carrier_assigned: [
        "Carrier Assigned",
        "Carrier Assign",
        "Carrier Assigned D/T UTC",
        "Carrier Assign D/T UTC",
        "LastCarrierAssignedDateEST",
        "Final Carrier Assigned Date/Time",
        "Final Carrier Assigned Date Time",
        "Final_Carrier_Assigned_Date",
    ],
}


def clean_str(s):
    """
    Removes all non-alphanumeric characters from a string.
    """
    replace_chars = {
        " ": "",
        "_": "",
        "-": "",
        ".": "",
    }
    cleaned_str = s.lower()
    for char in replace_chars:
        cleaned_str = cleaned_str.replace(char, replace_chars[char])
    return cleaned_str


def map_column(col, max_dist=3):
    """Map an input column name to the corresponding golden column.

    :param col: Column name to be mapped.
    :param max_dist: Maximum edit distance to allow a mapping.
    :return: Corresponding golden column.
    """
    inverse_map = {}
    for k in GOLDEN_COLUMN_MAPPING:
        inverse_map.update({i: k for i in GOLDEN_COLUMN_MAPPING[k]})

    model = edit_distance.EditDistance().get_model()
    # Calculate similarities for each column name and select the maximum.
    best_fit = sorted((model(clean_str(col), clean_str(i)), i) for i in inverse_map)[0]
    if best_fit[0] > max_dist:
        raise ValueError(f"Edit distance to any golden column >{max_dist}")
    return inverse_map[best_fit[1]]


if __name__ == "__main__":
    columns = ["Load Creation D/T UTC", "Load Creation D/T"]
    print({c: map_column(c).value for c in columns})
