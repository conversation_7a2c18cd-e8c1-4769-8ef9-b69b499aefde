## Test Ingestion Locally

It is required to test ingestion fully before deploying. Check generated logs under `ingestion/logs/`

The following commands should be run from the ingestion/ directory

(e.g. `PS C:\Users\<USER>\Code\truce> python main.py --ingestion_only --broker_name "Transfix" --use_tqdm`)

- Run ingestion for Arrive in dev
  `python main.py --dev_db --ingestion_only --broker_name "Arrive Logistics" --use_tqdm`
- Run scorer in dev
  `python main.py --dev_db --scorer_only --use_tqdm`

CAUTION: Remove `--dev_db` very sparingly and check with <PERSON><PERSON> before doing so.

## Install dependencies

`pip install -r requirements.txt`

## Deploy Ingestion to Dev/Prod

1. Run the `deploy/ingestion_dev.bat` command.
2. Verify dev ingestion works (wait 24 hrs or run on a specific broker in aws).
3. Check with <PERSON><PERSON> or <PERSON><PERSON> before running the `deploy/ingestion_prod.bat` command.

## Push Docker Image

1. In AWS go to Elastic Container Registry and click on ingestion
2. <PERSON><PERSON> view push commands to get commands

### Push Commands

1. Start docker daemon
2. Go to ingestion directory
3. `aws ecr get-login-password --region <PUT_REGION_HERE> | docker login --username AWS --password-stdin <PUT_REPOSITORY_URI_HERE>`
4. `docker build -t ingestion .`
5. `docker tag ingestion:latest <PUT_IMAGE_URI_HERE>:latest`
6. `docker push <PUT_IMAGE_URI_HERE>:latest`

### Verify Docker Image Pushed

1. Verify sha256 hash matches at [ECR &gt; Repositories &gt; Images](https://console.aws.amazon.com/ecr/repositories/private/903881895532/ingestion?region=us-east-1)

### Invoke lambda manually

1. Go to ([ECS &gt; Clusters](https://us-east-1.console.aws.amazon.com/ecs/home?region=us-east-1#/clusters/ingestion-network-cluster/tasks))
2. Click `Run New Task`
3. Configure the settings with the following values:
   - Launch type: `FARGATE`
   - Operating system family: `Linux`
   - Task Definition: `ingest`
   - Subnets: `Select both`
   - Auto-assign public IP: `ENABLED`
4. Click `Run Task`
