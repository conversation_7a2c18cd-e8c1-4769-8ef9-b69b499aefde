# Codebase Documentation

## constants.py

**File Path:** `constants.py`

```python
import collections
import os

import pandas as pd
import pytz
from mapping.column_mapping import GoldenColumns

from common import credentials
from common import s3_cache_file
from common.constants import BrokerNames
from common.constants import construct_named_tuple
from common.constants import PROJECT_ROOT

GC = GoldenColumns
CREDENTIALS = credentials.Credentials()


# ROWS CONSTANTS
# TODO(P0): Invoke the pull manually, since it triggers on import which can be dangerous.
# It also affects the unit test.
NA_STATES_PROVINCES = s3_cache_file.S3CacheFile(
    "ingestion-api-cache", "data/na_states_provinces.csv", idx=["State/Province"]
)
NA_STATES_PROVINCES.pull()

MIN_SHIPMENT_WEIGHT = 1000
MAX_SHIPMENT_WEIGHT = 55000
MAX_SHIPMENT_MARGIN = 15000

CountryCode = construct_named_tuple("CountryCode", ["US", "CA", "MX"])
COUNTRY_CODE_DICT = {
    "us": CountryCode.us,
    "ca": CountryCode.ca,
    "canada": CountryCode.ca,
    "mx": CountryCode.mx,
    "usa": CountryCode.us,
    "can": CountryCode.ca,
    "mex": CountryCode.mx,
    "united states": CountryCode.us,
}
ShipmentMode = construct_named_tuple(
    "ShipmentMode",
    ["TL", "LTL", "PARTIAL", "SMALL PARCEL", "IMDL", "AIR", "OCEAN", "DRAYAGE"],
)
SHIPMENT_MODE_DICT = {
    "tl": ShipmentMode.tl,
    "ltl": ShipmentMode.ltl,
    "partial": ShipmentMode.partial,
    "small parcel": ShipmentMode.small_parcel,
    "imdl": ShipmentMode.imdl,
    "air": ShipmentMode.air,
    "ocean": ShipmentMode.ocean,
    "drayage": ShipmentMode.drayage,
    "truckload": ShipmentMode.tl,
    # MoLo
    "truck": ShipmentMode.tl,
    # Echo
    "partial load": ShipmentMode.partial,
    # MX Solutions
    "vr": ShipmentMode.tl,
    "r": ShipmentMode.tl,
    "v": ShipmentMode.tl,
    "tord": ShipmentMode.tl,
    # Forsla
    "ftl": ShipmentMode.tl,
    # Loadsmart
    "ftl": ShipmentMode.tl,
    "ptl": ShipmentMode.partial,
    "vltl": ShipmentMode.tl,
}

# TODO(P0): Keep RawShipmentStatus in DB.
ShipmentStatus = construct_named_tuple(
    "ShipmentStatus",
    [
        "Delivered",
        "In Transit",
        "Loading",
        "Unloading",
        "Uncovered",
        "Covered",
        "Dispatched",
        "Tendered",
        "Canceled",
    ],
)
SHIPMENT_STATUS_DICT = {
    "delivered": ShipmentStatus.delivered,
    "invoiced": ShipmentStatus.delivered,
    "in transit": ShipmentStatus.in_transit,
    "unloading": ShipmentStatus.unloading,
    "uncovered": ShipmentStatus.uncovered,
    "loading": ShipmentStatus.loading,
    "covered": ShipmentStatus.covered,
    "dispatched": ShipmentStatus.dispatched,
    # BlueGrace
    "booked": ShipmentStatus.covered,
    # Nolan
    "booked": ShipmentStatus.covered,
    "onsite-shipper": ShipmentStatus.loading,
    "out for delivery": ShipmentStatus.in_transit,
    "onsite-consignee": ShipmentStatus.unloading,
    "delivered final": ShipmentStatus.delivered,
    "picked-up": ShipmentStatus.in_transit,
    "out for delivery": ShipmentStatus.in_transit,
    "paid": ShipmentStatus.delivered,
    "pending payment": ShipmentStatus.delivered,
    "picked-up": ShipmentStatus.in_transit,
    # Uber
    "driver_arrived_at_pickup": ShipmentStatus.loading,
    "delivery_completed_and_unpaid": ShipmentStatus.delivered,
    "delivery_completed_and_paid": ShipmentStatus.delivered,
    "load_enroute": ShipmentStatus.in_transit,
    "driver_arrived_at_dropoff": ShipmentStatus.unloading,
    # Coyote
    "available": ShipmentStatus.uncovered,
    "loading begin": ShipmentStatus.loading,
    "picked up": ShipmentStatus.in_transit,
    "unloading begin": ShipmentStatus.unloading,
    "terminated": ShipmentStatus.in_transit,
    "arrdest": ShipmentStatus.in_transit,
    "traindepart": ShipmentStatus.in_transit,
    # MoLo Solutions
    "open": ShipmentStatus.uncovered,
    "departed stop": ShipmentStatus.in_transit,
    "complete": ShipmentStatus.delivered,
    "driver assigned": ShipmentStatus.covered,
    # PartnerShip
    "enroute": ShipmentStatus.in_transit,
    "on site": ShipmentStatus.unloading,
    # Sage Freight
    "appt": ShipmentStatus.uncovered,
    "avl": ShipmentStatus.uncovered,
    "comit": ShipmentStatus.covered,
    "assgn": ShipmentStatus.covered,
    "disp": ShipmentStatus.dispatched,
    "osp": ShipmentStatus.loading,
    "pckup": ShipmentStatus.in_transit,
    "enrt": ShipmentStatus.in_transit,
    "osd": ShipmentStatus.unloading,
    "dlvrd": ShipmentStatus.delivered,
    "invau": ShipmentStatus.delivered,
    "tonu": ShipmentStatus.delivered,
    "paid": ShipmentStatus.delivered,
    "comp": ShipmentStatus.delivered,
    "invcd": ShipmentStatus.delivered,
    "completed": ShipmentStatus.delivered,
    "billing": ShipmentStatus.delivered,
    "tracking": ShipmentStatus.in_transit,
    # Arrive Logistics
    "pickedup": ShipmentStatus.in_transit,
    "finished": ShipmentStatus.delivered,
    "at delivery": ShipmentStatus.unloading,
    # Pathmark Transportation
    "finshed": ShipmentStatus.delivered,
    "pickup - a": ShipmentStatus.in_transit,
    # Echo Global Logistics
    "delivered": ShipmentStatus.delivered,
    "unloading": ShipmentStatus.unloading,
    # MX Solutions
    "delivered": ShipmentStatus.delivered,
    "loaded": ShipmentStatus.in_transit,
    "consignee": ShipmentStatus.in_transit,
    "tord": ShipmentStatus.delivered,
    # Forsla
    "invoice: ready": ShipmentStatus.delivered,
    "assigned": ShipmentStatus.covered,
    "departed pickup location": ShipmentStatus.in_transit,
    # Transfix
    "tendered": ShipmentStatus.tendered,
    "assigned": ShipmentStatus.covered,
    "invoice_sent": ShipmentStatus.delivered,
    "canceled": ShipmentStatus.canceled,
    # Loadsmart
    "accounting-review": ShipmentStatus.delivered,
    "at-delivery": ShipmentStatus.unloading,
    # Capstone
    "load": ShipmentStatus.in_transit,
    "del": ShipmentStatus.unloading,
    "delv": ShipmentStatus.delivered,
}

ShipmentRank = construct_named_tuple(
    "ShipmentRank", ["Primary", "Spot", "Backup", "Cost Plus"]
)
SHIPMENT_RANK_DICT = {
    # MoLo Solutions
    "primary": ShipmentRank.primary,
    "spot": ShipmentRank.spot,
    "backup": ShipmentRank.backup,
    "cost plus": ShipmentRank.cost_plus,
    "committed": ShipmentRank.primary,
    #
    "contract - secondary": ShipmentRank.backup,
    "rateonfile": ShipmentRank.backup,
    "brokerage": ShipmentRank.backup,
    # Echo Global Logistics
    "award": ShipmentRank.primary,
    "contract": ShipmentRank.primary,
    "cost_plus": ShipmentRank.cost_plus,
    "routing guide": ShipmentRank.backup,
    "back up": ShipmentRank.backup,
    # Transfix
    "backup/published": ShipmentRank.backup,
    # Arrive Logistics
    "contract - primary": ShipmentRank.primary,
    # Uber Freight
    "rateonfile": ShipmentRank.backup,
    # Nolan Transportation Group
    "secondary": ShipmentRank.backup,
    # Loadsmart
    "contracted": ShipmentRank.primary,
    # Capstone
    "p": ShipmentRank.primary,
}

# ROWS UTIL CONSTANTS
TONU_LCD = 25
TONU_MIN = 100
TONU_MAX = 250
USD_PER_MI_MIN = 0.5

ShipmentClass = construct_named_tuple(
    "ShipmentClass", ["accessorial_tonu", "not_shipment", "accessorial", "canonical"]
)


def set_open_time_range(t: pd.Timestamp) -> pd.Timestamp:
    """Set originOpenTime and destinationOpenTime to 0:00:00 when FCFS is part of time string"""
    return (
        t.replace(hour=0, minute=0, second=0, microsecond=0)
        if t.hour == t.minute == t.second == t.microsecond == 0
        else t
    )


def set_close_time_range(t: pd.Timestamp) -> pd.Timestamp:
    """Set and originCloseTime and destinationCloseTime to 23:59:59 when FCFS is part of time string"""
    return (
        t.replace(hour=23, minute=59, second=59, microsecond=0)
        if t.hour == t.minute == t.second == t.microsecond == 0
        else t
    )


def ignore_time(t: pd.Timestamp) -> pd.Timestamp:
    """Replace time to 0:00:00 and tzinfo with None value"""
    return t.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)


def time_without_tzinfo(t: pd.Timestamp) -> pd.Timestamp:
    """Replace tzinfo with None value"""
    return t.replace(tzinfo=None) if not pd.isna(t) else pd.NA


# Mapping of Brokers to column handler functions for data transformations.
# Link to Google Doc for specific information on Broker Status:
# https://docs.google.com/document/d/1EF7w5vUpXbh_UA5e_CBatVqclh8oqzkHv9EdkZBaxB0/edit#heading=h.2lsdv4o2agdm
BROKER_COLUMN_HANDLERS = {
    # Arrive Logistics Broker: remove timezone information in order to localize to timezone from google maps
    BrokerNames.arrive_logistics: {
        "originOpenTime": time_without_tzinfo,
        "originCloseTime": time_without_tzinfo,
        "originArrivalTime": time_without_tzinfo,
        "originDepartureTime": time_without_tzinfo,
        "destinationOpenTime": time_without_tzinfo,
        "destinationCloseTime": time_without_tzinfo,
        "destinationArrivalTime": time_without_tzinfo,
        "destinationDepartureTime": time_without_tzinfo,
        "loadCreationTime": time_without_tzinfo,
        "loadActivationTime": time_without_tzinfo,
        "carrierAssignedTime": time_without_tzinfo,
    },
    # Integrity Express Logistics Broker: remove timezone information in order to localize to timezone from google maps
    BrokerNames.integrity_express_logistics: {
        "originCloseTime": ignore_time,
        "destinationCloseTime": ignore_time,
    },
    # MX Solutions Broker: set originOpenTime and destinationOpenTime to 0:00:00 and originCloseTime and destinationCloseTime to 23:59:59 when FCFS is part of time string
    BrokerNames.mx_solutions: {
        "originOpenTime": set_open_time_range,
        "originCloseTime": set_close_time_range,
        "destinationOpenTime": set_open_time_range,
        "destinationCloseTime": set_close_time_range,
    },
    # Loadsmart Broker: remove timezone information in order to localize to timezone from google maps
    BrokerNames.loadsmart: {
        "originOpenTime": time_without_tzinfo,
        "originCloseTime": time_without_tzinfo,
        "originArrivalTime": time_without_tzinfo,
        "originDepartureTime": time_without_tzinfo,
        "destinationOpenTime": time_without_tzinfo,
        "destinationCloseTime": time_without_tzinfo,
        "destinationArrivalTime": time_without_tzinfo,
        "destinationDepartureTime": time_without_tzinfo,
    },
}

# List of Brokers with time normalization exemptions
TIME_NORMALIZATION_EXEMPT_BROKERS = [BrokerNames.arrive_logistics]

# INGESTION CONSTANTS
STRING_COLUMNS = [
    GC.broker_name,
    GC.customer_name,
    GC.business_unit,
    GC.broker_primary_reference,
    GC.shipper_primary_reference,
    GC.shipment_mode,
    GC.equipment_type,
    GC.origin_name,
    GC.destination_name,
    GC.origin_city,
    GC.destination_city,
    GC.origin_state,
    GC.destination_state,
    GC.origin_zip,
    GC.destination_zip,
    GC.origin_country,
    GC.destination_country,
    GC.shipment_status,
    GC.shipment_rank,
]
DATETIME_COLUMNS = [
    GC.origin_open,
    GC.destination_open,
    GC.origin_close,
    GC.destination_close,
    GC.origin_arrival,
    GC.destination_arrival,
    GC.origin_departure,
    GC.destination_departure,
    GC.load_creation,
    GC.load_activation,
    GC.carrier_assigned,
]
NUMERIC_COLUMNS = [
    GC.stop_count,
    GC.distance_miles,
    GC.cogs_line_haul,
    GC.revenue_line_haul,
    GC.cogs_fuel,
    GC.revenue_fuel,
    GC.cogs_accessorial,
    GC.revenue_accessorial,
    GC.cogs_total,
    GC.revenue_total,
    GC.weight,
]

ShipperNames = construct_named_tuple(
    "ShipperNames",
    [
        "Swire Coca Cola",
        "SC Johnson",
        "Reyes Holdings",
        "Campbell Soup Company",
        "Barilla America",
        "Utz Quality Foods",
        "Monster Beverage Corporation",
        "Coca Cola Southwest Beverages",
    ],
)
# TODO(P2): Use column mapping edit distance model for this.
SHIPPER_NAME_DICT = {
    # BlueGrace Logistics
    "s. c. johnson & son, inc. - tl": ShipperNames.sc_johnson,
    # Uber Freight
    "swire coca-cola - market access": ShipperNames.swire_coca_cola,
    "swire coca-cola": ShipperNames.swire_coca_cola,
    "transplace - campbell soup company - outbound": ShipperNames.campbell_soup_company,
    "transplace - campbell soup company - inbound": ShipperNames.campbell_soup_company,
    "reyes beverages, l.l.c.": ShipperNames.reyes_holdings,
    "utz quality foods llc": ShipperNames.utz_quality_foods,
    "barilla america": ShipperNames.barilla_america,
    # Sheer Transport
    "swire coca-cola usa": ShipperNames.swire_coca_cola,
    # Nolan Transportation Group
    "reyes holdings - great lakes coca-cola": ShipperNames.reyes_holdings,
    "reyes holdings - reyes beer division": ShipperNames.reyes_holdings,
    "coca-cola - swire": ShipperNames.swire_coca_cola,
    "campbell meals & beverages (outbound)": ShipperNames.campbell_soup_company,
    "campbell meals & beverages (inbound)": ShipperNames.campbell_soup_company,
    "barilla america, inc.": ShipperNames.barilla_america,
    # Coyote Logistics
    "s. c. johnson & son, inc. - blujay": ShipperNames.sc_johnson,
    "reyes - enpl": ShipperNames.reyes_holdings,
    "reyes beer division": ShipperNames.reyes_holdings,
    "reyes enpl - manual": ShipperNames.reyes_holdings,
    "reyes great lakes coca-cola distribution, l.l.c.": ShipperNames.reyes_holdings,
    "swire coca cola, usa": ShipperNames.swire_coca_cola,
    "campbell’s soup – outbound": ShipperNames.campbell_soup_company,
    "utz quality foods, llc c/o lean logistics": ShipperNames.utz_quality_foods,
    "monster beverage corp / hansen beverage": ShipperNames.monster_beverage_corporation,
    # BlueGrace
    "swire coca-cola, usa tl": ShipperNames.swire_coca_cola,
    "sc johnson & son, inc. tl": ShipperNames.sc_johnson,
    # Dupre,
    "reyes coca cola bottling": ShipperNames.reyes_holdings,
    # MoLo Solutions,
    "reyes holdings": ShipperNames.reyes_holdings,
    "swire coca cola": ShipperNames.swire_coca_cola,
    "sc johnson": ShipperNames.sc_johnson,
    "campbell soup company": ShipperNames.campbell_soup_company,
    # PartnerShip LLC,
    "glcc": ShipperNames.reyes_holdings,
    # Sage Freight
    "scjohnson": ShipperNames.sc_johnson,
    # Arrive Logistics
    "campbell soup company": ShipperNames.campbell_soup_company,
    "s. c. johnson & son, inc.": ShipperNames.sc_johnson,
    "reyes holdings, l.l.c.": ShipperNames.reyes_holdings,
    "barilla america inc.": ShipperNames.barilla_america,
    # Echo Global Logistics
    "campbells meals and beverage  - tp tms": ShipperNames.campbell_soup_company,
    "great lakes coca-cola distribution, llc c/o mvfs": ShipperNames.reyes_holdings,
    "great lakes coca-cola distribution  llc c/o mvfs": ShipperNames.reyes_holdings,
    "s.c. johnson & son": ShipperNames.sc_johnson,
    "monster energy company": ShipperNames.monster_beverage_corporation,
    # MX Solutions
    "reyes / great lakes coca cola": ShipperNames.reyes_holdings,
    "coca cola southwest - ani": ShipperNames.coca_cola_southwest_beverages,
    # Ryan Transportation
    "reyes holdings": ShipperNames.reyes_holdings,
    "swire coca cola": ShipperNames.swire_coca_cola,
    "utz quality foods inc.": ShipperNames.utz_quality_foods,
    # Integrity Express Logistics
    "reyes logistic solutions llc": ShipperNames.reyes_holdings,
    # Capstone Logistics
    "reyes coca cola": ShipperNames.reyes_holdings,
    # Trailer Bridge
    "ccb swire pacific holdings": ShipperNames.swire_coca_cola,
    # Axle Logistics
    "swire pacific": ShipperNames.swire_coca_cola,
}


BROKER_NAME_DICT = {
    "mx solutions": BrokerNames.mx_solutions,
    "bluegrace logistics": BrokerNames.bluegrace_logistics,
    "arrive": BrokerNames.arrive_logistics,
    "trailer bridge, inc.": BrokerNames.trailer_bridge,
    "child logistics inc": BrokerNames.child_logistics,
}


# Columns to ignore, indexed by broker. Typically a hack to ignore faked columns.
BROKER_IGNORE_COLS = {
    BrokerNames.nolan_transportation_group: [
        GC.origin_arrival,
        GC.destination_arrival,
        GC.origin_departure,
        GC.destination_departure,
    ],
    BrokerNames.integrity_express_logistics: [
        GC.load_creation,
        GC.carrier_assigned,
    ],
    BrokerNames.forsla: [
        GC.load_activation,
        GC.carrier_assigned,
        GC.origin_open,
        GC.destination_open,
    ],
    BrokerNames.sheer_transport: [
        GC.origin_arrival,
        GC.destination_arrival,
        GC.origin_departure,
        GC.destination_departure,
    ],
}

# Key is string column name in rows.py around line 281.
# From docs.google.com/spreadsheets/d/1gLugBW_e1PqbyQFKpD3HNgcZ0g6P6OyhMzQMDVs2-Io
# If broker not specified, all timezones are assumed to be UTC.
CUSTOM_TIMEZONES_DICT = collections.defaultdict(
    lambda: {},
    {
        BrokerNames.bluegrace_logistics.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.sheer_transport.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.nolan_transportation_group.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.dupre_logistics_llc.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.partnership_llc.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.arrive_logistics.lower(): {
            "loadCreationTime": pytz.timezone("America/Chicago"),
            "loadActivationTime": pytz.timezone("America/Chicago"),
            "carrierAssignedTime": pytz.timezone("America/Chicago"),
        },
        BrokerNames.echo_global_logistics.lower(): {
            "loadCreationTime": pytz.timezone("America/Chicago"),
            "loadActivationTime": pytz.timezone("America/Chicago"),
            "carrierAssignedTime": pytz.timezone("America/Chicago"),
        },
        BrokerNames.mx_solutions.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.integrity_express_logistics.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.transfix.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
        BrokerNames.loadsmart.lower(): {
            "loadCreationTime": pytz.timezone("America/New_York"),
            "loadActivationTime": pytz.timezone("America/New_York"),
            "carrierAssignedTime": pytz.timezone("America/New_York"),
        },
    },
)

# Ensure all columns are covered exactly once.
ALL_COLUMNS = STRING_COLUMNS + DATETIME_COLUMNS + NUMERIC_COLUMNS
assert len(ALL_COLUMNS) == len(set(ALL_COLUMNS))
assert set(ALL_COLUMNS) == set(GC.__members__.values())
NUM_THREADS = 1

# RDS API CONSTANTS
NUM_UPSERT_RETRIES = 10

# MAIL CONSTANTS
# Request all access (permission to read/send/receive emails, manage the inbox, and more)
# From email
OUR_EMAIL = "<EMAIL>"
INGESTION_DATA_DIR = os.path.join(PROJECT_ROOT, "ingestion", "incoming_email_data")

# CUSTOMER DIRECT COMPANY PATTERNS
# TODO (P3): Make regex more specific after data analysis
COMPANY_REGEX_PATTERNS = {
    "albertson[']*s": "Albertsons",
    "amazon": "Amazon",
    "big.*lots": "Big Lots",
    "bj[']*s": "BJ's Wholesale Club",
    "c.*&.*s": "C&S Wholesale Grocers",
    "costco": "Costco",
    "cvs": "CVS",
    "demoulas": "Market Basket",
    "dollar.*general": "Dollar General",
    "dollar.*tree": "Dollar Tree",
    "family.*dollar": "Family Dollar",
    "jetro": "Restaurant Depot",
    "kroger": "Kroger",
    "mclane": "McLane",
    "meijer": "Meijer",
    "publix": "Publix",
    "restaurant.*depot": "Restaurant Depot",
    "rite.*aid": "Rite Aid",
    "safeway": "Safeway",
    "sam[']*s.*club": "Sam's Club",
    "shamrock": "Shamrock Foods",
    "sysco": "Sysco",
    "target": "Target",
    "unfi": "UNFI",
    "us.*foods": "US Foods",
    "wal.*mart": "Walmart",
    "winco.*foods": "WinCo Foods",
    "woodman[']*s": "Woodman's Market",
}

# MAIN CONSTANTS
JOB_TIMEOUT_HOURS = 12

LOW_INGESTION_SUCCESS_RATE_THRESHOLD = 30

WHITELISTED_FILTERS = {
    BrokerNames.bluegrace_logistics: [
        "to:<EMAIL> from:<EMAIL> subject:***AUTOMATED MESSAGE***"
    ],
    BrokerNames.uber_freight: [
        'to:<EMAIL> replyto:<EMAIL> subject:"[Auto Email] Uber Freight Rates for Truce"',
    ],
    BrokerNames.sheer_transport: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Sheer Transport]"'
    ],
    BrokerNames.nolan_transportation_group: [
        'to:<EMAIL> replyto:<EMAIL> subject:"Nolan_Truce"'
    ],
    BrokerNames.coyote_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Coyote Logistics]"'
    ],
    BrokerNames.linehaul_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: LineHaul]"'
    ],
    BrokerNames.dupre_logistics_llc: [
        'to:<EMAIL> from:<EMAIL> subject:"Dupre Logistics LLC_REYES"'
    ],
    BrokerNames.molo_solutions: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: MoLo Solutions]"'
    ],
    BrokerNames.partnership_llc: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: PartnerShip]"'
    ],
    BrokerNames.sage_freight: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Sage Freight]"'
    ],
    BrokerNames.arrive_logistics: [
        'to:<EMAIL> replyto:<EMAIL> subject:"Arrive Logistics"'
    ],
    BrokerNames.pathmark_transportation: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Pathmark Transportation]"'
    ],
    BrokerNames.echo_global_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Echo Global Logistics]"'
    ],
    BrokerNames.mx_solutions: [
        'to:<EMAIL> from:<EMAIL> subject:"Automated Report MRKT Truce Export Job 40279"',
        'to:<EMAIL> from:<EMAIL> subject:"Automated Report MRKT Truce Export Job 41458"',
    ],
    BrokerNames.ryan_transportation: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Ryan Transportation]"'
    ],
    BrokerNames.integrity_express_logistics: [
        'to:<EMAIL> replyto:<EMAIL> subject:"FW: [EXTERNAL] Pulse: Reyes Logistics Solutions LLC Load Data"'
    ],
    BrokerNames.forsla: ['to:<EMAIL> from:<EMAIL> subject:"Forsla"'],
    BrokerNames.transfix: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Transfix]"'
    ],
    BrokerNames.loadsmart: [
        'to:<EMAIL> replyto:<EMAIL> subject:"Loadsmart Truce Daily Report"'
    ],
    BrokerNames.child_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Child Logistics Inc]"'
    ],
    BrokerNames.royal_transportation: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Royal Transportation]"'
    ],
    BrokerNames.schneider_brokerage: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Schneider Brokerage]"'
    ],
    BrokerNames.capstone_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Capstone]"'
    ],
    BrokerNames.trailer_bridge: [
        'to:<EMAIL> from:<EMAIL> subject:"[AutoIngestion: Trailer Bridge, Inc.]"'
    ],
    BrokerNames.ardent: [
        'to:<EMAIL> replyto:<EMAIL> subject:"Ardent"'
    ],
    BrokerNames.axle_logistics: [
        'to:<EMAIL> from:<EMAIL> subject:"AXLL"'
    ],
    BrokerNames.demo_data: [
        'to:<EMAIL> from:<EMAIL> subject:"[Auto Ingestion][Demo Data]"'
    ],
}


```

## ingestion.py

**File Path:** `ingestion.py`

```python
import collections
import datetime
import logging
import os
import re
import threading
import traceback
import typing
import zipfile

import numpy as np
import pandas as pd
from constants import BROKER_IGNORE_COLS
from constants import BROKER_NAME_DICT
from constants import CUSTOM_TIMEZONES_DICT
from constants import DATETIME_COLUMNS
from constants import GC
from constants import INGESTION_DATA_DIR
from constants import NUM_THREADS
from constants import NUMERIC_COLUMNS
from constants import SHIPPER_NAME_DICT
from constants import STRING_COLUMNS
from mapping.column_mapping import map_column
from rows import BrokerRow
from rows import CityRow
from rows import CityZipRow
from rows import LaneRow
from rows import ShipmentMetadataRow
from rows import ShipmentRow
from rows import ShipperRow
from rows import ZipRow
from tqdm import tqdm

from common import google_maps_utils
from common.constants import ShipmentIngestionStatus
from common.rds_api import RDS

# TODO(P0): Document this fucking code.


def build_df_type_dictionary(column_mapping: dict) -> tuple:
    """Build a dictionary of column names and their data types.

    The keys of the type dictionary are the keys of the column_mapping dictionary.

    :param column_mapping: A dictionary of input column names and corresponding golden column.
    :return: Partial dictionary of columns and their types.
    :return: List of datetime columns.
    """
    inverted_column_mapping = {}
    # Invert dictionary.
    for key, value in column_mapping.items():
        inverted_column_mapping[value] = key

    type_dictionary = {}
    date_columns = []
    for gold_col in STRING_COLUMNS:
        if gold_col.value in inverted_column_mapping:
            type_dictionary[inverted_column_mapping[gold_col.value]] = str
    for gold_col in DATETIME_COLUMNS:
        if gold_col.value in inverted_column_mapping:
            type_dictionary[inverted_column_mapping[gold_col.value]] = str
            date_columns.append(inverted_column_mapping[gold_col.value])
    return type_dictionary, date_columns


def round_time(dt: pd.Timestamp) -> pd.Timestamp:
    second = 0 if pd.isna(dt.second) else dt.second
    microsecond = 0 if pd.isna(dt.microsecond) else dt.microsecond
    # Calculate rounded seconds
    rounded_seconds = round(second + microsecond / 1000000)

    # Adjust the datetime
    rounded_dt = dt.replace(second=0, microsecond=0) + datetime.timedelta(
        seconds=rounded_seconds
    )

    return rounded_dt


def parse_time(datetime_str: str) -> pd.Timestamp:
    if pd.isna(datetime_str) or not datetime_str:
        return pd.NaT

    cleaned_datetime_str = re.sub(r"[^0-9/: -APMapmT]", "", datetime_str)

    try:
        parsed_time = pd.to_datetime(
            cleaned_datetime_str, format="mixed", errors="coerce"
        )

        if not pd.isna(parsed_time):
            return parsed_time

        # If "mixed" format parsing failed, try parsing with "AM/PM" format
        parsed_time_am_pm = pd.to_datetime(
            cleaned_datetime_str, format="%I:%M:%S %p", errors="coerce"
        )

        return parsed_time_am_pm
    except Exception:
        return pd.NaT


# TODO(P1): Use converters https://stackoverflow.com/questions/42958217/pandas-read-excel-datetime-converter
def convert_types(
    shipments_df: pd.DataFrame, added_empty_cols: typing.List[str]
) -> pd.DataFrame:
    for column in STRING_COLUMNS:
        if column.value not in shipments_df.columns or column.value in added_empty_cols:
            continue
        shipments_df[column.value] = shipments_df[column.value].str.strip()
        shipments_df[column.value].replace("nan", pd.NA, inplace=True)

    for column in DATETIME_COLUMNS:
        if column.value not in shipments_df.columns or column.value in added_empty_cols:
            continue
        # Convert NA to NaT.
        shipments_df[column.value].replace(pd.NA, pd.NaT, inplace=True)

        shipments_df[column.value] = shipments_df[column.value].apply(parse_time)

        shipments_df[column.value] = shipments_df[column.value].apply(round_time)

    for column in NUMERIC_COLUMNS:
        if column.value not in shipments_df.columns or column.value in added_empty_cols:
            continue
        shipments_df[column.value] = shipments_df[column.value].apply(
            lambda s: s.replace(",", "").replace("$", "") if type(s) == str else s
        )
        shipments_df[column.value] = pd.to_numeric(
            shipments_df[column.value], errors="coerce"
        )
    return shipments_df


def read_input_file(
    input_filename: str, type_dict: dict = {}, date_cols: list = []
) -> pd.DataFrame:
    """Read an input file and return a pandas dataframe.

    The file can be a zip archive, an excel file, or a csv file.

    :param input_filename: The name of the file to read.
    :param type_dict: A dictionary of column names and their types.
    :param date_cols: A list of column names that should be parsed as dates.
    :return: A pandas dataframe."""
    if input_filename[-3:] == "zip":
        with zipfile.ZipFile(input_filename) as z:
            if len(z.namelist()) > 1:
                raise NotImplementedError(
                    "There is more than one file in the zip archive"
                )
            first_file = z.namelist()[0]
            z.extract(first_file, path=INGESTION_DATA_DIR)
            extracted_file = os.path.join(INGESTION_DATA_DIR, first_file)
            return read_input_file(extracted_file, type_dict, date_cols)
    elif "xlsx" in input_filename:
        df = pd.read_excel(input_filename, dtype=type_dict)
        skip_rows = 0
        # TODO(P1): Consider checking for empty columns.
        while all(col.startswith("Unnamed") for col in df.columns):
            skip_rows += 1
            df = pd.read_excel(input_filename, dtype=type_dict, skiprows=skip_rows)
        # Only parse date_cols when we reach the column row.
        df = pd.read_excel(input_filename, dtype=type_dict, skiprows=skip_rows)
    elif "csv" in input_filename:
        df = pd.read_csv(
            input_filename,
            dtype=type_dict,
            skip_blank_lines=True,
        )
    else:
        logging.error(f"File not supported {input_filename}")
        raise ValueError("Unsupported file extension.")
    return df


def read_and_map_file(input_filename: str, broker: str) -> pd.DataFrame:
    df = read_input_file(input_filename)
    column_mappings = {}
    for col in df.columns:
        try:
            # TODO(P1): Increase this threshold, but only after adding regression testing.
            column_mappings[col] = map_column(col, 0).value
            logging.info(f"SUCCESS: Column '{col}' mapped to '{column_mappings[col]}'.")
        except ValueError as e:
            logging.warning(f"FAILURE: Column '{col}' could not be mapped because {e}.")

    if len(column_mappings.values()) != len(set(column_mappings.values())):
        raise AssertionError(f"Multiple columns map to single GC: {column_mappings}")

    df = read_input_file(input_filename, *build_df_type_dictionary(column_mappings))
    df = df.rename(columns=column_mappings)

    if broker in BROKER_IGNORE_COLS:
        drop_cols = BROKER_IGNORE_COLS[broker]
        for col in drop_cols:
            if col.value in df.columns:
                df.drop(col.value, axis=1, inplace=True)

    # TODO(P0): Ensure the optional columns are correct.
    required_cols = {
        GC.origin_zip,
        GC.destination_zip,
        GC.origin_city,
        GC.destination_city,
        GC.origin_state,
        GC.destination_state,
        GC.shipment_mode,
        GC.equipment_type,
        GC.customer_name,
        GC.broker_name,
        GC.broker_primary_reference,
        GC.cogs_total,
        GC.revenue_total,
        GC.origin_close,
    }
    added_empty_cols = []
    for g in GC:
        if g.value not in df.columns:
            if g in required_cols:
                raise AssertionError(f'Missing required column "{g.value}".')
            df[g.value] = pd.NA
            added_empty_cols.append(g.value)

    convert_types(df, added_empty_cols)
    return df


def ingest_row(
    r: pd.Series,
    rds: RDS,
    input_filename: str,
    GMAPS_UTILS: google_maps_utils.GoogleMapsUtils,
):
    return_dict = {}
    return_dict["derived_cols"] = set()
    return_dict["shipment_diffs"] = {}

    origin_city = CityRow(
        city=r[GC.origin_city.value],
        state=r[GC.origin_state.value],
        country=r[GC.origin_country.value],
    )
    origin_city.id = rds.upsert(rds.cities, origin_city.to_dict())
    destination_city = CityRow(
        city=r[GC.destination_city.value],
        state=r[GC.destination_state.value],
        country=r[GC.destination_country.value],
    )
    destination_city.id = rds.upsert(rds.cities, destination_city.to_dict())

    origin_zip = ZipRow(
        zipcode=r[GC.origin_zip.value],
        country=origin_city.country,
        city=origin_city.city,
        state=origin_city.state,
        return_dict=return_dict,
        rds=rds,
        GMAPS_UTILS=GMAPS_UTILS,
    )
    origin_zip.id = rds.upsert(rds.zips, origin_zip.to_dict())
    destination_zip = ZipRow(
        zipcode=r[GC.destination_zip.value],
        country=destination_city.country,
        city=destination_city.city,
        state=destination_city.state,
        return_dict=return_dict,
        rds=rds,
        GMAPS_UTILS=GMAPS_UTILS,
    )
    destination_zip.id = rds.upsert(rds.zips, destination_zip.to_dict())

    origin_city_zip = CityZipRow(city_id=origin_city.id, zip_id=origin_zip.id)
    origin_city_zip.id = rds.upsert(rds.cityzips, origin_city_zip.to_dict())
    destination_city_zip = CityZipRow(
        city_id=destination_city.id, zip_id=destination_zip.id
    )
    destination_city_zip.id = rds.upsert(
        rds.cityzips,
        destination_city_zip.to_dict(),
    )

    lane = LaneRow(
        origin_city_id=origin_city.id,
        destination_city_id=destination_city.id,
        shipment_mode=r[GC.shipment_mode.value],
        equipment_type=r[GC.equipment_type.value],
    )
    lane.id = rds.upsert(rds.lanes, lane.to_dict())

    if pd.isna(r[GC.broker_name.value]):
        raise AssertionError(
            "Broker name column not found and broker name not specified."
        )
    else:
        broker_name = r[GC.broker_name.value]
    if broker_name.lower().strip() in BROKER_NAME_DICT:
        broker_name = BROKER_NAME_DICT[broker_name.lower().strip()]
    broker = BrokerRow(name=broker_name)
    broker.id = rds.upsert(rds.brokers, broker.to_dict())

    shipper_name = r[GC.customer_name.value]
    if shipper_name.lower() in SHIPPER_NAME_DICT:
        shipper_name = SHIPPER_NAME_DICT[shipper_name.lower().strip()]
    shipper = ShipperRow(name=shipper_name)
    shipper.id = rds.upsert(rds.shippers, shipper.to_dict())

    shipment = ShipmentRow(
        broker_id=broker.id,
        broker_name=broker.name,
        lane_id=lane.id,
        shipper_id=shipper.id,
        broker_primary_reference=r[GC.broker_primary_reference.value],
        shipper_primary_reference=r[GC.shipper_primary_reference.value],
        stop_count=r[GC.stop_count.value],
        distance_miles=r[GC.distance_miles.value],
        cogs_line_haul=r[GC.cogs_line_haul.value],
        cogs_fuel=r[GC.cogs_fuel.value],
        cogs_accessorial=r[GC.cogs_accessorial.value],
        cogs_total=r[GC.cogs_total.value],
        revenue_line_haul=r[GC.revenue_line_haul.value],
        revenue_fuel=r[GC.revenue_fuel.value],
        revenue_accessorial=r[GC.revenue_accessorial.value],
        revenue_total=r[GC.revenue_total.value],
        origin_warehouse=r[GC.origin_name.value],
        destination_warehouse=r[GC.destination_name.value],
        shipment_status=r[GC.shipment_status.value],
        shipment_rank=r[GC.shipment_rank.value],
        weight=r[GC.weight.value],
        origin_open_time=r[GC.origin_open.value],
        origin_close_time=r[GC.origin_close.value],
        origin_arrival_time=r[GC.origin_arrival.value],
        origin_departure_time=r[GC.origin_departure.value],
        origin_zip_id=origin_zip.id,
        destination_open_time=r[GC.destination_open.value],
        destination_close_time=r[GC.destination_close.value],
        destination_arrival_time=r[GC.destination_arrival.value],
        destination_departure_time=r[GC.destination_departure.value],
        destination_zip_id=destination_zip.id,
        load_creation_time_utc=r[GC.load_creation.value],
        load_activation_time_utc=r[GC.load_activation.value],
        carrier_assigned_time_utc=r[GC.carrier_assigned.value],
        origin_city=origin_city,
        destination_city=destination_city,
        origin_zip=origin_zip,
        destination_zip=destination_zip,
        custom_timezones=CUSTOM_TIMEZONES_DICT[broker.name.lower()],
        return_dict=return_dict,
        rds=rds,
        GMAPS_UTILS=GMAPS_UTILS,
    )

    shipment.id, shipment_ingestion_status, modified_data = rds.shipment_upsert(
        rds.shipments, shipment.to_dict()
    )

    if shipment_ingestion_status == ShipmentIngestionStatus.updated:
        now = datetime.datetime.utcnow().replace(microsecond=0)
        date_string = now.strftime("%x %X")
        return_dict["shipment_diffs"][date_string] = modified_data

    shipmentmetadata = ShipmentMetadataRow(
        shipment_id=shipment.id,
        source_file=input_filename,
        derived_cols=return_dict["derived_cols"],
        shipment_diffs=return_dict["shipment_diffs"],
    )
    shipmentmetadata.id = rds.upsert(rds.shipmentmetadata, shipmentmetadata.to_dict())

    return shipment_ingestion_status


def ingest_rows(
    df: pd.DataFrame,
    rds: RDS,
    input_filename: str,
    return_dict: typing.Dict[str, object],
    use_tqdm=True,
):
    GMAPS_UTILS = google_maps_utils.GoogleMapsUtils()
    return_dict["successful_rows"] = 0
    return_dict["new_shipments"] = 0
    return_dict["updated_shipments"] = 0
    return_dict["value_errors"] = collections.defaultdict(lambda: 0)
    # TODO(P1): Enable/disable tqdm with command line argument.
    iterator = tqdm(df.iterrows(), total=len(df)) if use_tqdm else df.iterrows()

    for _, r in iterator:
        logging.debug("-" * 20 + str(_ + 2) + "-" * 20)
        try:
            status = ingest_row(r, rds, input_filename, GMAPS_UTILS)
            if status == ShipmentIngestionStatus.new:
                return_dict["new_shipments"] += 1
            elif status == ShipmentIngestionStatus.updated:
                logging.info(f"Updated row number: ({_})")
                return_dict["updated_shipments"] += 1
            return_dict["successful_rows"] += 1
        except ValueError as verror:
            logging.error(f"VALUE ERROR ({_}): {verror}")
            return_dict["value_errors"][str(verror)] += 1
        except Exception as error:
            # raise error
            logging.error(f"\n\nERROR ({_}): {error}")
            logging.error(traceback.format_exc())
            logging.debug(return_dict["value_errors"])
            return_dict["value_errors"][str(error)] += 1
    if len(return_dict["value_errors"]) > 0:
        logging.error("Value errors: %s" % dict(return_dict["value_errors"]))

    # Update cache
    logging.info("Syncing and updating cache.")
    # Reload internal df and pull new cache prior to push.
    GMAPS_UTILS.save_and_upload_cache_files()


def ingest_file(input_filename: str, rds: RDS, broker: str, use_tqdm: bool = True):
    logging.info(f"Reading from {input_filename}.")
    df = read_and_map_file(input_filename, broker)
    logging.info(f"Read {len(df)} rows.")

    return_dicts = [{} for _ in range(NUM_THREADS)]
    # Split dataframe into NUM_THREADS equal chunks.
    chunks = np.array_split(df, NUM_THREADS)
    threads = []
    for i, chunk in enumerate(chunks):
        threads.append(
            threading.Thread(
                target=ingest_rows,
                args=(chunk, rds, input_filename, return_dicts[i], use_tqdm),
            )
        )
        threads[-1].start()

    value_errors = collections.defaultdict(lambda: 0)
    successful_rows = 0
    updated_shipments = 0
    new_shipments = 0
    for i, thread in enumerate(threads):
        thread.join()
        logging.info(f"Thread {i + 1} joined.")
        if "error" in return_dicts[i]:
            raise return_dicts[i]["error"]
        for key, value in return_dicts[i]["value_errors"].items():
            value_errors[key] += value
        successful_rows += return_dicts[i]["successful_rows"]
        new_shipments += return_dicts[i]["new_shipments"]
        updated_shipments += return_dicts[i]["updated_shipments"]

    return (
        dict(value_errors),
        successful_rows,
        len(df),
        new_shipments,
        updated_shipments,
    )

```

## ingestion_mail.py

**File Path:** `ingestion_mail.py`

```python
import collections
import logging
import os
import typing

import boto3
import numpy as np
from constants import INGESTION_DATA_DIR
from constants import WHITELISTED_FILTERS

from common.mail import MailAPI

# Gmail API utils
# for encoding/decoding messages in base64
# for dealing with attachement MIME types


def clean(text):
    # clean text for creating a folder
    return "".join(c if c.isalnum() else "_" for c in text)


def remove_chars(s: str, c: typing.List[str]) -> str:
    """Removes all characters in c from s."""
    for char in c:
        s = s.replace(char, "")
    return s


def check_valid_lowercase_brokername(broker_name: str) -> str:
    """
    Check if the broker name from CLI is valid by comparing it with the keys in WHITELISTED_FILTERS and return the matching broker if found, otherwise return None.
    """
    for broker in WHITELISTED_FILTERS.keys():
        if broker.lower() == broker_name.lower():
            return broker


def find_ingestion_data(
    mail_api: MailAPI, broker_name=""
) -> typing.Dict[str, typing.Dict[str, typing.Dict[str, str]]]:
    """Searches email for ingestion data, saves all data, returns directory names.

    Searches based on filters specified in constants.WHITELISTED_FILTERS.
    returns:
        A dict of dirnames indexed by broker then email filter string (from constants.py) then date.
    """
    logging.info("Searching for emails...")
    # Create a dict in a dict in a dict.
    # Example usage: dirnames_dict[broker][filter_string][date] = dirname
    dirnames_dict = collections.defaultdict(lambda: collections.defaultdict(dict))
    broker_name = check_valid_lowercase_brokername(broker_name)
    if not broker_name:
        logging.error(f"Broker name not in whitelist: {broker_name}")
        raise ValueError(f"Broker name not in whitelist: {broker_name}")

    whitelisted_filters = (
        {broker_name: WHITELISTED_FILTERS[broker_name]}
        if broker_name
        else WHITELISTED_FILTERS
    )
    for broker in whitelisted_filters:
        for filter_str in WHITELISTED_FILTERS[broker]:
            # Only consider emails from the last week.
            output_dir_prefix = os.path.join(
                INGESTION_DATA_DIR, broker, remove_chars(filter_str, "\":@. []'-*_")
            )
            filter_str = filter_str + " newer_than:7d"
            os.makedirs(output_dir_prefix, exist_ok=True)
            results = mail_api.search_messages(filter_str)
            for msg in results:
                try:
                    date, dirname = mail_api.read_message(
                        msg, output_dir_prefix=output_dir_prefix
                    )
                except FileNotFoundError:
                    # Error handled in mail.read_message function.
                    continue
                mail_api.mark_as_read_and_archive(msg)
                dirnames_dict[broker][filter_str][date] = dirname

    logging.info("Finished finding all emails.")
    return dirnames_dict


def find_all_broker_emails(service) -> typing.List[str]:
    dirnames = []
    dirnames_dict = find_ingestion_data(service)
    logging.info(f"All datafile dirnames: {dict(dirnames_dict)}")
    for broker in dirnames_dict:
        for filter_str in dirnames_dict[broker]:
            dirnames.extend(
                np.array(list(dirnames_dict[broker][filter_str].values())).flatten()
            )

    return dirnames


def find_latest_broker_emails(service, broker_name) -> typing.List[str]:
    dirnames = []
    dirnames_dict = find_ingestion_data(service, broker_name)

    for broker in dirnames_dict:
        for filter_str in dirnames_dict[broker]:
            latest_date = sorted(dirnames_dict[broker][filter_str].keys())[-1]
            dirnames.append(dirnames_dict[broker][filter_str][latest_date])

    logging.info(f"Latest datafile dirnames: {dirnames}")
    return dirnames


# Verified <EMAIL>, <EMAIL>, <EMAIL> in us-east-1.
# Verification must only be run once.
def verify_email_identity(address: str):
    ses_client = boto3.client("ses", region_name="us-east-1")
    response = ses_client.verify_email_identity(EmailAddress=address)
    logging.info(response)

```

## ingestion_test.py

**File Path:** `ingestion_test.py`

```python
import os
import unittest

import pandas as pd
from pandas.testing import assert_frame_equal
from parameterized import parameterized

import ingestion


def get_relative_path(filename):
    current_dir = os.path.dirname(os.path.realpath(__file__))
    relative_path = os.path.join(current_dir, "ingestion_unittest_data", filename)
    return relative_path


class TestPandasDataFrames(unittest.TestCase):
    type_dict = {
        "Broker Name": str,
        "Customer Name": str,
        "Broker Primary Reference": str,
        "Shipper Primary Reference": str,
        "Shipment Mode": str,
        "Equipment Type": str,
        "Origin Facility Name": str,
        "Destination Facility Name": str,
        "Origin City": str,
        "Destination City": str,
        "Origin State": str,
        "Destination State": str,
        "Origin Zip": str,
        "Destination Zip": str,
        "Origin Country": str,
        "Destination Country": str,
        "Shipment Status": str,
        "Shipment Rank": str,
        "Origin Appt Open": str,
        "Destination Appt Open": str,
        "Origin Appt Close": str,
        "Destination Appt Close": str,
        "Origin Carrier Arrival": str,
        "Destination Carrier Arrival": str,
        "Origin Carrier Departure": str,
        "Destination Carrier Departure": str,
        "Load Creation Date/Time": str,
        "Load Activation Date/Time": str,
        "Final Carrier Assigned Date/Time": str,
    }

    @parameterized.expand(
        [
            [
                get_relative_path("first_row_empty_line.xlsx"),
                type_dict,
                pd.read_excel(
                    get_relative_path("first_row_empty_line_corrected.xlsx"),
                    dtype=type_dict,
                ),
            ],
        ],
    )
    def test_read_input_file(
        self, input_filename: str, type_dict: dict, df: pd.DataFrame
    ):
        assert_frame_equal(ingestion.read_input_file(input_filename, type_dict), df)


class TestDatetimeConversion(unittest.TestCase):
    def setUp(self):
        self.datetime_strings = [
            "2023-07-17 18:00:00-05:00",
            "2023-05-11 18:00:00-05:00",
            "2023-06-08 11:30:00-05:00",
            "2023-07-10 18:00:00-05:00",
            "2023-07-31 18:00:00-05:00",
            "2023-06-21 18:00:00-05:00",
            "2023-06-29 17:00:00-05:00",
            "2023-06-21 18:30:00-05:00",
            "2023-06-29 17:00:00-05:00",
            "2023-07-26 18:00:00-07:00",
            "2023-04-28 20:00:00-07:00",
            "2023-07-26 18:00:00-07:00",
        ]

    def test_datetime_conversion(self):
        # Apply pd.to_datetime() to the datetime_strings
        series = pd.Series(self.datetime_strings)
        converted_datetimes = pd.to_datetime(series, format="mixed", errors="coerce")

        # Define the expected output (example values)
        expected_output = [
            "2023-07-17 18:00:00-05:00",
            "2023-05-11 18:00:00-05:00",
            "2023-06-08 11:30:00-05:00",
            "2023-07-10 18:00:00-05:00",
            "2023-07-31 18:00:00-05:00",
            "2023-06-21 18:00:00-05:00",
            "2023-06-29 17:00:00-05:00",
            "2023-06-21 18:30:00-05:00",
            "2023-06-29 17:00:00-05:00",
            "2023-07-26 18:00:00-07:00",
            "2023-04-28 20:00:00-07:00",
            "2023-07-26 18:00:00-07:00",
        ]

        # Convert expected_output to Timestamps
        expected_output = pd.to_datetime(pd.Series(expected_output))

        # Compare the converted_datetimes with the expected_output
        self.assertTrue(converted_datetimes.equals(expected_output))


if __name__ == "__main__":
    unittest.main()

```

## main.py

**File Path:** `main.py`

```python
import argparse
import datetime
import logging
import multiprocessing
import os
import sys
import time
import traceback
import typing
from os import path
from os.path import dirname as pdir

import dateutil.tz
import ingestion_mail
import numpy as np
import pandas as pd
from constants import INGESTION_DATA_DIR
from constants import JOB_TIMEOUT_HOURS
from constants import LOW_INGESTION_SUCCESS_RATE_THRESHOLD
from constants import OUR_EMAIL
from scorer import scorer
from tqdm import tqdm

from common import init_main

PROJECT_ROOT = init_main.initialize()

import ingestion
from common import mail
from common import s3_cache_file
from common.constants import AURORA_DB_DEV_NAME
from common.constants import AURORA_DB_NAME

# Ensure imports work from root level directory.

sys.path.append(pdir(pdir(path.abspath(__file__))))

parser = argparse.ArgumentParser()


def is_valid_file(f, parser):
    if not os.path.exists(f):
        parser.error(f"The file {f} does not exist!")
    else:
        return f


def percentage(numerator: int, denominator: int) -> float:
    return round(numerator / denominator * 100)


parser.add_argument(
    "-i",
    "--input_filenames",
    nargs="+",
    help="Input broker data file to ingest.",
    metavar="FILE",
    type=lambda f: is_valid_file(f, parser),
)

parser.add_argument(
    "-v",
    "--verbose",
    help="Level of verbosity to log from set DEBUG|INFO|WARN|ERROR.",
    default="INFO",
)
parser.add_argument(
    "-l",
    "--log_to_stdout",
    action="store_true",
    help="Whether to log to console output (stdout).",
)
parser.add_argument(
    "-s", "--scorer_only", action="store_true", help="Whether to only run the scorer."
)
parser.add_argument(
    "--ingestion_only", action="store_true", help="Whether to only run the ingestion."
)
parser.add_argument(
    "--dev_db",
    action="store_true",
    help="Whether to ingest to the dev db. DO NOT REMOVE WHEN MANUALLY RUNNING INGESTION.",
)
parser.add_argument("--use_tqdm", action="store_true", help="Whether to use tqdm.")
parser.add_argument(
    "--ingest_all_emails",
    action="store_true",
    help="Whether to ingest all emails from brokers, not just last.",
)
# Argument for timeout time 5 hours default value.
parser.add_argument(
    "--timeout", type=int, default=JOB_TIMEOUT_HOURS, help="Job timeout time in hours."
)
parser.add_argument(
    "--broker_name", default=None, help="Specifies which broker to run ingestion for"
)
parser.add_argument(
    "--create_tables",
    action="store_true",
    help="Whether to create tables using sqlalchemy.",
)
parser.add_argument(
    "--dry_mode", action="store_true", help="Read only mode, does not update to RDS"
)

args = parser.parse_args()

# TODO(P2): Consider using multi-processing in order to speed up ingestion.


def fmt_timedelta(d: datetime.timedelta) -> str:
    """Returns formatted timedelta."""
    if type(d) is not datetime.timedelta:
        return d
    if d.days > 0:
        return "{}d{}h{}m{}s".format(
            d.days, d.seconds // 3600, (d.seconds // 60) % 60, d.seconds % 60
        )
    elif d.seconds > 3600:
        return "{}h{}m{}s".format(
            d.seconds // 3600, (d.seconds // 60) % 60, d.seconds % 60
        )
    elif d.seconds > 60:
        return f"{(d.seconds // 60) % 60}m{d.seconds % 60}s"
    else:
        return f"{d.seconds}s"


def set_up_logging() -> typing.Tuple[str, str]:
    # Set up logging. Create logs directory if it doesn't exist.
    if not os.path.isdir("logs"):
        os.makedirs("logs")

    numeric_level = getattr(logging, args.verbose.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError("Invalid log level: %s" % args.verbose)
    now_str = datetime.datetime.now(datetime.timezone.utc).strftime("%Y-%m-%d_%H-%M-%S")
    ingestion_log_filename = "{}/ingestion_{}.log".format("logs", now_str)
    # TODO(P1): Remove scorer log.
    scorer_log_filename = "{}/scorer_{}.log".format("logs", now_str)
    log_fmt_str = "%(asctime)s [%(threadName)-10.10s] [%(levelname)-4.4s]  %(message)s"

    logging.basicConfig(
        filename=ingestion_log_filename,
        format=log_fmt_str,
        level=numeric_level,
        datefmt="%H:%M:%S",
    )

    logging.info("info")
    logging.debug("debug")
    logging.warning("warning")
    logging.error("error")

    root_logger = logging.getLogger()
    if args.log_to_stdout:
        console_handler = logging.StreamHandler()
        root_logger.addHandler(console_handler)

    # Set sqlalchemy logging to ERROR.
    # logging.getLogger('sqlalchemy.engine').setLevel(logging.ERROR)

    return ingestion_log_filename, scorer_log_filename


def consolidate_all_files():
    mail_api = mail.MailAPI()
    dirnames_dict = ingestion_mail.find_ingestion_data(mail_api)
    for broker in dirnames_dict:
        df = pd.DataFrame()
        dirnames = np.array(list(dirnames_dict[broker].values())).flatten()
        for input_dirname in tqdm(dirnames):
            files = os.listdir(input_dirname)
            if len(files) > 1:
                logging.error(f"More than one file in {input_dirname}")
                raise AssertionError(f"More than one file in {input_dirname}")
            if len(files) == 0:
                logging.error(f"No files in {input_dirname}")
                raise AssertionError(f"No files in {input_dirname}")
            input_filename = os.path.join(input_dirname, files[0])
            try:
                data_to_append = ingestion.read_and_map_file(input_filename, broker)
                df = pd.concat([df, data_to_append], ignore_index=True)

            except Exception as e:
                print(f"\n\n\n{e}")

        df.drop_duplicates(subset="Broker Primary Reference", keep="last").to_csv(
            "data\\analytics\\"
            + dirnames[0].split("\\")[1].replace(" ", "_").lower()
            + "_all.csv"
        )


def upload_file_to_s3(
    s3_instance: s3_cache_file.S3, local_file_path: str, source_directory: str
):
    """
    Uploads broker file in the source_directory and its nested subdirectories
    to the S3 bucket associated with the provided S3 instance, overwriting
    the existing file.

    :param s3_instance: An instance of the S3 class, initialized with the desired bucket.
    :param local_file_path: The local file path to be uploaded to S3.
    :param source_directory: The local directory containing the broker file to be uploaded to S3.
    """
    s3_key = os.path.relpath(local_file_path, source_directory)
    try:
        s3_instance.upload(local_file_path, s3_key)
        logging.info("File %s uploaded.", s3_key)
    except Exception as upload_error:
        logging.error("Failed to upload %s due to %s.", local_file_path, upload_error)


def upload_directory_to_s3(s3_instance: s3_cache_file.S3, source_directory: str):
    """
    Uploads all files in the source_directory and its nested subdirectories
    to the S3 bucket associated with the provided S3 instance, overwriting
    existing files.

    :param s3_instance: An instance of the S3 class, initialized with the desired bucket.
    :param source_directory: The local directory containing files to be uploaded to S3.
    """
    for root, _, files in os.walk(source_directory):
        for file in files:
            local_file_path = os.path.join(root, file)
            s3_key = os.path.relpath(local_file_path, source_directory)

            try:
                s3_instance.upload(local_file_path, s3_key)
                logging.info("File %s uploaded.", s3_key)
            except Exception as upload_error:
                logging.error(
                    "Failed to upload %s due to %s.", local_file_path, upload_error
                )


# TODO(P1): Separate out the scoring logic into a separate file.
def main():
    job_start_time = datetime.datetime.now(datetime.timezone.utc)
    # Check if current date is in daylight savings time for central time.
    if datetime.datetime.now(
        dateutil.tz.gettz("America/Chicago")
    ).dst() != datetime.timedelta(0):
        central_tz = "CDT"
    else:
        central_tz = "CST"

    job_start_time_central = (
        datetime.datetime.now(tz=dateutil.tz.gettz("US/Central")).strftime("%x %H:%M ")
        + central_tz
    )

    if not args.dev_db:
        print(
            "WARNING: Are you sure you want to run ingestion on the production database?\n"
            + "If not, please CTRL+C then run the ingestion script with the --dev_db flag. Waiting 60 seconds..."
        )
        # Wait for 60 seconds for user to consider.
        time.sleep(60)

    dev_str = "[Dev]" if args.dev_db else ""
    broker_str = f"[{args.broker_name}]" if args.broker_name else ""
    email_header = "[Auto Ingestion Report]{}{} {}.".format(
        broker_str, dev_str, job_start_time_central
    )
    ingestion_log_filename, scorer_log_filename = set_up_logging()

    mail_report_attachments = [os.path.join(PROJECT_ROOT, "ingestion", "constants.py")]
    aurora_db = AURORA_DB_DEV_NAME if args.dev_db else AURORA_DB_NAME
    rds = ingestion.RDS(
        aurora_db, create_tables=args.create_tables, dry_mode=args.dry_mode
    )

    logging.info(f"Args: {args}")

    # TODO(P0): Add this flow to the below bit, not separate.
    if args.input_filenames:
        for input_filename in args.input_filenames:
            logging.info(input_filename)
            ingestion.ingest_file(
                input_filename, rds, args.broker_name, use_tqdm=args.use_tqdm
            )
        sys.exit(0)

    logging.info("No input files specified. Checking mail...")
    # Authenticate gmail account.
    mail_api = mail.MailAPI()
    all_value_errors = {}

    try:
        # TODO(P2): Consider using multi-processing in order to speed up ingestion.
        # Count how many successful rows and total rows were ingested per input file.
        mail_report_attachments.append(ingestion_log_filename)
        if not args.scorer_only:
            if args.ingest_all_emails:
                dirnames = ingestion_mail.find_all_broker_emails(mail_api)
            else:
                dirnames = ingestion_mail.find_latest_broker_emails(
                    mail_api, args.broker_name
                )

            total_success_count = 0
            total_row_count = 0
            total_new_shipments = 0
            total_updated_shipments = 0
            success_counts_by_file = {}
            row_counts_by_file = {}
            new_loads_by_file = {}
            updated_loads_by_file = {}
            ingestion_times_by_file = {}
            logging.info("Ingesting...")
            start_total_ingestion_time = datetime.datetime.now(datetime.timezone.utc)

            for input_dirname in dirnames:
                files = os.listdir(input_dirname)
                if len(files) > 1:
                    logging.error(f"More than one file in {input_dirname}")
                    raise AssertionError(f"More than one file in {input_dirname}")
                if len(files) == 0:
                    logging.error(f"No files in {input_dirname}")
                    raise AssertionError(f"No files in {input_dirname}")

                input_filename = os.path.join(
                    input_dirname,
                    files[0].decode() if isinstance(files[0], bytes) else files[0],
                )
                # upload latest broker file to s3
                upload_file_to_s3(
                    s3_cache_file.S3("raw-broker-data"),
                    input_filename,
                    INGESTION_DATA_DIR,
                )

                # TODO(P1): Upload input filename to S3.
                start_ingestion_time = datetime.datetime.now(datetime.timezone.utc)
                (
                    all_value_errors[input_filename],
                    success_counts_by_file[input_filename],
                    row_counts_by_file[input_filename],
                    new_loads_by_file[input_filename],
                    updated_loads_by_file[input_filename],
                ) = ingestion.ingest_file(
                    input_filename, rds, args.broker_name, use_tqdm=args.use_tqdm
                )
                total_success_count += success_counts_by_file[input_filename]
                total_row_count += row_counts_by_file[input_filename]
                total_new_shipments += new_loads_by_file[input_filename]
                total_updated_shipments += updated_loads_by_file[input_filename]
                ingestion_times_by_file[input_filename] = (
                    datetime.datetime.now(datetime.timezone.utc) - start_ingestion_time
                )

                logging.info(
                    "Successfully ingested %d/%d rows from %s in %d seconds."
                    % (
                        success_counts_by_file[input_filename],
                        row_counts_by_file[input_filename],
                        input_filename,
                        ingestion_times_by_file[input_filename].total_seconds(),
                    )
                )
            total_ingestion_time = (
                datetime.datetime.now(datetime.timezone.utc)
                - start_total_ingestion_time
            )
            logging.info("Finished ingestions.")

            if total_row_count == 0:
                logging.error("No rows were ingested.")
                raise ValueError("No rows were ingested.")

        scoring_time = "NA (not run)"
        if not args.ingestion_only:
            logging.info("Scoring...")
            start_scoring_time = datetime.datetime.now(datetime.timezone.utc)
            # mail_report_attachments.append(scorer_log_filename)
            scorer_object = scorer.Scorer(aurora_db, scorer_log_filename)
            # Choose how to group shipments for cost score.
            scorer_object.score_all_shipments_by_lane()
            scoring_time = (
                datetime.datetime.now(datetime.timezone.utc) - start_scoring_time
            )
            logging.info("Finished scoring.")
        job_time = datetime.datetime.now(datetime.timezone.utc) - job_start_time

        logging.info("Mailing...")
        low_success_rate_alert_flag = False
        mail_body = f"Ingestion{dev_str} ran successfully in {job_time}. "
        if not args.scorer_only:
            mail_body += f"Ingestion took {total_ingestion_time}. \n"
            mail_body += f"Total new loads/shipments: {total_new_shipments}.\n"
            mail_body += "Total updated loads/shipments: {}.\n".format(
                total_updated_shipments
            )
            mail_body += "Invalid rows and runtimes are listed below by file.\n\n"
        if not args.ingestion_only:
            mail_body += f"Scoring took {scoring_time}. \n"

        for input_filename in all_value_errors:
            ingestion_success_rate = percentage(
                success_counts_by_file[input_filename],
                row_counts_by_file[input_filename],
            )
            mail_body += f"{input_filename}:\n"
            if all_value_errors[input_filename]:
                for k, v in all_value_errors[input_filename].items():
                    mail_body += f"\t{k}: {v}\n"
            if ingestion_success_rate < LOW_INGESTION_SUCCESS_RATE_THRESHOLD:
                low_success_rate_alert_flag = True
                mail_body += "\tIngested {}/{} rows ({}%), resulting low ingestion percentage.\n".format(
                    success_counts_by_file[input_filename],
                    row_counts_by_file[input_filename],
                    ingestion_success_rate,
                )
            else:
                mail_body += "\tSuccessfully ingested {}/{} rows ({}%).\n".format(
                    success_counts_by_file[input_filename],
                    row_counts_by_file[input_filename],
                    ingestion_success_rate,
                )
            mail_body += "\tIngestion time: {}.\n\n".format(
                fmt_timedelta(ingestion_times_by_file[input_filename])
            )

        if not args.scorer_only and total_success_count == total_row_count:
            mail_body += "No invalid rows! 🙂\n"
        if args.scorer_only:
            mail_api.send_email_aws(
                OUR_EMAIL,
                ["<EMAIL>"],
                f"[Scorer]{email_header}",
                mail_body,
                mail_report_attachments,  # Scorer log files are typically too big to send.
            )
        else:
            if low_success_rate_alert_flag:
                email_header = "[Auto Ingestion Failure Report]{}{} {}.".format(
                    broker_str, dev_str, job_start_time_central
                )
                failure_mail_body = (
                    "Alert email due to low ingestion percentage of files.\n"
                    + mail_body
                )
                mail_api.send_email_aws(
                    OUR_EMAIL,
                    ["<EMAIL>"],
                    email_header,
                    failure_mail_body,
                    mail_report_attachments,
                )
            else:
                mail_api.send_email_aws(
                    OUR_EMAIL,
                    ["<EMAIL>"],
                    "{} {}/{} ({}%)".format(
                        email_header,
                        total_success_count,
                        total_row_count,
                        percentage(total_success_count, total_row_count),
                    ),
                    mail_body,
                    mail_report_attachments,
                )
        logging.info("Finished mailing.")
    except Exception as e:
        logging.error(f"{type(e).__name__}: {e}")
        email_header = "[Auto Ingestion Failure Report]{}{}{} {}.".format(
            "[Scorer]" if args.scorer_only else "",
            broker_str,
            dev_str,
            job_start_time_central,
        )
        mail_api.send_email_aws(
            OUR_EMAIL,
            ["<EMAIL>"],
            f"{email_header}",
            '{} failed with severe error "{}": {}\n\nError:\n\ttype: "{}"\n\tvalue: "{}"\n\ttraceback:\n{}\nCheck attached log.'.format(
                email_header,
                type(e).__name__,
                e,
                sys.exc_info()[0],
                sys.exc_info()[1],
                traceback.format_exc(),
            ),
            mail_report_attachments,
        )
        logging.error("Mailed error report.")


if __name__ == "__main__":
    mail_api = mail.MailAPI()
    # Start bar as a process
    email_header = "Auto Ingestion Dev" if args.dev_db else "Auto Ingestion"
    p = multiprocessing.Process(target=main)
    p.start()

    # Wait for args.timeout hours or until process finishes
    p.join(args.timeout * 60 * 60)

    # If thread is still active
    if p.is_alive():
        timeout_str = fmt_timedelta(datetime.timedelta(hours=args.timeout))
        logging.error(f"Job timed out after {timeout_str} seconds.")
        mail_api.send_email_aws(
            ["<EMAIL>"],
            f"[{email_header} Failure Report]",
            f"Ingestion timed out after {timeout_str}.",
            os.listdir("logs"),
        )

        # Terminate - may not work if process is stuck for good
        p.terminate()
        # OR Kill - will work for sure, no chance for process to finish nicely however
        # p.kill()

        p.join()

# Sample commands for running locally:
# python main.py --dev_db --ingestion_only --broker_name "Sage Freight" --use_tqdm --dry_mode
# python main.py --dev_db --scorer_only --use_tqdm

```

## rows.py

**File Path:** `rows.py`

```python
import datetime
import logging
import typing
from abc import ABCMeta
from abc import abstractmethod

import gitinfo
import pandas as pd
from constants import BROKER_COLUMN_HANDLERS
from constants import COUNTRY_CODE_DICT
from constants import MAX_SHIPMENT_MARGIN
from constants import MAX_SHIPMENT_WEIGHT
from constants import MIN_SHIPMENT_WEIGHT
from constants import NA_STATES_PROVINCES
from constants import SHIPMENT_MODE_DICT
from constants import SHIPMENT_RANK_DICT
from constants import SHIPMENT_STATUS_DICT
from constants import ShipmentStatus
from constants import TIME_NORMALIZATION_EXEMPT_BROKERS
from rows_util import check_if_valid_timestamp
from rows_util import classify_shipment
from rows_util import compute_cogs_rev_err
from rows_util import detect_customer_direct
from rows_util import is_cogs_rev_na
from rows_util import normalize_times

from common import google_maps_utils
from common.constants import BrokerNames
from common.constants import EQUIPMENT_TYPE_DICT
from common.rds_api import RDS


class Row(metaclass=ABCMeta):
    def __init__(self, **kwargs):
        self.id = None
        self.key = ()
        self.values = {}
        self._validate(**kwargs)

    @abstractmethod
    def _validate(self, **kwargs):
        pass

    # Remove all NA values from dictionary.
    def to_dict(self):
        return {k: v for k, v in self.values.items() if not pd.isna(v)}


class CityRow(Row):
    # TODO(P1): Write unit tests for this.
    def _validate(self, city: str, state: str, country: str):
        self.city = pd.NA if pd.isna(city) or not city else city.title()
        if pd.isna(state):
            logging.error("State is missing.")
            raise ValueError("State is missing.")
        if pd.isna(city):
            logging.error("City is missing.")
            raise ValueError("City is missing.")
        if not isinstance(state, str):
            logging.error(f"State {state} is not a string.")
            raise TypeError("State is not a string.")
        if not isinstance(city, str):
            logging.error(f"City {city} is not a string.")
            raise TypeError("City is not a string.")
        if len(state) != 2:
            logging.error(f"Expected 2-character state/province code, given {state}.")
            raise ValueError("Expected 2-character state/province code.")
        self.state = state.upper()
        expected_country = NA_STATES_PROVINCES.df.loc[state].values[0]
        if pd.isna(country):
            self.country = expected_country
            logging.info(f"Country not given, using {self.country} from {self.state}.")
        elif not isinstance(country, str):
            logging.error(f"Country {country} is not a string.")
            raise TypeError("Country is not a string.")
        elif country.strip().lower() in COUNTRY_CODE_DICT:
            self.country = COUNTRY_CODE_DICT[country.strip().lower()]
            if self.country != expected_country:
                logging.error(
                    "Expected country {}, was given {}.".format(
                        expected_country, self.country
                    )
                )
                raise ValueError(
                    "Given country does not match expected country based on state/province code."
                )
        else:
            logging.error(f"Country code {country} is not in country code dict.")
            raise ValueError("Country code is not in country code dict.")
        if self.country not in ["US", "CA", "MX"]:
            logging.error(f"Country {self.country} is not of set US, CA, MX.")
            raise ValueError("Country is not of set US, CA, MX.")
        # TODO(P2): Add city, state, country verification based on zip (using google maps?).
        self.key = (self.city, self.state, self.country)
        self.values = {"city": self.city, "state": self.state, "country": self.country}


class ZipRow(Row):
    # TODO(P1): Write unit tests for this.
    def _validate(
        self,
        zipcode,
        country: str,
        city: str,
        state: str,
        return_dict: dict,
        rds: RDS,
        GMAPS_UTILS: google_maps_utils.GoogleMapsUtils,
    ):
        if pd.isna(zipcode) or not zipcode:
            zipcode = GMAPS_UTILS.get_zip_from_city_state(city, state, country)
            return_dict["derived_cols"].add(str(rds.zips.columns["zip"]))
        zip_len = 6 if country == "CA" else 5
        if type(zipcode) == int:
            self.zip = str(zipcode).replace(" ", "")
        else:
            if type(zipcode) != str:
                logging.error(
                    "Zipcode {} has type {}, which is not str or int".format(
                        zipcode, type(zipcode)
                    )
                )
                raise ValueError("Type of zipcode is not str or int.")
            self.zip = zipcode.replace(" ", "")

        if "-" in self.zip:
            if country != "US":
                logging.error(f"Zipcode {self.zip} has hyphen, but country is not US.")
                raise ValueError("Zipcode contains hyphen, but country is not US.")
            self.zip = self.zip.split("-")[0]

        # Consider plus4 for zip codes in the US.
        if country == "US" and len(self.zip) > 5:
            # Split on last 4 digits of string.
            self.zip = self.zip[:-4]

        if len(self.zip) < zip_len:
            logging.warning(
                "Zip {} does not have correct zip length of {} for {}".format(
                    self.zip, zip_len, country
                )
            )
            self.zip = "0" * (zip_len - len(self.zip)) + self.zip
        elif len(self.zip) > zip_len:
            logging.error(
                "Zip {} does not have correct zip length of {} for {}".format(
                    self.zip, zip_len, country
                )
            )
            raise ValueError("Zipcode does not have correct zip length.")
        # TODO(P2): Add city, state, country verification based on zip (using google maps?).
        self.key = self.zip
        self.values = {"zip": self.zip}


# Junction table
class CityZipRow(Row):
    def _validate(self, city_id: str, zip_id: str):
        self.key = (city_id, zip_id)
        self.values = {"cityId": city_id, "zipId": zip_id}


# Change lane row to identify based on origin city, origin state, origin country, destination city, destination state, destination country
class LaneRow(Row):
    def _validate(
        self,
        origin_city_id: str,
        destination_city_id: str,
        shipment_mode: str,
        equipment_type: str,
    ):
        if pd.isna(shipment_mode) or not shipment_mode:
            logging.warning("Shipment mode is empty.")
        elif shipment_mode.strip().lower() in SHIPMENT_MODE_DICT:
            shipment_mode = SHIPMENT_MODE_DICT[shipment_mode.strip().lower()]
        else:
            logging.error(f"Shipment mode {shipment_mode} is invalid.")
            raise ValueError("Shipment mode is invalid.")

        if pd.isna(equipment_type) or not equipment_type:
            self.equipment_type = pd.NA
            logging.warning("Equipment type is empty.")
        elif equipment_type.strip().lower() in EQUIPMENT_TYPE_DICT:
            self.equipment_type = EQUIPMENT_TYPE_DICT[equipment_type.strip().lower()]
        else:
            logging.error(f"Equipment type {equipment_type} is invalid.")
            raise ValueError("Equipment type is invalid.")

        self.key = (origin_city_id, destination_city_id)
        self.values = {
            "originCityId": origin_city_id,
            "destinationCityId": destination_city_id,
            "shipmentMode": shipment_mode,
            "equipmentType": self.equipment_type,
        }


class BrokerRow(Row):
    def _validate(self, name: str):
        self.key = (name,)
        self.name = name.strip().replace(",", "")
        self.values = {"name": self.name}


class ShipperRow(Row):
    def _validate(self, name: str, business_unit=None):
        self.key = (name, business_unit)
        self.values = {"name": name, "businessUnit": business_unit}


class ShipmentRow(Row):
    def _validate(
        self,
        broker_id: str,
        broker_name: str,
        lane_id: int,
        shipper_id: str,
        broker_primary_reference: int,
        shipper_primary_reference: int,
        stop_count: int,
        distance_miles: float,
        cogs_line_haul: float,
        cogs_fuel: float,
        cogs_accessorial: float,
        cogs_total: float,
        revenue_line_haul: float,
        revenue_fuel: float,
        revenue_accessorial: float,
        revenue_total: float,
        origin_warehouse: str,
        destination_warehouse: str,
        shipment_status: str,
        shipment_rank: str,
        weight: float,
        origin_open_time: datetime.datetime,
        origin_close_time: datetime.datetime,
        origin_arrival_time: datetime.datetime,
        origin_departure_time: datetime.datetime,
        origin_zip_id: str,
        destination_open_time: datetime.datetime,
        destination_close_time: datetime.datetime,
        destination_arrival_time: datetime.datetime,
        destination_departure_time: datetime.datetime,
        destination_zip_id: str,
        load_creation_time_utc: datetime.datetime,
        load_activation_time_utc: datetime.datetime,
        carrier_assigned_time_utc: datetime.datetime,
        origin_city: CityRow,
        destination_city: CityRow,
        origin_zip: ZipRow,
        destination_zip: ZipRow,
        custom_timezones: dict,
        return_dict: dict,
        rds: RDS,
        GMAPS_UTILS: google_maps_utils.GoogleMapsUtils,
    ):
        """Construct shipment row

        TODO(P1): Add all params to docstring
        :param custom_timezones: Dictionary of timezones to localize to normalize from specified timezone.
         Key is column name, value is pytz.timezone to normalize from.
        """
        # Distance miles
        if pd.isna(distance_miles) or distance_miles < 0:
            logging.warning(f"Distance {distance_miles} is empty or negative.")
            origin_str = f"{origin_city.city}, {origin_city.state} {origin_zip.zip}"
            destination_str = f"{destination_city.city}, {destination_city.state} {destination_zip.zip}"
            try:
                distance_miles = GMAPS_UTILS.get_distance_miles(
                    origin_str, destination_str
                )
            except Exception as e:
                logging.error(f"Error getting distance miles: {e}")
                distance_miles = pd.NA
            return_dict["derived_cols"].add(str(rds.shipments.columns["distanceMiles"]))

        # COGS
        cogs_error = compute_cogs_rev_err(
            cogs_line_haul, cogs_fuel, cogs_accessorial, cogs_total
        )
        cogs_na = is_cogs_rev_na(
            cogs_line_haul, cogs_fuel, cogs_accessorial, cogs_total
        )
        if not cogs_na and cogs_error >= 0.01:
            logging.error(
                "Cogs not adding up. {}: cogs_line_haul: {}, cogs_fuel: {}, cogs_accesorial: {}, cogs_total: {}".format(
                    shipper_primary_reference,
                    cogs_line_haul,
                    cogs_fuel,
                    cogs_accessorial,
                    cogs_total,
                )
            )
            raise ValueError("COGS not adding up.")

        # Revenue
        revenue_error = compute_cogs_rev_err(
            revenue_line_haul, revenue_fuel, revenue_accessorial, revenue_total
        )
        revenue_na = is_cogs_rev_na(
            revenue_line_haul, revenue_fuel, revenue_accessorial, revenue_total
        )
        if not revenue_na and revenue_error >= 0.01:
            logging.error(
                "Revenue not adding up. {}: revenue_line_haul: {}, revenue_fuel: {}, revenue_accessorial: {}, revenue_total: {}".format(
                    shipper_primary_reference,
                    revenue_line_haul,
                    revenue_fuel,
                    revenue_accessorial,
                    revenue_total,
                )
            )
            raise ValueError("Revenue not adding up.")

        # Margin
        if revenue_total - cogs_total > MAX_SHIPMENT_MARGIN:
            logging.error(
                "Shipment {} has a margin too large of {}.".format(
                    shipper_primary_reference, revenue_total - cogs_total
                )
            )
            raise ValueError("Shipment has a margin that is too large.")

        # Shipment class
        shipment_class = classify_shipment(
            cogs_line_haul,
            cogs_fuel,
            cogs_accessorial,
            cogs_total,
            revenue_line_haul,
            revenue_fuel,
            revenue_accessorial,
            revenue_total,
            distance_miles,
        )

        # Shipment status
        if pd.isna(shipment_status) or not shipment_status:
            logging.warning("Shipment status is empty.")
        elif shipment_status.strip().lower() in SHIPMENT_STATUS_DICT:
            shipment_status = SHIPMENT_STATUS_DICT[shipment_status.strip().lower()]
        else:
            logging.error(f"Shipment status {shipment_status} is invalid.")
            raise ValueError("Shipment status is invalid.")
        ingested_shipment_statuses = [
            ShipmentStatus.delivered,
            ShipmentStatus.in_transit,
            ShipmentStatus.unloading,
        ]
        if shipment_status not in ingested_shipment_statuses:
            logging.error(f"Shipment status {shipment_status} will not be ingested.")
            raise ValueError("Shipment status will not be ingested.")

        # Weight
        if not pd.isna(weight) and (
            weight < MIN_SHIPMENT_WEIGHT or weight > MAX_SHIPMENT_WEIGHT
        ):
            logging.warning(f"Weight {weight} is not in range.")
            weight = pd.NA

        # Shipment rank
        if pd.isna(shipment_rank) or not shipment_rank:
            logging.warning("Shipment rank is empty.")
        elif shipment_rank.strip().lower() in SHIPMENT_RANK_DICT:
            shipment_rank = SHIPMENT_RANK_DICT[shipment_rank.strip().lower()]
        else:
            logging.warning(f"Shipment rank {shipment_rank} is not ingested.")

        # Detect customer direct loads
        if pd.isna(destination_warehouse) or not destination_warehouse:
            logging.warning(
                "Null destination warehouse. Not computing customer-direct."
            )
            customer_direct = pd.NA
        else:
            customer_direct = detect_customer_direct(str(destination_warehouse))

        broker_column_dict = {
            "shipmentStatus": shipment_status,
            "originOpenTime": origin_open_time,
            "originCloseTime": origin_close_time,
            "originArrivalTime": origin_arrival_time,
            "originDepartureTime": origin_departure_time,
            "destinationOpenTime": destination_open_time,
            "destinationCloseTime": destination_close_time,
            "destinationArrivalTime": destination_arrival_time,
            "destinationDepartureTime": destination_departure_time,
            "loadCreationTime": load_creation_time_utc,
            "loadActivationTime": load_activation_time_utc,
            "carrierAssignedTime": carrier_assigned_time_utc,
        }

        # TODO: Recode BrokerColumnHandlers more generic
        if broker_name == BrokerNames.capstone_logistics and pd.isna(
            broker_column_dict["originCloseTime"]
        ):
            broker_column_dict["originCloseTime"] = broker_column_dict["originOpenTime"]

        if broker_name == BrokerNames.capstone_logistics and pd.isna(
            broker_column_dict["destinationCloseTime"]
        ):
            broker_column_dict["destinationCloseTime"] = broker_column_dict[
                "destinationOpenTime"
            ]

        broker_col_handlers = BROKER_COLUMN_HANDLERS.get(broker_name, {})
        # Handle Broker columns that need specific modification
        for col, handler in broker_col_handlers.items():
            if col in broker_column_dict:
                broker_column_dict[col] = handler(broker_column_dict[col])
            else:
                raise ValueError(
                    f"Invalid column '{col}' specified for broker '{broker_name}'."
                )

        broker_time_column_dict = {
            key: value for key, value in broker_column_dict.items() if "Time" in key
        }

        # Check if timestamps are valid.
        for time in broker_time_column_dict.keys():
            broker_time_column_dict[time] = check_if_valid_timestamp(
                broker_time_column_dict[time]
            )

        # Normalize timestamps
        if broker_col_handlers not in TIME_NORMALIZATION_EXEMPT_BROKERS:
            normalized_times = normalize_times(
                broker_time_column_dict,
                custom_timezones,
                origin_city,
                origin_zip,
                destination_city,
                destination_zip,
                GMAPS_UTILS,
            )

        broker_column_dict.update(normalized_times)

        self.key = (
            broker_id,
            shipper_id,
            broker_primary_reference,
            shipper_primary_reference,
        )
        self.values = {
            "brokerId": broker_id,
            "laneId": lane_id,
            "shipperId": shipper_id,
            "brokerPrimaryReference": broker_primary_reference,
            "shipperPrimaryReference": shipper_primary_reference,
            "stopCount": stop_count,
            "distanceMiles": distance_miles,
            "cogsLineHaul": cogs_line_haul,
            "cogsAccessorial": (
                cogs_accessorial if not pd.isna(cogs_accessorial) else pd.NA
            ),
            "cogsFuel": cogs_fuel,
            "cogsTotal": cogs_total,
            "revenueLineHaul": revenue_line_haul,
            "revenueFuel": revenue_fuel,
            "revenueAccessorial": (
                revenue_accessorial if not pd.isna(revenue_accessorial) else pd.NA
            ),
            "revenueTotal": revenue_total,
            "originWarehouse": origin_warehouse,
            "destinationWarehouse": destination_warehouse,
            "originZipId": origin_zip_id,
            "destinationZipId": destination_zip_id,
            "shipmentRank": shipment_rank,
            "weight": weight,
            "shipmentClass": shipment_class,
            "customerDirect": customer_direct,
        }

        for col_name, col_value in broker_column_dict.items():
            self.values[col_name] = col_value


class ShipmentMetadataRow(Row):
    def _validate(
        self,
        shipment_id: str,
        source_file: str,
        derived_cols: typing.List[str],
        shipment_diffs: dict,
    ):
        # The hash is searchable in GitHub to find code status.
        git_info = gitinfo.get_git_info()
        self.key = (shipment_id,)
        self.values = {
            "shipmentId": shipment_id,
            "sourceFile": source_file,
            "lastModifiedTime": datetime.datetime.utcnow().replace(microsecond=0),
            "derivedColumns": ",".join(derived_cols),
            "gitCommitHash": git_info["commit"],
            "shipmentDiffs": shipment_diffs,
            "gitAuthor": git_info["author"].split("<")[0].strip(),
        }

```

## rows_util.py

**File Path:** `rows_util.py`

```python
import datetime
import logging
import re

import numpy as np
import pandas as pd
import pytz
from constants import COMPANY_REGEX_PATTERNS
from constants import ShipmentClass
from constants import TONU_LCD
from constants import TONU_MAX
from constants import TONU_MIN
from constants import USD_PER_MI_MIN

from common import google_maps_utils

dt = datetime.datetime


def check_if_valid_timestamp(
    time: pd.Timestamp,
) -> pd.Timestamp:
    if pd.isna(time) or type(time) == str and time.isspace():
        return pd.NA
    if type(time) != dt and type(time) != pd.Timestamp:
        logging.error(f"Time {time} has type {type(time)}, which is not datetime.")
        raise ValueError("Type of time is not datetime.")
    return time


def is_tz_aware(time: pd.Timestamp):
    """Checks if a datetime object is timezone aware."""
    return time.tzinfo is not None and time.tzinfo.utcoffset(time) is not None


def normalize_time_given_tz(time: pd.Timestamp, tz: pytz.timezone) -> pd.Timestamp:
    """Normalize a time to UTC.

    First, localize the naive datetime object time with given timezone tz. Then, convert to UTC.
    """
    if pd.isna(time):
        return pd.NaT

    if not is_tz_aware(time):
        time = tz.localize(time)
    else:
        # Verify that time is in the correct timezone, based on given tz which comes from zip.
        if type(time.tzinfo) != type(tz):
            logging.error(f"Time {time} is not in timezone {tz}.")
            raise ValueError("Time is not in correct timezone.")

    # If timezone is localized, normalize to UTC.
    return pytz.utc.normalize(time).replace(tzinfo=None)


def normalize_time_given_zip(
    city_row,
    zip_row,
    time: pd.Timestamp,
    GMAPS_UTILS: google_maps_utils.GoogleMapsUtils,
) -> dt:
    tz = GMAPS_UTILS.get_tz_from_zip(zip_row.zip, city_row.country)
    return normalize_time_given_tz(time, tz)


def compute_cogs_rev_err(
    line_haul: float, fuel: float, accessorial: float, total: float
) -> float:
    """Computes the COGS or revenue error for each row."""
    # If each field is pd.NA, convert to np.nan.
    line_haul = np.nan if pd.isna(line_haul) else line_haul
    fuel = np.nan if pd.isna(fuel) else fuel
    accessorial = np.nan if pd.isna(accessorial) else accessorial
    total = np.nan if pd.isna(total) else total
    # np.nansum to treat nan as 0.
    error = abs(np.nansum([line_haul, fuel, accessorial, -total]))
    return error


def is_cogs_rev_na(
    line_haul: float, fuel: float, accessorial: float, total: float
) -> bool:
    """Checks if all constituent fields or the total field is pd.NA."""
    if pd.isna(line_haul) and pd.isna(fuel) and pd.isna(accessorial):
        return True
    if pd.isna(total):
        return True
    return False


def classify_shipment(
    cogs_line_haul,
    cogs_fuel,
    cogs_accessorial,
    cogs_total,
    revenue_line_haul,
    revenue_fuel,
    revenue_accessorial,
    revenue_total,
    distance_miles,
) -> ShipmentClass:
    def is_empty(v):
        return pd.isna(v) or v == 0

    if is_empty(revenue_total) and is_empty(cogs_total):
        return ShipmentClass.not_shipment

    def is_tonu(total):
        return (
            not is_empty(total)
            and total >= TONU_MIN
            and total <= TONU_MAX
            and total % TONU_LCD == 0
        )

    if (
        (is_tonu(cogs_total) and is_empty(revenue_total))
        or (is_tonu(revenue_total) and is_empty(cogs_total))
        or (is_tonu(cogs_total) and is_tonu(revenue_total))
    ):
        return ShipmentClass.accessorial_tonu

    if is_empty(cogs_total) or is_empty(revenue_total):
        return ShipmentClass.accessorial

    if (
        is_empty(cogs_line_haul)
        and is_empty(cogs_fuel)
        and not is_empty(cogs_accessorial)
    ):
        return ShipmentClass.accessorial
    if (
        is_empty(revenue_line_haul)
        and is_empty(revenue_fuel)
        and not is_empty(revenue_accessorial)
    ):
        return ShipmentClass.accessorial

    # Cost per mile is way too low. Must be junk.
    if distance_miles == 0:
        return ShipmentClass.canonical

    if distance_miles < 0:
        raise ValueError("Distance in miles is negative.")

    if cogs_total / distance_miles < USD_PER_MI_MIN:
        return ShipmentClass.accessorial

    if revenue_total / distance_miles < USD_PER_MI_MIN:
        return ShipmentClass.accessorial

    return ShipmentClass.canonical


def detect_customer_direct(destination_name: str) -> str:
    """
    Companies that charge a fee on late delivery are designated
    customer direct, or "white glove."
    Determines if destination is a customer direct warehouse.
    """
    customer_direct_match = pd.NA

    for expr in COMPANY_REGEX_PATTERNS:
        matches = re.findall(expr, destination_name.lower())

        if len(matches) != 0:
            if not pd.isna(customer_direct_match):
                logging.error(
                    f"""Discovered multiple matches for expr {expr}:
                            {customer_direct_match}, {matches}"""
                )
                raise AssertionError("Multiple customer direct destinations found.")
            customer_direct_match = COMPANY_REGEX_PATTERNS[expr]

    return customer_direct_match


def normalize_times(
    time_columns: dict,
    custom_timezones: dict,
    origin_city_row,
    origin_zip_row,
    destination_city_row,
    destination_zip_row,
    GMAPS_UTILS: google_maps_utils.GoogleMapsUtils,
):
    """
    Normalization on times, whether with custom_timezones or with city_row, zip_row and GMAP_UTILS
    """
    for time in time_columns.keys():
        if time in custom_timezones:
            assert "Time" in time, f"Invalid time column name: {time}"
            time_columns[time] = normalize_time_given_tz(
                time_columns[time], custom_timezones[time]
            )
        # Add times normalized to UTC based on origin/destination zip.
        elif "origin" in time:
            time_columns[time] = normalize_time_given_zip(
                origin_city_row, origin_zip_row, time_columns[time], GMAPS_UTILS
            )
        elif "destination" in time:
            time_columns[time] = normalize_time_given_zip(
                destination_city_row,
                destination_zip_row,
                time_columns[time],
                GMAPS_UTILS,
            )
    return time_columns

```

## rows_util_test.py

**File Path:** `rows_util_test.py`

```python
import datetime
import unittest

import pandas as pd
import pytz
import rows_util
from parameterized import parameterized


dt = datetime.datetime


class TestUtil(unittest.TestCase):
    @parameterized.expand(
        [
            [
                pd.Timestamp(2011, 8, 15, 8, 15, 12, 0, tz=pytz.UTC),
                True,
            ],  # case where pd.Timestamp object is timezone aware
            [
                pd.Timestamp(2023, 3, 16, 12, 0),
                False,
            ],  # case where time.tzinfo is None, i.e timezone naive
        ]
    )
    def test_is_tz_aware(self, time: pd.Timestamp, aware: bool):
        """Tests datetime/time aware function"""
        self.assertEqual(rows_util.is_tz_aware(time), aware)

    @parameterized.expand(
        [
            [
                pd.Timestamp(2023, 3, 16, 12, 0),
                pytz.timezone("US/Pacific"),
                pd.Timestamp(2023, 3, 16, 19, 0),
            ],  # base case with naize datetime and timezone input
            [
                pytz.FixedOffset(-120).localize(pd.Timestamp(2023, 3, 18, 12, 0)),
                pytz.FixedOffset(-120),
                pd.Timestamp(2023, 3, 18, 14, 0),
            ],  # base case where datetime and tz input have FixedOffsets
            [
                pytz.timezone("US/Eastern").localize(pd.Timestamp(2023, 3, 16, 12, 0)),
                pytz.timezone("US/Eastern"),
                pd.Timestamp(2023, 3, 16, 16, 0),
            ],  # base case with naize datetime and timezone input
        ]
    )
    def test_normalize_time_given_tz(
        self, time: pd.Timestamp, tz: pytz.timezone, normalized_dt: pd.Timestamp
    ):
        """Tests whether function returns datetime that is normalized to UTC"""
        self.assertEqual(rows_util.normalize_time_given_tz(time, tz), normalized_dt)

    def test_edge_case_normalize_time_given_tz(self):
        """Tests ValueError cases of normalize_time_given_tz function"""
        self.assertTrue(
            pd.isna(
                rows_util.normalize_time_given_tz(pd.NA, pytz.timezone("US/Eastern"))
            )
        )  # case where time is pd.NA
        self.assertRaises(
            ValueError,
            rows_util.normalize_time_given_tz,
            pd.Timestamp(2023, 3, 14, 12, 0, tz=pytz.timezone("US/Pacific")),
            pytz.timezone("UTC"),
        )  # case where timezone of timestamp input and tz input are different

    def test_check_if_valid_timestamp(self):
        """Tests check_if_valid_timestamp function"""
        self.assertRaises(
            ValueError, rows_util.check_if_valid_timestamp, "test string"
        )  # case where input is not pd.Timestamp object
        self.assertTrue(pd.isna(rows_util.check_if_valid_timestamp("\n\n  ")))
        # case where input is empty string with spaces and newlines

    @parameterized.expand(
        [
            [pd.NA, pd.NA, pd.NA, pd.NA, 0],  # case where all inputs are pd.NA
            [pd.NaT, pd.NaT, pd.NaT, pd.NaT, 0],  # case where all inputs are pd.NaT
            [pd.NA, pd.NA, 3.442, 1.32, 2.122],  # case where some inputs are pd.NA
        ]
    )
    def test_compute_cogs_rev_err(
        self,
        line_haul: float,
        fuel: float,
        accessorial: float,
        total: float,
        error: float,
    ):
        """Checks computer_cogs_rev_err returns correct output"""
        self.assertEqual(
            rows_util.compute_cogs_rev_err(line_haul, fuel, accessorial, total), error
        )

    @parameterized.expand(
        [
            [pd.NA, pd.NA, pd.NA, pd.NA, True],  # case where all inputs are pd.NA
            [pd.NaT, pd.NaT, pd.NaT, pd.NaT, True],  # case where all inputs are pd.NaT
            [3.12, 5.13421, 1.2342, pd.NA, True],  # case where total input is pd.NA
            [1.23, 2.45, 4.123, 4.4213, False],  # base case where all inputs are floats
        ]
    )
    def test_is_cogs_rev_na(
        self,
        line_haul: float,
        fuel: float,
        accessorial: float,
        total: float,
        all_NA: bool,
    ):
        """Checks is_cogs_rev_na returns correct boolean output"""
        self.assertEqual(
            rows_util.is_cogs_rev_na(line_haul, fuel, accessorial, total), all_NA
        )

    def test_detect_customer_direct(self):
        """Tests base case and assertion error case for detect_customer_direct function"""
        self.assertRaises(
            AssertionError, rows_util.detect_customer_direct, "Safeway riteaid"
        )  # Assertion Error case having multiple customer direct destinations
        self.assertEqual(
            rows_util.detect_customer_direct("Safeway"), "Safeway"
        )  # base case


if __name__ == "__main__":
    unittest.main()

```

## __init__.py

**File Path:** `mapping\__init__.py`

```python

```

## column_mapping.py

**File Path:** `mapping\column_mapping.py`

```python
import enum

from mapping.models import edit_distance


class GoldenColumns(enum.Enum):
    broker_name = "Broker Name"
    customer_name = "Customer Name"
    business_unit = "Business Unit"
    broker_primary_reference = "Broker Primary Reference"
    shipper_primary_reference = "Shipper Primary Reference"
    shipment_mode = "Shipment Mode"
    equipment_type = "Equipment Type"
    origin_name = "Origin Name"
    destination_name = "Destination Name"
    origin_city = "Origin City"
    destination_city = "Destination City"
    origin_state = "Origin State"
    destination_state = "Destination State"
    origin_zip = "Origin Zip"
    destination_zip = "Destination Zip"
    origin_country = "Origin Country"
    destination_country = "Destination Country"
    shipment_status = "Shipment Status"
    shipment_rank = "Shipment Rank"
    weight = "Weight"
    origin_open = "Origin Open"
    destination_open = "Destination Open"
    origin_close = "Origin Close"
    destination_close = "Destination Close"
    origin_arrival = "Origin Arrival"
    destination_arrival = "Destination Arrival"
    origin_departure = "Origin Departure"
    destination_departure = "Destination Departure"
    stop_count = "Stop Count"
    distance_miles = "Distance Miles"
    cogs_line_haul = "COGS Line Haul"
    revenue_line_haul = "Revenue Line Haul"
    cogs_fuel = "COGS Fuel"
    revenue_fuel = "Revenue Fuel"
    cogs_accessorial = "COGS Accessorial"
    revenue_accessorial = "Revenue Accessorial"
    cogs_total = "COGS Total"
    revenue_total = "Revenue Total"
    load_creation = "Load Creation"
    load_activation = "Load Activation"
    carrier_assigned = "Carrier Assigned"


# Lists of all possible column values in input file.
GOLDEN_COLUMN_MAPPING = {
    GoldenColumns.broker_name: ["Broker Name"],
    GoldenColumns.customer_name: [
        "Customer Name",
        "Parent Customer Name",
        "Shipper Name",
    ],
    GoldenColumns.business_unit: ["Business Unit", "BusinessUnitID"],
    GoldenColumns.broker_primary_reference: [
        "Broker Primary Reference",
        "Supplier Primary Reference",
        "Order_ID",
    ],
    GoldenColumns.shipper_primary_reference: [
        "Shipper Primary Reference",
        "Customer Primary Reference",
    ],
    GoldenColumns.shipment_mode: ["Shipment Mode", "Mode"],
    GoldenColumns.equipment_type: ["Equipment Type", "Shipment Truck Type"],
    GoldenColumns.origin_name: ["Origin Name", "Origin Facility Name"],
    GoldenColumns.destination_name: [
        "Destination Facility Name",
        "Destination Name",
        "Destination Company Name",
    ],
    GoldenColumns.origin_city: ["Origin City"],
    GoldenColumns.destination_city: ["Destination City", "Delivery City"],
    GoldenColumns.origin_state: [
        "Origin State",
        "Origin State Province",
        "Origin Province",
    ],
    GoldenColumns.destination_state: [
        "Destination State",
        "Destination State Province",
        "Destination Province",
        "Delivery State",
    ],
    GoldenColumns.origin_zip: ["Origin Zip", "Origin Postal Code", "Origin Zipcode"],
    GoldenColumns.destination_zip: [
        "Destination Zip",
        "Destination Postal Code",
        "Delivery Zip",
        "Destination Zipcode",
    ],
    GoldenColumns.origin_country: ["Origin Country"],
    GoldenColumns.destination_country: ["Destination Country", "Delivery Country"],
    GoldenColumns.shipment_status: ["Shipment Status"],
    GoldenColumns.shipment_rank: ["Shipment Rank", "Shipment Ranking"],
    GoldenColumns.weight: ["Weight"],
    GoldenColumns.origin_open: [
        "Origin Open",
        "Origin Open D/T",
        "Earliest Pickup",
        "Origin Appt Open",
        "Origin Appointment Open",
    ],
    GoldenColumns.destination_open: [
        "Destination Open",
        "Destination Open D/T",
        "Earliest Delivery",
        "Destination Appt Open",
        "Delivery Appt Open",
        "Destination Appointment Open",
        "Delivery Appointment Open",
    ],
    GoldenColumns.origin_close: [
        "Origin Close",
        "Origin Close D/T",
        "Latest Pickup",
        "Origin Appt Close",
        "Origin Appointment Close",
    ],
    GoldenColumns.destination_close: [
        "Destination Close",
        "Destination Close D/T",
        "Latest Delivery",
        "Destination Appt Close",
        "Delivery Appt Close",
        "Destination Appointment Close",
        "Delivery Appointment Close",
    ],
    GoldenColumns.origin_arrival: [
        "Origin Arrival",
        "Origin Arrival D/T",
        "Origin Tracking Earliest Arrival",
        "Origin Carrier Arrival",
    ],
    GoldenColumns.destination_arrival: [
        "Destination Arrival",
        "Destination Arrival D/T",
        "Destination Tracking Earliest Arrival",
        "Destination Carrier Arrival",
    ],
    GoldenColumns.origin_departure: [
        "Origin Departure",
        "Origin Departure D/T",
        "Actual Pickup",
        "Origin Carrier Departure",
    ],
    GoldenColumns.destination_departure: [
        "Destination Departure",
        "Destination Departure D/T",
        "Actual Delivery",
        "Destination Carrier Departure",
    ],
    GoldenColumns.stop_count: ["Stop Count"],
    GoldenColumns.distance_miles: ["Distance", "Miles", "Distance Miles"],
    GoldenColumns.cogs_line_haul: ["COGS Line Haul", "Carrier Linehaul"],
    GoldenColumns.revenue_line_haul: ["Revenue Line Haul", "Customer Linehaul"],
    GoldenColumns.cogs_fuel: ["COGS Fuel", "Carrier Fuel"],
    GoldenColumns.revenue_fuel: ["Revenue Fuel", "Customer Fuel"],
    GoldenColumns.cogs_accessorial: [
        "COGS Accessorial",
        "Carrier Accessorial",
        "COGS Total Accessorial",
        "COGS Total Accessorials",
        "COGS Total Acessorial",
    ],
    GoldenColumns.revenue_accessorial: [
        "Revenue Accessorial",
        "Customer Accessorial",
        "Revenue Total Accessorial",
        "Revenue Total Accessorials",
        "Revenue Total Accesorial",
    ],
    GoldenColumns.cogs_total: ["COGS Total", "Carrier Charge Total", "Total COGS"],
    GoldenColumns.revenue_total: ["Revenue Total", "Customer Cost Total"],
    GoldenColumns.load_creation: [
        "Load Creation",
        "Load Create",
        "Load Creation D/T UTC",
        "Load Create D/T UTC",
        "Date Time Created UTC",
        "Load Creation Date/Time",
        "Load Creation Date Time",
        "Load_Creation_Date",
    ],
    GoldenColumns.load_activation: [
        "Load Activation",
        "Load Activate",
        "Load Activation D/T UTC",
        "Load Activate D/T UTC",
        "Off Hold Time Stamp UTC",
        "Load Activation Date/Time",
        "Load Activation Date Time",
        "Load_Activation_Date",
    ],
    GoldenColumns.carrier_assigned: [
        "Carrier Assigned",
        "Carrier Assign",
        "Carrier Assigned D/T UTC",
        "Carrier Assign D/T UTC",
        "LastCarrierAssignedDateEST",
        "Final Carrier Assigned Date/Time",
        "Final Carrier Assigned Date Time",
        "Final_Carrier_Assigned_Date",
    ],
}


def clean_str(s):
    """
    Removes all non-alphanumeric characters from a string.
    """
    replace_chars = {
        " ": "",
        "_": "",
        "-": "",
        ".": "",
    }
    cleaned_str = s.lower()
    for char in replace_chars:
        cleaned_str = cleaned_str.replace(char, replace_chars[char])
    return cleaned_str


def map_column(col, max_dist=3):
    """Map an input column name to the corresponding golden column.

    :param col: Column name to be mapped.
    :param max_dist: Maximum edit distance to allow a mapping.
    :return: Corresponding golden column.
    """
    inverse_map = {}
    for k in GOLDEN_COLUMN_MAPPING:
        inverse_map.update({i: k for i in GOLDEN_COLUMN_MAPPING[k]})

    model = edit_distance.EditDistance().get_model()
    # Calculate similarities for each column name and select the maximum.
    best_fit = sorted((model(clean_str(col), clean_str(i)), i) for i in inverse_map)[0]
    if best_fit[0] > max_dist:
        raise ValueError(f"Edit distance to any golden column >{max_dist}")
    return inverse_map[best_fit[1]]


if __name__ == "__main__":
    columns = ["Load Creation D/T UTC", "Load Creation D/T"]
    print({c: map_column(c).value for c in columns})

```

## evaluate.py

**File Path:** `mapping\evaluate.py`

```python
import pandas as pd
from models import edit_distance
from models import tfidf_cos
from tqdm import tqdm


def evaluate(model, zipped_data, top_k=1):
    X, y = zip(*zipped_data)
    y_categories = sorted(set(y))
    num_true = 0
    for i, data in tqdm(enumerate(zipped_data), total=len(zipped_data)):
        # Tuples of distances and categories.
        distances = ((model(data[0], y_i), y_i) for y_i in y_categories)
        for dist in sorted(distances)[:top_k]:
            if dist[1] == data[1]:
                num_true += 1
    return num_true / (i + 1)


if __name__ == "__main__":
    df = pd.read_csv("evaluation_data.csv", header=None)
    zipped_data = list(df[[0, 1]].value_counts().keys())

    model = edit_distance.EditDistance()
    acc = evaluate(model.get_model(), zipped_data)
    print(f"Edit Distance has {round(acc * 100)}% accuracy")
    model = tfidf_cos.TfIdfCos()
    acc = evaluate(model.get_model(), zipped_data)
    print(f"TfIdf Cos has {round(acc * 100)}% accuracy")

```

## mapper.py

**File Path:** `mapping\mapper.py`

```python
import datetime
import typing

import pandas as pd
import pytz
import uszipcode


def normalize_time(zipcode: str, time: datetime.datetime) -> datetime.datetime:
    """Normalize a time to UTC.

    :param zipcode: Zipcode of desired timezone.
    :param time: Time normalize to UTC.
    :return: Time normalized to UTC.
    """
    zip_full = search.by_zipcode(zipcode)
    tz = pytz.timezone("US/" + zip_full.timezone)
    return tz.normalize(tz.localize(time)).astimezone(pytz.utc).replace(tzinfo=None)


def process_row(row: pd.Series) -> typing.Tuple[dict, dict]:
    """Processes raw broker data into our golden columns.

    :param row: Raw broker data row as pandas Series.
    :return: Dict of golden column values, ID. Timedelta values are
    in seconds, percentage values are floats 0-1. For performance to index, positive means more expensive. For
    OTP/OTD, positive means late.
    """
    cleaned_data = {
        "Performance to Index": (row["COGS Total"] - DAT) / DAT,
        "Gross Profit Margin": (row["Revenue Total"] - row["COGS Total"])
        / row["Revenue Total"],
        "External Lead Time": (
            normalize_time(row["Origin Zip"], row["Origin Close D/T"])
            - row["Load Creation D/T UTC"]
        ).total_seconds(),
        "Internal Lead Time": (
            row["Load Activation D/T UTC"] - row["Load Creation D/T UTC"]
        ).total_seconds(),
        "Pre-book Time": (
            normalize_time(row["Origin Zip"], row["Origin Close D/T"])
            - row["Carrier Assigned D/T UTC"]
        ).total_seconds(),
        "OTP": (row["Origin Arrival D/T"] - row["Origin Close D/T"]).total_seconds(),
        "OTD": (
            row["Destination Arrival D/T"] - row["Destination Close D/T"]
        ).total_seconds(),
    }
    identifiers = {
        "Load ID": row["Customer Primary Refernce"],
        "Customer Name": row["Customer Name"],
        "Business Unit": row["Business Unit"],
    }
    return identifiers, cleaned_data


if __name__ == "__main__":
    # Setup code.
    search = uszipcode.SearchEngine(simple_zipcode=True)
    GOLDEN_COLUMNS = (
        "Performance to Index",
        "Gross Profit Margin",
        "External Lead Time",
        "Internal Lead Time",
        "Pre-book Time",
        "OTP",
        "OTD",
    )
    DAT = 500  # Arbitrary constant.
    df = pd.read_excel("../data/Synthetic Data.xlsx")

    for _, r in df.iterrows():
        ids, cleaned = process_row(r)
        # TODO(vivek, jr): Store cleaned identifiers, data.

```

## __init__.py

**File Path:** `mapping\models\__init__.py`

```python

```

## edit_distance.py

**File Path:** `mapping\models\edit_distance.py`

```python
import editdistance


class EditDistance:
    def __init__(self):
        pass

    # Model uses edit distance, negative to represent similarity.
    def get_model(self):
        def model(text1, text2):
            return editdistance.eval(text1, text2)

        return model

```

## tfidf_cos.py

**File Path:** `mapping\models\tfidf_cos.py`

```python
import re

import nltk

nltk.download("stopwords")
from nltk.corpus import stopwords
from sklearn.feature_extraction.text import TfidfVectorizer
from nltk.stem.porter import PorterStemmer


def clean(review, remove_stopwords=True):
    # Clean the text, with the option to remove stopwords.

    # Convert words to lower case and split them
    words = review.lower().split()

    # Optionally remove stop words (true by default)
    if remove_stopwords:
        stops = set(stopwords.words("english"))
        words = [w for w in words if w not in stops]

    review_text = " ".join(words)

    # Clean the text
    review_text = re.sub(r"[^A-Za-z0-9(),!.?\'\`]", " ", review_text)
    review_text = re.sub(r"\'s", " 's ", review_text)
    review_text = re.sub(r"\'ve", " 've ", review_text)
    review_text = re.sub(r"n\'t", " 't ", review_text)
    review_text = re.sub(r"\'re", " 're ", review_text)
    review_text = re.sub(r"\'d", " 'd ", review_text)
    review_text = re.sub(r"\'ll", " 'll ", review_text)
    review_text = re.sub(r",", " ", review_text)
    review_text = re.sub(r"_", " ", review_text)
    review_text = re.sub(r"\.", " ", review_text)
    review_text = re.sub(r"!", " ", review_text)
    review_text = re.sub(r"\(", " ( ", review_text)
    review_text = re.sub(r"\)", " ) ", review_text)
    review_text = re.sub(r"\?", " ", review_text)
    review_text = re.sub(r"\s{2,}", " ", review_text)

    words = review_text.split()

    # Shorten words to their stems
    stemmer = PorterStemmer()
    stemmed_words = [stemmer.stem(word) for word in words]

    review_text = " ".join(stemmed_words)

    # Return a list of words
    return review_text


class TfIdfCos:
    def __init__(self):
        self.vectorizer = TfidfVectorizer()

    # Model uses cosine distance
    def get_model(self):
        def model(text1, text2):
            text1 = clean(text1)
            text2 = clean(text2)
            tfidf = self.vectorizer.fit_transform([text1, text2])
            return 1 - (tfidf * tfidf.T).A[0, 1]

        return model

```

## __init__.py

**File Path:** `scorer\__init__.py`

```python

```

## broker_score.py

**File Path:** `scorer\broker_score.py`

```python
import pandas as pd
from scorer.util import BUSINESS_DAY_HOURS
from scorer.util import linear_quadratic_piecewise
from scorer.util import sigmoid
from scorer.util import weighted_score

CLT_THRESHOLD = int(1.5 * BUSINESS_DAY_HOURS)


def prebook_threshold_score(shipment: dict) -> float:
    """Calculate prebook score based on a constant threshold value.

    Punish score if it's less than the threshold value.

    :param shipment: Shipment object with all properties, including prebook, blt and clt.
    :return: Prebook score between 0 and 1.
    """
    if (
        pd.isna(shipment["preBook"])
        or pd.isna(shipment["clt"])
        or shipment["clt"] < CLT_THRESHOLD
    ):
        return pd.NA
    # A larger pre-book yields a higher score.
    if shipment["preBook"] > CLT_THRESHOLD:
        return 1
    return linear_quadratic_piecewise(shipment["preBook"], CLT_THRESHOLD)


def prebook_piecewise_linear_score(shipment: dict) -> float:
    """Calculate prebook score on various linear scales.

    Punish score more severely at higher prebook and more gradually at lower prebook.
    https://www.desmos.com/calculator/cprmt2kbq2

    :param shipment: Shipment object with all properties, including prebook, blt and clt.
    :return: Prebook score between 0 and 1.
    """
    if (
        pd.isna(shipment["preBook"])
        or pd.isna(shipment["clt"])
        or shipment["clt"] < CLT_THRESHOLD
    ):
        return pd.NA
    # A larger pre-book yields a higher score.
    if shipment["preBook"] > CLT_THRESHOLD:
        return 1.0
    if shipment["preBook"] > BUSINESS_DAY_HOURS:
        # scale to 0.9 - 1.0
        return (shipment["preBook"] + 35) / 50
    if shipment["preBook"] > int(0.5 * BUSINESS_DAY_HOURS):
        # scale to 0.6 - 0.9
        return (3 * shipment["preBook"] + 15) / 50
    # scale to 0.0 - 0.6
    return 3 / 25 * shipment["preBook"]


def ltu_score(shipment: dict, epsilon=0.000001) -> float:
    """Calculate ltu score based on a constant threshold value.

    LTU is lead time utilization. Is calculated as blt / clt or clt - blt. The latter is used here.
    Punish score if it's greater than the threshold value. If blt = 0, give score of 0.

    :param shipment: Shipment object with all properties, including blt and clt.
    :return: LTU score between 0 and 1.
    """
    if (
        pd.isna(shipment["clt"])
        or pd.isna(shipment["blt"])
        or shipment["clt"] < CLT_THRESHOLD
    ):
        return pd.NA
    # Lead time difference between external lead and internal lead. Punish higher values.
    ltd = shipment["clt"] - shipment["blt"]
    if ltd < BUSINESS_DAY_HOURS:
        return 1
    # This only occurs if blt is negative.
    if ltd > shipment["clt"]:
        return pd.NA
    # https://www.desmos.com/calculator/hpahhlbiya
    return shipment["blt"] / (shipment["clt"] - BUSINESS_DAY_HOURS + epsilon)


def prebook_score(shipment: dict, distributions: dict) -> float:
    """Calculate pre-book score of a shipment.

    Compare a shipment's pre-book hours to the distribution of pre-book hours on the same lane. This is done by
    subtracting the pre-book total from the distribution mean and dividing by standard deviation, then applying a
    sigmoid in order to bound the score between 0 and 1.

    :param shipment: Shipment object with all properties, including prebook.
    :param distributions: Distribution of pre-book hour totals, indexed by lane id.
    :return: Pre-book score between 0 and 1.
    """
    if pd.isna(shipment["preBook"]) or shipment["laneId"] not in distributions:
        return pd.NA
    d = distributions[shipment["laneId"]]["preBook"]
    assert len(d) > 0
    if d.std() == 0:
        return 0.5
    x = (d.mean() - shipment["preBook"]) / d.std()
    return sigmoid(x)


def broker_score(
    shipment: dict, prebook_weight: float = None, ltu_weight: float = None
) -> float:
    """Calculate broker score of a shipment.

    Measures the broker's ability to give carriers time and
    their use of the time given to them by the customer."""
    prebookscore = prebook_piecewise_linear_score(shipment)
    ltuscore = ltu_score(shipment)
    if prebook_weight is None or ltu_weight is None:
        return weighted_score((prebookscore, ltuscore)), prebookscore, ltuscore
    return (
        weighted_score((prebookscore, ltuscore), (prebook_weight, ltu_weight)),
        prebookscore,
        ltuscore,
    )

```

## carrier_score.py

**File Path:** `scorer\carrier_score.py`

```python
import datetime

from scorer.util import bound_score
from scorer.util import make_linear_fn
from scorer.util import weighted_score


def carrier_score(shipment: dict, weights: tuple = (1, 3)) -> float:
    # OTD is three times as important as OTP.
    # Linear function from 1 to 0 per minute late.
    # For pickup function, 30 minutes is 0.5 and 60 is 0.
    pickup_fn = make_linear_fn(60)
    delivery_fn = make_linear_fn(30)
    otp_score = bound_score(pickup_fn(shipment["originDelayMinutes"]))
    otd_score = bound_score(delivery_fn(shipment["destinationDelayMinutes"]))
    return weighted_score((otp_score, otd_score), weights), otp_score, otd_score


if __name__ == "__main__":
    shipment = {
        "brokerId": "af9b8398-d8c9-4f13-9ab8-7307a3c74207",
        "laneId": "4ed8390f-5f39-4859-8d12-164fbed547c7",
        "shipperId": "504b47c8-0657-4eee-a243-6ba3cee4809f",
        "brokerPrimaryReference": "5244509",
        "shipperPrimaryReference": "*********",
        "stopCount": 2,
        "distanceMiles": 908.0,
        "cogsLineHaul": 3400.0,
        "cogsAccessorial": 0.0,
        "cogsFuel": None,
        "cogsTotal": 3400.0,
        "revenueLineHaul": 3144.0,
        "revenueFuel": 462.77,
        "revenueAccessorial": 0.0,
        "revenueTotal": 3606.77,
        "originWarehouse": "CAMPBELL SOUP SUPPLY COMPANY",
        "destinationWarehouse": "COSTCO WEST FARGO ND",
        "shipmentStatus": "Delivered",
        "shipmentRank": "Spot",
        "weight": 44081.0,
        "originOpenTime": datetime.datetime.strptime(
            "2022-03-02 01:00:00", "%Y-%m-%d %H:%M:%S"
        ),
        "originCloseTime": datetime.datetime.strptime(
            "2022-03-01 19:00:00", "%Y-%m-%d %H:%M:%S"
        ),
        "originArrivalTime": datetime.datetime.strptime(
            "2022-03-01 18:00:00", "%Y-%m-%d %H:%M:%S"
        ),
        "originDepartureTime": datetime.datetime.strptime(
            "2022-03-02 02:00:00", "%Y-%m-%d %H:%M:%S"
        ),
        "destinationOpenTime": datetime.datetime.strptime(
            "2022-03-03 13:30:00", "%Y-%m-%d %H:%M:%S"
        ),
        "destinationCloseTime": datetime.datetime.strptime(
            "2022-03-03 07:30:00", "%Y-%m-%d %H:%M:%S"
        ),
        "destinationArrivalTime": datetime.datetime.strptime(
            "2022-03-03 06:30:00", "%Y-%m-%d %H:%M:%S"
        ),
        "destinationDepartureTime": datetime.datetime.strptime(
            "2022-03-03 14:30:00", "%Y-%m-%d %H:%M:%S"
        ),
        "loadCreationTime": datetime.datetime.strptime(
            "2022-02-25 08:56:57", "%Y-%m-%d %H:%M:%S"
        ),
        "loadActivationTime": datetime.datetime.strptime(
            "2022-02-28 10:20:56", "%Y-%m-%d %H:%M:%S"
        ),
        "carrierAssignedTime": datetime.datetime.strptime(
            "2022-03-01 10:16:50", "%Y-%m-%d %H:%M:%S"
        ),
        "humanModified": False,
        "score": 81.2811629035,
        "blt": 16.65111111111111,
        "clt": 28.050833333333333,
        "preBook": 6.719444444444444,
        "shipmentClass": "canonical",
        "originDelayMinutes": 120,
        "destinationDelayMinutes": -34,
        "pendingUpdate": "{}",
        "marginDollars": 206.76999999999998,
        "marginPercent": 0.05732830205419253,
    }
    print(carrier_score(shipment))

```

## composite_score.py

**File Path:** `scorer\composite_score.py`

```python
import json
import logging

import pandas as pd
from scorer.broker_score import broker_score
from scorer.carrier_score import carrier_score
from scorer.cost_score import calculate_cost_value
from scorer.cost_score import cost_score
from scorer.util import calculate_blt
from scorer.util import calculate_clt
from scorer.util import calculate_delay
from scorer.util import calculate_prebook
from scorer.util import format_score
from scorer.util import localize_utc_to_central
from scorer.util import weighted_score


def performance_score(shipment: dict) -> float:
    """Calculates broker, carrier, and composited performance score.

    :param shipment: Shipment object with all relevant computed properties.
    :return: Composite score between 0 and 100.
    """
    brokerscore, prebookscore, ltuscore = broker_score(shipment)
    carrierscore, otpscore, otdscore = carrier_score(shipment)

    if pd.isna(carrierscore):
        performancescore = brokerscore
    elif pd.isna(brokerscore):
        performancescore = carrierscore
    else:
        if carrierscore >= 0.5:
            performancescore = 0.75 * carrierscore + 0.25 * brokerscore
        else:
            performancescore = 0.5 * carrierscore + 0.5 * brokerscore

    return (
        brokerscore,
        carrierscore,
        performancescore,
        prebookscore,
        ltuscore,
        otpscore,
        otdscore,
    )


def final_score(shipment: dict) -> float:
    """Calculate cost score and final composited score.

    :param shipment: Shipment object with all properties, including constituent scores.
    :param weights: Tuple of weights for each constituent score.
    :return: Composite score between 0 and 100.
    """

    # Weights if carrier score > 75:
    # cost 40, broker 40, carrier 20
    # Weights if carrier score < 75:
    # cost 25, broker 25, carrier 50

    carrierscore = shipment["carrierScore"]
    brokerscore = shipment["brokerScore"]
    # performancescore = shipment["performanceScore"]

    costscore = cost_score(shipment["costValue"], carrierscore)
    if pd.isna(carrierscore):
        finalscore = weighted_score((brokerscore, costscore))
    elif carrierscore > 0.75:
        finalscore = weighted_score(
            (costscore, brokerscore, carrierscore), (30, 40, 30)
        )
    else:
        finalscore = weighted_score(
            (costscore, brokerscore, carrierscore), (20, 20, 60)
        )

    # if pd.isna(costscore):
    #     finalscore = performancescore
    # elif pd.isna(performancescore):
    #     finalscore = costscore
    # else:
    #     finalscore = 0.5 * performancescore + 0.5 * costscore
    return costscore, finalscore


def calculate_margin_percent(shipment: dict) -> float:
    if shipment["revenueTotal"] == 0:
        return pd.NA
    return shipment["marginDollars"] / shipment["revenueTotal"]


def score_shipments_df(shipments_df: pd.DataFrame) -> dict:
    """Score shipments based on revenue, pre-book, and delay times.

    :param shipments_df: DataFrame with all shipment data.
    :return: Score dictionary indexed by shipment id.
    """
    scored_times = [
        "originArrivalTime",
        "originCloseTime",
        "destinationArrivalTime",
        "destinationCloseTime",
        "carrierAssignedTime",
        "loadCreationTime",
        "loadActivationTime",
    ]
    # Change all dataframe columns to lowercase.
    score_df = shipments_df.set_index("id")

    for i, old_row in score_df.iterrows():
        if pd.isna(old_row["pendingUpdate"]):
            pending_update = {}
        else:
            pending_update = json.loads(old_row["pendingUpdate"])
        origin_delay_minutes, pending_update = calculate_delay(
            old_row,
            pending_update,
            "originArrivalTime",
            "originCloseTime",
            "originDelayMinutes",
        )
        # Update new row.
        score_df.at[i, "originDelayMinutes"] = origin_delay_minutes
        score_df.at[i, "pendingUpdate"] = json.dumps(pending_update).replace("'", '"')

        destination_delay_minutes, pending_update = calculate_delay(
            old_row,
            pending_update,
            "destinationArrivalTime",
            "destinationCloseTime",
            "destinationDelayMinutes",
        )
        # Update new row.
        score_df.at[i, "destinationDelayMinutes"] = destination_delay_minutes
        score_df.at[i, "pendingUpdate"] = json.dumps(pending_update).replace("'", '"')

    def error_handling_wrapper(f, shipment: dict):
        try:
            return f(shipment)
        except ValueError as e:
            logging.error(
                "(brokerPrimaryReference: %s), %s",
                shipment["brokerPrimaryReference"],
                e,
            )
            return pd.NA

    # Convert shipment (s) Series to a dict before passing to helper functions.
    score_df.loc[:, "blt"] = score_df.apply(
        lambda s: error_handling_wrapper(calculate_blt, s.to_dict()), axis="columns"
    )
    score_df.loc[:, "clt"] = score_df.apply(
        lambda s: error_handling_wrapper(calculate_clt, s.to_dict()), axis="columns"
    )
    score_df.loc[:, "preBook"] = score_df.apply(
        lambda s: error_handling_wrapper(calculate_prebook, s.to_dict()), axis="columns"
    )
    score_df.loc[:, scored_times] = score_df[scored_times].applymap(
        localize_utc_to_central
    )

    score_df.loc[:, "marginDollars"] = score_df["revenueTotal"] - score_df["cogsTotal"]
    score_df.loc[:, "marginPercent"] = score_df.apply(
        lambda s: error_handling_wrapper(calculate_margin_percent, s.to_dict()),
        axis="columns",
    )

    for i, row in score_df.iterrows():
        (
            score_df.at[i, "brokerScore"],
            score_df.at[i, "carrierScore"],
            score_df.at[i, "performanceScore"],
            score_df.at[i, "prebookScore"],
            score_df.at[i, "ltuScore"],
            score_df.at[i, "otpScore"],
            score_df.at[i, "otdScore"],
        ) = performance_score(row.to_dict())
    score_df = calculate_cost_value(score_df)
    for i, row in score_df.iterrows():
        (
            score_df.at[i, "costScore"],
            score_df.at[i, "score"],
        ) = final_score(row.to_dict())

    score_cols = [
        "brokerScore",
        "carrierScore",
        "performanceScore",
        "costScore",
        "score",
        "prebookScore",
        "ltuScore",
        "otpScore",
        "otdScore",
    ]
    # Convert scores from floats [0, 1] to ints [0, 100] for display.
    score_df.loc[:, score_cols] = score_df[score_cols].applymap(format_score)

    score_df.drop(
        columns=["marginDollars", "marginPercent"],
        inplace=True,
    )

    return score_df

```

## cost_score.py

**File Path:** `scorer\cost_score.py`

```python
import logging

import pandas as pd
from scorer.util import bound_score
from scorer.util import sigmoid


def cost_score(cost_value: float, carrier_score: float) -> float:
    if pd.isna(cost_value):
        return pd.NA
    return bound_score(-2.5 * cost_value + 2.25)
    if pd.isna(carrier_score) or carrier_score > 0.9:
        # https://www.desmos.com/calculator/9xfywvwibn
        # Punish shipments with cost value above the mean.
        # Temp experiment
        # Just green line in https://www.desmos.com/calculator/m19acsi81o
        return bound_score(-2.5 * cost_value + 2.25)
        # return bound_score(-2.5 * cost_value + 2.25)
    else:
        # https://www.desmos.com/calculator/m19acsi81o
        # Punish cost values that are further from mean.
        # std | score
        # 0   | 100
        # 0.5 |  85
        # 1   |  61
        # 2   |  27
        # if abs(cost_value - 0.5) < 0.1:
        #     return 1.0
        if cost_value > 0.5:
            return bound_score(-2.25 * cost_value + 2.25)
            # return bound_score(-2.5 * cost_value + 2.25)
        else:
            return bound_score(2.25 * cost_value)
            # return bound_score(2.5 * cost_value - 0.25)
        # return bound_score(-4.75 * cost_value * cost_value + 4.75 * cost_value)


def cost_value(df_row: dict) -> float:
    # Calculate the number of standard deviations the shipment's cogs is from the mean.
    # Apply a sigmoid to bound the value between 0 and 1.
    # std > mean | cost value
    #    -1      |    0.269
    #     0      |    0.500
    #     0.5    |    0.622
    #     1      |    0.731
    #     2      |    0.882
    if (
        pd.isna(df_row["cogsStd"])
        or pd.isna(df_row["cogsMean"])
        or pd.isna(df_row["cogs"])
    ):
        return pd.NA
    if df_row["cogsStd"] == 0:
        return 0.5
    x = (df_row["cogs"] - df_row["cogsMean"]) / df_row["cogsStd"]
    return sigmoid(x)


def calculate_cost_value(
    score_df: pd.DataFrame, min_sample_size: int = 3
) -> pd.DataFrame:
    """Calculate cost value of all shipments in dataframe.

    Compare a shipment's cogs to the distribution of cogs on the same lane in 30d rolling window. This is done by
    subtracting the revenue total from the distribution mean and dividing by standard deviation, then applying a sigmoid
    in order to bound the score between 0 and 1.
    """
    # TODO(P1): Do value counting instead of looping for efficiency.
    if len(score_df) < min_sample_size:
        score_df["costValue"] = pd.NA
        return score_df

    logging.info("Score df has %d rows", score_df.shape[0])

    filtered_df = score_df[score_df["cogsTotal"].notnull()]
    filtered_df = filtered_df[filtered_df["originCloseTime"].notnull()]
    # TODO(P0): Once imports are fixed, uncomment these line.
    # filtered_df = filtered_df[filtered_df["shipmentClass"] == ShipmentClass.canonical]
    # filtered_df = filtered_df[filtered_df["shipmentMode"] != ShipmentMode.imdl]
    logging.info(
        "Filtered df has %d rows, after filtering canonical shipments.",
        filtered_df.shape[0],
    )
    # filtered_df = filtered_df[filtered_df["clt"] > 1.5]
    # logging.info(
    #     "Filtered df has %d rows, after filtering CLT > 1.5.", filtered_df.shape[0]
    # )
    # In order to add carrier score filtering, the distribution of shipments must be separated from
    # the shipments we give a cost value to. This can only be done by writing a manual rolling window
    # calculation, which is not worth the effort at this time.
    # distribution_filters = []
    # cost_score_filters = []

    # Efficient mean is mu_2 = mu_1 - x_1/n_1 + x_2/n_2 I think
    # Efficient std is tricker, but can be done with a rolling window.
    # https://math.stackexchange.com/questions/106700/incremental-computation-of-standard-deviation
    # for index, row in score_df.iterrows():
    # while right - current <= 15:
    #      right += 1
    # while current - left > 15:
    #      left += 1

    # filtered_df = filtered_df[filtered_df["carrierScore"] >= 0.5]
    # logging.info(
    #     "Filtered df has %d rows, after filtering carrier score >= 0.5.",
    #     filtered_df.shape[0],
    # )

    if len(filtered_df) < min_sample_size:
        logging.info(
            "Filtered df has %d rows, which is less than min_sample_size %d. Returning NA.",
            len(filtered_df),
            min_sample_size,
        )
        score_df["costValue"] = pd.NA
        return score_df

    # Compare shipments in a 60 day rolling window.
    filtered_df.sort_values(by=["originCloseTime"], inplace=True)
    window = filtered_df.rolling(
        "60d", center=True, on="originCloseTime", min_periods=min_sample_size
    )

    # Only consider cogs at book, ignoring accessorials for cost value comparison.
    filtered_df["cogsAccessorial"] = filtered_df["cogsAccessorial"].fillna(0.0)
    filtered_df["cogs"] = filtered_df["cogsTotal"] - filtered_df["cogsAccessorial"]

    # Precompute the mean and std for each window.
    filtered_df["cogsMean"] = window["cogs"].mean()
    filtered_df["cogsStd"] = window["cogs"].std()
    filtered_df.loc[:, "costValue"] = filtered_df.apply(
        lambda s: cost_value(s.to_dict()), axis="columns"
    )

    score_df.loc[:, "costValue"] = filtered_df["costValue"]
    return score_df

```

## query_aurora.py

**File Path:** `scorer\query_aurora.py`

```python
import sys
from os import path
from os.path import dirname as pdir

import boto3

# Ensure imports work from root level directory.

sys.path.append(pdir(pdir(pdir(pdir(path.abspath(__file__))))))
from common import credentials

CREDENTIALS = credentials.Credentials()
secret_arn = CREDENTIALS.rds_secret_arn
cluster_arn = "arn:aws:rds:us-east-1:903881895532:cluster:mvp-db-cluster"


def query_chunked(query: str, database_name: str, chunk_size: int = 1000) -> dict:
    """
    Query RDS, using stored credentials.
    TODO: Refactor to use aurora data API for speed?
    TODO: Evaluate chunk size

    :param wheres: Where causes for sql query.
    :return: Dictionary of boto3 client response containing SQL query's results.
    """
    client = boto3.client("rds-data")

    start_idx = 0
    response_size = chunk_size
    response_all = {}
    while response_size >= chunk_size:
        query_text = query + f" LIMIT {start_idx},{chunk_size};"
        include_metadata = start_idx == 0
        response = client.execute_statement(
            resourceArn=cluster_arn,
            database=database_name,
            secretArn=secret_arn,
            sql=query_text,
            includeResultMetadata=include_metadata,
        )

        if start_idx == 0:
            response_all = response
        else:
            response_all["records"] += response["records"]

        response_size = len(response["records"])
        start_idx += chunk_size

    return response_all

```

## scorer.py

**File Path:** `scorer\scorer.py`

```python
import logging

import pandas as pd
import scorer.composite_score as composite_score
from scorer.query_aurora import query_chunked
from tqdm import tqdm

from common import query_utils


class Scorer:
    def __init__(self, aurora_db: str, scorer_log_filename: str) -> None:
        self.__aurora_db = aurora_db
        self.scorer_log_filename = self.set_up_logging(scorer_log_filename)

    def get_lane_shipments(self, lane_id: str) -> pd.DataFrame:
        """Gets all shipments for a lane.

        :param lane_id: The lane to get shipments for.
        :return: Dataframe of shipments in the lane.
        """
        query_response = query_chunked(
            f'SELECT * FROM shipments WHERE laneid ="{lane_id}"', self.__aurora_db
        )
        return query_utils.append_records_to_df(query_response)

    def set_up_logging(self, scorer_log_filename: str) -> None:
        """Sets up logging for the lambda.

        :param scorer_log_filename: The filename to log to.
        """

        # TODO(P0): Configure this properly.
        # logging.basicConfig(filename=scorer_log_filename,
        #                     level=logging.INFO,
        #                     format='%(asctime)s [%(threadName)-10.10s] [%(levelname)-4.4s]  %(message)s',
        #                     datefmt='%H:%M:%S')`
        root_logger = logging.getLogger()
        logging.info("Scoring started.")

        # log_formatter = logging.Formatter("%(asctime)s [%(threadName)-10.10s] [%(levelname)-4.4s]  %(message)s")
        file_handler = logging.FileHandler(scorer_log_filename)
        root_logger.setLevel(logging.WARN)

        # file_handler.setFormatter(log_formatter)
        root_logger.addHandler(file_handler)
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(threadName)-10.10s] [%(levelname)-4.4s]  %(message)s",
            datefmt="%H:%M:%S",
        )
        logging.getLogger("sqlalchemy").setLevel(logging.ERROR)

        logging.info("Scoring started.")
        return

    def score_and_update_df(self, shipments_df: pd.DataFrame) -> pd.DataFrame:
        """Scores a shipment dataframe.

        :param shipment_df: The shipment dataframe to score.
        :return: The scored shipment dataframe.
        """

        if len(shipments_df) == 0:
            return
        logging.info(f"{len(shipments_df)} shipments read.")
        # Dictionary with values as formatstring for the query parameter.
        computed_cols = {
            "score": "{} = {}, ",
            "carrierScore": "{} = {}, ",
            "brokerScore": "{} = {}, ",
            "performanceScore": "{} = {}, ",
            "costScore": "{} = {}, ",
            "ltuScore": "{} = {}, ",
            "prebookScore": "{} = {}, ",
            "otpScore": "{} = {}, ",
            "otdScore": "{} = {}, ",
            "blt": "{} = {}, ",
            "clt": "{} = {}, ",
            "preBook": "{} = {}, ",
            "originDelayMinutes": "{} = {}, ",
            "destinationDelayMinutes": "{} = {}, ",
            "pendingUpdate": "{} = '{}', ",  # string column, add quotes
        }

        score_df = composite_score.score_shipments_df(shipments_df)

        for index, row in score_df.iterrows():
            # TODO(P1): Consider adding a last scored time field.
            # last_modified_time = datetime.datetime.utcnow().replace(microsecond=0)
            query_str = "UPDATE shipments SET "
            for col in computed_cols:
                if not pd.isna(row[col]):
                    query_str += computed_cols[col].format(col, row[col])
            # query_str += f'lastModifiedTime = "{last_modified_time}" WHERE id = "{index}";'
            query_str += f'id = "{index}" WHERE id = "{index}";'
            # Example query:
            # UPDATE shipments SET score = 35.58571428571429, blt = 101.24138888888889, clt = 103.0,
            # preBook = 0.8183333333333334, originDelayMinutes = 60.0, destinationDelayMinutes = -60.0, pendingUpdate = '{"originDelayMinutes": 0.0}',
            # id = "261ac7cf-03d9-416f-b659-26ce03f54d31" WHERE id = "261ac7cf-03d9-416f-b659-26ce03f54d31";
            logging.info(query_str)
            query_utils.query(query_str, self.__aurora_db)

    def score_all_shipments_by_lane(self) -> None:
        """Scores all shipments for a list of lanes.

        :param lane_ids: The list of lanes to score.
        """
        # Gets list of lanes to score.
        query_response = query_utils.query("SELECT id FROM lanes;", self.__aurora_db)
        lane_ids = []

        for record in query_response["records"]:
            lane_ids.append(next(iter(record[0].values())))

        for lane_id in tqdm(lane_ids):
            logging.info(f"Scoring lane {lane_id}")
            shipments_df = self.get_lane_shipments(lane_id)
            self.score_and_update_df(shipments_df)

    def score_all_shipments_by_3zip(self) -> None:
        """The "main" function of the lambda. This is where the scoring queries are handled.

        Gets list of lanes, and scores shipments lane by lane.
        """

        # Gets list of lanes to score, grouped by 3-zip.
        query_response = query_utils.query(
            """SELECT GROUP_CONCAT(l.id) AS laneIds, oz.originZipN, dz.destinationZipN FROM lanes l
                JOIN cities oc ON l.originCityId = oc.id
                JOIN cityzips ocz ON oc.id = ocz.cityId
                JOIN (SELECT id, SUBSTRING(zip, 1, {ziplen}) AS originZipN FROM zips) AS oz ON ocz.zipId = oz.id
                JOIN cities dc ON l.destinationCityId = dc.id
                JOIN cityzips dcz ON dc.id = dcz.cityId
                JOIN (SELECT id, SUBSTRING(zip, 1, {ziplen}) AS destinationZipN FROM zips) AS dz ON dcz.zipId = dz.id
                GROUP BY oz.originZipN, dz.destinationZipN;""".format(
                ziplen=3
            ),
            self.__aurora_db,
        )

        grouped_lane_ids = []
        for record in query_response["records"]:
            values = [next(iter(i.values())) for i in record]
            lane_ids_str, origin_3zip, destination_3zip = values
            grouped_lane_ids.append(
                (lane_ids_str.split(","), origin_3zip, destination_3zip)
            )

        for grouped_lane_id in tqdm(grouped_lane_ids):
            lane_ids, origin_3zip, destination_3zip = grouped_lane_id
            logging.info(f"Scoring lanes from {origin_3zip}* to {destination_3zip}*.")
            shipments_df = pd.DataFrame()
            for lane_id in lane_ids:
                shipments_df = pd.concat(
                    [shipments_df, self.get_lane_shipments(lane_id)]
                )

            self.score_and_update_df(shipments_df)


def main():
    scorer = Scorer("mvp_db_dev", "test_scorer.log")
    scorer.score_all_shipments_by_3zip()


if __name__ == "__main__":
    # main()
    qry_rspns = query_utils.query("SELECT * FROM shipments;", "mvp_db_dev")

```

## util.py

**File Path:** `scorer\util.py`

```python
import datetime
import logging
import typing

import numpy as np
import pandas as pd
import pytz

BUSINESS_DAY_HOURS = 10
# Datetime starts with MON = 0.
MON, TUE, WED, THU, FRI, SAT, SUN = range(7)
# All working days should be 10 hours.
# TODO(P2): Consider consolidating into 4 or 2 variables.
_CLT_WORKING_DAY_START_HOUR = 7
_CLT_WORKING_DAY_END_HOUR = 17
_BLT_WORKING_DAY_START_HOUR = 7
_BLT_WORKING_DAY_END_HOUR = 17
_PREBOOK_WORKING_DAY_START_HOUR = 7
_PREBOOK_WORKING_DAY_END_HOUR = 17


def sigmoid(x):
    # https://www.desmos.com/calculator/sestwriuji
    return 1 / (1 + np.exp(-x))


def linear_quadratic_piecewise(x, threshold, boundary=20 / 3, quadratic_slope=100):
    # https://www.desmos.com/calculator/xltl2xr3cl
    if x < boundary:
        return x**2 / quadratic_slope
    else:
        return 1 - (threshold - x) / threshold


def make_linear_fn(x_intercept: float, y_intercept: float = 1.0) -> float:
    """Return a linear function intercepting given intercepts."""
    assert x_intercept != 0
    return (
        lambda x: -y_intercept / x_intercept * x + y_intercept
        if not pd.isna(x)
        else pd.NA
    )


def bound_score(scr: float, min_score: float = 0.0, max_score: float = 1.0) -> float:
    """Bound score between min and max scores."""
    if pd.isna(scr):
        return pd.NA
    return max(min_score, min(scr, max_score))


def format_score(score: float) -> int:
    if pd.isna(score):
        return pd.NA
    return round(score * 100)


def weighted_score(scores: tuple, weights: tuple = None) -> float:
    """Calculated weighted scores.

    All scores are between 0 and 1. If they are NA, they are ignored.
    Weights cannot be NA.
    """
    # If weights are not provided, use equal weights.
    if weights is None:
        weights = (1,) * len(scores)
    assert len(scores) == len(weights)
    weighted_avg = 0.0
    denominator = 0.0
    for scr, weight in zip(scores, weights):
        if not pd.isna(scr):
            weighted_avg += weight * scr
            denominator += weight
    if denominator == 0.0:
        return pd.NA
    return weighted_avg / denominator


def parse_time_string(time_str: str) -> datetime.datetime:
    """Convert a time string from expected format to datetime object.

    :param time_str: String representation of time (ex: 2019-03-19T13:48:00.0).
    :return: datetime.datetime object.
    """
    if type(time_str) != str or not time_str:
        return pd.NaT
    try:
        return datetime.datetime.strptime(
            time_str.replace("T", " "), "%Y-%m-%d %H:%M:%S.%f"
        )
    except ValueError:
        # TODO(P0): Figure out how to parse both ways without try/except block.
        return datetime.datetime.strptime(
            time_str.replace("T", " "), "%Y-%m-%d %H:%M:%S"
        )


def localize_utc_to_central(dt: datetime.datetime) -> datetime.datetime:
    """Localize UTC timestmap to central time.

    :param datetime.datetime: UTC timestamp.
    :return: CDT or CST timestamp.
    """
    if pd.isna(dt):
        return pd.NA
    return (
        pytz.utc.localize(dt)
        .astimezone(pytz.timezone("US/Central"))
        .replace(tzinfo=None)
    )


# TODO(P0): Write unit tests for these functions.
def clip(
    day: datetime.datetime, working_day_start_hour: int, working_day_end_hour: int
) -> datetime.datetime:
    """Clip time to within working hours.

    Excepted to always have correct input (i.e, no NA inputs).

    :param day: Input datetime object, must be a weekday.
    :param working_day_start_hour: int representing Central Time hour when working day starts.
    :param working_day_end_hour: int representing Central Time hour when working day ends.
    :return: datetime object clipped to within working start and end hours.
    """
    hour = max(working_day_start_hour, min(day.hour, working_day_end_hour))

    # If hour is clipped, chop off rest of time.
    if hour != day.hour or (
        hour == working_day_end_hour
        and (day - datetime.timedelta(hours=day.hour)).time()
    ):
        return datetime.datetime(day.year, day.month, day.day, hour)
    return day


def working_hours_between(
    start: datetime.datetime,
    end: datetime.datetime,
    working_day_start_hour: int,
    working_day_end_hour: int,
) -> float:
    """Calculate working hours between start and end datetimes.

    This is done assuming a MON - FRI workweek, with a working day's start and end hours set as constants. weekday_start
    and weekday_end are calculated by finding the nearest weekday and working hours. Then, the number of days in between
    them are calculated and added to the number of work hours left in weekday_start and weekday_end.

    start timestamp must always be earlier than end timestamp.
    working_day_start_hour must always be earlier than working_day_end_hour.

    :param start: Start datetime object.
    :param end: End datetime object.
    :param working_day_start_hour: int representing Central Time hour when working day starts.
    :param working_day_end_hour: int representing Central Time hour when working day ends.
    :return: # of working hours between start and end, as a float.
    """
    if start > end:
        logging.error("Start time is after end time.")
        raise ValueError(f"Start {start} is not less than end {end}.")
    this_fri = start.date() + datetime.timedelta(FRI - start.weekday())
    end_of_workweek = datetime.datetime(
        this_fri.year, this_fri.month, this_fri.day, working_day_end_hour
    )
    # If the start date is after end of workweek, set to friday this week at 5pm.
    if start > end_of_workweek:
        weekday_start = end_of_workweek
    else:
        weekday_start = clip(start, working_day_start_hour, working_day_end_hour)

    this_fri = end.date() + datetime.timedelta(FRI - end.weekday())
    end_of_workweek = datetime.datetime(
        this_fri.year, this_fri.month, this_fri.day, working_day_end_hour
    )
    # If end date is before start of workweek, set to monday next week at 7am.
    if end > end_of_workweek:
        next_mon = end + datetime.timedelta(days=MON - end.weekday() + 7)
        weekday_end = datetime.datetime(
            next_mon.year, next_mon.month, next_mon.day, working_day_start_hour
        )
    else:
        weekday_end = clip(end, working_day_start_hour, working_day_end_hour)

    assert weekday_start <= weekday_end
    if weekday_start.date() == weekday_end.date():
        return (weekday_end - weekday_start).total_seconds() / 3600
    # Counts business days between weekday_start and weekday_end.
    working_days = (
        np.busday_count(
            weekday_start.strftime("%Y-%m-%d"), weekday_end.strftime("%Y-%m-%d")
        )
        - 1
    )
    # Calculate seconds between weekday_start and the end of the weekday_start work day.
    start_partial = (
        datetime.datetime(
            weekday_start.year,
            weekday_start.month,
            weekday_start.day,
            working_day_end_hour,
        )
        - weekday_start
    ).total_seconds()
    # Calculate seconds between weekday_end and the beginning of the weekday_start work day.
    end_partial = (
        weekday_end
        - datetime.datetime(
            weekday_end.year, weekday_end.month, weekday_end.day, working_day_start_hour
        )
    ).total_seconds()
    assert start_partial >= 0 and end_partial >= 0
    return (
        working_days * (working_day_end_hour - working_day_start_hour)
        + (start_partial + end_partial) / 3600
    )


# CLT (Customer Lead Time)
def calculate_clt(shipment: dict) -> float:
    """Calculate external lead time.

    How much time the customer gives to the broker.

    :param shipment: Shipment object with all properties.
    :return: Working hours from load creation to origin close.
    """
    if "loadCreationTime" not in shipment or "originCloseTime" not in shipment:
        return pd.NA
    load_creation_time = localize_utc_to_central(shipment["loadCreationTime"])
    origin_close_time = localize_utc_to_central(shipment["originCloseTime"])
    if pd.isna(load_creation_time) or pd.isna(origin_close_time):
        return pd.NA
    if load_creation_time > origin_close_time:
        return 0
    return working_hours_between(
        load_creation_time,
        origin_close_time,
        _CLT_WORKING_DAY_START_HOUR,
        _CLT_WORKING_DAY_END_HOUR,
    )


# BLT (Broker Lead Time)
def calculate_blt(shipment: dict) -> float:
    """Calculate internal lead time.

    How much time the broker sales team gives their internal team.

    :param shipment: Shipment object with all properties.
    :return: Working hours from load activation to origin close.
    """
    if "loadActivationTime" not in shipment or "originCloseTime" not in shipment:
        return pd.NA
    load_activation_time = localize_utc_to_central(shipment["loadActivationTime"])
    origin_close_time = localize_utc_to_central(shipment["originCloseTime"])
    if pd.isna(load_activation_time) or pd.isna(origin_close_time):
        return pd.NA
    if load_activation_time > origin_close_time:
        return 0
    return working_hours_between(
        load_activation_time,
        origin_close_time,
        _BLT_WORKING_DAY_START_HOUR,
        _BLT_WORKING_DAY_END_HOUR,
    )


def calculate_prebook(shipment: dict) -> float:
    """Calculate pre-book time (in hours).

    How many hours in advance the broker books the carrier.

    :param shipment: Shipment object with all properties.
    :return: Working hours from carrier assigned to origin close.
    """
    if "carrierAssignedTime" not in shipment or "originCloseTime" not in shipment:
        return pd.NA
    carrier_assigned_time = localize_utc_to_central(shipment["carrierAssignedTime"])
    origin_close_time = localize_utc_to_central(shipment["originCloseTime"])
    if pd.isna(carrier_assigned_time) or pd.isna(origin_close_time):
        return pd.NA
    if carrier_assigned_time > origin_close_time:
        return 0
    return working_hours_between(
        carrier_assigned_time,
        origin_close_time,
        _PREBOOK_WORKING_DAY_START_HOUR,
        _PREBOOK_WORKING_DAY_END_HOUR,
    )


def turn_all_shipment_keys_to_lower(shipment: dict) -> dict:
    """
    Keys in object are mixed bag of camel case and all lowercase causing above functions to fail.
    Workaround until more long-term or sustainable fix.

    "param shipment: Shipment object with all properties.
    "return" Shipment object with all keys lowercase.
    """
    return {k.lower(): v for k, v in shipment.items()}


def normalize_datetime(dt: datetime.datetime) -> datetime.datetime:
    return dt if not pd.isna(dt) else pd.NaT


def calculate_delay(
    shipment: dict,
    pending_update: dict,
    arrival_col: str,
    close_col: str,
    delay_col: str,
) -> typing.Tuple[int, dict]:
    """Calculate if shipment is picked up on time.

    If there is an existing value in delay and it is greater than newly calculated delay, create
    a pending update with the values and do not update the shipment delay.
    """
    arrival_time = normalize_datetime(shipment[arrival_col])
    close_time = normalize_datetime(shipment[close_col])
    if pd.isna(arrival_time) or pd.isna(close_time):
        return shipment[delay_col], pending_update
    # Minutes between arrival and close.
    proposed_delay = int((arrival_time - close_time).total_seconds()) // 60
    # If shipment delay was already calculated and the proposed delay is less,
    # do not update the shipment delay and create a pending update.
    if not pd.isna(shipment[delay_col]) and shipment[delay_col] > proposed_delay:
        pending_update[delay_col] = proposed_delay
        return int(shipment[delay_col]), pending_update
    return proposed_delay, pending_update


if __name__ == "__main__":
    test_load_creation_time = datetime.datetime(2022, 3, 10, 18, 55)
    test_load_activation_time = datetime.datetime(2022, 3, 10, 19, 28)
    test_origin_close_time = datetime.datetime(2022, 3, 10, 19, 0)

    test_load_creation_time = localize_utc_to_central(test_load_creation_time)
    test_load_activation_time = localize_utc_to_central(test_load_activation_time)
    test_origin_close_time = localize_utc_to_central(test_origin_close_time)

    print(test_load_creation_time, test_load_activation_time, test_origin_close_time)
    print(
        "blt",
        working_hours_between(
            test_load_activation_time,
            test_origin_close_time,
            _PREBOOK_WORKING_DAY_START_HOUR,
            _PREBOOK_WORKING_DAY_END_HOUR,
        ),
    )
    print(
        "clt",
        working_hours_between(
            test_load_creation_time,
            test_origin_close_time,
            _PREBOOK_WORKING_DAY_START_HOUR,
            _PREBOOK_WORKING_DAY_END_HOUR,
        ),
    )

```

## util_test.py

**File Path:** `scorer\util_test.py`

```python
import datetime
import typing
import unittest

import pandas as pd
import scorer.util as util
from parameterized import parameterized


class TestUtil(unittest.TestCase):
    @parameterized.expand(
        [
            [0.5, 0.5],  # case when min_score < score < max_score
            [0, 0],  # case when score = min_score
            [1, 1],  # case when score = max_score
            [-1, 0],  # edge case when score < min_score
            [1.1, 1],  # edge case when score > max_score
            [2, 2, 2, 1],  # edge case when min_score > max_score and score = min_score
        ]
    )
    def test_bound_score(
        self,
        scr: float,
        bound_score_output: float,
        min_score: float = 0.0,
        max_score: float = 1.0,
    ):
        """Tests bound_score function."""
        self.assertEqual(
            util.bound_score(scr, min_score, max_score), bound_score_output
        )

    def test_edge_case_bound_score(self):
        self.assertTrue(pd.isna(util.bound_score(None)))  # edge case when score is None

    @parameterized.expand(
        [
            [(0, 0), 0],  # case when scores are 0 and weights are 1
            [(0, 1), 0.5],  # case when single score is 1 and weights are 1
            [(1, 3, 5), 3],  # case when three scores and weights are 1
            [
                (1, pd.NA, 5),
                3,
            ],  # case when one of the scores is pd.NA and weights are 1
            [
                (1, pd.NA, 5),
                11 / 3,
                (1, 3, 2),
            ],  # case when one of the scores is pd.NA with specific weights
        ]
    )
    def test_weighted_score(
        self, scores: tuple, weighted_score_output: float, weights: tuple = None
    ):
        """Tests weighted_score function."""
        self.assertEqual(util.weighted_score(scores, weights), weighted_score_output)

    def test_edge_case_weighted_score(self):
        self.assertTrue(
            pd.isna(util.weighted_score((1, 3), (0, 0)))
        )  # edge case when weights are all 0
        self.assertTrue(
            pd.isna(util.weighted_score((pd.NA, pd.NA, pd.NA)))
        )  # edge case when scores are all pd.NA

    @parameterized.expand(
        [
            [
                "2019-03-19T13:48:00",
                datetime.datetime(2019, 3, 19, 13, 48, 0, 0),
            ],  # case with normal time string
            [
                "2019-03-19T13:48:00.0",
                datetime.datetime(2019, 3, 19, 13, 48, 0, 0),
            ],  # case with normal time string including microsceond
            [
                "2019-03-19T13:48:00.5",
                datetime.datetime(2019, 3, 19, 13, 48, 0, 500000),
            ],  # case with normal time string including specific microseconds
        ]
    )
    def test_parse_time_string(self, time_str: str, time_str_output: datetime.datetime):
        """Ensure parsed string returns the correct datetime output"""
        self.assertEqual(util.parse_time_string(time_str), time_str_output)

    def test_edge_case_parse_time_string(self):
        self.assertTrue(
            pd.isna(util.parse_time_string(None))
        )  # edge case when input is None
        self.assertTrue(
            pd.isna(util.parse_time_string(32))
        )  # edge case where input is not a string

    @parameterized.expand(
        [
            [
                datetime.datetime(2020, 1, 1, 0, 0, 0),
                datetime.datetime(2019, 12, 31, 18, 0, 0),
            ],  # case with normal timestamp
            [
                datetime.datetime(2022, 3, 13, 7, 59, 59),
                datetime.datetime(2022, 3, 13, 1, 59, 59),
            ],  # case with normal timestamp (just before it's about to switch from cst to cdt)
            [
                datetime.datetime(2022, 3, 13, 8, 0, 0),
                datetime.datetime(2022, 3, 13, 3, 0, 0),
            ],  # edge case when cdt (just switched from cst to cdt)
        ]
    )
    def test_localize_utc_to_central(
        self, utc_time: datetime.datetime, central_time: datetime.datetime
    ):
        """Ensure utc_time correctly converts to central time"""
        self.assertEqual(util.localize_utc_to_central(utc_time), central_time)

    @parameterized.expand(
        [
            [
                datetime.datetime(2019, 12, 31, 16, 0, 0),
                7,
                17,
                datetime.datetime(2019, 12, 31, 16, 0, 0),
            ],  # case when day is after working_day_start_hour and before working_day_end_hour
            [
                datetime.datetime(2019, 12, 31, 23, 0, 0),
                7,
                17,
                datetime.datetime(2019, 12, 31, 17, 0, 0),
            ],  # case when day is after working_day_end
            [
                datetime.datetime(2019, 12, 31, 23, 59, 59),
                7,
                17,
                datetime.datetime(2019, 12, 31, 17, 0, 0),
            ],  # case when day is just before the next day and clips correctly
            [
                datetime.datetime(2020, 1, 1, 0, 0, 0),
                7,
                17,
                datetime.datetime(2020, 1, 1, 7, 0, 0),
            ],  # case when day just turned next day and clips correctly
        ]
    )
    def test_clip(
        self,
        day: datetime.datetime,
        working_day_start_hour: int,
        working_day_end_hour: int,
        day_output: datetime.datetime,
    ):
        """Ensure clip function returns the correct time"""
        self.assertEqual(
            util.clip(day, working_day_start_hour, working_day_end_hour), day_output
        )

    @parameterized.expand(
        [
            [
                datetime.datetime(2019, 12, 31, 7, 0, 0),
                datetime.datetime(2019, 12, 31, 17, 0, 0),
                7,
                17,
                10.0,
            ],  # case with working hours exactly between working_day_start_hour and working_day_end_hour
            [
                datetime.datetime(2019, 12, 30, 7, 0, 0),
                datetime.datetime(2019, 12, 31, 7, 0, 0),
                7,
                17,
                10.0,
            ],  # case with working hours of 24 hours
            [
                datetime.datetime(2019, 12, 30, 7, 0, 0),
                datetime.datetime(2019, 12, 31, 17, 0, 0),
                7,
                17,
                20.0,
            ],  # case with working hours of 48 hours
            [
                datetime.datetime(2019, 12, 31, 2, 0, 0),
                datetime.datetime(2019, 12, 31, 9, 0, 0),
                7,
                17,
                2.0,
            ],  # case when start < working_day_start_hour < end
            [
                datetime.datetime(2022, 3, 12, 8, 0, 0),
                datetime.datetime(2022, 3, 13, 8, 0, 0),
                7,
                17,
                0.0,
            ],  # case when start and end is in the weekend
            [
                datetime.datetime(2022, 3, 11, 18, 0, 0),
                datetime.datetime(2022, 3, 14, 7, 0, 0),
                9,
                19,
                1.0,
            ],  # case when there is a weekend between start and end
        ]
    )
    def test_working_hours_between(
        self,
        start: datetime.datetime,
        end: datetime.datetime,
        working_day_start_hour: int,
        working_day_end_hour: int,
        hours: float,
    ):
        """Ensure working_hours_between returns the corrects hours worked"""
        self.assertEqual(
            util.working_hours_between(
                start, end, working_day_start_hour, working_day_end_hour
            ),
            hours,
        )

    @parameterized.expand(
        [
            [
                {
                    "carrierAssignedTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                    "originCloseTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                },
                0.0,
            ],  # case when carrierAssignedTime = originCloseTime
            [
                {
                    "carrierAssignedTime": datetime.datetime(2023, 2, 14, 13, 0, 0),
                    "originCloseTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                },
                0.0,
            ],  # edge case when carrierAssignedTime is after originCloseTime
            [
                {
                    "carrierAssignedTime": datetime.datetime(2023, 2, 13, 14, 0, 0),
                    "originCloseTime": datetime.datetime(2023, 2, 14, 19, 0, 0),
                },
                15.0,
            ],  # case when timestamps in CST
            [
                {
                    "carrierAssignedTime": datetime.datetime(2023, 3, 10, 14, 0, 0),
                    "originCloseTime": datetime.datetime(2023, 3, 13, 19, 0, 0),
                },
                16.0,
            ],  # edge case when timestamp switches from CST to CDT
        ]
    )
    def test_calculate_prebook(self, shipment: dict, hours: float):
        """Ensure prebook is calculated correctly, given carrier assigned and origin close."""
        self.assertEqual(util.calculate_prebook(shipment), hours)

    def test_edge_case_calculate_prebook(self):
        self.assertTrue(
            pd.isna(util.calculate_prebook({}))
        )  # edge case when shipment is None
        self.assertTrue(
            pd.isna(util.calculate_prebook({"carrierAssignedTime": pd.NA}))
        )  # edge case when carrier assign is pd.NA

    def test_normalize_datetime(self):
        """Tests normalize_datetime function"""
        self.assertTrue(pd.isna(util.normalize_datetime(pd.NA)))

    @parameterized.expand(
        [
            [
                {
                    "originArrivalTime": pd.NaT,
                    "originCloseTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                    "originDelayMinutes": pd.NaT,
                },
                {},
                "originArrivalTime",
                "originCloseTime",
                "originDelayMinutes",
                (pd.NaT, {}),
            ],  # case when arrival time is pd.NA/pd.NaT
            [
                {
                    "originArrivalTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                    "originCloseTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                    "originDelayMinutes": pd.NaT,
                },
                {"originDelayMinutes": 5},
                "originArrivalTime",
                "originCloseTime",
                "originDelayMinutes",
                (0, {"originDelayMinutes": 5}),
            ],  # case when shipment[delay_col] is pd.NA/pd.NaT
            [
                {
                    "originArrivalTime": datetime.datetime(2019, 12, 31, 17, 57, 0),
                    "originCloseTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                    "originDelayMinutes": 0,
                },
                {"originDelayMinutes": 0},
                "originArrivalTime",
                "originCloseTime",
                "originDelayMinutes",
                (0, {"originDelayMinutes": -3}),
            ],  # case when arrival time is earlier than close time
            [
                {
                    "originArrivalTime": datetime.datetime(2019, 12, 31, 18, 3, 0),
                    "originCloseTime": datetime.datetime(2019, 12, 31, 18, 0, 0),
                    "originDelayMinutes": 4,
                },
                {"originDelayMinutes": 4},
                "originArrivalTime",
                "originCloseTime",
                "originDelayMinutes",
                (4, {"originDelayMinutes": 3}),
            ],  # case when shipment delay was already calculated and proposed delay is less
        ]
    )
    def test_calculate_delay(
        self,
        shipment: dict,
        pending_update: dict,
        arrival_col: str,
        close_col: str,
        delay_col: str,
        delay_output: typing.Tuple[int, dict],
    ):
        """Ensure delay is calculated correctly."""
        self.assertEqual(
            util.calculate_delay(
                shipment, pending_update, arrival_col, close_col, delay_col
            ),
            delay_output,
        )


if __name__ == "__main__":
    unittest.main()

```

