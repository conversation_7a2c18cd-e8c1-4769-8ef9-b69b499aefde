import datetime
import unittest

import pandas as pd
import pytz
import rows_util
from parameterized import parameterized


dt = datetime.datetime


class TestUtil(unittest.TestCase):
    @parameterized.expand(
        [
            [
                pd.Timestamp(2011, 8, 15, 8, 15, 12, 0, tz=pytz.UTC),
                True,
            ],  # case where pd.Timestamp object is timezone aware
            [
                pd.Timestamp(2023, 3, 16, 12, 0),
                False,
            ],  # case where time.tzinfo is None, i.e timezone naive
        ]
    )
    def test_is_tz_aware(self, time: pd.Timestamp, aware: bool):
        """Tests datetime/time aware function"""
        self.assertEqual(rows_util.is_tz_aware(time), aware)

    @parameterized.expand(
        [
            [
                pd.Timestamp(2023, 3, 16, 12, 0),
                pytz.timezone("US/Pacific"),
                pd.Timestamp(2023, 3, 16, 19, 0),
            ],  # base case with naize datetime and timezone input
            [
                pytz.FixedOffset(-120).localize(pd.Timestamp(2023, 3, 18, 12, 0)),
                pytz.FixedOffset(-120),
                pd.Timestamp(2023, 3, 18, 14, 0),
            ],  # base case where datetime and tz input have FixedOffsets
            [
                pytz.timezone("US/Eastern").localize(pd.Timestamp(2023, 3, 16, 12, 0)),
                pytz.timezone("US/Eastern"),
                pd.Timestamp(2023, 3, 16, 16, 0),
            ],  # base case with naize datetime and timezone input
        ]
    )
    def test_normalize_time_given_tz(
        self, time: pd.Timestamp, tz: pytz.timezone, normalized_dt: pd.Timestamp
    ):
        """Tests whether function returns datetime that is normalized to UTC"""
        self.assertEqual(rows_util.normalize_time_given_tz(time, tz), normalized_dt)

    def test_edge_case_normalize_time_given_tz(self):
        """Tests ValueError cases of normalize_time_given_tz function"""
        self.assertTrue(
            pd.isna(
                rows_util.normalize_time_given_tz(pd.NA, pytz.timezone("US/Eastern"))
            )
        )  # case where time is pd.NA
        self.assertRaises(
            ValueError,
            rows_util.normalize_time_given_tz,
            pd.Timestamp(2023, 3, 14, 12, 0, tz=pytz.timezone("US/Pacific")),
            pytz.timezone("UTC"),
        )  # case where timezone of timestamp input and tz input are different

    def test_check_if_valid_timestamp(self):
        """Tests check_if_valid_timestamp function"""
        self.assertRaises(
            ValueError, rows_util.check_if_valid_timestamp, "test string"
        )  # case where input is not pd.Timestamp object
        self.assertTrue(pd.isna(rows_util.check_if_valid_timestamp("\n\n  ")))
        # case where input is empty string with spaces and newlines

    @parameterized.expand(
        [
            [pd.NA, pd.NA, pd.NA, pd.NA, 0],  # case where all inputs are pd.NA
            [pd.NaT, pd.NaT, pd.NaT, pd.NaT, 0],  # case where all inputs are pd.NaT
            [pd.NA, pd.NA, 3.442, 1.32, 2.122],  # case where some inputs are pd.NA
        ]
    )
    def test_compute_cogs_rev_err(
        self,
        line_haul: float,
        fuel: float,
        accessorial: float,
        total: float,
        error: float,
    ):
        """Checks computer_cogs_rev_err returns correct output"""
        self.assertEqual(
            rows_util.compute_cogs_rev_err(line_haul, fuel, accessorial, total), error
        )

    @parameterized.expand(
        [
            [pd.NA, pd.NA, pd.NA, pd.NA, True],  # case where all inputs are pd.NA
            [pd.NaT, pd.NaT, pd.NaT, pd.NaT, True],  # case where all inputs are pd.NaT
            [3.12, 5.13421, 1.2342, pd.NA, True],  # case where total input is pd.NA
            [1.23, 2.45, 4.123, 4.4213, False],  # base case where all inputs are floats
        ]
    )
    def test_is_cogs_rev_na(
        self,
        line_haul: float,
        fuel: float,
        accessorial: float,
        total: float,
        all_NA: bool,
    ):
        """Checks is_cogs_rev_na returns correct boolean output"""
        self.assertEqual(
            rows_util.is_cogs_rev_na(line_haul, fuel, accessorial, total), all_NA
        )

    def test_detect_customer_direct(self):
        """Tests base case and assertion error case for detect_customer_direct function"""
        self.assertRaises(
            AssertionError, rows_util.detect_customer_direct, "Safeway riteaid"
        )  # Assertion Error case having multiple customer direct destinations
        self.assertEqual(
            rows_util.detect_customer_direct("Safeway"), "Safeway"
        )  # base case


if __name__ == "__main__":
    unittest.main()
